syntax = "proto3";

package trpc.KEP.plugin_config_server;
option go_package = "git.woa.com/dialogue-platform/lke_proto/pb-protocol/plugin_config_server";

import "trpc.proto";

import "bot-task-config-server/data-sync.proto";

service PluginConfig {
  // 获取所有插件
  // @alias=/ListPlugins
  rpc ListPlugins(ListPluginsReq) returns (ListPluginsRsp){}

  // 创建插件
  // @alias=/CreatePlugin
  rpc CreatePlugin(CreatePluginReq) returns (CreatePluginRsp){}

  // 编辑插件
  // @alias=/ModifyPlugin
  rpc ModifyPlugin(ModifyPluginReq) returns (ModifyPluginRsp){}

  // 创建MCP插件
  // @alias=/CreateMCPPlugin
  rpc CreateMCPPlugin(CreateMCPPluginReq) returns (CreateMCPPluginRsp){}

  // 编辑MCP插件
  // @alias=/ModifyMCPPlugin
  rpc ModifyMCPPlugin(ModifyMCPPluginReq) returns (ModifyMCPPluginRsp){}

  // 校验MCP server可用性并保存结果
  // @alias=/CheckMCPServer
  rpc CheckMCPServer(CheckMCPServerReq) returns (CheckMCPServerRsp){}

  // 删除插件
  // @alias=/DeletePlugin
  rpc DeletePlugin(DeletePluginReq) returns (DeletePluginRsp){}

  // 获取插件详情
  // @alias=/DescribePlugin
  rpc DescribePlugin(DescribePluginReq) returns (DescribePluginRsp){}

  // 获取工具详情
  // @alias=/ListTools
  rpc ListTools(ListToolsReq) returns (ListToolsRsp){}

  // 工具校验
  // @alias=/CheckTool
  rpc CheckTool(CheckToolReq) returns (CheckToolRsp){}

  // 查询工具引用信息
  // @alias=/ListToolRefs
  rpc ListToolRefs(ListToolRefsReq) returns (ListToolRefsRsp){}

  // 应用添加工具
  // @alias=/AddAppTool
  rpc AddAppTool(AddAppToolReq) returns (AddAppToolRsp) {}

  // 应用删除工具
  // @alias=/DeleteAppTool
  rpc DeleteAppTool(DeleteAppToolReq) returns (DeleteAppToolRsp) {}

  // 保存应用对工具的配置
  // @alias=/SaveAppTool
  rpc SaveAppTool(SaveAppToolReq) returns (SaveAppToolRsp) {}

  // 获取应用的工具列表
  // @alias=/ListAppTools
  rpc ListAppTools(ListAppToolsReq) returns (ListAppToolsRsp) {}

  // 获取应用的工具详情
  // @alias=/DescribeAppTool
  rpc DescribeAppTool(DescribeAppToolReq) returns (DescribeAppToolRsp){}

  // 获取MCP插件必填且应用未配置过的信息
  // @alias=/GetAppPluginRequiredHeader
  rpc GetAppPluginRequiredHeader(GetAppPluginRequiredHeaderReq) returns (GetAppPluginRequiredHeaderRsp);

  // 获取应用对插件的配置信息，目前仅配置MCP插件header信息
  // @alias=/DescribeAppPlugin
  rpc DescribeAppPlugin(DescribeAppPluginReq) returns (DescribeAppPluginRsp);

  // 应用保存对插件的配置，目前仅配置MCP插件header信息
  // @alias=/SaveAppPlugin
  rpc SaveAppPlugin(SaveAppPluginReq) returns (SaveAppPluginRsp){}

  // ======================= 发布 ==========================
  // 获取应用插件发布列表
  // @alias=/ListAppToolReleasePreview
  rpc ListAppToolReleasePreview(ListAppToolReleasePreviewReq) returns (ListAppToolReleasePreviewRsp);
  // ======================================================
}

service PluginConfigApi {
  // 获取所有插件
  // @alias=/ListPlugins
  rpc ListPlugins(ListPluginsReq) returns (ListPluginsRsp){}

  // 创建插件
  // @alias=/CreatePlugin
  rpc CreatePlugin(CreatePluginReq) returns (CreatePluginRsp){}

  // 创建MCP插件内部接口
  // @alias=/CreateMCPPluginInner
  rpc CreateMCPPluginInner(CreateMCPPluginInnerReq) returns (CreateMCPPluginInnerRsp){}

  // 编辑插件
  // @alias=/ModifyPlugin
  rpc ModifyPlugin(ModifyPluginReq) returns (ModifyPluginRsp){}

  // 编辑MCP插件内部接口
  // @alias=/ModifyMCPPluginInner
  rpc ModifyMCPPluginInner(ModifyMCPPluginInnerReq) returns (ModifyMCPPluginInnerRsp){}

  // 删除插件
  // @alias=/DeletePlugin
  rpc DeletePlugin(DeletePluginReq) returns (DeletePluginRsp){}

  // 获取插件详情
  // @alias=/DescribePlugin
  rpc DescribePlugin(DescribePluginReq) returns (DescribePluginRsp){}

  // 获取工具详情
  // @alias=/ListTools
  rpc ListTools(ListToolsReq) returns (ListToolsRsp){}

  // 获取工具详情，与批量的差异是能够获取到鉴权信息
  // @alias=/DescribeTool
  rpc DescribeTool(DescribeToolReq) returns (DescribeToolRsp){}

  // 工具校验
  // @alias=/CheckTool
  rpc CheckTool(CheckToolReq) returns (CheckToolRsp){}

  // 获取应用的工具信息列表 chat调用
  // @alias=/ListAppTools
  rpc ListAppToolsInfo(ListAppToolsInfoReq) returns (ListAppToolsInfoRsp) {}

  // 添加知识库问答插件工具
  // @alias=/AddKnowledgeQATool
  rpc AddKnowledgeQATool(AddKnowledgeQAToolReq) returns (AddKnowledgeQAToolRsp) {}

  // 校验是否有权限添加插件工具
  rpc CheckPermission(CheckPermissionReq) returns (CheckPermissionRsp) {}

  // ======================= 发布任务 ==========================

  // 获取未发布的数量
  // @alias=/GetUnreleasedCount
  rpc GetUnreleasedCount(trpc.KEP.bot_task_config_server.GetUnreleasedCountReq) returns (trpc.KEP.bot_task_config_server.GetUnreleasedCountRsp);

  // 发送同步数据任务事件, 任务采集、任务发布、任务暂停重试
  // @alias=/SendDataSyncTaskEvent
  rpc SendDataSyncTaskEvent(trpc.KEP.bot_task_config_server.SendDataSyncTaskEventReq) returns (trpc.KEP.bot_task_config_server.SendDataSyncTaskEventRsp);

  // [单个]获取同步任务, 详情, 状态等
  // @alias=/GetDataSyncTask
  rpc GetDataSyncTask(trpc.KEP.bot_task_config_server.GetDataSyncTaskReq) returns (trpc.KEP.bot_task_config_server.GetDataSyncTaskRsp);

  // ===========================================================

  // ======================= 内部后台接口 ==========================

  // 同步应用的工具信息到redis
  // @alias=/SyncAppToolRedis
  rpc SyncAppToolRedis(SyncAppToolRedisReq) returns (SyncAppToolRedisRsp) {}

  // ===========================================================


}

message ListPluginsReq {
  enum QueryTypeEnum {
    NAME = 0; // 插件名
    ID = 1; // 插件id
  }

  enum PluginTypeEnum {
    ALL = 0; // 所有插件
    CUSTOM = 1; // 自定义插件
    OFFICIAL = 2; // 官方插件
    THIRD_PARTY = 3; // 第三方插件
  }

  enum ModuleEnum {
    MODULE_ALL = 0; // 所有模块
    MODULE_AGENT = 1; // agent模式模块
    MODULE_WORKFLOW = 2; // 工作流模块
    MODULE_STANDARD = 3; // 标准模式模块
  }

  string Query = 1; // 查询内容
  int32 PageSize = 2; // 每页大小
  int32 PageNumber = 3; // 页码
  QueryTypeEnum QueryType = 4; // 查询类型，默认工具NAME查询
  PluginTypeEnum PluginType = 5; // 插件类型
  repeated string PluginIds = 6; // 插件id列表
  // 可见模块，插件中心调用可以不填或者填ModuleAll，agent模式中获取填ModuleAgent，工作流模式中获取填ModuleWorkflow，标准模式中获取填ModuleStandard
  ModuleEnum Module = 7;
  // uin信息
  string uin = 8;
  repeated CreateTypeEnum CreateTypes = 9; // 插件创建类型，0-服务 1-代码 2-MCP 空表示不过滤
}

message ListPluginsRsp {
  repeated PluginInfo Plugins = 1;
  int32 Total = 2;
  int32 PageNumber = 3;
}

message CreatePluginReq {
  string Name = 1; // 插件名称
  string Desc = 2; // 插件描述信息
  string IconUrl = 3; // 插件图标url
  AuthTypeEnum AuthType = 4; // 授权方式，仅自定义插件返回
  AuthInfo AuthInfo = 5; // 授权信息，仅自定义插件返回
  string OpenApi = 6; // OpenAPI描述，仅自定义插件返回
  PluginTypeEnum PluginType = 7; // 插件类型
  repeated ToolInfo Tools = 8; // 工具信息
  CreateTypeEnum CreateType = 9; // 创建方式
}

message CreatePluginRsp {
  string PluginId = 1;
}

message ModifyPluginReq {
  string PluginId = 1; // 插件id
  string Name = 2; // 插件名称
  int32 PluginVersion = 3; // 插件版本，每次更新后会加1
  string Desc = 4; // 插件描述信息
  string IconUrl = 5; // 插件图标url
  AuthTypeEnum AuthType = 6; // 授权方式，仅自定义插件返回
  AuthInfo AuthInfo = 7; // 授权信息，仅自定义插件返回
  string OpenApi = 8; // OpenAPI描述，仅自定义插件返回
  PluginTypeEnum PluginType = 9; // 插件类型
  repeated ToolInfo Tools = 10; // 工具信息
  CreateTypeEnum CreateType = 11; // 创建方式
}

message ModifyPluginRsp {

}

// PluginHeader MCP插件header信息
message PluginHeader {
  string ParamName = 1; // 参数名称
  string ParamValue = 2; // 参数值
  bool GlobalHidden = 3; // header参数配置是否隐藏不可见，true-隐藏不可见，false-可见
  bool IsRequired = 4; // header参数是否必填，true-必填，false-非必填
}

message CreateMCPPluginReq {
  message MCPPluginItem {
    string Name = 1; // 插件名称
    string Desc = 2; // 插件描述信息
    string IconUrl = 3; // 插件图标url
    string McpServerUrl = 4; // MCP server地址
    repeated PluginHeader Headers = 5; // MCP server header信息
    int32 Timeout = 6; // 超时时间，单位秒
    int32 SseReadTimeout = 7; // sse服务超时时间，单位秒
  }
  repeated MCPPluginItem Plugins = 1; // 插件信息
}


message CreateMCPPluginRsp {
  repeated string PluginIds = 1; // 创建成功返回对应的插件id
}

message ModifyMCPPluginReq {
  string PluginId = 1; // 插件id
  string Name = 2; // 插件名称
  int32 PluginVersion = 3; // 插件版本，每次更新后会加1
  string Desc = 4; // 插件描述信息
  string IconUrl = 5; // 插件图标url
  string McpServerUrl = 6; // MCP server地址
  repeated PluginHeader Headers = 7; // MCP server header信息
  int32 Timeout = 8; // 超时时间，单位秒
  int32 SseReadTimeout = 9; // sse服务超时时间，单位秒
}

message ModifyMCPPluginRsp {

}

message CheckMCPServerReq {
  string PluginId = 1; // 插件id
}

message CheckMCPServerRsp {
  int32 Status = 1; //插件状态 1-成功(可用)，2-不可用
}

message CreateMCPPluginInnerReq {
  message PluginHeader {
    string ParamName = 1; // 参数名称
    string ParamValue = 2; // 参数值
    int32 ShowType = 3; // header参数展示类型，1-展示参数名称，2-不展示该参数
    bool IsRequired = 4; // header参数是否必填，true-必填，false-非必填
  }
  string Name = 1; // 插件名称
  string Desc = 2; // 插件描述信息
  string IconUrl = 3; // 插件图标url
  string Module = 4; //工具所属模块
  string McpServerUrl = 5; // MCP server地址
  repeated PluginHeader Headers = 6; // MCP server header信息
  int32 Timeout = 7; // 超时时间，单位秒
  int32 SseReadTimeout = 8; // sse服务超时时间，单位秒
  string RunCommand = 9; //启动命令
  string RunArgs = 10;    //启动参数
  string RunEnv = 11;    //环境变量
  string Developer = 12; //开发者
  PluginTypeEnum PluginType = 13; // 插件类型
}


message CreateMCPPluginInnerRsp {
  string PluginId = 1;
}

message ModifyMCPPluginInnerReq {
  message PluginHeader {
    string ParamName = 1; // 参数名称
    string ParamValue = 2; // 参数值
    int32 ShowType = 3; // header参数展示类型，1-展示参数名称，2-不展示该参数
    bool IsRequired = 4; // header参数是否必填，true-必填，false-非必填
  }
  string PluginId = 1; // 插件id
  string Name = 2; // 插件名称
  int32 PluginVersion = 3; // 插件版本，每次更新后会加1
  string Desc = 4; // 插件描述信息
  string IconUrl = 5; // 插件图标url
  string Module = 6; //工具所属模块
  string McpServerUrl = 7; // MCP server地址
  repeated PluginHeader Headers = 8; // MCP server header信息
  int32 Timeout = 9; // 超时时间，单位秒
  int32 SseReadTimeout = 10; // sse服务超时时间，单位秒
  string RunCommand = 11; //启动命令
  string RunArgs = 12;    //启动参数
  string RunEnv = 13;    //环境变量
  string Developer = 14; //开发者
  PluginTypeEnum PluginType = 15; // 插件类型
}

message ModifyMCPPluginInnerRsp {

}

message DeletePluginReq {
  repeated string PluginIds = 1;
}

message DeletePluginRsp {

}

message DescribePluginReq {
  string PluginId = 1; // 插件id
}

message DescribePluginRsp {
  string PluginId = 1; // 插件id
  string Name = 2; // 插件名称
  int32 PluginVersion = 3; // 插件版本，每次更新后会加1
  string Desc = 4; // 插件描述信息
  string IconUrl = 5; // 插件图标url
  AuthTypeEnum AuthType = 6; // 授权方式，仅自定义插件返回
  AuthInfo AuthInfo = 7; // 授权信息，仅自定义插件返回
  string OpenApi = 8; // OpenAPI描述，仅自定义插件返回
  UserInfo UserInfo = 9; // 创建者信息
  PluginTypeEnum PluginType = 10; // 插件类型
  repeated ToolInfo Tools = 11; // 工具信息
  string CreateTime = 12; // 创建时间
  string UpdateTime = 13; // 更新时间
  FinanceTypeEnum FinanceType = 14; // 计费类型
  CreateTypeEnum CreateType = 15; // 创建方式
  int32 Status = 16; //插件状态 1-成功(可用)，2-不可用
  string McpServerUrl = 17 ; // MCP server地址
  repeated PluginHeader Headers = 18; // mcp插件的header参数
  int32 Timeout = 19; // 超时时间，单位秒
  int32 SseReadTimeout = 20 ; // sse服务超时时间，单位秒
}

message ListToolsReq {
  repeated string ToolIds = 1; // 工具id
}

message ListToolsRsp {
  repeated ToolInfo Tools = 1; // 工具信息
}

message DescribeToolReq {
  string ToolId = 1; // 工具id
  string PluginId = 2; // 插件id
}

message DescribeToolRsp {
  string ToolId = 1; // 工具id
  string PluginId = 2; // 插件id
  string Name = 3; // 工具名称
  string Desc = 4; // 工具描述信息
  string Url = 5; // 请求的url
  string Path = 6; // 请求的path
  string Method = 7; // 请求method
  repeated RequestParam Header = 8; // 头信息
  repeated RequestParam Query = 9; //  输入参数query
  repeated RequestParam Body = 10; //  输入参数body
  repeated ResponseParam Outputs = 11; // 输出参数
  repeated RequestParam Inputs = 12; // 输入参数
  ToolExample Example = 13; // 示例
  AuthTypeEnum AuthType = 14; // 授权方式
  AuthInfo AuthInfo = 15; // 授权信息
  string Code = 16; // 代码
  CreateTypeEnum CreateType = 17; // 创建方式
  MCPServerInfo MCPServer = 18; // MCP插件的配置信息
  bool IsStreamReply = 19; // 是否是流式回复
}

message ToolExample {
  string Request = 1; // 请求示例，json字符串
  string Response = 2; // 回复示例，json字符串
}

enum TypeEnum {
  STRING = 0; // 默认值是string，如果不填就按string处理
  INT = 1;
  FLOAT = 2;
  BOOL = 3;
  OBJECT = 4;
  ARRAY_STRING = 5;
  ARRAY_INT = 6;
  ARRAY_FLOAT = 7;
  ARRAY_BOOL = 8;
  ARRAY_OBJECT = 9;
  NULL = 99;           // 空值
  UNSPECIFIED = 100;    // 未指定类型，用于类型为OneOf和AnyOf的场景
}

// 定义工具的请求信息
message RequestParam {
  string Name = 1;  // 参数名称
  string Desc = 2;  // 参数描述
  TypeEnum Type = 3;// 参数类型
  bool IsRequired = 4; // 是否必选
  string DefaultValue = 5; // 默认值
  repeated RequestParam SubParams = 6;  // 子参数,ParamType 是OBJECT 或 ARRAY<>类型有用
  bool GlobalHidden = 7;  //插件参数配置是否隐藏不可见，true-隐藏不可见，false-可见
  repeated RequestParam OneOf = 8;  // OneOf类型的参数
  repeated RequestParam AnyOf = 9; // AnyOf类型的参数
}

// 定义工具的回复信息
message ResponseParam {
  string Name = 1; // 参数名称
  string Desc = 2; // 变量描述
  TypeEnum Type = 3; // 参数类型
  repeated ResponseParam SubParams = 4; // 只对 OBJECT 或 ARRAY_OBJECT 类型有用
  bool IsIncrementReply = 5; // 工具如果是流式回复，该字段是否是增量回复
}

message CheckToolReq {
  string Url = 1; // 请求的url
  string Path = 2; // 请求的path
  string Method = 3; // 请求method
  string HeaderValue = 4; // 头信息
  string QueryValue = 5; // 输入参数query
  string BodyValue = 6; // 输入参数body
  repeated RequestParam Header = 7; // 头信息
  repeated RequestParam Query = 8; // 输入参数query
  repeated RequestParam Body = 9; // 输入参数body
  repeated ResponseParam Outputs = 10; // 输出参数
  AuthTypeEnum AuthType = 11; // 授权方式
  AuthInfo AuthInfo = 12; // 授权信息
  string Code = 13; // 代码
  CreateTypeEnum CreateType = 14; // 创建方式 0-服务 1-代码 2-MCP
  bool IsStreamReply = 15; // 是否是流式回复
}

message CheckToolRsp {
  string Result = 1; // 执行结果，解析后的json字符串
  string RawResult = 2; // 执行结果，解析前的原始字符串
}

// 用户信息
message UserInfo {
  string Name = 1; // 名称
  string AvatarUrl = 2; // 用户头像
}

// 插件类型
enum PluginTypeEnum {
  CUSTOM= 0; // 自定义插件
  OFFICIAL = 1; // 官方插件
  THIRD_PARTY = 2; // 第三方插件 目前用于第三方实现的mcp server
}

// 授权方式
enum AuthTypeEnum {
  NONE = 0; // 无鉴权
  API_KEY = 1; // api key鉴权
}

// 计费类型
enum FinanceTypeEnum {
  FREE = 0; // 免费
  PAID = 1; // 收费
}

// 创建方式
enum CreateTypeEnum {
  SERVICE = 0; // 服务
  CODE = 1; // 代码
  MCP = 2; // MCP
}

enum EnvType {
  TEST = 0; // 测试环境
  PROD = 1; // 正式环境
}

enum WhiteListTypeEnum {
  OPEN = 0; // 非白名单插件 全量开放
  IN_WHITELIST = 1; // 在白名单里
  NOT_IN_WHITELIST = 2; // 不在白名单里,需要提交申请
}

message PluginInfo {
  string PluginId = 1; // 插件id
  string Name = 2; // 插件名称
  int32 PluginVersion = 3; // 插件版本，每次更新后会加1
  string Desc = 4; // 插件描述信息
  string IconUrl = 5; // 插件图标url
  AuthTypeEnum AuthType = 6; // 授权方式，仅自定义插件返回
  AuthInfo AuthInfo = 7; // 授权信息，仅自定义插件返回
  string OpenApi = 8; // OpenAPI描述，仅自定义插件返回
  UserInfo UserInfo = 9; // 创建者信息
  PluginTypeEnum PluginType = 10; // 插件类型
  repeated ToolInfo Tools = 11; // 工具信息
  string CreateTime = 12; // 创建时间
  string UpdateTime = 13; // 更新时间
  FinanceTypeEnum FinanceType = 14; // 计费类型
  CreateTypeEnum CreateType = 15; // 创建方式
  int32 Status = 16; //插件状态 1-成功(可用)，2-不可用
  repeated PluginHeader Headers = 17; // mcp插件的header参数
  WhiteListTypeEnum WhiteListType = 18; // 白名单类型
}

message ToolInfo {
  string ToolId = 1; // 工具id
  string PluginId = 2; // 插件id
  string Name = 3; // 工具名称
  string Desc = 4; // 工具描述信息
  string Url = 5; // 请求的url
  string Path = 6; // 请求的path
  string Method = 7; // 请求method
  repeated RequestParam Header = 8; // 头信息
  repeated RequestParam Query = 9; //  输入参数query
  repeated RequestParam Body = 10; //  输入参数body
  repeated ResponseParam Outputs = 11; // 输出参数
  repeated RequestParam Inputs = 12; // 输入参数
  ToolExample Example = 13; // 示例
  string Code = 14; // 代码
  bool IsStreamReply = 15; // 是否是流式回复
}

// 鉴权信息，后续可能要支持Oauth2.0
message AuthInfo {
  // 密钥位置
  enum KeyLocationTypeEnum {
    HEADER = 0; // 头鉴权
    QUERY = 1; // 请求信息鉴权
  }

  KeyLocationTypeEnum KeyLocation = 1; // 密钥位置 HEADER/QUERY
  string KeyParamName = 2; // 密钥参数名
  string KeyParamValue = 3; // 密钥参数值
}

message ListToolRefsReq {
  string ToolId = 1; // 工具id
  string PluginId = 2; // 插件id
}

message ListToolRefsRsp{
  repeated ToolRef ToolRefs = 1; // 工具引用信息
}

message ToolRef {
  enum RefTypeEnum {
    AGENT = 0; // agent引用
    WORKFLOW = 1; // 工作流引用
    STANDARD = 2; // 标准模式引用
  }

  string ToolId = 1; // 工具id
  string PluginId = 2; // 插件id
  string AppId = 3; // 应用id
  string AppName = 4; // 应用名称
  string WorkflowId = 5; // 工作流id
  string WorkflowName = 6; // 工作流名称
  RefTypeEnum RefType = 7; // 引用类型
}

message AddAppToolReq {
  string AppBizId = 1; // 应用ID
  string PluginId = 2; // 插件ID
  string ToolId = 3; // 工具ID
}

message AddAppToolRsp {

}

message DeleteAppToolReq {
  string AppBizId = 1; // 应用ID
  repeated string ToolIds = 2; // 工具ID
}

message DeleteAppToolRsp {

}

// 应用工具的请求参数定义
message AppToolReqParam {
  string Name = 1;  // 参数名称
  string Desc = 2;  // 参数描述
  TypeEnum Type = 3;// 参数类型
  bool IsRequired = 4; // 是否必选
  string DefaultValue = 5; // 默认值
  repeated AppToolReqParam SubParams = 6;  // 子参数,ParamType 是OBJECT 或 ARRAY<>类型有用
  bool AgentHidden = 7;  //agent模式下模型是否可见
}

// 应用工具的响应参数定义
message AppToolRspParam {
  string Name = 1; // 参数名称
  string Desc = 2; // 变量描述
  TypeEnum Type = 3; // 参数类型
  repeated AppToolRspParam SubParams = 4; // 只对 OBJECT 或 ARRAY_OBJECT 类型有用
  bool AgentHidden = 5;  //agent模式下模型是否可见
}

message SaveAppToolReq {
  string AppBizId = 1; // 应用ID
  string PluginId = 2; // 插件ID
  string ToolId = 3; // 工具ID
  string ToolDesc = 4 ; //自定义插件描述
  repeated AppToolReqParam Inputs = 5; //输入参数自定义配置
  repeated AppToolRspParam Outputs = 6; //输出参数自定义配置
}

message SaveAppToolRsp {

}


message ListAppToolsReq {
  string AppBizId = 1; // 应用ID
}

message ListAppToolsRsp {
  repeated AppToolItem Tools = 1; // 应用的工具信息
}

// 应用的工具信息（前端展示用）
message AppToolItem {
  string PluginId = 1; // 插件ID
  string PluginName = 2; // 插件名称
  string IconUrl = 3; // 插件图标url
  string ToolId = 4; // 工具id
  string ToolName = 5; // 工具名称
  string ToolDesc = 6; // 工具描述信息
  repeated AppToolReqParam Inputs = 7; // 输入参数
  bool IsBindingKnowledge = 8; //该工具是否和知识库绑定
  CreateTypeEnum CreateType = 9; // 创建方式 0-服务 1-代码 2-MCP
  int32 Status = 10; //插件状态 1-成功(可用)，2-不可用
  repeated AppPluginHeader Headers = 11; // 应用配置的插件header信息
}

message DescribeAppToolReq {
  string AppBizId = 1; // 应用ID
  string ToolId = 2; // 工具id
}

message DescribeAppToolRsp {
  string ToolId = 1; // 工具id
  string ToolName = 2; // 工具名称
  string ToolDesc = 3; // 工具描述信息
  repeated AppToolReqParam Inputs = 4; // 输入参数
  repeated AppToolRspParam Outputs = 5; // 输出参数
}

message ListAppToolsInfoReq {
  string AppBizId = 1; // 应用ID
  EnvType EnvTag = 2 ; // 环境标识
}

message ListAppToolsInfoRsp {
  repeated AppToolInfo Tools = 1; // 应用的工具信息
}

// 应用配置MCP插件header信息
message AppPluginHeader {
  string ParamName = 1; // 参数名称
  string ParamValue = 2; // 参数值
}

message GetAppPluginRequiredHeaderReq {
  string AppBizId = 1; // 应用id
  string PluginId = 2; // 插件id
}

message GetAppPluginRequiredHeaderRsp {
  repeated AppPluginHeader Headers = 1; // mcp插件必填的header参数，仅对内置官方插件且应用未配置过才返回
}

message DescribeAppPluginReq {
  string AppBizId = 1; // 应用id
  string PluginId = 2; // 插件id
}

message DescribeAppPluginRsp {
   repeated AppPluginHeader Headers = 1; // 应用配置的插件header信息
}

message SaveAppPluginReq {
  string AppBizId = 1; // 应用id
  string PluginId = 2; // 插件id
  string ToolId = 3; // 工具id 配置的是插件维度，这里把对应的工具id也带上来
  repeated AppPluginHeader Headers = 4; // 应用配置的插件header信息
}

message SaveAppPluginRsp {

}

// 应用的工具信息（后端调用）
message AppToolInfo {
  string PluginId = 1; // 插件ID
  string PluginName = 2; // 插件名称
  string IconUrl = 3; // 插件图标url
  PluginTypeEnum PluginType = 4; // 插件类型
  string ToolId = 5; // 工具id
  string ToolName = 6; // 工具名称
  string ToolDesc = 7; // 工具描述信息
  repeated AppToolReqParam Inputs = 8; // 输入参数
  repeated AppToolRspParam Outputs = 9; // 输出参数
  CreateTypeEnum CreateType = 10; // 创建方式 0-服务 1-代码 2-MCP
  MCPServerInfo MCPServer = 11; // MCP插件的配置信息
}

// MCP插件的配置信息
message MCPServerInfo {
  string McpServerUrl = 1; // mcp server地址
  map<string,string> Headers = 2; // mcp server header信息
  int32 Timeout = 3; // 超时时间，单位秒
  int32 SseReadTimeout = 4; // sse服务超时时间，单位秒
}

message AddKnowledgeQAToolReq {
  string AppBizId = 1; // 应用ID
}

message AddKnowledgeQAToolRsp{
}

// 获取应用插件发布列表请求
message ListAppToolReleasePreviewReq {
  uint64          BotBizId = 1 [(trpc.go_tag) = 'valid:"required~请传入正确的应用ID"']; // 机器人
  string          Query = 2; // 查询关键字, 用于模糊匹配标题
  uint64          StartTime = 3; // 任务更新时间起点, 时间单位 unix 秒
  uint64          EndTime = 4; // 任务更新时间止点, 时间单位 unix 秒
  repeated uint32 Actions = 5; // 状态, 状态值：1:新增, 2:修改, 3:删除, 4:发布
  uint32          PageNumber = 6; // 页码
  uint32          PageSize = 7 [(trpc.go_tag) = 'valid:"required,range(1|200)~每页数量在1到200之间"']; // 每页数量
  uint64          ReleaseBizId = 8; // 发布任务 ID
  repeated uint32 ReleaseStatus = 9; // 发布状态(2 待发布 3 发布中 4 已发布 5 发布失败)
}

// 获取应用插件发布列表返回
message ListAppToolReleasePreviewRsp {
  // AppTool 发布列表详情
  message AppTool {
    string        ToolId = 1; // 工具ID
    string        ToolName = 2; // 工具名称
    uint64        UpdateTime = 3; // 更新时间, unix 秒时间戳 (s)
    uint32        Action = 4; // 状态, 状态值：1:新增, 2:修改, 3:删除, 4:发布
    string        ActionDesc = 5; // 状态, 状态值：1:新增, 2:修改, 3:删除, 4:发布
    string        Message = 6; // 发布消息
  }
  uint32           Total = 1; // 总数
  repeated AppTool List = 2; // 列表
}

message SyncAppToolRedisReq {
  repeated string AppBizIds = 1; // 应用ID
  EnvType EnvTag = 2 ; // 环境标识
}

message SyncAppToolRedisRsp {
}

message CheckPermissionReq {
  // 模块类型枚举
  enum ModuleEnum {
    MODULE_UNKNOWN = 0;  // 未知模块
    MODULE_AGENT = 1;    // agent模式模块
    MODULE_WORKFLOW = 2; // 工作流模块
  }
  message CheckItem {
    string PluginId = 1; // 插件ID
  }
  string Uin = 1; // 用户uin
  ModuleEnum Module = 2; //业务模块 agent/workflow
  repeated CheckItem List = 3; // 校验的插件列表
}

message CheckPermissionRsp {
  // 权限状态枚举
  enum PermissionStatus {
    ALLOWED = 0;         // 有权限使用
    NOT_IN_WHITELIST = 1; // 不在白名单中，不能使用
  }

  //校验权限结果
  message Result {
    string PluginId = 1; // 插件ID
    PermissionStatus Status = 2; // 权限状态
  }
  repeated Result Results = 1; // 校验结果
}