syntax = "proto3";

package trpc.KEP.bot_admin_config_server;
option go_package = "git.woa.com/dialogue-platform/lke_proto/pb-protocol/bot_admin_config_server";

import "validate.proto";
import "bot-admin-common.proto";
import "bot-admin-custom-resource.proto";
import "bot-admin-file-manager-callback.proto";
import "bot-admin.proto";

// Api接口 不需要鉴权
service Api {
  // 创建企业
  rpc CreateCorp(CreateCorpReq) returns (CreateCorpRsp);
  // 企业列表
  rpc ListCorp(ListCorpReq) returns (ListCorpRsp);
  // 根据企业ID获取企业
  rpc GetCorp(GetCorpReq) returns (GetCorpRsp);
  // 修改企业安全审核策略
  rpc ModifyInfosecBizType(ModifyInfosecBizTypeReq) returns (ModifyInfosecBizTypeRsp);
  // 修改企业机器人token数
  rpc ModifyMaxTokenUsage(ModifyMaxTokenUsageReq) returns (ModifyMaxTokenUsageRsp);
  // 修改企业机器人字符数
  rpc ModifyMaxCharSize(ModifyMaxCharSizeReq) returns (ModifyMaxCharSizeRsp);
  // 审核企业
  rpc AuditCorp(AuditCorpReq) returns (AuditCorpRsp);
  // 修改企业机器人配额
  rpc ModifyCorpRobotQuota(ModifyCorpRobotQuotaReq) returns (ModifyCorpRobotQuotaRsp);
  // 获取企业员工列表
  rpc CorpStaffList(CorpStaffListReq) returns (CorpStaffListRsp);
  // 通过Ids获取员工信息列表
  rpc ListCorpStaffByIds(ListCorpStaffByIdsReq) returns (ListCorpStaffByIdsRsp);
  // 更新QA/Segment状态回调
  rpc ReleaseDetailNotify(ReleaseDetailNotifyReq) returns (ReleaseDetailNotifyRsp);
  // 更新发布任务回调
  rpc ReleaseNotify(ReleaseNotifyReq) returns (ReleaseNotifyRsp);
  // 获取临时链接
  rpc GetPresignedURL(GetPresignedURLReq) returns (GetPresignedURLRsp);
  // Deprecated 获取机器人
  rpc GetRobotInfo(GetRobotInfoReq) returns (GetRobotInfoRsp);
  // 获取机器人
  rpc DescribeRobotInfo(DescribeRobotInfoReq) returns (DescribeRobotInfoRsp);
  // 对话评测
  rpc SearchPreview(SearchPreviewReq) returns (SearchPreviewRsp);
  // 向量特征检索
  rpc Search(SearchReq) returns (SearchRsp);
  // 对话评测
  rpc CustomSearchPreview(CustomSearchPreviewReq) returns (CustomSearchPreviewRsp);
  // 查找
  rpc CustomSearch(CustomSearchReq) returns (CustomSearchRsp);
  // 匹配来源
  rpc MatchRefer(MatchReferReq) returns (MatchReferRsp);
  // 审核回调
  rpc AuditResultCallback(CheckResultReq) returns (CheckResultRsp);
  // 获取机器人列表
  rpc GetRobotList(GetRobotListReq) returns (GetRobotListRsp);
  // 获取应用列表
  rpc GetAppList(GetAppListReq) returns (GetAppListRsp);
  // 通过AppKey获取机器人
  rpc GetRobotByAppKey(GetRobotByAppKeyReq) returns (GetRobotByAppKeyRsp);
  // 通过AppKey获取应用
  rpc GetAppByAppKey(GetAppByAppKeyReq) returns (GetAppByAppKeyRsp);
  // 获取应用信息
  rpc GetAppInfo(GetAppInfoReq) returns (GetAppInfoRsp);
  // 编辑机器人信息
  rpc EditRobot(EditRobotReq) returns (EditRobotRsp);
  // 获取企业信息
  rpc GetCorpList(GetCorpListReq) returns (GetCorpListRsp);
  // 获取文档内容
  rpc GetDocs(GetDocsReq) returns (GetDocsRsp);

  // 拒答问题测评库查询
  rpc SearchPreviewRejectedQuestion(SearchPreviewRejectedQuestionReq) returns (SearchPreviewRejectedQuestionRsp);
  // 拒答问题线上库查询
  rpc SearchReleaseRejectedQuestion(SearchReleaseRejectedQuestionReq) returns (SearchReleaseRejectedQuestionRsp);
  // 获取拒答问题列表
  rpc ListRejectedQuestion(ListRejectedQuestionReq) returns (ListRejectedQuestionRsp);
  // 添加不满意回复
  rpc AddUnsatisfiedReply(AddUnsatisfiedReplyReq) returns (AddUnsatisfiedReplyRsp);
  // 获取QA列表
  rpc ListQA(ListQAReq) returns (ListQARsp);

  // 获取admin任务列表
  rpc GetAdminTaskList(GetAdminTaskListReq) returns (GetAdminTaskListRsp);
  // 获取admin历史任务列表
  rpc GetAdminTaskHistoryList(GetAdminTaskHistoryListReq) returns (GetAdminTaskHistoryListRsp);
  // 获取获取vector_doc任务列表
  rpc GetVectorDocTaskList(GetVectorDocTaskListReq) returns (GetVectorDocTaskListRsp);
  // 获取vector_doc任务历史列表
  rpc GetVectorDocTaskHistoryList(GetVectorDocTaskHistoryListReq) returns (GetVectorDocTaskHistoryListRsp);
  // 发布记录列表
  rpc GetReleaseList(GetReleaseListReq) returns (GetReleaseListRsp);

  // 添加企业成员
  rpc AddCorpStaff(AddCorpStaffReq) returns (AddCorpStaffRsp);
  // 修改企业员工密码
  rpc EditCorpStaffPassword(EditCorpStaffPasswordReq) returns (EditCorpStaffPasswordRsp);
  // 企业员工退出企业
  rpc LeaveCorp(LeaveCorpReq) returns (LeaveCorpRsp);

  // 更新审核单状态
  rpc UpdateAuditStatus(UpdateAuditStatusReq) returns (UpdateAuditStatusRsp);
  // 获取机器人默认配置信息
  rpc GetRobotDefaultConfig(GetRobotDefaultConfigReq) returns (GetRobotDefaultConfigRsp);
  // 清除机器人自定义配置
  rpc ClearRobotCustomConfig(ClearRobotCustomConfigReq) returns (ClearRobotCustomConfigRsp);
  // 全局干预知识列表
  rpc ListGlobalKnowledge(ListGlobalKnowledgeReq) returns (ListGlobalKnowledgeRsp);
  // 添加全局干预知识
  rpc AddGlobalKnowledge(AddGlobalKnowledgeReq) returns (AddGlobalKnowledgeRsp);
  // 删除全局干预知识
  rpc DelGlobalKnowledge(DelGlobalKnowledgeReq) returns (DelGlobalKnowledgeRsp);
  // 更新全局干预知识
  rpc UpdGlobalKnowledge(UpdGlobalKnowledgeReq) returns (UpdGlobalKnowledgeRsp);
  // 全局干预知识
  rpc GlobalKnowledge(GlobalKnowledgeReq) returns (GlobalKnowledgeRsp);
  // 强制同步全局干预知识
  rpc ForceSyncGlobalKnowledge(ForceSyncGlobalKnowledgeReq) returns (ForceSyncGlobalKnowledgeRsp);
  // 计算相似度
  rpc CustomSimilarity(CustomSimilarityReq) returns (CustomSimilarityRsp);
  // 启用企业请求
  rpc EnableCorp(EnableCorpReq) returns (EnableCorpRsp);
  // 禁用企业请求
  rpc DisableCorp(DisableCorpReq) returns (DisableCorpRsp);
  // 底座获取资源列表
  rpc GetCustomResource(GetCustomResourceReq) returns (GetCustomResourceRsp);
  // 底座通知产品开通
  rpc ActivateProduct(ActivateProductReq) returns (ActivateProductRsp);
  // 创建通知
  rpc CreateNotice(CreateNoticeReq) returns (CreateNoticeRsp);
  // 主账号维度创建通知
  rpc CreateNoticeByUin(CreateNoticeByUinReq) returns (CreateNoticeByUinRsp);
  // 查询集成商信息
  rpc DescribeIntegrator(DescribeIntegratorReq) returns (DescribeIntegratorRsp);
  // 通过appKey获取机器人业务id
  rpc DescribeRobotBizIDByAppKey(DescribeRobotBizIDByAppKeyReq) returns (DescribeRobotBizIDByAppKeyRsp);
  // 获取机器人最新发布状态
  rpc DescribeLatestReleaseStatus(DescribeLatestReleaseStatusReq) returns (DescribeLatestReleaseStatusRsp);
  // 试用开通
  rpc TrialProduct(TrialProductReq) returns (TrialProductRsp);
  // 编辑应用
  rpc EditApp(EditAppReq) returns (EditAppRsp);
  // 获取应用默认配置
  rpc GetAppDefaultConfig(GetAppDefaultConfigReq) returns (GetAppDefaultConfigRsp);
  // 清除应用自定义配置
  rpc ClearAppCustomConfig(ClearAppCustomConfigReq) returns(ClearAppCustomConfigRsp);
  // 获取意图
  rpc GetIntent(GetIntentReq) returns (GetIntentRsp);
  // 获取意图列表
  rpc ListIntent(ListIntentReq) returns (ListIntentRsp);
  // 创建意图
  rpc CreateIntent(CreateIntentReq) returns (CreateIntentRsp);
  // 更新意图
  rpc UpdateIntent(UpdateIntentReq) returns (UpdateIntentRsp);
  // 删除意图
  rpc DeleteIntent(DeleteIntentReq) returns (DeleteIntentRsp);
  // 获取策略绑定的意图列表
  rpc ListIntentByPolicyID(ListIntentByPolicyIDReq) returns (ListIntentByPolicyIDRsp);
  // 获取策略列表
  rpc ListIntentPolicy(ListIntentPolicyReq) returns (ListIntentPolicyRsp);
  // 创建策略
  rpc CreateIntentPolicy(CreateIntentPolicyReq) returns (CreateIntentPolicyRsp);
  // 更新策略
  rpc UpdateIntentPolicy(UpdateIntentPolicyReq) returns (UpdateIntentPolicyRsp);
  // 删除策略
  rpc DeleteIntentPolicy(DeleteIntentPolicyReq) returns (DeleteIntentPolicyRsp);
  // 获取未使用的意图列表
  rpc ListUnusedIntentKeyMap(ListUnusedIntentKeyMapReq) returns (ListUnusedIntentKeyMapRsp);
  // 获取策略列表映射关系
  rpc ListIntentPolicyKeyMap(ListIntentPolicyKeyMapReq) returns (ListIntentPolicyKeyMapRsp);
  // 新增企业自定义模型
  rpc CreateCorpCustomModel(CreateCorpCustomModelReq) returns (CreateCorpCustomModelRsp);

  // 获取版本配置
  rpc GetRobotConfigByVersionID(GetRobotConfigByVersionIDReq) returns (GetRobotConfigByVersionIDRsp);

  // ListAgentFeedbackInner 查询 agent反馈信息列表
  rpc ListAgentFeedbackInner(ListAgentFeedbackInnerReq) returns (ListAgentFeedbackInnerRsp);
  // UpdateFeedbackStatus 修改 agent反馈信息状态
  rpc UpdateAgentFeedbackStatus(UpdateAgentFeedbackStatusReq) returns (UpdateAgentFeedbackStatusRsp);
  // UpdateAgentFeedbackTapd 修改 agent反馈信息关联的tapd
  rpc UpdateAgentFeedbackTapd(UpdateAgentFeedbackTapdReq) returns (UpdateAgentFeedbackTapdRsp);
  // CountAgentFeedback 获取 agent新增反馈个数
  rpc CountAgentFeedback(CountAgentFeedbackReq) returns (CountAgentFeedbackRsp);
  // GetAgentFeedback 获取 agent反馈信息
  rpc GetAgentFeedback(GetAgentFeedbackReq) returns (GetAgentFeedbackRsp);
  // UpdateAgentFeedbackAndonInfo 修改 agent反馈信息状态
  rpc UpdateAgentFeedbackAndonInfo(UpdateAgentFeedbackAndonInfoReq) returns (UpdateAgentFeedbackAndonInfoRsp);

  rpc DeleteFeedbackByFlowIds(DeleteFeedbackByFlowIdsReq) returns(DeleteFeedbackByFlowIdsRsp);
  // ListFeedbackInner 查询反馈信息列表
  rpc ListFeedbackInner(ListFeedbackInnerReq) returns (ListFeedbackInnerRsp);
  // ListFeedbackByBizIDInner 查询反馈信息列表
  rpc ListFeedbackByBizIDInner(ListFeedbackByBizIDInnerReq) returns (ListFeedbackByBizIDInnerRsp);
  // UpdateFeedbackClassification 修改一级分类，二级分类
  rpc UpdateFeedbackClassification(UpdateFeedbackClassificationReq) returns (UpdateFeedbackClassificationRsp);
  // UpdateFeedbackAndonIfoReq 修改安灯状态信息
  rpc UpdateFeedbackAndonInfo(UpdateFeedbackAndonInfoReq) returns (UpdateFeedbackAndonInfoRsp);
  // UpdateFeedbackStatus 修改反馈信息状态
  rpc UpdateFeedbackStatus(UpdateFeedbackStatusReq) returns (UpdateFeedbackStatusRsp);
  // UpdateFeedbackTapd 修改反馈信息关联的tapd
  rpc UpdateFeedbackTapd(UpdateFeedbackTapdReq) returns (UpdateFeedbackTapdRsp);
  // CountFeedback 获取新增反馈个数
  rpc CountFeedback(CountFeedbackReq) returns (CountFeedbackRsp);
  // CountFeedback 获取新增反馈个数
  rpc GetFeedback(GetFeedbackReq) returns (GetFeedbackRsp);
  // 获取企业账户余额信息
  rpc DescribeAccountBalance(DescribeAccountBalanceReq) returns (DescribeAccountBalanceRsp);
  // GetAppType 获取应用类型
  rpc GetAppType(GetAppTypeReq) returns (GetAppTypeRsp);
  // 获取应用License
  rpc GetDescribeLicense(GetDescribeLicenseReq) returns (GetDescribeLicenseRsp);
  // GetExperienceApps 体验中心-获取体验应用列表
  rpc GetExperienceApps(GetExperienceAppsReq) returns (GetExperienceAppsRsp);
  // ModifyExperienceApp 修改体验应用
  rpc ModifyExperienceApp(ModifyExperienceAppReq) returns (ModifyExperienceAppRsp);
  // ListSelectDoc 选择文档列表
  rpc ListSelectDoc(ListSelectDocReq) returns (ListSelectDocRsp);
  // CreateCorpAndAssignPermission
  rpc CreateCorpAndAssignPermission(CreateCorpAndAssignPermissionReq) returns (CreateCorpAndAssignPermissionRsp);
  // CheckCorpAndPermission
  rpc CheckCorpAndPermission(CheckCorpAndPermissionReq) returns (CheckCorpAndPermissionRsp);
  // DescribeCropByUin
  rpc DescribeCropByUin(DescribeCropByUinReq) returns (DescribeCropByUinRsp);
  // GetListModel 获取企业模型列表
  rpc GetListModel(GetListModelReq) returns (GetListModelRsp);
  // ModifyAppInfosecBizType 修改应用安全审核策略
  rpc ModifyAppInfosecBizType(ModifyAppInfosecBizTypeReq) returns (ModifyAppInfosecBizTypeRsp);
  // GetTotalConcurrency 获取企业总并发数
  rpc GetTotalConcurrency(GetTotalConcurrencyReq) returns (GetTotalConcurrencyRsp);
  // MultiLock 加锁
  rpc MultiLock(MultiLockReq) returns (MultiLockRsp);
  // MultiUnlock 解锁
  rpc MultiUnlock(MultiUnlockReq) returns (MultiUnlockRsp);

  // 检查自定义参数是否被使用
  rpc CheckVarIsUsed(CheckVarIsUsedReq) returns (CheckVarIsUsedRsp);
  // 修改应用检索范围自定义参数
  rpc ModifyAppVar(ModifyAppVarReq) returns (ModifyAppVarRsp);

  // 清理应用知识资源回调结果
  rpc ClearAppKnowledgeCallback(ClearAppKnowledgeCallbackReq) returns(ClearAppKnowledgeCallbackRsp);
  // 清理应用流程资源回调
  rpc ClearAppFlowCallback(ClearAppFlowCallbackReq) returns (ClearAppFlowCallbackRsp);
  // 清理应用向量库资源回调
  rpc ClearAppVectorCallback(ClearAppVectorCallbackReq) returns (ClearAppVectorCallbackRsp);
  // 清理应用资源回调
  rpc ClearAppResourceCallback(ClearAppResourceCallbackReq) returns (ClearAppResourceCallbackRsp);

  // 查询插件关联的应用信息
  rpc GetAppByPluginId(GetAppByPluginIdReq) returns (GetAppByPluginIdRsp);
  // 查询用户昵称
  rpc GetCorpStaffName(GetCorpStaffNameReq) returns (GetCorpStaffNameRsp);
  // 获取模型的计费控制信息
  rpc GetModelFinanceInfo(GetModelFinanceInfoReq) returns(GetModelFinanceInfoRsp);
  // 获取企业模型信息
  rpc GetModelInfo(GetModelInfoReq) returns (GetModelInfoRsp);
  // 获取有效的体验应用信息
  rpc GetValidExperienceApps(GetValidExperienceAppsReq) returns (GetValidExperienceAppsRsp);
  // 获取模型列表信息
  rpc GetModelList(GetModelListReq) returns (GetModelListRsp);
  // 获取应用对话query输入长度限制
  rpc GetAppChatInputNum(GetAppChatInputNumReq) returns (GetAppChatInputNumRsp);
  // 创建Prompt版本信息
  rpc CreatePromptVersion(CreatePromptVersionReq) returns (CreatePromptVersionRsp);
  // 编辑prompt版本信息
  rpc EditPromptVersion(EditPromptVersionReq) returns (EditPromptVersionRsp);
  // 查询prompt版本信息
  rpc GetPromptVersionList(GetPromptVersionListReq) returns (GetPromptVersionListRsp);
  // 升级Prompt版本
  rpc UpgradePromptVersion(UpgradePromptVersionReq) returns(UpgradePromptVersionRsp);
  // 创建账户并发白名单
  rpc CreateCorpConcurrencyWL(CreateCorpConcurrencyWLReq) returns (CreateCorpConcurrencyWLRsp);
  // 修改账户并发白名单
  rpc EditCorpConcurrencyWL(EditCorpConcurrencyWLReq) returns(EditCorpConcurrencyWLRsp);
  // 删除账户并发白名单
  rpc DeleteCorpConcurrencyWL(DeleteCorpConcurrencyWLReq) returns (DeleteCorpConcurrencyWLRsp);
  // 查询账户并发白名单
  rpc GetCorpConcurrencyWLList(GetCorpConcurrencyWLListReq) returns(GetCorpConcurrencyWLListRsp);
  // 应用升级embedding开始
  rpc StartEmbeddingUpgradeApp(StartEmbeddingUpgradeAppReq) returns(StartEmbeddingUpgradeAppRsp);
  // 应用升级embedding结束
  rpc FinishEmbeddingUpgradeApp(FinishEmbeddingUpgradeAppReq) returns(FinishEmbeddingUpgradeAppRsp);
  // 通过应用业务ID获取应用信息，基于缓存读取
  rpc GetAppsByBizIDs(GetAppsByBizIDsReq) returns (GetAppsByBizIDsRsp);
  // 通过员工业务ID获取员工信息，基于缓存读取
  rpc GetCorpStaffsByBizIDs(GetCorpStaffsByBizIDsReq) returns (GetCorpStaffsByBizIDsRsp);
  // 复制应用配置
  rpc CopyAppConfig(CopyAppConfigReq) returns(CopyAppConfigRsp);
  // 更新账户自定义模型白名单
  rpc EditCorpCustomModelWL(EditCorpCustomModelWLReq) returns (EditCorpCustomModelWLRsp);
  // 删除账户自定义模型白名单
  rpc DeleteCorpCustomModelWL(DeleteCorpCustomModelWLReq) returns (DeleteCorpCustomModelWLRsp);
  // 获取账户自定义模型白名单列表
  rpc GetCorpCustomModelWLList(GetCorpCustomModelWLListReq) returns(GetCorpCustomModelWLListRsp);
  // 获取账户模型默认的QPM、TPM上限值
  rpc GetCorpModelQpmTpmLimit(GetCorpModelQpmTpmLimitReq) returns (GetCorpModelQpmTpmLimitRsp);
  // 获取账户模型的默认QPM、TPM值
  rpc GetDefaultQpmTpmLimit(GetDefaultQpmTpmLimitReq) returns (GetDefaultQpmTpmLimitRsp);
  // 编辑账户模型QPM、TPM配置
  rpc EditCorpQpmTpmLimit(EditCorpQpmTpmLimitReq) returns(EditCorpQpmTpmLimitRsp);
  // 删除账户模型QPM、TPM配置
  rpc DeleteCorpQpmTpmLimit(DeleteCorpQpmTpmLimitReq) returns(DeleteCorpQpmTpmLimitRsp);
  // 获取账户模型QPM、TPM配置
  rpc GetCorpQpmTpmLimitList(GetCorpQpmTpmLimitListReq) returns(GetCorpQpmTpmLimitListRsp);
  // 创建共享知识库应用
  rpc CreateShareKnowledgeBaseApp(CreateShareKnowledgeBaseAppReq) returns(CreateShareKnowledgeBaseAppRsp);
  // 删除共享知识库应用
  rpc DeleteShareKnowledgeBaseApp(DeleteShareKnowledgeBaseAppReq) returns (DeleteShareKnowledgeBaseAppRsp);
}

// 创建企业请求
message CreateCorpReq {
  // 企业全称
  string full_name = 1 [(validate.rules).string.min_len = 1];
  // 联系人名称
  string contact_name = 2 [(validate.rules).string.min_len = 1];
  // 邮箱地址
  string email = 3;
  // 手机号
  string telephone = 4;
}

// 创建企业响应
message CreateCorpRsp {
  // 企业ID
  uint64 corp_biz_id = 1;
}

// 企业列表请求
message ListCorpReq {
  // 查询条件
  string query = 1;
  // 腾讯云主账号
  string uin = 2;
  // 企业状态
  repeated uint32 status = 3;
  // 企业ID
  repeated uint64 corp_biz_id = 4;
  // 页码
  uint32 page = 5 [(validate.rules).uint32.gt = 0];
  // 分页数量
  uint32 page_size = 6 [(validate.rules).uint32.gt = 0];
}

// 企业列表响应
message ListCorpRsp {
  message Corp {
    // 企业ID
    uint64 corp_biz_id = 1;
    // 企业全称
    string full_name = 2;
    // 机器人配额
    uint32 robot_quota = 3;
    // 联系人名称
    string contact_name = 4;
    // 联系人邮箱
    string email = 5;
    // 联系人手机号
    string cellphone = 6;
    // 腾讯云主账号
    string uin = 7;
    // 安全审核策略
    string infosec_biz_type = 8;
    // 企业状态
    uint32 status = 9;
    // 状态描述
    string status_desc = 10;
    // 创建时间
    int64 create_time = 11;
    // 字符数
    uint64 max_char_size = 12;
    // token数
    uint64 max_token_usage = 13;
  }
  uint64 total = 1;
  repeated Corp list = 2;
}

// 获取企业请求
message GetCorpReq {
  // 企业id
  uint64 id = 1;
}

// 获取企业响应
message GetCorpRsp {
  // 企业ID
  uint64 corp_biz_id = 1;
  // 企业全称
  string full_name = 2;
  // 机器人配额
  uint32 robot_quota = 3;
  // 联系人名称
  string contact_name = 4;
  // 联系人邮箱
  string email = 5;
  // 联系人手机号
  string cellphone = 6;
  // 腾讯云主账号
  string uin = 7;
  // 安全审核策略
  string infosec_biz_type = 8;
  // 企业状态
  uint32 status = 9;
  // 状态描述
  string status_desc = 10;
  // 创建时间
  int64 create_time = 11;
  // sid
  uint32 sid = 12;
}

// 修改安全审核策略请求
message ModifyInfosecBizTypeReq {
  // 企业ID
  uint64 corp_biz_id = 1 [(validate.rules).uint64.gt = 0];
  // 安全审核策略
  string infosec_biz_type = 2 [(validate.rules).string = {min_len: 1}];
}

// 修改安全审核策略响应
message ModifyInfosecBizTypeRsp {}

// 修改企业机器人token数请求
message ModifyMaxTokenUsageReq {
  // 企业ID
  uint64 corp_biz_id = 1 [(validate.rules).uint64.gt = 0];
  // 字符数
  uint64 max_token_usage = 2;
}

// 修改企业机器人token数响应
message ModifyMaxTokenUsageRsp {}

// 修改企业机器人字符数请求
message ModifyMaxCharSizeReq {
  // 企业ID
  uint64 corp_biz_id = 1 [(validate.rules).uint64.gt = 0];
  // 字符数
  uint64 max_char_size = 2;
}

// 修改企业机器人字符数响应
message ModifyMaxCharSizeRsp {}


// 审核企业请求
message AuditCorpReq {
  // 企业ID
  uint64 corp_biz_id = 1 [(validate.rules).uint64.gt = 0];
  // 是否审核通过
  bool is_pass = 2;
}

// 审核企业响应
message AuditCorpRsp {}

// 修改企业机器人配额请求
message ModifyCorpRobotQuotaReq {
  // 企业ID
  uint64 corp_biz_id = 1 [(validate.rules).uint64.gt = 0];
  // 配额
  uint32 quota = 2;
}

// 修改企业机器人配额响应
message ModifyCorpRobotQuotaRsp {}

// 获取企业员工列表 请求
message CorpStaffListReq {
  // 企业ID
  uint64 corp_biz_id = 1;
  // 员工状态
  repeated uint32 status = 2;
  // 查询内容
  string query = 3;
  // 页码
  uint32 page = 5 [(validate.rules).uint32.gt = 0];
  // 分页数量
  uint32 page_size = 6 [(validate.rules).uint32.gt = 0];
  // 员工业务ID
  repeated uint64 staff_biz_ids = 7;
}

// 获取企业员工列表 响应
message CorpStaffListRsp {
  message Staff {
    uint64 staff_biz_id = 1;
    string nick_name = 2;
    string avatar = 3;
    string cellphone = 4;
    uint32 status = 6;
    string status_desc = 7;
    int64 join_time = 8;
    int64 create_time = 9;
    uint64 corp_biz_id = 10;
    string corp_full_name = 11;
    uint64 id = 12;
    string account = 13;
  }
  uint64 total = 1;
  repeated Staff list = 2;
}

message ListCorpStaffByIdsReq{
  // 企业ID
  uint64 corp_biz_id = 1;
  // 员工状态
  repeated uint32 status = 2;
  // 查询内容
  string query = 3;
  // 页码
  uint32 page = 5 [(validate.rules).uint32.gt = 0];
  // 分页数量
  uint32 page_size = 6 [(validate.rules).uint32.gt = 0];
  // 员工业务ID
  repeated uint64 staff_ids = 7;
}

message ListCorpStaffByIdsRsp {
  message Staff {
    uint64 staff_biz_id = 1;
    string nick_name = 2;
    string avatar = 3;
    string cellphone = 4;
    uint32 status = 6;
    string status_desc = 7;
    int64 join_time = 8;
    int64 create_time = 9;
    uint64 corp_biz_id = 10;
    string corp_full_name = 11;
    uint64 id = 12;
  }
  uint64 total = 1;
  repeated Staff list = 2;
}

// 更新QA状态请求
message ReleaseDetailNotifyReq {
  // QAID / SegmentID
  uint64 id = 1;
  // 是否发布成功
  bool is_success = 2;
  // 失败原因
  string reason = 3;
  // 机器人ID
  uint64 robot_id = 4;
  // 版本ID
  uint64 version_id = 5;
  // 类型 1:qa 2:segment 3:拒答问题
  uint32 type = 6;
  // qa类型 1:主问答 2:相似问答 仅当type为1时有效
  uint32 qa_type = 7;
}

// 更新QA状态响应
message ReleaseDetailNotifyRsp {}

// 发布结果同步请求
message ReleaseNotifyReq {
  // 机器人ID
  uint64 robot_id = 1;
  // 版本ID
  uint64 version_id = 2;
  // 是否成功
  bool is_success = 3;
  // 失败原因
  string message = 4;
  // 透传字段
  string transparent = 5;
  // 回调方标识 0：vector,1：任务型,2：agent，3：数据库
  uint32 callback_source = 6;
  // 机器人业务ID
  uint64 robot_biz_id = 7;
}

// 发布结果同步响应
message ReleaseNotifyRsp {}

// 获取临时链接请求
message GetPresignedURLReq {
  // 文档ID
  uint64 business_id = 1;
  // 无需检查是否展示引用
  bool is_no_check_refer = 2;
}

// 获取临时链接响应
message GetPresignedURLRsp {
  // 文件名
  string file_name = 1;
  // 文件类型
  string file_type = 2;
  // cos路径
  string cos_url = 3;
  // cos临时地址
  string url = 4;
  // cos桶
  string bucket = 5;
}

// 机器人信息请求
message GetRobotInfoReq {
  // 机器人ID
  uint64 bot_biz_id = 1 [(validate.rules).uint64.gt = 0];
}

// 机器人信息响应
message GetRobotInfoRsp {
  // 机器人昵称
  string name = 1;
  // 机器人头像
  string avatar = 2;
  // 是否有效
  bool is_available = 3;
  // 机器人ID
  uint64 bot_biz_id = 4;
}

// 机器人信息请求
message DescribeRobotInfoReq {
  // 机器人ID
  uint64 bot_biz_id = 1 [(validate.rules).uint64.gt = 0];
  // 场景 1 评测 2 发布
  uint32 scenes = 2;
}

// 机器人信息响应
message DescribeRobotInfoRsp {
  // 机器人昵称
  string name = 1;
  // 机器人头像
  string avatar = 2;
  // 是否有效
  bool is_available = 3;
  // 机器人ID
  uint64 bot_biz_id = 4;
  // 欢迎语
  string greeting = 5;
  // 对话长度
  uint32 chat_words_limit = 6;
  // 当前账号token数
  uint64 token_total = 7;
  // 当前账号剩余token数量
  uint64 token_balance = 8;
  // 是否开启图片安全处理
  bool image_security = 9;
  // 是否开启异步任务
  bool async_workflow = 10;
  // 数智人相关配置
  AICallConfig ai_call = 11;
}

// 问题查询请求
message SearchPreviewReq {
  // 机器人business_id
  uint64 bot_biz_id = 1 [(validate.rules).uint64 = {gte: 1}];
  // 问题
  string question = 2 [(validate.rules).string = {min_len: 1, max_len: 6000}];
  // 过滤器名称
  string filter_key = 3;
  // labels 标签
  repeated VectorLabel labels = 4;
  // 使用占位符
  bool use_placeholder = 5;
}

// 特征标签
message VectorLabel {
  // 标签名
  string name = 1;
  // 标签值，一个标签多个标签值
  repeated string values = 2;
}

// 问题查询响应
message SearchPreviewRsp {
  message Doc {
    // 文档ID
    uint64 doc_id = 1;
    // 1是QA 2是segment
    uint32 doc_type = 2;
    // QAID/SegmentID
    uint64 related_id = 3;
    // 问题
    string question = 4;
    // qa答案
    string answer = 5;
    // 置信度
    float confidence = 7;
    // 文档片段
    string org_data = 8;
    // QABizID/SegmentBizID
    uint64 related_biz_id = 9;
    // 占位符
    repeated Placeholder question_placeholders = 10;
    repeated Placeholder answer_placeholders = 11;
    repeated Placeholder org_data_placeholders = 12;
    // 自定义参数 qa自定义参数
    string custom_param = 13;
    // 是否big_data true-表示org_data是由big_data填充
    bool is_big_data = 14;
  }
  repeated Doc docs = 1;
}

// 占位符
message Placeholder {
  // 占位符
  string key = 1;
  // 占位符内容
  string value = 2;
}


// 向量特征检索请求
message SearchReq {
  // 机器人business_id
  uint64 bot_biz_id = 1 [(validate.rules).uint64 = {gte: 1}];
  // 问题
  string question = 2 [(validate.rules).string = {min_len: 1, max_len: 6000}];
  // 过滤器名称
  string filter_key = 3;
  // labels 标签
  repeated VectorLabel labels = 4;
  // 内容使用占位符替换链接和图片
  bool use_placeholder = 8;
}

// 向量特征检索响应
message SearchRsp {
  message Doc {
    // 文档ID
    uint64 doc_id = 1;
    // 文档类型 (1 QA, 2 文档段)
    uint32 doc_type = 2;
    // 对应文档类型关联的数据ID
    uint64 related_id = 3;
    // 问题, 当文档类型为 QA(1) 时有效
    string question = 4;
    // 答案, 当文档类型为 QA(1) 时有效
    string answer = 5;
    // 置信度
    float confidence = 7;
    // 文档段, 当文档类型为 文档段(2) 时有效
    string org_data = 8;
    // QABizID/SegmentBizID
    uint64 related_biz_id = 9;
    // 占位符
    repeated Placeholder question_placeholders = 10;
    repeated Placeholder answer_placeholders = 11;
    repeated Placeholder org_data_placeholders = 12;
    // 自定义参数 当文档类型为 QA(1) 时有效
    string custom_param = 13;
    // 是否big_data true-表示org_data是由big_data填充
    bool is_big_data = 14;
  }
  // 文档数据
  repeated Doc docs = 1;
}

// 问题查询请求
message CustomSearchPreviewReq {
  // 机器人ID
  uint64 bot_biz_id = 1 [(validate.rules).uint64 = {gte: 1}];
  // 问题
  string question = 2 [(validate.rules).string = {min_len: 1, max_len: 2000}];
  message Filter {
    // 文档类型 (1 QA, 2 文档段)
    uint32 doc_type = 1;
    // 置信度
    float confidence = 2;
    // 取 top_n
    uint32 top_n = 3;
  }
  repeated Filter filters = 3;
  // 取前 n 条 (默认3)
  uint32 top_n = 4;
  // labels 标签
  repeated VectorLabel labels = 5;
  // 使用占位符
  bool use_placeholder = 6;
}

// 问题查询响应
message CustomSearchPreviewRsp {
  message Doc {
    // 文档ID
    uint64 doc_id = 1;
    // 1是QA 2是segment
    uint32 doc_type = 2;
    // QAID/SegmentID
    uint64 related_id = 3;
    // 问题
    string question = 4;
    // qa答案
    string answer = 5;
    // 置信度
    float confidence = 7;
    // 原始文本
    string org_data = 8;
    // QABizID/SegmentBizID
    uint64 related_biz_id = 9;
    // 占位符
    repeated Placeholder question_placeholders = 10;
    repeated Placeholder answer_placeholders = 11;
    repeated Placeholder org_data_placeholders = 12;
  }
  repeated Doc docs = 1;
}

// 问题查询请求
message CustomSearchReq {
  // 机器人ID
  uint64 bot_biz_id = 1 [(validate.rules).uint64 = {gte: 1}];
  // 问题
  string question = 2 [(validate.rules).string = {min_len: 1, max_len: 2000}];
  message Filter {
    // 文档类型 (1 QA, 2 文档段)
    uint32 doc_type = 1;
    // 置信度
    float confidence = 2;
    // 取 top_n
    uint32 top_n = 3;
  }
  // 筛选器
  repeated Filter filters = 3;
  // 取前 n 条 (默认3)
  uint32 top_n = 4;
  // labels 标签
  repeated VectorLabel labels = 5;
  // 使用占位符
  bool use_placeholder = 6;
}

// 问题查询响应
message CustomSearchRsp {
  message Doc {
    // 文档ID
    uint64 doc_id = 1;
    // 文档类型 (1:QA 2:文档段 4:搜索引擎)
    uint32 doc_type = 2;
    // 对应文档类型关联的数据ID
    uint64 related_id = 3;
    // 问题, 当文档类型为 QA(1) 或 搜索引擎(4) 时有效
    string question = 4;
    // 答案, 当文档类型为 QA(1) 或 搜索引擎(4) 时有效
    string answer = 5;
    // 置信度
    float confidence = 7;
    // 原始文本, 当文档类型为 文档段(2) 时有效
    string org_data = 8;
    // QABizID/SegmentBizID
    uint64 related_biz_id = 9;
    // 占位符
    repeated Placeholder question_placeholders = 10;
    repeated Placeholder answer_placeholders = 11;
    repeated Placeholder org_data_placeholders = 12;
  }
  // 文档数据
  repeated Doc docs = 1;
}

// 匹配来源请求
message MatchReferReq {
  message Doc {
    // 文档ID
    uint64 doc_id = 1;
    // 文档类型 (1 QA, 2 文档段)
    uint32 doc_type = 2;
    // 对应文档类型关联的数据ID
    uint64 related_id = 3;
    // 问题, 当文档类型为 QA(1) 时有效
    string question = 4;
    // 答案, 当文档类型为 QA(1) 时有效
    string answer = 5;
    // 文档段, 当文档类型为 文档段(2) 时有效
    string org_data = 7;
  }
  // 机器人business_id
  uint64 bot_biz_id = 1 [(validate.rules).uint64 = {gte: 1}];
  // 推理前的文档原文
  repeated Doc docs = 2;
  // 大模型回复内容
  string answer = 3 [(validate.rules).string = {min_len: 1}];
  // 是否匹配的是发布版本
  bool is_release = 4;
  // 消息ID
  string msg_id = 5 [(validate.rules).string.min_len = 1];
  // 问题
  string question = 6 [(validate.rules).string = {min_len: 1, max_len: 2000}];
}

// 匹配来源响应
message MatchReferRsp {
  message Refer {
    // 来源ID
    uint64 refer_id = 1;
    // 文档类型 (1:QA 2:文档段)
    uint32 doc_type = 2;
    // 文档名称/问题
    string name = 3;
    // 文档链接
    string url = 4;
    // 文档类型 (1:QA 2:文档段 3:文档)
    uint32 refer_type = 5;
    // 文档ID
    uint64 doc_id = 6;
    // 文档业务ID
    uint64 doc_biz_id = 7;
  }
  // 来源数据
  repeated Refer Refers = 1;
}

// 获取机器人列表请求
message GetRobotListReq {
  // 页码
  uint32 page = 1;
  // 每页大小
  uint32 page_size = 2;
  // 企业ID
  uint64 corp_biz_id = 3;
  // 昵称
  string name = 4;
  // 机器人ID
  repeated uint64 bot_biz_ids = 5;
  // 删除标记位 0不过滤 1未删除 2已删除
  uint32 delete_flag = 6;
  // 是否校验企业
  bool is_skip_check_corp = 7;
}

message GetRobotListRsp {
  // 总数
  uint32 total = 1;
  // 机器人信息
  repeated RobotInfo list = 2;
}

// 机器人信息
message RobotInfo {
  // 机器人ID
  uint64 id = 1;
  // 企业ID
  uint64 corp_id = 2;
  // 机器人名称
  string name = 3;
  // 机器人头像
  string avatar = 4;
  // 机器人描述
  string description = 5;
  // QA检索库线上版本
  uint64 qa_version = 6;
  // 是否转人工
  bool can_transfer_keyword = 7;
  // 转人工关键词
  repeated string transfer_keywords = 8;
  // 是否启用
  bool enabled = 9;
  // 过期时间
  int64 expire_time = 10;
  // 未知问题回复语
  string bare_answer = 11;
  // 是否使用行业通用知识库
  bool use_general_knowledge = 12;
  // 创建时间
  int64 create_time = 13;
  // 更新时间
  int64 update_time = 14;
  // 模型配置
  map<string, RobotModelInfo> model = 15;
  // 索引配置
  map<string, RobotFilters> filters = 16;
  // 切片配置
  map<string, RobotSplitDoc> split_doc = 17;
  // 相似配置
  RobotSearchVector search_vector = 18;
  // 模型配置是否为默认配置
  bool is_model_default = 19;
  // 索引配置是否为默认配置
  bool is_filters_default = 20;
  // 切片配置是否为默认配置
  bool is_split_doc_default = 21;
  // 相似配置是否为默认配置
  bool is_search_vector_default = 22;
  // 拒答问题库线上版本
  uint64 rejected_question_version = 23;
  // embedding 配置
  RobotEmbedding embedding = 24;
  // embedding 是否为默认配置
  bool is_embedding_default = 25;
  // 机器人ID
  uint64 bot_biz_id = 26;
  // 是否删除
  bool is_delete = 27;
  // 是否开启搜索增强
  bool use_search_engine = 28;
  // 欢迎语
  string greeting = 29;
  // 根据用户语义转人工
  bool can_transfer_intent = 30;
  // 机器人答案评价不满意转人工
  bool can_transfer_unsatisfied = 31;
  // 机器人答案评价不满意转人工，设定的触发次数
  uint32 transfer_unsatisfied_count = 32;
  // 机器人描述(prompt 场景使用)
  string role_description = 33;
  // appkey
  string app_key = 34;
  // 机器人回复灵活度
  uint32 reply_flexibility = 35;
  // 是否显示搜索引擎检索状态
  bool show_search_engine = 36;
  // 意图策略ID
  uint32 intent_policy_id = 37;
}

// 机器人索引配置
message RobotFilters {
  // 总计TopN
  uint32 top_n = 1;
  // 索引配置
  repeated RobotFiltersInfo filter = 2;
}

// 机器人索引配置子项
message RobotFiltersInfo {
  // 文档类型
  uint32 doc_type = 1;
  uint32 index_id = 2;
  // 置信度
  float confidence = 3;
  // 子项TopN
  uint32 top_n = 4;
}

// 机器人相似度配置
message RobotSearchVector {
  // 置信度
  float confidence = 1;
  // top_n 最多匹配条数
  uint32 top_n = 2;
}

// 机器人切分配置
message RobotSplitDoc {
  // 文档解析配置
  RobotSplitDocParserConfig parser_config = 1;
  // 切分配置
  RobotSplitDocSplitterConfig splitter_config = 2;
  // 合并配置
  RobotSplitDocMergerConfig merger_config = 3;
  // 重组配置
  RobotSplitDocRechunkConfig rechunk_config = 4;
  // 模式切分配置
  RobotSplitDocPatternSplitterConfig pattern_splitter_config = 5;
}

// RobotEmbedding 机器人 embedding 配置
message RobotEmbedding {
  // embedding 版本
  uint64 Version = 1;
}

// RobotSplitDocParserConfig 文档解析配置
message RobotSplitDocParserConfig {
  // 是否作为一整段处理
  bool single_paragraph = 1;
}

// RobotSplitDocSplitterConfig 切分配置
message RobotSplitDocSplitterConfig {
  // 切分器
  string splitter = 1;
  // 句切分配置
  SplitterSentenceConfig splitter_sentence_config = 2;
  // token 切分配置
  SplitterTokenConfig splitter_token_config = 3;
}

// RobotSplitDocPatternSplitterConfig 模式切分配置
message RobotSplitDocPatternSplitterConfig {
  string regexp_json = 1;
}

// RobotSplitDocMergerConfig 合并配置
message RobotSplitDocMergerConfig {
  // 合并器
  string merger = 1;
  // 按长度合并
  MergerLengthConfig merger_length_config = 2;
  // 按 mini chunk 数量合并
  MergerAmountConfig merger_amount_config = 3;
}

// RobotSplitDocRechunkConfig 重组配置
message RobotSplitDocRechunkConfig {
  // 重组时, 头部分块的重组数
  uint32 head_overlap_size = 1;
  // 重组时, 尾部分块的重组数
  uint32 tail_overlap_size = 2;
  // 根据符号trim
  repeated string trim_by_symbols = 3;
}

// SplitterSentenceConfig 按句子切分
message SplitterSentenceConfig {
  // 是否处理表格
  bool enable_table = 1;
  // 是否处理图片
  bool enable_image = 2;
  // 句符号
  repeated string sentence_symbols = 3;
  // 最大分块大小
  uint64 max_mini_chunk_length = 4;
}

// SplitterTokenConfig 按 token 切分
message SplitterTokenConfig {
  // 是否处理表格
  bool enable_table = 1;
  // 是否处理图片
  bool enable_image = 2;
  // 切片大小
  uint32 mini_chunk_length = 3;
}

// MergerLengthConfig 按长度合并
message MergerLengthConfig {
  // 页体长度
  uint32 page_content_length = 1;
  // 页头长度
  uint32 head_overlap_length = 2;
  // 页尾长度
  uint32 tail_overlap_length = 3;
  // 表体长度
  uint32 table_page_content_length = 4;
  // 表头长度
  uint32 table_head_overlap_length = 5;
  // 表尾长度
  uint32 table_tail_overlap_length = 6;
  // 按符号trim
  repeated string trim_by_symbols = 7;
}

// MergerAmountConfig 按数量合并
message MergerAmountConfig {
  // 页体 mini chunk 数量
  uint32 page_content_size = 1;
  // 页头 mini chunk 数量
  uint32 head_overlap_size = 2;
  // 页尾 mini chunk 数量
  uint32 tail_overlap_size = 3;
  // 表体长度
  uint32 table_page_content_length = 4;
  // 表头  mini chunk 数量
  uint32 table_head_overlap_size = 5;
  // 表尾  mini chunk 数量
  uint32 table_tail_overlap_size = 6;
  // 按符号trim
  repeated string trim_by_symbols = 7;
}

message GetAppListReq {
  // 页码
  uint32 page = 1;
  // 每页大小
  uint32 page_size = 2;
  // 企业ID
  uint64 corp_biz_id = 3;
  // 昵称
  string name = 4;
  // 机器人ID
  repeated uint64 bot_biz_ids = 5;
  // 删除标记位 0不过滤 1未删除 2已删除
  uint32 delete_flag = 6;
  // 是否校验企业
  bool is_skip_check_corp = 7;
  // 场景
  uint32 scenes = 8 [(validate.rules).uint32 = {in: [1, 2]}];
  // 应用类型；knowledge_qa-知识问答管理；summary-知识摘要；classify-知识标签提取
  repeated string app_types = 9;
  // appkey
  string app_key = 10;
}

message GetAppListRsp {
  message KnowledgeQaConfig {
    // QA检索库线上版本
    uint64 qa_version = 1;
    // 是否转人工
    bool can_transfer_keyword = 2;
    // 转人工关键词
    repeated string transfer_keywords = 3;
    // 模型配置
    map<string, AppModelInfo> model = 4;
    // 索引配置
    map<string, AppFilters> filters = 5;
    // 切片配置
    map<string, AppSplitDoc> split_doc = 6;
    // 相似配置
    AppSearchVector search_vector = 7;
    // 模型配置是否为默认配置
    bool is_model_default = 8;
    //
    bool is_filters_default = 9;
    // 切片配置是否为默认配置
    bool is_split_doc_default = 10;
    // 相似配置是否为默认配置
    bool is_search_vector_default = 11;
    // 拒答问题库线上版本
    uint64 rejected_question_version = 12;
    // embedding 配置
    AppEmbedding embedding = 13;
    // embedding 是否为默认配置
    bool is_embedding_default = 14;
    // 输出配置
    KnowledgeQaOutput output = 15;
    // 是否开启搜索增强
    bool use_search_engine = 16;
    // 机器人描述(prompt 场景使用)
    string role_description = 17;
    // 欢迎语
    string greeting = 18;
    // 机器人回复灵活度
    uint32 reply_flexibility = 19;
    // 是否显示搜索引擎检索状态
    bool show_search_engine = 20;
    // 意图策略ID
    uint32 intent_policy_id = 21;
    // 是否开启根据用户语义转人工
    bool can_transfer_intent = 22;
    // 是否开启机器人答案评价不满意转人工
    bool can_transfer_unsatisfied = 23;
    // 是否开启任务型
    bool can_use_task_flow = 24;
    // 答案评价不满意转人工，设定的触发次数
    uint32 transfer_unsatisfied_count = 25;
    // 模型自定义topK配置
    map<string, uint32> model_top_k = 26;
    // 工作流程
    AppWorkflow workflow = 27;
    // 应用模式 standard:标准模式, agent: agent模式，single_workflow：单工作流模式
    string pattern = 28;
    // 单工作流模式传入工作流ID
    string workflow_id = 29;
    // 应用检索策略
    AppSearchStrategy search_strategy = 30;
    // 知识问答配置插件
    repeated AppPlugin plugins = 31;
    // Cot配置
    CoTConfig cot = 32;
    // 多意图开关 可控制是否输出多个意图，避免澄清概率太高，有客户希望关闭这个开关。 默认开启
    bool multiple_intent = 33;
    // 意图达成优先级, qa:问答回复、doc：文档回复、workflow：工作流回复，llm：大模型回复
    repeated string intent_achievements = 34;
    // 图文检索
    bool image_text_retrieval = 35;
    // 组合知识检索 文档阅读理解的时候 默认关闭
    bool combined_knowledge_retrieval = 36;
    // 复杂问题拆解检索 会将复杂query拆解成多个子query，多个子query会同时检索，提升知识检索的召回 默认关闭
    bool complex_problem_retrieval = 37;
    // 多模态出图 检索知识库中的知识包含图片，提供给多模态阅读理解大模型理解图片后做出回答，提升答案的出图率 默认关闭
    bool multi_modal_generation_image = 38;
    // 实时文档上下文记忆，默认开启
    bool realtime_doc_context = 39;
    // 联网搜索引擎，0（默认）：元宝，1：搜狗，
    uint32 search_engine = 40;
    // 送意图带FAQ,默认true
    bool intent_with_faq = 41;
  }
  message SummaryConfig {
    // 模型配置
    map<string, AppModelInfo> model = 1;
    // 输出配置
    SummaryOutput output = 2;
    // 模型列表，给chat做用户选择使用
    map<string, AppModelInfo> module_list = 3;
    // 欢迎语
    string greeting = 4;
  }
  message ClassifyConfig {
    // 模型配置
    map<string, AppModelInfo> model = 1;
    // 标签列表
    repeated ClassifyLabel labels = 2;
    // 模型列表，给chat做用户选择使用
    map<string, AppModelInfo> module_list = 3;
    // 欢迎语
    string greeting = 4;
  }
  // 应用信息
  message AppInfo {
    // 应用ID
    uint64 id = 1;
    // 企业ID
    uint64 corp_id = 2;
    // 应用业务ID
    uint64 app_biz_id = 3;
    // app_key
    string app_key = 4;
    // 应用类型；knowledge_qa-知识问答管理；summary-知识摘要；classify-知识标签提取
    string app_type = 5;
    // 应用类型说明
    string app_type_desc = 6;
    // 是否删除
    bool is_delete = 7;
    // 是否启用
    bool enabled = 8;
    // 过期时间
    int64 expire_time = 9;
    // 创建时间
    int64 create_time = 10;
    // 更新时间
    int64 update_time = 11;
    // 应用状态 1未上线 2运行中 3停用
    int64 status = 12;
    // 机器人基础配置
    BaseConfig base_config = 13;
    // 知识问答应用配置
    KnowledgeQaConfig knowledge_qa = 14;
    // 知识摘要应用配置
    SummaryConfig summary = 15;
    // 标签配置
    ClassifyConfig classify = 16;
    // 应用状态描述
    string status_desc = 17;
    // 应用配置版本ID
    uint64 config_version_id = 18;
    // 是否体验中心应用
    bool is_exp_app = 19;
    // 安全审核策略
    string infosec_biz_type = 20;
    // 是否共享知识库
    bool is_share_knowledge_base = 21;
  }
  // 总数
  uint32 total = 1;
  // 应用信息
  repeated AppInfo list = 2;
}

// 机器人模型配置
message AppModelInfo {
  // 提示词
  string prompt = 1;
  // 提示词内容字符限制
  uint32 prompt_words_limit = 2;
  // 对话历史条数限制
  uint32 history_limit = 6;
  // 对话历史条数限制
  uint32 history_words_limit = 7;
  // 模型名称 (trpc 接口使用)
  string model_name = 8;
  // 下游服务名 (trpc 接口使用)
  string service_name = 9;
  // 模型是否开启
  bool is_enabled = 10;

  // 模型调用接口路由 (http 接口使用)
  string path = 3 [deprecated = true];
  // 模型调用接口地址 (http 接口使用)
  string target = 4 [deprecated = true];
  // 模型类型 (http 接口使用)
  uint32 type = 5 [deprecated = true];

  // 对话限制长度
  uint32 chat_words_limit = 11;
  // 模型的top_k配置
  uint32 top_k = 12;
  // 指定版本的prompt
  map<string, string> prompts = 13;
  // 温度
  float temperature = 14;
  // TopP
  float top_p = 15;
  // prompt版本
  string prompt_version = 16;
  // input输入框长度限制
  uint32 input_limit = 17;
}

// 机器人索引配置
message AppFilters {
  // 总计TopN
  uint32 top_n = 1;
  // 索引配置
  repeated AppFiltersInfo filter = 2;
}

// 机器人索引配置子项
message AppFiltersInfo {
  // 文档类型 1：问答，2：文档，3：拒答，4：搜索增强，5：任务型检索，6：图搜图检索类型，7：问搜图检索类型，8：数据库检索
  uint32 doc_type = 1;
  uint32 index_id = 2;
  // 置信度
  float confidence = 3;
  // 子项TopN
  uint32 top_n = 4;
  // Rouge分数
  RougeScore rouge_score = 5;
  // 是否启用
  bool is_enable = 6;
}

// 机器人切分配置
message AppSplitDoc {
  // 文档解析配置
  AppSplitDocParserConfig parser_config = 1;
  // 切分配置
  AppSplitDocSplitterConfig splitter_config = 2;
  // 合并配置
  AppSplitDocMergerConfig merger_config = 3;
  // 重组配置
  AppSplitDocRechunkConfig rechunk_config = 4;
  // 模式切分配置
  AppSplitDocPatternSplitterConfig pattern_splitter_config = 5;
}

// AppEmbedding 应用 embedding 配置
message AppEmbedding {
  // embedding 版本
  uint64 Version = 1;
  // 升级版本
  uint64 UpgradeVersion = 2;
}

// AppSplitDocParserConfig 文档解析配置
message AppSplitDocParserConfig {
  // 是否作为一整段处理
  bool single_paragraph = 1;
}

// AppSplitDocPatternSplitterConfig 模式切分配置
message AppSplitDocPatternSplitterConfig {
  string regexp_json = 1;
}

// AppSplitDocSplitterConfig 切分配置
message AppSplitDocSplitterConfig {
  // 切分器
  string splitter = 1;
  // 句切分配置
  SplitterSentenceConfig splitter_sentence_config = 2;
  // token 切分配置
  SplitterTokenConfig splitter_token_config = 3;
}

// AppSplitDocMergerConfig 合并配置
message AppSplitDocMergerConfig {
  // 合并器
  string merger = 1;
  // 按长度合并
  MergerLengthConfig merger_length_config = 2;
  // 按 mini chunk 数量合并
  MergerAmountConfig merger_amount_config = 3;
}

// AppSplitDocRechunkConfig 重组配置
message AppSplitDocRechunkConfig {
  // 重组时, 头部分块的重组数
  uint32 head_overlap_size = 1;
  // 重组时, 尾部分块的重组数
  uint32 tail_overlap_size = 2;
  // 根据符号trim
  repeated string trim_by_symbols = 3;
}

// 应用相似度配置
message AppSearchVector {
  // 置信度
  float confidence = 1;
  // top_n 最多匹配条数
  uint32 top_n = 2;
}

// 应用工作流配置
message AppWorkflow {
  // 是否启用工作流程
  bool is_enabled = 1;
  // 是否启用PDL
  bool use_pdl = 2;
  // 工作流检索意图置信度 0.2 (默认值) ， 范围（0.0-1.0， step:0.1）
  float workflow_confidence = 3;
  // 工作流检索最相关意图的个数 100 （默认）， 范围（1-500）
  uint32 workflow_relevant_top_size = 4;
  // 工作流检索最相关意图的个数 5 （默认）， 范围（1-50）
  uint32 workflow_uniq_relevant_top_size = 5;
  // 工作流相关节点请求白名单urls
  repeated string safe_white_urls = 6;
  // 工作流相关节点请求白名单ports
  repeated string safe_white_ports = 7;
}

// 应用检索策略
message AppSearchStrategy {
  // 检索策略类型 0:混合检索，1：语义检索
  SearchStrategyTypeEnum strategy_type = 1;
  // excel检索增强，默认关闭
  bool table_enhancement = 2;
}

// 应用插件信息
message AppPlugin {
  string plugin_id = 1;
  string tool_id = 2;
}

// 应用知识范围配置
message AppSearchRange {
  message ApiVarAttrInfo {
    string api_var_id = 1;
    uint64 attr_biz_id = 2;
  }
  string condition = 1;
  map<string, string> api_var_map = 2;
  map<uint64, string> label_attr_map = 3;
  repeated ApiVarAttrInfo api_var_attr_infos = 4;
}

// 共享知识库配置
message APIShareKnowledgeBase {
  // 共享知识库ID
  uint64 knowledge_biz_id = 1;
  // 检索范围
  AppSearchRange search_range = 2;
}

message CoTConfig {
  // 是否开启CoT模式
  bool is_enabled = 1;
}

// 通过AppKye查询机器人信息 请求
message GetRobotByAppKeyReq {
  string app_key = 1 [(validate.rules).string = {min_len: 1}];
}

// 通过AppKye查询机器人信息 响应
message GetRobotByAppKeyRsp {
  // 机器人信息
  message RobotInfo {
    // 机器人ID
    uint64 id = 1;
    // 企业ID
    uint64 corp_id = 2;
    // 机器人名称
    string name = 3;
    // 机器人头像
    string avatar = 4;
    // 机器人描述
    string description = 5;
    // QA检索库线上版本
    uint64 qa_version = 6;
    // 是否转人工
    bool can_transfer_keyword = 7;
    // 转人工关键词
    repeated string transfer_keywords = 8;
    // 是否启用
    bool enabled = 9;
    // 过期时间
    int64 expire_time = 10;
    // 未知问题回复语
    string bare_answer = 11;
    // 是否使用行业通用知识库
    bool use_general_knowledge = 12;
    // 创建时间
    int64 create_time = 13;
    // 更新时间
    int64 update_time = 14;
    // 模型配置
    map<string, RobotModelInfo> model = 15;
    // 索引配置
    map<string, RobotFilters> filters = 16;
    // 切片配置
    map<string, RobotSplitDoc> split_doc = 17;
    // 相似配置
    RobotSearchVector search_vector = 18;
    // 模型配置是否为默认配置
    bool is_model_default = 19;
    // 索引配置是否为默认配置
    bool is_filters_default = 20;
    // 切片配置是否为默认配置
    bool is_split_doc_default = 21;
    // 相似配置是否为默认配置
    bool is_search_vector_default = 22;
    // embedding 配置
    RobotEmbedding embedding = 24;
    // embedding 是否为默认配置
    bool is_embedding_default = 25;
    // 机器人ID
    uint64 bot_biz_id = 26;
    // 是否删除
    bool is_delete = 27;
    // 是否开启搜索增强
    bool use_search_engine = 28;
    // 是否开启根据用户语义转人工
    bool can_transfer_intent = 29;
    // 是否开启机器人答案评价不满意转人工
    bool can_transfer_unsatisfied = 30;
    // 机器人答案评价不满意转人工，设定的触发次数
    uint32 transfer_unsatisfied_count = 31;
    // 机器人描述(prompt 场景使用)
    string role_description = 32;
    // 欢迎语
    string greeting = 33;
    // 机器人回复灵活度
    uint32 reply_flexibility = 34;
    // 是否体验中心应用
    bool is_exp_app = 35;
    // 安全审核策略
    string infosec_biz_type = 36;
  }
  RobotInfo info = 1;
}

// 通过AppKye查询应用信息 请求
message GetAppByAppKeyReq {
  // 机器人ID
  string app_key = 1 [(validate.rules).string = {min_len: 1}];
  // 场景 1是评测 2是正式
  uint32 scenes = 2 [(validate.rules).uint32 = {in: [1, 2]}];
}

// 通过AppKye查询应用信息 响应
message GetAppByAppKeyRsp {
  message KnowledgeQaConfig {
    // QA检索库线上版本
    uint64 qa_version = 1;
    // 是否转人工
    bool can_transfer_keyword = 2;
    // 转人工关键词
    repeated string transfer_keywords = 3;
    // 模型配置
    map<string, AppModelInfo> model = 4;
    // 索引配置
    map<string, AppFilters> filters = 5;
    // 切片配置
    map<string, AppSplitDoc> split_doc = 6;
    // 相似配置
    AppSearchVector search_vector = 7;
    // 模型配置是否为默认配置
    bool is_model_default = 8;
    //
    bool is_filters_default = 9;
    // 切片配置是否为默认配置
    bool is_split_doc_default = 10;
    // 相似配置是否为默认配置
    bool is_search_vector_default = 11;
    // 拒答问题库线上版本
    uint64 rejected_question_version = 12;
    // embedding 配置
    AppEmbedding embedding = 13;
    // embedding 是否为默认配置
    bool is_embedding_default = 14;
    // 输出配置
    KnowledgeQaOutput output = 15;
    // 是否开启搜索增强
    bool use_search_engine = 16;
    // 机器人描述(prompt 场景使用)
    string role_description = 17;
    // 欢迎语
    string greeting = 18;
    // 机器人回复灵活度
    uint32 reply_flexibility = 19;
    // 是否显示搜索引擎检索状态
    bool show_search_engine = 20;
    // 意图策略ID
    uint32 intent_policy_id = 21;
    // 是否开启根据用户语义转人工
    bool can_transfer_intent = 22;
    // 是否开启机器人答案评价不满意转人工
    bool can_transfer_unsatisfied = 23;
    // 是否开启任务型
    bool can_use_task_flow = 24;
    // 工作流程
    AppWorkflow workflow = 25;
    // COT配置
    CoTConfig cot = 26;
  }
  message SummaryConfig {
    // 模型配置
    map<string, AppModelInfo> model = 1;
    // 输出配置
    SummaryOutput output = 2;
    // 模型列表，给chat做用户选择使用
    map<string, AppModelInfo> module_list = 3;
    // 欢迎语
    string greeting = 4;
  }
  message ClassifyConfig {
    // 模型配置
    map<string, AppModelInfo> model = 1;
    // 标签列表
    repeated ClassifyLabel labels = 2;
    // 模型列表，给chat做用户选择使用
    map<string, AppModelInfo> module_list = 3;
    // 欢迎语
    string greeting = 4;
  }
  // 应用ID
  uint64 id = 1;
  // 企业ID
  uint64 corp_id = 2;
  // 应用业务 ID
  uint64 app_biz_id = 3;
  // 应用类型；knowledge_qa-知识问答管理；summary-知识摘要；classify-知识标签提取
  string app_type = 4;
  // 应用类型说明
  string app_type_desc = 5;
  // 是否删除
  bool is_delete = 6;
  // 是否启用
  bool enabled = 7;
  // 过期时间
  int64 expire_time = 8;
  // 创建时间
  int64 create_time = 9;
  // 更新时间
  int64 update_time = 10;
  // 应用状态 1：未上线，2：运行中，3：停用
  int64 status = 11;
  // 机器人基础配置
  BaseConfig base_config = 12;
  // 知识问答应用配置
  KnowledgeQaConfig knowledge_qa = 13;
  // 知识摘要应用配置
  SummaryConfig summary = 14;
  // 标签配置
  ClassifyConfig classify = 15;
  // 是否体验中心应用
  bool is_exp_app = 16;
  // 安全审核策略
  string infosec_biz_type = 17;
}

// 查询应用信息 请求
message GetAppInfoReq {
  // 应用ID
  uint64 app_biz_id = 1;
  // 应用key
  string app_key = 2;
  // 场景 1是评测 2是正式
  uint32 scenes = 3 [(validate.rules).uint32 = {in: [1, 2]}];
}

message GetAppInfoRsp {
  message KnowledgeQaConfig {
    // QA检索库线上版本
    uint64 qa_version = 1;
    // 是否转人工
    bool can_transfer_keyword = 2;
    // 转人工关键词
    repeated string transfer_keywords = 3;
    // 模型配置
    map<string, AppModelInfo> model = 4;
    // 索引配置
    map<string, AppFilters> filters = 5;
    // 切片配置
    map<string, AppSplitDoc> split_doc = 6;
    // 相似配置
    AppSearchVector search_vector = 7;
    // 模型配置是否为默认配置
    bool is_model_default = 8;
    //
    bool is_filters_default = 9;
    // 切片配置是否为默认配置
    bool is_split_doc_default = 10;
    // 相似配置是否为默认配置
    bool is_search_vector_default = 11;
    // 拒答问题库线上版本
    uint64 rejected_question_version = 12;
    // embedding 配置
    AppEmbedding embedding = 13;
    // embedding 是否为默认配置
    bool is_embedding_default = 14;
    // 输出配置
    KnowledgeQaOutput output = 15;
    // 是否开启搜索增强
    bool use_search_engine = 16;
    // 机器人描述(prompt 场景使用)
    string role_description = 17;
    // 欢迎语
    string greeting = 18;
    // 机器人回复灵活度
    uint32 reply_flexibility = 19;
    // 是否显示搜索引擎检索状态
    bool show_search_engine = 20;
    // 意图策略ID
    uint32 intent_policy_id = 21;
    // 是否开启根据用户语义转人工
    bool can_transfer_intent = 22;
    // 是否开启机器人答案评价不满意转人工
    bool can_transfer_unsatisfied = 23;
    // 是否开启任务型
    bool can_use_task_flow = 24;
    // 答案评价不满意转人工，设定的触发次数
    uint32 transfer_unsatisfied_count = 25;
    // 模型自定义topK配置
    map<string, uint32> model_top_k = 26;
    // 工作流程
    AppWorkflow workflow = 27;
    // 应用模式 standard:标准模式, agent: agent模式，single_workflow：单工作流模式
    string pattern = 28;
    // 单工作流模式传入工作流ID
    string workflow_id = 29;
    // 应用检索策略
    AppSearchStrategy search_strategy = 30;
    // 知识问答配置插件
    repeated AppPlugin plugins = 31;
    // COT配置
    CoTConfig cot = 32;
    // 知识检索范围
    AppSearchRange search_range = 33;
    // 是否开启重排
    bool enable_rerank = 34;
    // 多意图开关 可控制是否输出多个意图，避免澄清概率太高，有客户希望关闭这个开关。 默认开启
    bool multiple_intent = 35;
    // 意图达成优先级, qa:问答回复、doc：文档回复、workflow：工作流回复，llm：大模型回复
    repeated string intent_achievements = 36;
    // 图文检索
    bool image_text_retrieval = 37;
    // 组合知识检索 文档阅读理解的时候 默认关闭
    bool combined_knowledge_retrieval = 38;
    // 复杂问题拆解检索 会将复杂query拆解成多个子query，多个子query会同时检索，提升知识检索的召回 默认关闭
    bool complex_problem_retrieval = 39;
    // 多模态出图 检索知识库中的知识包含图片，提供给多模态阅读理解大模型理解图片后做出回答，提升答案的出图率 默认关闭
    bool multi_modal_generation_image = 40;
    // 实时文档上下文记忆，默认开启
    bool realtime_doc_context = 41;
    // 是否开启异步工作流
    bool async_workflow = 42;
    // 联网搜索引擎，0（默认）：元宝，1：搜狗，
    uint32 search_engine = 43;
    // 送意图带FAQ,默认true
    bool intent_with_faq = 44;
    // AI通话
    AICallConfig ai_call = 45;
    // 共享知识库配置
    repeated APIShareKnowledgeBase share_knowledge_bases = 46;
  }
  message SummaryConfig {
    // 模型配置
    map<string, AppModelInfo> model = 1;
    // 输出配置
    SummaryOutput output = 2;
    // 模型列表，给chat做用户选择使用
    map<string, AppModelInfo> module_list = 3;
    // 欢迎语
    string greeting = 4;
  }
  message ClassifyConfig {
    // 模型配置
    map<string, AppModelInfo> model = 1;
    // 标签列表
    repeated ClassifyLabel labels = 2;
    // 模型列表，给chat做用户选择使用
    map<string, AppModelInfo> module_list = 3;
    // 欢迎语
    string greeting = 4;
  }
  // 应用ID
  uint64 id = 1;
  // 企业ID
  uint64 corp_id = 2;
  // 应用业务ID
  uint64 app_biz_id = 3;
  // app_key
  string app_key = 4;
  // 应用类型；knowledge_qa-知识问答管理；summary-知识摘要；classify-知识标签提取
  string app_type = 5;
  // 应用类型说明
  string app_type_desc = 6;
  // 是否删除
  bool is_delete = 7;
  // 是否启用
  bool enabled = 8;
  // 过期时间
  int64 expire_time = 9;
  // 创建时间
  int64 create_time = 10;
  // 更新时间
  int64 update_time = 11;
  // 应用状态 1未上线 2运行中 3停用
  int64 status = 12;
  // 机器人基础配置
  BaseConfig base_config = 13;
  // 知识问答应用配置
  KnowledgeQaConfig knowledge_qa = 14;
  // 知识摘要应用配置
  SummaryConfig summary = 15;
  // 标签配置
  ClassifyConfig classify = 16;
  // 应用状态描述
  string status_desc = 17;
  // 应用配置版本ID
  uint64 config_version_id = 18;
  // 是否体验中心应用
  bool is_exp_app = 19;
  // 安全审核策略
  string infosec_biz_type = 20;
  // 企业业务ID
  uint64 corp_biz_id = 21;
  // UIN
  string uin = 22;
  // 是否共享知识库
  bool is_share_knowledge_base = 23;
}

// 机器人模型配置
message RobotModelInfo {
  // 提示词
  string prompt = 1;
  // 提示词内容字符限制
  uint32 prompt_words_limit = 2;
  // 对话历史条数限制
  uint32 history_limit = 6;
  // 对话历史条数限制
  uint32 history_words_limit = 7;
  // 模型名称 (trpc 接口使用)
  string model_name = 8;
  // 下游服务名 (trpc 接口使用)
  string service_name = 9;
  // 模型是否开启
  bool is_enabled = 10;

  // 模型调用接口路由 (http 接口使用)
  string path = 3 [deprecated = true];
  // 模型调用接口地址 (http 接口使用)
  string target = 4 [deprecated = true];
  // 模型类型 (http 接口使用)
  uint32 type = 5 [deprecated = true];
}

// 编辑机器人配置请求
message EditRobotReq {
  // 机器人ID
  uint64 bot_biz_id = 1 [(validate.rules).uint64.gt = 0];
  // 昵称
  string name = 2;
  // 描述信息
  string description = 3;
  // QA检索库线上版本
  uint64 qa_version = 4;
  // 是否转人工
  bool can_transfer_keyword = 5;
  // 转人工关键词
  repeated string transfer_keywords = 6;
  // 是否启用
  bool enabled = 7;
  // 过期时间
  int64 expire_time = 8;
  // 未知问题回复语
  string bare_answer = 9;
  // 是否使用行业通用知识库
  bool use_general_knowledge = 10;
  // 模型配置
  map<string, RobotModelInfo> model = 11;
  // 索引配置
  map<string, RobotFilters> filters = 12;
  // 切片配置
  map<string, RobotSplitDoc> split_doc = 13;
  // 相似配置
  RobotSearchVector search_vector = 14;
  // embedding 配置
  RobotEmbedding embedding = 16;
  // 是否开启根据用户语义转人工
  bool can_transfer_intent = 17;
  // 是否开启机器人答案评价不满意转人工
  bool can_transfer_unsatisfied = 18;
  // 机器人答案评价不满意转人工，设定的触发次数
  uint32 transfer_unsatisfied_count = 19;
  // 机器人描述(prompt 场景使用)
  string role_description = 20 [(validate.rules).string = {min_len: 0, max_len: 1000}];
  // 欢迎语
  string greeting = 21 [(validate.rules).string = {min_len: 0, max_len: 200}];
  // 意图策略ID
  uint32 intent_policy_id = 22;
}

// 编辑机器人配置响应
message EditRobotRsp {}

// 获取企业信息列表请求
message GetCorpListReq {
  string cellphone = 1;
}

message GetCorpListRsp {
  // 企业信息
  repeated CorpInfo list = 1;
}

message CorpInfo {
  // 企业ID
  uint64 corp_biz_id = 1;
  // 手机号
  string cellphone = 2;
}

message GetDocsReq {
  repeated uint64 ids = 1;
}

message GetDocsRsp {
  message Doc {
    uint64 id = 1;
    string page_content = 2;
    string org_data = 3;
  }
  repeated Doc docs = 1;
}

// 拒答问题测评库查询请求
message SearchPreviewRejectedQuestionReq {
  // 机器人ID
  uint64 bot_biz_id = 1 [(validate.rules).uint64 = {gte: 1}];
  // 查询问题
  string question = 2 [(validate.rules).string = {min_len: 1, max_len: 6000}];
}

// 拒答问题测评库查询响应
message SearchPreviewRejectedQuestionRsp {
  // 拒答问题测评库查询命中结果列表
  message RejectedQuestions {
    // 拒绝问题ID
    uint64 id = 1;
    // 内容
    string question = 2;
    // 置信度
    float confidence = 3;
  }
  repeated RejectedQuestions list = 1;
}


// 拒答问题线上库查询请求
message SearchReleaseRejectedQuestionReq {
  // 机器人ID
  uint64 bot_biz_id = 1 [(validate.rules).uint64 = {gte: 1}];
  // 查询问题
  string question = 2 [(validate.rules).string = {min_len: 1, max_len: 6000}];
}

// 拒答问题线上库查询响应
message SearchReleaseRejectedQuestionRsp {
  // 拒答问题测评库查询命中结果列表
  message RejectedQuestions {
    // 拒绝问题ID
    uint64 id = 1;
    // 内容
    string question = 2;
    // 置信度
    float confidence = 3;
  }
  repeated RejectedQuestions list = 1;
}

// 添加不满意回复请求
message AddUnsatisfiedReplyReq {
  // 机器人业务ID
  uint64 bot_biz_id = 1 [(validate.rules).uint64 = {gte: 1}];
  // 消息记录ID
  string record_id = 2 [(validate.rules).string = {min_len: 1}];
  // 用户问题
  string question = 3 [(validate.rules).string = {min_len: 1}];
  // 机器人回复答案
  string answer = 4 [(validate.rules).string = {min_len: 1}];
  // 机器人回复上下文
  repeated UnsatisfiedReplyContext context = 5 [(validate.rules).repeated .min_items = 1];
  // 错误原因,C端反馈可不传原因
  repeated string reasons = 6;
}

// 不满意回复上下文
message UnsatisfiedReplyContext {
  // 消息记录ID
  string record_id = 1 [(validate.rules).string = {min_len: 1}];
  // 是否为用户
  bool is_visitor = 2;
  // 是否为机器人
  bool is_robot = 3;
  // 消息发送者ID
  uint64 from_id = 4 [(validate.rules).uint64 = {gte: 1}];
  // 消息内容
  string content = 5 [(validate.rules).string = {min_len: 1}];
}

// 添加不满意回复响应
message AddUnsatisfiedReplyRsp {}

// 获取admin任务列表
message GetAdminTaskListReq {
  // 机器人业务ID
  uint64 bot_biz_id = 1 [(validate.rules).uint64 = {gte: 1}];
  // 任务类型
  repeated uint32 task_type = 2;
  // 页码
  uint32 page = 3 [(validate.rules).uint32.gt = 0];
  // 分页数量
  uint32 page_size = 4 [(validate.rules).uint32.gt = 0];
}

// 获取admin任务列表响应
message GetAdminTaskListRsp {
  // 总数
  uint64 total = 1;
  repeated TaskInfo list = 2;
}

// 获取admin历史任务列表
message GetAdminTaskHistoryListReq {
  // 机器人业务ID
  uint64 bot_biz_id = 1 [(validate.rules).uint64 = {gte: 1}];
  // 任务类型
  repeated uint32 task_type = 2;
  // 页码
  uint32 page = 3 [(validate.rules).uint32.gt = 0];
  // 分页数量
  uint32 page_size = 4 [(validate.rules).uint32.gt = 0];
}

// 获取admin任务历史列表响应
message GetAdminTaskHistoryListRsp {
  // 总数
  uint64 total = 1;
  repeated TaskHistoryInfo list = 2;
}

// 获取vector_doc任务列表
message GetVectorDocTaskListReq {
  // 机器人业务ID
  uint64 bot_biz_id = 1 [(validate.rules).uint64 = {gte: 1}];
  // 任务类型
  repeated uint32 task_type = 2;
  // 页码
  uint32 page = 3 [(validate.rules).uint32.gt = 0];
  // 分页数量
  uint32 page_size = 4 [(validate.rules).uint32.gt = 0];
}

// 获取vector_doc任务列表响应
message GetVectorDocTaskListRsp {
  // 总数
  uint64 total = 1;
  repeated TaskInfo list = 2;
}

// 获取vector_doc任务历史列表
message GetVectorDocTaskHistoryListReq {
  // 机器人业务ID
  uint64 bot_biz_id = 1 [(validate.rules).uint64 = {gte: 1}];
  // 任务类型
  repeated uint32 task_type = 2;
  // 页码
  uint32 page = 3 [(validate.rules).uint32.gt = 0];
  // 分页数量
  uint32 page_size = 4 [(validate.rules).uint32.gt = 0];
}

// 获取vector_doc任务历史列表响应
message GetVectorDocTaskHistoryListRsp {
  // 总数
  uint64 total = 1;
  repeated TaskHistoryInfo list = 2;
}

// 发布记录列表请求
message GetReleaseListReq {
  // 页码
  uint32 page = 1 [(validate.rules).uint32.gt = 0];
  // 分页数量
  uint32 page_size = 2 [(validate.rules).uint32.gt = 0];
  // 机器人ID
  uint64 bot_biz_id = 3 [(validate.rules).uint64.gt = 0];
}

// 发布记录列表响应
message GetReleaseListRsp {
  message Release {
    // 版本ID
    uint64 id = 1;
    // 发布人
    string operator = 2;
    // 发布描述
    string desc = 3;
    // 更新时间
    int64 update_time = 4;
    // 发布状态
    uint32 status = 5;
    // 状态描述
    string status_desc = 6;
    // 失败原因
    string reason = 7;
  }
  uint64 total = 1;
  repeated Release list = 2;
}

// 添加企业员工请求
message AddCorpStaffReq {
  // 企业ID
  uint64 corp_biz_id = 1 [(validate.rules).uint64.gt = 0];
  // 昵称
  string nick_name = 2 [(validate.rules).string.min_len = 1];
  // 用户账号
  string account = 3 [(validate.rules).string = {min_len: 1, max_len: 20}];
  // 用户密码
  string password = 4 [(validate.rules).string = {min_len: 32, max_len: 32}];
  // 手机号
  string telephone = 5 [(validate.rules).string.min_len = 1];
}

// 添加企业员工响应
message AddCorpStaffRsp {}

// 修改企业员工密码请求
message EditCorpStaffPasswordReq {
  // 员工ID
  uint64 staff_biz_id = 1 [(validate.rules).uint64.gt = 0];
  // 用户密码，MD5加密
  string password = 2 [(validate.rules).string = {min_len: 32, max_len: 32}];
}

// 修改企业员工密码响应
message EditCorpStaffPasswordRsp {}

// 退出企业请求
message LeaveCorpReq {
  // 员工ID
  uint64 staff_biz_id = 1 [(validate.rules).uint64.gt = 0];
}

// 退出企业响应
message LeaveCorpRsp {}

message UpdateAuditStatusReq {
  // t_audit 表中的 父审核 id
  uint64 audit_parent_id = 1;
  // t_audit 表中的 审核 id
  uint64 audit_id = 2;
  // 人工审核结果 是否通过
  bool is_pass = 3;
  // type t_audit 表中的类型 含义保持一致
  uint32 audit_type = 4;
  // 最后一次审核人
  string operator = 5;
  // 申诉单父 id
  uint64 appeal_parent_id = 6;
  // 申诉 id
  uint64 appeal_id = 7;
  // 申诉任务完成
  bool appeal_task_done = 8;
  // 申诉人工审核成功数
  uint32 num_success = 9;
  // 申诉人工审核失败数
  uint32 num_fail = 10;
  // 申诉人工审核总数
  uint32 num_total = 11;
  // t_appeal 申诉表 关联 id
  uint64 relate_id = 12;
  // corp_id 企业 ID
  uint64 corp_id = 13;
  // robot_id 机器人 ID
  uint64 robot_id = 14;
  // 申诉单创建人
  uint64 create_staff_id = 15;
  // 申诉失败原因
  string reject_reason = 16;
}

message UpdateAuditStatusRsp {}

// GetRobotDefaultConfigReq 获取机器人默认配置信息请求
message GetRobotDefaultConfigReq {}

// GetRobotDefaultConfigRsp 获取机器人默认配置信息响应
message GetRobotDefaultConfigRsp {
  // 模型配置
  map<string, RobotModelInfo> model = 1;
  // 索引配置
  map<string, RobotFilters> filters = 2;
  // 切片配置
  map<string, RobotSplitDoc> split_doc = 3;
  // 相似配置
  RobotSearchVector search_vector = 4;
}

// ClearRobotCustomConfigReq 清除机器人自定义配置请求
message ClearRobotCustomConfigReq {
  // 机器人ID
  uint64 bot_biz_id = 1 [(validate.rules).uint64.gt = 0];
  // 是否清理自定义模型配置
  bool is_clear_model = 2;
  // 是否清理自定义索引配置
  bool is_clear_filters = 3;
  // 是否清理自定义切片配置
  bool is_clear_split_doc = 4;
  // 是否清理自定义相似配置
  bool is_clear_search_vector = 5;
}

// ClearRobotCustomConfigRsp 清除机器人自定义配置响应
message ClearRobotCustomConfigRsp {}

// 全局干预知识列表请求
message ListGlobalKnowledgeReq {
  // 查询内容
  string query = 1;
  // 页码
  uint32 page_number = 2 [(validate.rules).uint32.gt = 0];
  // 分页数量
  uint32 page_size = 3 [(validate.rules).uint32.gt = 0];
}

// 全局干预知识列表响应
message ListGlobalKnowledgeRsp {
  message GlobalKnowledge {
    // ID
    uint64 id = 1;
    // 问题
    string question = 2;
    // 答案
    string answer = 3;
    // 是否同步
    bool is_sync = 4;
    // 是否删除
    bool is_deleted = 5;
    // 创建时间
    int64 create_time = 6;
    // 更新时间
    int64 update_time = 7;
  }
  uint64 total = 1;
  repeated GlobalKnowledge list = 2;
}

// 添加全局干预知识
message AddGlobalKnowledgeReq {
  // 问题
  string question = 4 [(validate.rules).string = {min_len: 1, max_len: 2000}];
  // qa答案
  string answer = 5 [(validate.rules).string = {min_len: 1, max_len: 2000}];
}

// 添加全局干预知识
message AddGlobalKnowledgeRsp {
  // ID
  uint64 id = 1;
}

// 删除全局干预知识
message DelGlobalKnowledgeReq {
  // ID
  uint64 id = 1 [(validate.rules).uint64 = {gte: 1}];
}

// 删除添加全局干预知识
message DelGlobalKnowledgeRsp {}

// 更新全局干预知识
message UpdGlobalKnowledgeReq {
  // ID
  uint64 id = 1 [(validate.rules).uint64 = {gte: 1}];
  // 问题
  string question = 4 [(validate.rules).string = {min_len: 1, max_len: 2000}];
  // qa答案
  string answer = 5 [(validate.rules).string = {min_len: 1, max_len: 2000}];
}

// 更新全局干预知识
message UpdGlobalKnowledgeRsp {}

// 问题查询请求
message GlobalKnowledgeReq {
  // 问题
  string question = 1 [(validate.rules).string = {min_len: 1, max_len: 6000}];
  // 过滤器名称
  string filter_key = 2;
  // labels 标签
  repeated VectorLabel labels = 3;
}

// 问题查询响应
message GlobalKnowledgeRsp {
  message Doc {
    // 文档ID
    uint64 doc_id = 1;
    // 1是QA 2是segment
    uint32 doc_type = 2;
    // QAID/SegmentID
    uint64 related_id = 3;
    // 问题
    string question = 4;
    // qa答案
    string answer = 5;
    // 置信度
    float confidence = 7;
    // 文档片段
    string org_data = 8;
    // QABizID/SegmentBizID
    uint64 related_biz_id = 9;
  }
  repeated Doc docs = 1;
}

// 强制同步全局干预知识
message ForceSyncGlobalKnowledgeReq {
  // ID
  uint64 id = 1 [(validate.rules).uint64 = {gte: 1}];
}

// 强制同步全局干预知识
message ForceSyncGlobalKnowledgeRsp {}


// 获取相似度
message CustomSimilarityReq {
  string ori = 1 [(validate.rules).string = {min_len: 1, max_len: 2000}];
  repeated string docs = 2;
}

message CustomSimilarityRsp {
  message RougeScore {
    double f = 1;
    double p = 2;
    double r = 3;
  }
  repeated RougeScore scores = 1;
}

// 相似分数
message RougeScore {
  double f = 1;
  double p = 2;
  double r = 3;
}

// 启用企业请求
message EnableCorpReq {
  repeated uint64 corp_biz_ids = 1 [(validate.rules).repeated = {
    min_items: 1,
    items: {uint64: {gt: 0}}
  }];
}

message EnableCorpRsp {}

// 禁用企业请求
message DisableCorpReq {
  repeated uint64 corp_biz_ids = 1 [(validate.rules).repeated = {
    min_items: 1,
    items: {uint64: {gt: 0}}
  }];
}

message DisableCorpRsp {}

// 创建通知请求
message CreateNoticeReq {
  // 机器人ID
  uint64 bot_biz_id = 1;
  // 页面ID 标记页面
  uint32 page_id = 2;
  // 通知业务类型
  uint32 type = 3;
  // 通知级别
  string level = 4;
  // 业务ID
  uint64 relate_id = 5;
  // 通知主题
  string subject = 6;
  // 通知内容
  string content = 7;
  // 是否全局通知
  bool is_global = 8;
  // 是否允许被关闭
  bool is_allow_close = 9;
  // 企业ID
  uint64 corp_id = 10;
  // 员工ID
  uint64 staff_id = 11;
  // 通知操作
  message Operation {
    // 操作参数
    message Params {
      // cos下载地址
      string cos_path = 1;
      // 发布ID
      string version_id = 2;
      // 申诉类型
      uint32 appeal_type = 3;
      // 文档ID
      uint64 doc_biz_id = 4;
      // 反馈ID
      uint64 feedback_biz_id = 5;
      // 扩展json string
      string extra_json_data = 6;
    }
    // 对应操作type
    uint32 type = 1;
    // 操作参数
    Params params = 2;
  }
  // 操作列表
  repeated Operation operations = 12;
}

// 创建通知响应
message CreateNoticeRsp {}

// 创建通知请求
message CreateNoticeByUinReq {
  // uin
  string uin = 1 [(validate.rules).string = {min_len: 1}];
  // 通知主题
  string subject = 2  [(validate.rules).string = {min_len: 1}];
  // 通知内容
  string content = 3  [(validate.rules).string = {min_len: 1}];
  // 类型
  uint32 type = 4;
  // 是否赠品
  bool is_gift = 5;
  // 通知级别 info:基础信息（蓝色） warning:警告（黄色） error:错误（红色）success:成功（绿色）
  string level = 6;
}

// 创建通知响应
message CreateNoticeByUinRsp {}

// 查询集成商请求
message DescribeIntegratorReq{
  // uin主账号
  string uin = 1 [(validate.rules).string = {min_len: 1}];
  // 子账号
  string sub_account_uin = 2 [(validate.rules).string = {min_len: 1}];
  // 集成商ID
  uint32 sid = 3;
}

// 查询集成商响应
message DescribeIntegratorRsp{
  // 集成商ID
  uint64 id = 1;
  // 集成商名称
  string name = 2;
  // 集成商状态
  uint32 status = 3;
  // 集成商主账号
  string uin = 4;
  // 集成商子账号
  string sub_account_uin = 5;
  // 是否集成商自己管理权限与资源
  bool is_self_permission = 6;
  // 创建时间
  int64 create_time = 7;
  // 更新时间
  int64 update_time = 8;
}

// 查询机器人业务ID请求
message DescribeRobotBizIDByAppKeyReq{
  string app_key = 1 [(validate.rules).string = {min_len: 1}];
}

// 查询机器人业务ID响应
message DescribeRobotBizIDByAppKeyRsp{
  uint64 bot_biz_id = 1;
}

// 获取机器人最新发布状态请求
message DescribeLatestReleaseStatusReq {
  // 机器人ID
  uint64 bot_biz_id = 1 [(validate.rules).uint64.gt = 0];
  // 企业ID
  uint64 corp_id = 2 [(validate.rules).uint64.gt = 0];
}

// 获取机器人最新发布状态响应
message DescribeLatestReleaseStatusRsp{
  //
  uint64 release_biz_id = 1;
  //
  uint32 status = 2;
}

// TrialProductReq 试用开通请求
message TrialProductReq {}

// TrialProductRsp 试用开通响应
message TrialProductRsp {
  // 账户类型 Enterprise:企业 Personal:个人
  string account_type = 1;
  // 是否开通过产品
  bool is_activated = 2;
  // 活动状态 0：无活动，1：活动开启
  uint32 activity_status = 3;
}

// EditAppReq 编辑应用配置请求
message EditAppReq {
  message KnowledgeQaConfig {
    // 模型配置
    map<string, AppModelInfo> model = 1;
    // 索引配置
    map<string, AppFilters> filters = 2;
    // 切片配置
    map<string, AppSplitDoc> split_doc = 3;
    // 相似配置
    AppSearchVector search_vector = 4;
    // 输出配置
    KnowledgeQaOutput output = 5;
    // 自定义模型topK设置
    uint32 new_model_top_k = 6;
    // 工作流程
    AppWorkflow workflow = 7;
    // 应用模式 standard:标准模式, agent: agent模式，single_workflow：单工作流模式
    string pattern = 8;
    // 单工作流模式传入工作流ID
    string workflow_id = 9;
    // 应用检索策略
    AppSearchStrategy search_strategy = 10;
    // 知识问答配置插件
    repeated AppPlugin plugins = 11;
    // CoT配置
    CoTConfig cot = 12;
    // 多意图开关 可控制是否输出多个意图，避免澄清概率太高，有客户希望关闭这个开关。 默认开启
    bool multiple_intent = 13;
    // 组合知识检索 文档阅读理解的时候 默认关闭
    bool combined_knowledge_retrieval = 14;
    // 复杂问题拆解检索 会将复杂query拆解成多个子query，多个子query会同时检索，提升知识检索的召回 默认关闭
    bool complex_problem_retrieval = 15;
    // 多模态出图 检索知识库中的知识包含图片，提供给多模态阅读理解大模型理解图片后做出回答，提升答案的出图率 默认关闭
    bool multi_modal_generation_image = 16;
    // 实时文档上下文记忆，默认开启
    bool realtime_doc_context = 17;
    // 联网搜索引擎，0（默认）：元宝，1：搜狗，
    uint32 search_engine = 18;
    // 送意图带FAQ,默认true
    bool intent_with_faq = 19;
  }
  message SummaryConfig {
    // 模型配置
    map<string, AppModelInfo> model = 1;
  }
  message ClassifyConfig {
    // 模型配置
    map<string, AppModelInfo> model = 1;
  }
  // 应用ID
  uint64 app_biz_id = 1 [(validate.rules).uint64.gt = 0];
  // 知识问答应用配置
  KnowledgeQaConfig knowledge_qa = 2;
  // 知识摘要应用配置
  SummaryConfig summary = 3;
  // 标签配置
  ClassifyConfig classify = 4;
  // 场景 1是评测 2是正式
  uint32 scenes = 5 [(validate.rules).uint32 = {in: [1, 2]}];
  // 意图策略ID
  uint32 intent_policy_id = 6;
}

// 编辑应用配置响应
message EditAppRsp {}

// GetAppDefaultConfigReq 获取应用默认配置请求
message GetAppDefaultConfigReq {}

// GetAppDefaultConfigRsp 获取应用默认配置响应
message GetAppDefaultConfigRsp {
  message KnowledgeQaConfig {
    // 模型配置
    map<string, AppModelInfo> model = 1;
    // 索引配置
    map<string, AppFilters> filters = 2;
    // 切片配置
    map<string, AppSplitDoc> split_doc = 3;
    // 相似配置
    AppSearchVector search_vector = 4;
  }
  message SummaryConfig {
    // 模型配置
    map<string, AppModelInfo> model = 1;
  }
  message ClassifyConfig {
    // 模型配置
    map<string, AppModelInfo> model = 1;
  }
  // 知识问答应用配置
  KnowledgeQaConfig knowledge_qa = 1;
  // 知识摘要应用配置
  SummaryConfig summary = 2;
  // 标签配置
  ClassifyConfig classify = 3;
}

// ClearAppCustomConfigReq 清除应用自定义配置请求
message ClearAppCustomConfigReq {
  // 应用ID
  uint64 app_biz_id = 1 [(validate.rules).uint64.gt = 0];
  // 是否清理自定义模型配置
  bool is_clear_model = 2;
  // 是否清理自定义索引配置
  bool is_clear_filters = 3;
  // 是否清理自定义切片配置
  bool is_clear_split_doc = 4;
  // 是否清理自定义相似配置
  bool is_clear_search_vector = 5;
  // 场景 1是评测 2是正式
  uint32 scenes = 6 [(validate.rules).uint32 = {in: [1, 2]}];
  // 是否清理自定义模型topK
  bool is_clear_model_top_k = 7;
}

// ClearAppCustomConfigRsp 清除应用自定义配置响应
message ClearAppCustomConfigRsp {}

// 获取意图请求
message GetIntentReq {
  // 意图策略
  uint32 policy_id = 1;
  // 意图名称
  string name = 2;
}

// 获取意图响应
message GetIntentRsp {
  // 意图名称
  string name = 1;
  // 意图类型
  string category = 2;
  // 所属策略ID
  uint32 policy_id = 3;
}

// 获取意图列表请求
message ListIntentReq{
  // 意图名称
  string name = 1;
  // 是否删除
  int32 is_deleted = 2;
  // 是否使用
  int32 is_used = 3;
  // 是否筛选了使用状态
  bool used_filter = 4;
  // 页码
  uint32 page = 5;
  // 页面大小
  uint32 page_size = 6;
}

// 获取意图列表响应
message ListIntentRsp{
  message Intents{
    // 意图ID
    uint64 intent_id = 1 [(validate.rules).uint64 = {gte: 1}];
    // 意图名称
    string name = 2;
    // 是否删除
    int32 is_deleted = 3;
    // 是否使用
    int32 is_used = 4;
    // 创建时间
    int64 create_time = 5;
    // 更新时间
    int64 update_time = 6;
    // 操作人
    string operator = 7;
  }
  // 意图列表
  repeated Intents list = 1;
  // 数量
  uint32 total = 2;
}

// 创建意图请求
message CreateIntentReq{
  // 意图名称
  string name = 1 [(validate.rules).string = {min_len: 1}];
  // 意图分类
  string category = 2;
  // 操作人
  string operator = 3;
  // 意图策略
  uint32 policy_id = 4;
}

// 创建意图响应
message CreateIntentRsp{}

// 更新意图请求
message UpdateIntentReq{
  // 意图ID
  uint64 intent_id = 1 [(validate.rules).uint64 = {gte: 1}];
  // 意图名称
  string name = 2;
  // 意图分类
  string category = 3;
  // 操作人
  string operator = 4;
  // 意图策略
  uint32 policy_id = 5;
}

// 更新意图响应
message UpdateIntentRsp{}

// 删除意图请求
message DeleteIntentReq{
  // 意图ID
  uint64 intent_id = 1 [(validate.rules).uint64 = {gte: 1}];
  // 操作人
  string operator = 2;
}

// 删除意图响应
message DeleteIntentRsp{}

// 获取策略绑定的意图列表请求
message ListIntentByPolicyIDReq{
  // 策略ID
  uint64 policy_id = 1 [(validate.rules).uint64 = {gte: 1}];
}

// 获取策略绑定的意图列表响应
message ListIntentByPolicyIDRsp{
  message Intents{
    // 意图ID
    uint64 intent_id = 1;
    // 意图名称
    string name = 2;
    // 意图分类
    string category = 3;
  }
  repeated Intents list = 1;
}

// 获取策略列表请求
message ListIntentPolicyReq{
  // 策略名称
  string name = 1;
  // 页码
  uint32 page = 2;
  // 页面大小
  uint32 page_size = 3;
}

// 获取策略列表响应
message ListIntentPolicyRsp{
  // 意图
  message Intent{
    // 意图ID
    uint64 intent_id = 1;
    // 意图名称
    string name = 2;
    // 意图分类
    string category = 3;
  }
  // 策略
  message IntentPolicy{
    // 策略ID
    uint64 id = 1 [(validate.rules).uint64 = {gte: 1}];
    // 意图名称
    string name = 2;
    // 是否使用
    int32 is_used = 3;
    // 创建时间
    int64 create_time = 4;
    // 更新时间
    int64 update_time = 5;
    // 操作人
    string operator = 6;
    // 策略绑定的意图内容
    repeated Intent intents = 7;
  }
  // 策略列表
  repeated IntentPolicy list = 1;
  // 数量
  uint32 total = 2;
}

// 创建策略请求
message CreateIntentPolicyReq{
  // 策略名称
  string name = 1 [(validate.rules).string = {min_len: 1}];
  // 操作人
  string operator = 2;
  // 知识问答意图id列表
  repeated uint32 knowledge_intent_id = 3;
  // 搜索引擎id列表
  repeated uint32 engine_intent_id = 4;
  // 文档摘要id列表
  repeated uint32 summary_intent_id = 5;
  // 通用模型闲聊id列表
  repeated uint32 chat_intent_id = 6;
  // 自我认知id列表
  repeated uint32 self_intent_id = 7;
  // 数学计算id列表
  repeated uint32 mathematics_calculate_id = 8;
}

// 创建策略响应
message CreateIntentPolicyRsp{}

// 更新策略请求
message UpdateIntentPolicyReq{
  // 策略ID
  uint64 id = 1 [(validate.rules).uint64 = {gte: 1}];
  // 策略名称
  string name = 2;
  // 需要更新的意图分类
  string category = 3;
  // 变更后的意图id列表
  repeated uint32 intent_id = 4;
  // 操作人
  string operator = 5;
  // 是否为新增绑定关系
  bool is_new = 6;
  // 是否手动删除所有意图
  bool is_manual = 7;
  // 配置入口意图名称
  string intent_name = 8;
}

// 更新策略响应
message UpdateIntentPolicyRsp{}

// 删除策略请求
message DeleteIntentPolicyReq{
  // 策略ID
  uint64 id = 1 [(validate.rules).uint64 = {gte: 1}];
  // 操作人
  string operator = 2;
}

// 删除策略响应
message DeleteIntentPolicyRsp{}



// 获取未使用未删除的策略意图列表请求
message ListUnusedIntentKeyMapReq{}

// 获取未使用未删除的策略意图列表响应
message ListUnusedIntentKeyMapRsp{
  message Options{
    // 意图ID
    uint64 intent_id = 1;
    // 意图名称
    string name = 2;
  }
  repeated Options options = 1;
}

// 获取策略列表请求
message ListIntentPolicyKeyMapReq{}

// 获取策略列表列表响应
message ListIntentPolicyKeyMapRsp{
  message Options{
    // 策略ID
    uint64 id = 1;
    // 策略名称
    string name = 2;
  }
  repeated Options options = 1;
}

// 新增自定义模型请求
message CreateCorpCustomModelReq{
  // uin信息
  string uin = 1 [(validate.rules).string = {min_len: 1}];
  // 模型名称
  string model_name = 2 [(validate.rules).string = {min_len: 1}];
  // 模型展示名称
  string alias = 3 [(validate.rules).string = {min_len: 1}];
  // 备注
  string note = 4;
  // 操作人
  string operator = 5;
}

// 新增自定义模型响应
message CreateCorpCustomModelRsp{}

// 获取版本配置
message GetRobotConfigByVersionIDReq {
  // 机器人ID
  uint64 bot_biz_id = 1 [(validate.rules).uint64.gt = 0];
  // 版本ID
  uint64 version_id = 2;
}

// 获取版本配置响应
message GetRobotConfigByVersionIDRsp {
  string config = 1;
}

// ================== inner api agent  反馈相关 start ====================

// 查询反馈信息列表
message ListAgentFeedbackInnerReq {
  // 企业biz id
  uint64 corp_biz_id = 1;
  // 应用业务ID
  string app_biz_id = 2;
  // 错误类型检索
  string reason = 3;
  // 页码
  uint32 page_number = 4 [(validate.rules).uint32.gt = 0];
  // 分页数量
  uint32 page_size = 5 [(validate.rules).uint32.gt = 0];
  // 处理状态
  repeated uint32 status = 6;
  uint64 feed_biz_id = 7 ; // 反馈业务id
}

message AgentFeedbackInner {
  // 反馈消息业务ID
  string business_id = 1;
  // 用户问题
  string question = 2;
  // 机器人回复
  string answer = 3;
  // 错误类型
  repeated string reasons = 4;
  // 处理状态
  uint32 status = 5;
  // 应用业务ID
  string app_biz_id = 6;
  // session id
  string session_id = 7;
  // reply method
  uint32 reply_method = 8;
  // intent category
  string intent_category = 9;
  // tapd
  string tapd = 10;
  // create time
  int64 create_time = 11;
  // corp
  uint64 corp_id = 12;
  // session id
  string record_id = 13;
  // 员工id
  uint64 staff_id = 14;
  // 企业名称
  string corp_name = 15;
  // 应用名称
  string app_name = 16;
  // 员工名称
  string staff_name = 17;
  // 拒绝理由
  string reject_reason = 18;
  // 优化结果
  string optimized_result = 19;
  string andon_ticket = 20; // 安灯ticket
  string andon_url = 21; // 安灯url
}
// 查询反馈信息列表响应
message ListAgentFeedbackInnerRsp {
  // 总数
  uint32 total = 1;
  repeated AgentFeedbackInner list = 2;
}

// 更新反馈信息状态
message UpdateAgentFeedbackStatusReq {
  // 反馈业务ID列表
  string feedback_biz_id = 1;
  // 处理状态
  uint32 status = 2;
  // 拒绝理由
  string reject_reason = 3;
  // 优化结果
  string optimized_result = 4;
}

// 更新反馈信息状态响应
message UpdateAgentFeedbackStatusRsp {}

// 更新反馈信息状态
message UpdateAgentFeedbackAndonInfoReq {
  string feedback_biz_id = 1;  // 反馈业务ID列表
  string andon_ticket = 2; // andon_ticket
  string andon_url = 3; // andon_url
}

// 更新反馈信息状态响应
message UpdateAgentFeedbackAndonInfoRsp {}





// 更新反馈信息TAPD
message UpdateAgentFeedbackTapdReq {
  // 反馈业务ID列表
  string feedback_biz_id = 1;
  // tapd信息
  string tapd = 2;
}

// 更新反馈信息TAPD响应
message UpdateAgentFeedbackTapdRsp{}

// 查询反馈信息个数
message CountAgentFeedbackReq {
  // 创建时间
  int64 create_time = 1;
}

// 查询反馈信息个数响应
message CountAgentFeedbackRsp{
  // 个数
  uint64 count = 1;
}

// 获取反馈信息
message GetAgentFeedbackReq {
  // 反馈业务ID列表
  string feedback_biz_id = 1;
}

// 获取反馈信息响应
message GetAgentFeedbackRsp{
  AgentFeedbackInner feedback = 1;
}

// ================== inner api agent 反馈相关 end ====================

message  DeleteFeedbackByFlowIdsReq {
  repeated  string  flow_ids = 1;
  uint64 app_biz_id = 2;
}

message  DeleteFeedbackByFlowIdsRsp {}

// 查询反馈信息列表
message ListFeedbackInnerReq {
  // 企业biz id
  uint64 corp_biz_id = 1;
  // 应用业务ID
  uint64 app_biz_id = 2;
  // 错误类型检索
  string reason = 3;
  // 页码
  uint32 page_number = 4 [(validate.rules).uint32.gt = 0];
  // 分页数量
  uint32 page_size = 5 [(validate.rules).uint32.gt = 0];
  // 处理状态
  repeated uint32 status = 6;
  // 一级分类
  string first_classification = 7;
  // 二级分类
  string second_classification = 8;
  uint64 feedback_biz_id = 9 ; // 反馈bizID
}

// 查询反馈信息列表响应
message ListFeedbackInnerRsp {
  // 总数
  uint32 total = 1;
  repeated FeedbackInner list = 2;
}

// 查询反馈信息列表
message ListFeedbackByBizIDInnerReq {
  // 反馈消息业务ID
  uint64 business_id = 1;
}
// 查询反馈信息列表
message ListFeedbackByBizIDInnerRsp {
  FeedbackInner feedback = 1;
}

message FeedbackInner {
  // 反馈消息业务ID
  uint64 business_id = 1;
  // 用户问题
  string question = 2;
  // 机器人回复
  string answer = 3;
  // 错误类型
  repeated string reasons = 4;
  // 处理状态
  uint32 status = 5;
  // 参考来源信息
  repeated AnswerReference references = 6;
  // 正确答案
  string right_answer = 7;
  // 正确参考来源
  repeated AnswerReference right_references = 8;
  // 应用业务ID
  uint64 app_biz_id = 9;
  // session id
  string session_id = 10;
  // reply method
  uint32 reply_method = 11;
  // intent category
  string intent_category = 12;
  // tapd
  string tapd = 13;
  // create time
  int64 create_time = 14;
  // corp
  uint64 corp_id = 15;
  // session id
  string record_id = 16;
  // 员工id
  uint64 staff_id = 17;
  // 企业名称
  string corp_name = 18;
  // 应用名称
  string app_name = 19;
  // 员工名称
  string staff_name = 20;
  // 拒绝理由
  string reject_reason = 21;
  // 优化结果
  string optimized_result = 22;
  string feed_type = 23; // 反馈类型
  string flow_id = 24; // 工作流ID
  string node_id = 25; // 画布节点Id
  string workflow = 26; // taskflow：旧画布；workflow：新画布
  string node_name = 27; // 节点名称
  string first_classification = 28; // 一级分类
  string second_classification = 29; // 二级分类
  string andon_ticket = 30; // andon工单id
  string andon_url = 31; // andonURL
  string expect_intent_category = 32; // 预期达成意图
}


// 更新分类信息
message UpdateFeedbackAndonInfoReq {
  uint64 feedback_biz_id = 1  [(validate.rules).uint64.gt = 0];  // 反馈业务ID列表
  string andon_ticket = 2; //  andon工单id
  string andon_url = 3; // andonURL
}

// 更新分类信息
message UpdateFeedbackAndonInfoRsp {
}


// 更新分类信息
message UpdateFeedbackClassificationReq {
  uint64 feedback_biz_id = 1  [(validate.rules).uint64.gt = 0];  // 反馈业务ID列表
  string first_classification = 2; // 一级分类
  string second_classification = 3; // 二级分类
}

// 更新分类信息
message UpdateFeedbackClassificationRsp {
}

// 更新反馈信息状态响应
message UpdateFeedbackStatusRsp {}

// 更新反馈信息TAPD
message UpdateFeedbackTapdReq {
  // 反馈业务ID列表
  uint64 feedback_biz_id = 1  [(validate.rules).uint64.gt = 0];
  // tapd信息
  string tapd = 2;
}

// 更新反馈信息TAPD响应
message UpdateFeedbackTapdRsp{}

// 查询反馈信息个数
message CountFeedbackReq {
  // 创建时间
  int64 create_time = 1;
}

// 查询反馈信息个数响应
message CountFeedbackRsp{
  // 个数
  uint64 count = 1;
}

// 获取反馈信息
message GetFeedbackReq {
  // 反馈业务ID列表
  uint64 feedback_biz_id = 1  [(validate.rules).uint64.gt = 0];
}

// 获取反馈信息响应
message GetFeedbackRsp{
  FeedbackInner feedback = 1;
}

// 获取企业账户余额请求
message DescribeAccountBalanceReq {
  // 业务类型 1:大模型知识引擎
  uint32 biz_type = 1;
  // 企业类型 1:企业ID 2:uin
  uint32 corp_type = 2;
  // 企业
  string corp_id = 3;
  // 账户标识
  repeated AccountType account_types = 4;
}

// 获取企业账户余额响应
message DescribeAccountBalanceRsp {
  // 企业账户余额详情 
  message AccountInfo {
    // 账户类型
    AccountType account_type = 1;
    // 总额
    double total = 2;
    // 余额
    double balance = 3;
    // 单位 token/页/次/并发/字符
    string unit = 4;
  }
  repeated AccountInfo account_list = 1;
}

// 获取应用类型请求
message GetAppTypeReq {
  uint64 app_biz_id = 1;
}

// 获取应用类型响应
message GetAppTypeRsp {
  string app_type = 1;
}

message GetDescribeLicenseReq {
  // 应用类型；knowledge_qa-知识问答管理；summary-知识摘要；classify-知识标签提取
  string app_type = 1;
}

message GetDescribeLicenseRsp {
  // 证书使用状态: valid-有效; invalid-无效; expired-已过期; expiring-即将过期; probation-试用期
  string state = 1;
  // 证书使用状态说明
  string state_desc = 2;
  // 剩余天数
  uint32 remaining = 3;
  // 平台有效期起始日,ms
  uint64 validity_start = 4;
  // 平台有效期结束日,ms
  uint64 validity_end = 5;
}

message GetExperienceAppsReq {
  uint64 app_biz_id = 1;
  // app_key
  string app_key = 2;
  // 应用类型；knowledge_qa-知识问答管理；summary-知识摘要；classify-知识标签提取
  repeated string app_types = 3;
  // 模型类型,knowledge_engine：知识引擎，hunyuan：混元
  string model_type = 4;
  // 每页数目，整型
  uint32 page_size = 5;
  // 页码，整型
  uint32 page_number = 6;
  // 应用名称
  string keyword = 7;
}

message DisplayDocSummery {
  uint64 doc_biz_id = 1;
  // 文件名称
  string file_name = 2;
}

message RecommendQuestion {
  // 问题id，如果新增填0
  uint64 question_id = 1;
  // 问题
  string question = 2;
}

// ShortcutInfo快捷按钮详情
message ShortcutInfo {
  // 问题ID
  uint64 question_id = 1;
  // 快捷按钮问题
  string question = 2;
  // 快捷按钮内容
  string content = 3;
  // 快捷菜单类型；0-不显示；1-仅首页显示；2-仅对话框上面显示；3-全部显示
  uint32 show_type = 4;
}

message GetExperienceAppsRsp {
  message ExperienceAppApiInfo {
    // 体验应用ID
    uint64 exp_app_biz_id = 1;
    // app_key
    string app_key = 2;
    // 应用类型；knowledge_qa-知识问答管理；summary-知识摘要；classify-知识标签提取
    string app_type = 3;
    // 应用类型说明
    string app_type_desc = 4;
    // 应用名称
    string name = 5;
    // 应用头像
    string avatar = 6;
    // 应用描述
    string desc = 7;
    // 模型类型
    string model_type = 8;
    // 模型类型描述
    string model_type_desc = 9;
    // 是否上线体验中心
    bool is_online_expCenter = 10;
    // 创建时间
    int64 create_time = 11;
    // 更新时间
    int64 update_time = 12;
    // 文档信息
    repeated DisplayDocSummery list = 13;
    // 推荐问题
    repeated RecommendQuestion questions = 14;
    // 快捷按钮列表
    repeated ShortcutInfo shortcut_list = 15;
  }
  // 数量
  uint64 total = 1;
  // 体验应用列表
  repeated ExperienceAppApiInfo list = 2;
}

message ModifyExperienceAppReq {
  // 体验应用ID
  uint64 exp_app_biz_id = 1;
  // 应用描述
  string desc = 4;
  // 模型类型
  string model_type = 7;
  // 是否上线体验中心
  bool IsOnlineExpCenter = 8;
  // 文档信息
  repeated uint64 docIds = 9;
  // 推荐问题
  repeated string recommend_question = 10;
  // 快捷按钮列表
  repeated ShortcutInfo shortcut_list = 11;
}

message ModifyExperienceAppRsp {}

message CreateCorpAndAssignPermissionReq{
  // 腾讯云账号
  string uin = 1;
  // 账单号
  string deal_name = 2;
}

message CreateCorpAndAssignPermissionRsp{}

message CheckCorpAndPermissionReq{
  // 腾讯云账号
  string uin = 1;
}

message CheckCorpAndPermissionRsp{
  // 任务状态
  uint32 status = 1;
}

message DescribeCropByUinReq{
  // uin
  string uin_account = 1;
}

message DescribeCropByUinRsp{
  // 企业ID
  uint64 corp_biz_id = 1;
}

// 获取企业模型列表请求
message GetListModelReq {
  uint64 corp_biz_id = 1;
  string app_type = 2;
}

message GetListModelRsp {
  message ModelInfo {
    // 模型名称
    string model_name = 1;
    // 模型描述
    string model_desc = 2;
    // 模型别名
    string alias_name = 3;
  }
  repeated ModelInfo list = 1;
}

// 修改应用审核策略
message ModifyAppInfosecBizTypeReq {
  // 应用ID
  uint64 app_biz_id = 1 [(validate.rules).uint64.gt = 0];
  // 安全审核策略
  string infosec_biz_type = 2;
}

message ModifyAppInfosecBizTypeRsp {}

message GetTotalConcurrencyReq{
  uint64 app_biz_id = 1;
  string model_name = 2;
}

message GetTotalConcurrencyRsp {
  uint64 total_concurrency = 1;
}

message MultiLockReq {
  // 模型名称
  string model_name = 1;
  // 应用业务ID
  uint64 app_biz_id = 2;
  // 持有者
  string holder = 3;
}

message MultiLockRsp {
  // 是否成功
  bool ok = 1;
  // 模型状态
  bool model_status = 2;
}

message MultiUnlockReq {
  // 模型名称
  string model_name = 1;
  // 应用业务ID
  uint64 app_biz_id = 2;
  // 持有者
  string holder = 3;
}

message MultiUnlockRsp {
  // 是否成功
  bool ok = 1;
}

// CheckVarIsUsedReq 检查自定义参数是否被使用入参
message CheckVarIsUsedReq {
  uint64 app_biz_id = 1;
  // 自定义变量信息
  string var_id = 2;
}

// CheckVarIsUsedReq 检查自定义参数是否被使用返回
message CheckVarIsUsedRsp {
  bool is_used = 1;
}

// ModifyAppVarReq 修改应用检索范围的自定义变量入参
message ModifyAppVarReq {
  message varInfo {
    string var_id = 1;
    string var_name = 2;
  }
  uint64 app_biz_id = 1;
  // 自定义变量信息
  varInfo var_info = 2;
}

// ModifyAppVarRsp 修改应用检索范围的自定义变量返回
message ModifyAppVarRsp {}

// 清理应用知识资源回调结果
message ClearAppKnowledgeCallbackReq {
  // 任务ID
  uint64 task_id = 1 [(validate.rules).uint64.gt = 0];
  // 是否成功
  bool is_success = 2;
  // 失败原因
  string message = 3;
}

message ClearAppKnowledgeCallbackRsp {
}

// 清理应用流程资源回调
message ClearAppFlowCallbackReq {
  // 任务ID
  uint64 task_id = 1 [(validate.rules).uint64.gt = 0];
  // 是否成功
  bool is_success = 2;
  // 失败原因
  string message = 3;
}

message  ClearAppFlowCallbackRsp {
}

// 清理应用向量库资源回调
message ClearAppVectorCallbackReq {
  // 任务ID
  uint64 task_id = 1 [(validate.rules).uint64.gt = 0];
  // 是否成功
  bool is_success = 2;
  // 失败原因
  string message = 3;
}

message  ClearAppVectorCallbackRsp {
}


// ClearAppResourceType 升级promptversion数据类型枚举
enum ClearAppResourceType {
  ClearAppResourceTypeNone = 0;
  ClearAppResourceTypeKnowledge = 1; // 应用知识资源
  ClearAppResourceTypeFlow = 2;      // 任务、工作流程
  ClearAppResourceTypeVector = 3;    // 向量、ES
  ClearAppResourceTypeAgent = 4;     // Agent
}


// 清理应用资源回调
message ClearAppResourceCallbackReq {
  // 资源类型
  ClearAppResourceType resource_type = 1;
  // 任务ID
  uint64 task_id = 2 [(validate.rules).uint64.gt = 0];
  // 是否成功
  bool is_success = 3;
  // 失败原因
  string message = 4;
}

message ClearAppResourceCallbackRsp {
}


// 查询插件关联的应用信息
message GetAppByPluginIdReq {
  // uin信息
  string uin = 1 [(validate.rules).string = {min_len: 1}];
  // 插件ID
  string plugin_id = 2 [(validate.rules).string = {min_len: 1}];
}

message GetAppByPluginIdRsp {
  message AppInfo {
    // 应用业务ID
    uint64 app_biz_id = 1;
    // 应用名称
    string app_name = 2;
  }
  repeated AppInfo app_list = 2;
}

// 查询用户昵称
message GetCorpStaffNameReq {
  // uin主账号
  string uin = 1 [(validate.rules).string = {min_len: 1}];
  // 子账号
  string sub_account_uin = 2 [(validate.rules).string = {min_len: 1}];
}

message GetCorpStaffNameRsp {
  // 用户昵称
  string staff_name = 1;
}

// 获取模型的计费控制信息
message GetModelFinanceInfoReq {
  // 模型名称
  repeated string model_names = 1 [(validate.rules).repeated = {items: {string: {min_len: 1}}}];
}

message GetModelFinanceInfoRsp {
  message ModelFinance {
    // 是否自定义模型
    bool is_custom_model = 1;
    // 是否免费
    bool is_free = 2;
  }
  // 模型计费控制信息
  map<string, ModelFinance> ModelFinanceInfo = 1;
}

// 获取模型信息详情
message GetModelInfoReq {
  // 企业ID
  uint64 corp_id = 1 [(validate.rules).uint64.gt = 0];
  // 模型名称
  string model_name = 2 [(validate.rules).string.min_len = 1];
}

message GetModelInfoRsp {
  // 模型名称
  string model_name = 1;
  // 模型的上下文长度
  string model_length = 2;
  // 是不是自定义模型
  bool is_custom_model = 3;
}

// 获取有效的体验应用信息
message GetValidExperienceAppsReq {
  // 每页数目，整型
  uint32 page_size = 1 [(validate.rules).uint32.gt = 0];
  // 页码，整型
  uint32 page_number = 2 [(validate.rules).uint32.gt = 0];
}

message GetValidExperienceAppsRsp {
  message ExperienceAppInfo {
    // 应用业务ID
    uint64 app_biz_id = 1;
  }
  // 数量
  uint64 total = 1;
  // 体验应用列表
  repeated ExperienceAppInfo list = 2;
}

// 获取模型列表信息
message GetModelListReq {
  // 企业ID
  uint64 corp_id = 1 [(validate.rules).uint64.gt = 0];
  // 应用模式 standard:标准模式, agent: agent模式，single_workflow：单工作流模式
  string pattern = 2;
}

message GetModelListRsp {
  // 模型列表
  repeated ModelInfo list = 1;
}

// 获取应用对话query输入长度限制
message GetAppChatInputNumReq {
  // 应用ID
  uint64 app_biz_id = 1 [(validate.rules).uint64.gt = 0];
  // 模型名称
  string model_name = 2 [(validate.rules).string = {min_len: 1}];
}

message GetAppChatInputNumRsp {
  int32 input_len_limit = 1;
}

// 创建Prompt版本信息
message CreatePromptVersionReq {
  // 模型策略
  string model_type = 1 [(validate.rules).string = {min_len: 1}];
  // 模型名称
  string model_name = 2;
  // 模型版本
  string version = 3 [(validate.rules).string = {min_len: 1}];
  // 模型提示词
  string prompt = 4 [(validate.rules).string = {min_len: 1}];
  // 备注信息
  string remark = 5;
  // 操作人
  string operator = 6;
}

message CreatePromptVersionRsp {}

// 编辑prompt版本信息
message EditPromptVersionReq {
  // 模型策略
  string model_type = 1 [(validate.rules).string = {min_len: 1}];
  // 模型名称
  string model_name = 2;
  // 模型版本
  string version = 3 [(validate.rules).string = {min_len: 1}];
  // 模型提示词
  string prompt = 4 [(validate.rules).string = {min_len: 1}];
  // 备注信息
  string remark = 5;
  // 操作人
  string operator = 6;
}

message EditPromptVersionRsp{}

// UpgradePromptVersionDataType 升级promptversion数据类型枚举
enum UpgradePromptVersionDataType {
  UpgradePromptVersionDataTypeNone = 0;
  AppIdRange = 1;
  AppBizIds = 2;
}

// 升级PromptVersion
message UpgradePromptVersionReq {
  message UpgratePromtVersion {
    // 模型策略
    string model_type = 1 [(validate.rules).string = {min_len: 1}];
    // 指定模型
    string model_name = 2;
    // 升级版本
    string upgrade_version = 3 [(validate.rules).string = {min_len: 1}];
    // 是否升级自定义prompt
    bool is_upgrade_custom = 4;
  }
  // 起始应用ID
  uint64 start_robot_id = 1;
  // 结束应用ID 
  uint64 end_robot_id = 2;
  // 升级信息
  repeated UpgratePromtVersion upgrate_prompt_versions = 3;
  // 应用数据类型 1:按照应用主键范围处理 2：按照指定应用业务ID处理
  UpgradePromptVersionDataType data_type = 4;
  // 指定应用业务IDs
  repeated uint64 app_biz_ids = 5;
}

message UpgradePromptVersionRsp {}

// 查询prompt版本信息
message GetPromptVersionListReq {
  // 模型策略
  string model_type = 1;
  // 指定模型名称
  repeated string model_names = 2;
  // 是否自定义prompt
  repeated uint32 customs = 3;
  // 版本
  string version = 4;
  // 页码
  uint32 page = 5 [(validate.rules).uint32.gt = 0];
  // 分页数量
  uint32 page_size = 6 [(validate.rules).uint32.gt = 0];
}

message GetPromptVersionListRsp {
  message PromptVersion {
    // 模型策略
    string model_type = 1;
    // 模型名称
    string model_name = 2;
    // 模型版本
    string version = 3;
    // 模型提示词
    string prompt = 4;
    // 备注信息
    string remark = 5;
    // 操作人
    string operator = 6;
    // 更新时间
    int64 update_time = 7;
  }
  uint64 total = 1;
  repeated PromptVersion list = 2;
}

// 创建账户并发白名单
message CreateCorpConcurrencyWLReq {
  // uin信息
  string uin = 1 [(validate.rules).string = {min_len: 1}];
  // 模型名称
  string model_name = 2 [(validate.rules).string = {min_len: 1}];
  // 并发数
  uint32 concurrency = 3 [(validate.rules).uint32.gt = 0];
  // 过期时间
  int64 expire_time = 4;
  // 操作人
  string operator = 5;
  // 描述
  string remark = 6;
}

message CreateCorpConcurrencyWLRsp{}

// 修改账户并发白名单
message EditCorpConcurrencyWLReq {
  // uin信息
  string uin = 1 [(validate.rules).string = {min_len: 1}];
  // 模型名称
  string model_name = 2 [(validate.rules).string = {min_len: 1}];
  // 并发数
  uint32 concurrency = 3 [(validate.rules).uint32.gt = 0];
  // 过期时间
  int64 expire_time = 4;
  // 操作人
  string operator = 5;
  // 描述
  string remark = 6;
}

message EditCorpConcurrencyWLRsp{}

// 删除账户并发白名单
message DeleteCorpConcurrencyWLReq {
  // uin信息
  string uin = 1 [(validate.rules).string = {min_len: 1}];
  // 模型名称
  string model_name = 2 [(validate.rules).string = {min_len: 1}];
  // 操作人
  string operator = 3;
}

message DeleteCorpConcurrencyWLRsp {}

// 查询账户并发白名单
message GetCorpConcurrencyWLListReq {
  // uin信息
  string uin = 1;
  // 页码
  uint32 page = 2 [(validate.rules).uint32.gt = 0];
  // 分页数量
  uint32 page_size = 3 [(validate.rules).uint32.gt = 0];
  // 描述
  string remark = 4;
}

message GetCorpConcurrencyWLListRsp {
  message ConcurrencyWL {
    // uin信息
    string uin = 1 [(validate.rules).string = {min_len: 1}];
    // 模型名称
    string model_name = 2 [(validate.rules).string = {min_len: 1}];
    // 并发数
    uint32 concurrency = 3 [(validate.rules).uint32.gt = 0];
    // 操作人
    string operator = 4;
    // 更新时间
    int64 update_time = 5;
    // 过期时间
    int64 expire_time = 6;
    // 描述
    string remark = 7;
  }
  uint64 total = 1;
  repeated ConcurrencyWL list = 2;
}

// 应用升级embedding开始
message StartEmbeddingUpgradeAppReq {
  // 应用ID
  uint64 app_biz_id = 1 [(validate.rules).uint64.gt = 0];
  // from embedding 版本
  uint64 from_embedding_version = 2;
  // to embedding version
  uint64 to_embedding_version = 3;
}

message StartEmbeddingUpgradeAppRsp {}

// 应用升级embedding结束
message FinishEmbeddingUpgradeAppReq {
  // 应用ID
  uint64 app_biz_id = 1 [(validate.rules).uint64.gt = 0];
  // from embedding 版本
  uint64 from_embedding_version = 2;
  // to embedding version
  uint64 to_embedding_version = 3;
}

message FinishEmbeddingUpgradeAppRsp {}

// 通过应用业务ID获取应用信息，基于缓存读取
message GetAppsByBizIDsReq {
  // 应用IDs
  repeated uint64 app_biz_ids = 1 [(validate.rules).repeated .min_items = 1];
  // 场景
  uint32 scenes = 2 [(validate.rules).uint32 = {in: [1, 2]}];
  // 应用模式 standard:标准模式, agent: agent模式，single_workflow：单工作流模式
  string pattern = 3;
}

message GetAppsByBizIDsRsp {
  message AppInfo {
    // 应用业务ID
    uint64 app_biz_id = 1;
    // 应用名称
    string name = 2;
    // 机器人头像
    string avatar = 3 [(validate.rules).string.min_len = 1];
    // 应用类型；knowledge_qa-知识问答管理；summary-知识摘要；classify-知识标签提取
    string app_type = 4;
    // 应用主键ID
    uint64 app_id = 5;
    // 最后修改人
    string operator = 6;
    // 应用模式 standard:标准模式, agent: agent模式，single_workflow：单工作流模式
    string pattern = 7;
  }
  repeated AppInfo apps = 1;
}

// 通过员工业务ID获取员工信息，基于缓存读取
message GetCorpStaffsByBizIDsReq {
  // 员工业务ID
  repeated uint64 staff_biz_ids = 1;
}

message GetCorpStaffsByBizIDsRsp {
  message Staff {
    uint64 staff_id = 1;
    uint64 staff_biz_id = 2;
    string nick_name = 3;
    string avatar = 4;
  }
  repeated Staff staffs = 1;
}

// 复制应用配置
message CopyAppConfigReq {
  // 来源应用业务ID
  uint64 src_app_biz_id = 1;
  // 目标应用业务ID
  uint64 dest_app_biz_id = 2;
}

message CopyAppConfigRsp{}

// 更新账户自定义模型白名单
message EditCorpCustomModelWLReq {
  // uin信息
  string uin = 1 [(validate.rules).string = {min_len: 1}];
  // 子uin信息
  string sub_account_uin = 2;
  // 描述信息
  string remark = 3;
  // 过期时间
  int64 expire_time = 4;
  // 操作人
  string operator = 5;
}

message EditCorpCustomModelWLRsp {}

// 删除账户自定义模型白名单
message DeleteCorpCustomModelWLReq {
  // uin信息
  string uin = 1 [(validate.rules).string = {min_len: 1}];
  // 操作人
  string operator = 2;
}

message DeleteCorpCustomModelWLRsp {}


// 获取账户自定义模型白名单列表
message GetCorpCustomModelWLListReq {
  // uin信息
  string uin = 1;
  // 页码
  uint32 page = 2 [(validate.rules).uint32.gt = 0];
  // 分页数量
  uint32 page_size = 3 [(validate.rules).uint32.gt = 0];
  // 描述
  string remark = 4;
}

message GetCorpCustomModelWLListRsp {
  message CustomModelWL {
    // uin信息
    string uin = 1 [(validate.rules).string = {min_len: 1}];
    // 子uin信息
    string sub_account_uin = 2;
    // 描述信息
    string remark = 3;
    // 过期时间
    int64 expire_time = 4;
    // 操作人
    string operator = 5;
    // 更新时间
    int64 update_time = 6;
  }
  uint64 total = 1;
  repeated CustomModelWL list = 2;
}

// 获取账户模型的QPM、TPM值
message GetCorpModelQpmTpmLimitReq {
  // 账户UIN
  string uin = 1;
  // 模型名称
  string model_name = 2;
}

message GetCorpModelQpmTpmLimitRsp {
  // qpm
  uint64 qpm = 1;
  // tpm
  uint64 tpm = 2;
}

// 获取账户模型的默认QPM、TPM值
message GetDefaultQpmTpmLimitReq {}

message GetDefaultQpmTpmLimitRsp {
  // 默认QPM配置
  uint64 qpm = 1;
  // 默认TPM配置
  uint64 tpm = 2;
  // ds模型默认QPM配置
  uint64 ds_qpm = 3;
  // ds 模型默认TPM配置
  uint64 ds_tpm = 4;
}

// 编辑账户模型QPM、TPM配置
message EditCorpQpmTpmLimitReq {
  // uin信息
  string uin = 1 [(validate.rules).string = {min_len: 1}];
  // 模型名称
  string model_name = 2 [(validate.rules).string = {min_len: 1}];
  // qpm
  uint64 qpm = 3 [(validate.rules).uint64.gt = 0];
  // tpm
  uint64 tpm = 4 [(validate.rules).uint64.gt = 0];
  // 备注
  string remark = 5;
  // 操作人
  string opeartor = 6;
  // 过期时间 秒级时间戳
  int64 expire_time = 7;
}

message EditCorpQpmTpmLimitRsp {}

// 删除账户模型QPM、TPM配置
message DeleteCorpQpmTpmLimitReq {
  // uin信息
  string uin = 1 [(validate.rules).string = {min_len: 1}];
  // 模型名称
  string model_name = 2 [(validate.rules).string = {min_len: 1}];
  // 操作人
  string operator = 3;
}

message DeleteCorpQpmTpmLimitRsp {}

// 获取账户模型QPM、TPM配置
message GetCorpQpmTpmLimitListReq {
  // uin信息
  string uin = 1;
  // 页码
  uint32 page = 2 [(validate.rules).uint32.gt = 0];
  // 分页数量
  uint32 page_size = 3 [(validate.rules).uint32.gt = 0];
  // 描述
  string remark = 4;
}

message GetCorpQpmTpmLimitListRsp {
  message CorpQpmTpmLimit {
    // uin信息
    string uin = 1 [(validate.rules).string = {min_len: 1}];
    // 模型名称
    string model_name = 2 [(validate.rules).string = {min_len: 1}];
    // qpm
    uint64 qpm = 3 [(validate.rules).uint64.gt = 0];
    // tpm
    uint64 tpm = 4 [(validate.rules).uint64.gt = 0];
    // 备注
    string remark = 5;
    // 操作人
    string opeartor = 6;
    // 过期时间 秒级时间戳
    int64 expire_time = 7;
    // 更新时间 秒级时间戳
    int64 update_time = 8;
  }
  uint64 total = 1;
  repeated CorpQpmTpmLimit list = 2;
}

// 创建共享知识库应用
message CreateShareKnowledgeBaseAppReq {
  // 账户UIN
  string uin = 1 [(validate.rules).string = {min_len: 1}];
  // 机器人昵称
  string name = 2 [(validate.rules).string = {min_len: 1, max_len: 30}];
  // 机器人头像
  string avatar = 3 [(validate.rules).string.min_len = 1];
}

message CreateShareKnowledgeBaseAppRsp {
  uint64 app_biz_id = 1;
}

// 删除共享知识库应用
message DeleteShareKnowledgeBaseAppReq {
  // 账户UIN
  string uin = 1 [(validate.rules).string = {min_len: 1}];
  // 共享知识库关联的应用ID
  uint64 app_biz_id = 2;
}

message DeleteShareKnowledgeBaseAppRsp {}