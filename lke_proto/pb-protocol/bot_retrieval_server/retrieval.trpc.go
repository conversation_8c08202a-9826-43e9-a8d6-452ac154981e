// Code generated by trpc-go/trpc-go-cmdline v2.8.37. DO NOT EDIT.
// source: retrieval.proto

package bot_retrieval_server

import (
	"context"
	"errors"
	"fmt"

	_ "git.code.oa.com/trpc-go/trpc-go"
	"git.code.oa.com/trpc-go/trpc-go/client"
	"git.code.oa.com/trpc-go/trpc-go/codec"
	_ "git.code.oa.com/trpc-go/trpc-go/http"
	"git.code.oa.com/trpc-go/trpc-go/server"
)

// START ======================================= Server Service Definition ======================================= START

// RetrievalService defines service.
type RetrievalService interface {
	// Search 查找
	Search(ctx context.Context, req *SearchReq) (*SearchRsp, error)
	// Publish 发布版本
	Publish(ctx context.Context, req *PublishReq) (*PublishRsp, error)
	// Similarity 计算相似度
	Similarity(ctx context.Context, req *SimilarityReq) (*SimilarityRsp, error)
	// IndexRebuild 索引库重建
	IndexRebuild(ctx context.Context, req *IndexRebuildReq) (*IndexRebuildRsp, error)
	// ContinueTerminatedTask 继续已经终止的任务
	ContinueTerminatedTask(ctx context.Context, req *ContinueTerminatedTaskReq) (*ContinueTerminatedTaskRsp, error)
	// UpgradeEmbedding 线上库embedding升级任务
	UpgradeEmbedding(ctx context.Context, req *UpgradeEmbeddingReq) (*UpgradeEmbeddingRsp, error)
	// CheckVersion vector发布后校验版本号是否有效
	CheckVersion(ctx context.Context, req *CheckVersionReq) (*CheckVersionRsp, error)
	// BatchDeleteAllKnowledgeProd 批量删除发布库的所有知识（包括QA/文档/混合检索/text2sql等）
	BatchDeleteAllKnowledgeProd(ctx context.Context, req *BatchDeleteAllKnowledgeProdReq) (*BatchDeleteAllKnowledgeProdRsp, error)
	// ClearAppVectorResource 按照引用维度删除数据[es,vector,db]
	ClearAppVectorResource(ctx context.Context, req *ClearAppVectorResourceReq) (*ClearAppVectorResourceRsp, error)
}

func RetrievalService_Search_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &SearchReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(RetrievalService).Search(ctx, reqbody.(*SearchReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func RetrievalService_Publish_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &PublishReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(RetrievalService).Publish(ctx, reqbody.(*PublishReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func RetrievalService_Similarity_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &SimilarityReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(RetrievalService).Similarity(ctx, reqbody.(*SimilarityReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func RetrievalService_IndexRebuild_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &IndexRebuildReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(RetrievalService).IndexRebuild(ctx, reqbody.(*IndexRebuildReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func RetrievalService_ContinueTerminatedTask_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &ContinueTerminatedTaskReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(RetrievalService).ContinueTerminatedTask(ctx, reqbody.(*ContinueTerminatedTaskReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func RetrievalService_UpgradeEmbedding_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &UpgradeEmbeddingReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(RetrievalService).UpgradeEmbedding(ctx, reqbody.(*UpgradeEmbeddingReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func RetrievalService_CheckVersion_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &CheckVersionReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(RetrievalService).CheckVersion(ctx, reqbody.(*CheckVersionReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func RetrievalService_BatchDeleteAllKnowledgeProd_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &BatchDeleteAllKnowledgeProdReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(RetrievalService).BatchDeleteAllKnowledgeProd(ctx, reqbody.(*BatchDeleteAllKnowledgeProdReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func RetrievalService_ClearAppVectorResource_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &ClearAppVectorResourceReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(RetrievalService).ClearAppVectorResource(ctx, reqbody.(*ClearAppVectorResourceReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

// RetrievalServer_ServiceDesc descriptor for server.RegisterService.
var RetrievalServer_ServiceDesc = server.ServiceDesc{
	ServiceName: "trpc.KEP.bot_retrieval_server.Retrieval",
	HandlerType: ((*RetrievalService)(nil)),
	Methods: []server.Method{
		{
			Name: "/trpc.KEP.bot_retrieval_server.Retrieval/Search",
			Func: RetrievalService_Search_Handler,
		},
		{
			Name: "/trpc.KEP.bot_retrieval_server.Retrieval/Publish",
			Func: RetrievalService_Publish_Handler,
		},
		{
			Name: "/trpc.KEP.bot_retrieval_server.Retrieval/Similarity",
			Func: RetrievalService_Similarity_Handler,
		},
		{
			Name: "/trpc.KEP.bot_retrieval_server.Retrieval/IndexRebuild",
			Func: RetrievalService_IndexRebuild_Handler,
		},
		{
			Name: "/trpc.KEP.bot_retrieval_server.Retrieval/ContinueTerminatedTask",
			Func: RetrievalService_ContinueTerminatedTask_Handler,
		},
		{
			Name: "/trpc.KEP.bot_retrieval_server.Retrieval/UpgradeEmbedding",
			Func: RetrievalService_UpgradeEmbedding_Handler,
		},
		{
			Name: "/trpc.KEP.bot_retrieval_server.Retrieval/CheckVersion",
			Func: RetrievalService_CheckVersion_Handler,
		},
		{
			Name: "/trpc.KEP.bot_retrieval_server.Retrieval/BatchDeleteAllKnowledgeProd",
			Func: RetrievalService_BatchDeleteAllKnowledgeProd_Handler,
		},
		{
			Name: "/trpc.KEP.bot_retrieval_server.Retrieval/ClearAppVectorResource",
			Func: RetrievalService_ClearAppVectorResource_Handler,
		},
	},
}

// RegisterRetrievalService registers service.
func RegisterRetrievalService(s server.Service, svr RetrievalService) {
	if err := s.Register(&RetrievalServer_ServiceDesc, svr); err != nil {
		panic(fmt.Sprintf("Retrieval register error:%v", err))
	}
}

// DirectIndexService defines service.
type DirectIndexService interface {
	// CreateIndex 增加检索库
	CreateIndex(ctx context.Context, req *CreateIndexReq) (*CreateIndexRsp, error)
	// DeleteIndex 删除检索库
	DeleteIndex(ctx context.Context, req *DeleteIndexReq) (*DeleteIndexRsp, error)
	// AddVector 增加特征
	AddVector(ctx context.Context, req *AddVectorReq) (*AddVectorRsp, error)
	// DeleteVector 删除特征
	DeleteVector(ctx context.Context, req *DeleteVectorReq) (*DeleteVectorRsp, error)
	// UpdateVector 修改特征
	UpdateVector(ctx context.Context, req *UpdateVectorReq) (*UpdateVectorRsp, error)
	// SearchVector 检索特征
	SearchVector(ctx context.Context, req *SearchVectorReq) (*SearchVectorRsp, error)
	// SearchMultiKnowledge 检索多个知识库
	SearchMultiKnowledge(ctx context.Context, req *SearchMultiKnowledgeReq) (*SearchVectorRsp, error)
	// DirectCreateIndex 增加检索库(无用户关系绑定)
	DirectCreateIndex(ctx context.Context, req *DirectCreateIndexReq) (*DirectCreateIndexRsp, error)
	// DirectDeleteIndex 删除检索库(无用户关系绑定)
	DirectDeleteIndex(ctx context.Context, req *DirectDeleteIndexReq) (*DirectDeleteIndexRsp, error)
	// DirectAddVector 增加特征(无用户关系绑定)
	DirectAddVector(ctx context.Context, req *DirectAddVectorReq) (*DirectAddVectorRsp, error)
	// DirectDeleteVector 删除特征(无用户关系绑定)
	DirectDeleteVector(ctx context.Context, req *DirectDeleteVectorReq) (*DirectDeleteVectorRsp, error)
	// DirectUpdateVector 修改特征(无用户关系绑定)
	DirectUpdateVector(ctx context.Context, req *DirectUpdateVectorReq) (*DirectUpdateVectorRsp, error)
	// DirectSearchVector 检索特征(无用户关系绑定)
	DirectSearchVector(ctx context.Context, req *DirectSearchVectorReq) (*DirectSearchVectorRsp, error)
	// AddBigDataElastic 新建或更新BigData数据到ES
	AddBigDataElastic(ctx context.Context, req *AddBigDataElasticReq) (*AddBigDataElasticRsp, error)
	// DeleteBigDataElastic 从ES里删除BigData
	DeleteBigDataElastic(ctx context.Context, req *DeleteBigDataElasticReq) (*DeleteBigDataElasticRsp, error)
	// RecoverBigDataElastic 从ES恢复离线知识库的BigData
	RecoverBigDataElastic(ctx context.Context, req *RecoverBigDataElasticReq) (*RecoverBigDataElasticRsp, error)
	// BatchGetBigDataESByRobotBigDataID 根据robot_id, big_data_id 从bigDataEs中批量查找数据
	BatchGetBigDataESByRobotBigDataID(ctx context.Context, req *BatchGetBigDataESByRobotBigDataIDReq) (*BatchGetBigDataESByRobotBigDataIDResp, error)
	// AddKnowledge 增加知识
	AddKnowledge(ctx context.Context, req *AddKnowledgeReq) (*AddKnowledgeRsp, error)
	// BatchAddKnowledge 批量增加知识
	BatchAddKnowledge(ctx context.Context, req *BatchAddKnowledgeReq) (*BatchAddKnowledgeRsp, error)
	// DeleteKnowledge 删除知识
	DeleteKnowledge(ctx context.Context, req *DeleteKnowledgeReq) (*DeleteKnowledgeRsp, error)
	// BatchDeleteKnowledge 批量删除知识
	BatchDeleteKnowledge(ctx context.Context, req *BatchDeleteKnowledgeReq) (*BatchDeleteKnowledgeRsp, error)
	// UpdateKnowledge 修改知识
	UpdateKnowledge(ctx context.Context, req *UpdateKnowledgeReq) (*UpdateKnowledgeRsp, error)
	// AddText2SQL 批量增加或修改text2sql
	AddText2SQL(ctx context.Context, req *AddText2SQLReq) (*AddText2SQLRsp, error)
	// DeleteText2SQL 批量删除text2sql
	DeleteText2SQL(ctx context.Context, req *DeleteText2SQLReq) (*DeleteText2SQLRsp, error)
	// AddRealTimeKnowledge 实时文档添加切片
	AddRealTimeKnowledge(ctx context.Context, req *AddRealTimeKnowledgeReq) (*AddRealTimeKnowledgeRsp, error)
	// DeleteRealTimeKnowledge 实时文档删除切片
	DeleteRealTimeKnowledge(ctx context.Context, req *DeleteRealTimeKnowledgeReq) (*DeleteRealTimeKnowledgeRsp, error)
	// RetrievalRealTime 实时文档检索接口
	RetrievalRealTime(ctx context.Context, req *RetrievalRealTimeReq) (*RetrievalRealTimeRsp, error)
	// AddDBText2SQL 批量增加或修改外部数据库的Text2SQL信息
	AddDBText2SQL(ctx context.Context, req *AddDBText2SQLReq) (*AddDBText2SQLRsp, error)
	// DeleteDBText2SQL 批量删除外部数据库的Text2SQL信息
	DeleteDBText2SQL(ctx context.Context, req *DeleteDBText2SQLReq) (*DeleteDBText2SQLRsp, error)
	// AddAndUpdateESData 向ES中添加或者更新用于普通关键字检索的数据
	AddAndUpdateESData(ctx context.Context, req *AddESDataReq) (*AddESDataReqRsp, error)
	// DeleteESData 从ES中删除用于普通关键字检索的数据
	DeleteESData(ctx context.Context, req *DeleteESDataReq) (*DeleteESDataRsp, error)
	// SearchES 在ES中做关键字搜索
	SearchES(ctx context.Context, req *SearchESReq) (*SearchESRsp, error)
}

func DirectIndexService_CreateIndex_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &CreateIndexReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(DirectIndexService).CreateIndex(ctx, reqbody.(*CreateIndexReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func DirectIndexService_DeleteIndex_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &DeleteIndexReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(DirectIndexService).DeleteIndex(ctx, reqbody.(*DeleteIndexReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func DirectIndexService_AddVector_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &AddVectorReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(DirectIndexService).AddVector(ctx, reqbody.(*AddVectorReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func DirectIndexService_DeleteVector_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &DeleteVectorReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(DirectIndexService).DeleteVector(ctx, reqbody.(*DeleteVectorReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func DirectIndexService_UpdateVector_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &UpdateVectorReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(DirectIndexService).UpdateVector(ctx, reqbody.(*UpdateVectorReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func DirectIndexService_SearchVector_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &SearchVectorReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(DirectIndexService).SearchVector(ctx, reqbody.(*SearchVectorReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func DirectIndexService_SearchMultiKnowledge_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &SearchMultiKnowledgeReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(DirectIndexService).SearchMultiKnowledge(ctx, reqbody.(*SearchMultiKnowledgeReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func DirectIndexService_DirectCreateIndex_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &DirectCreateIndexReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(DirectIndexService).DirectCreateIndex(ctx, reqbody.(*DirectCreateIndexReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func DirectIndexService_DirectDeleteIndex_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &DirectDeleteIndexReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(DirectIndexService).DirectDeleteIndex(ctx, reqbody.(*DirectDeleteIndexReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func DirectIndexService_DirectAddVector_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &DirectAddVectorReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(DirectIndexService).DirectAddVector(ctx, reqbody.(*DirectAddVectorReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func DirectIndexService_DirectDeleteVector_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &DirectDeleteVectorReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(DirectIndexService).DirectDeleteVector(ctx, reqbody.(*DirectDeleteVectorReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func DirectIndexService_DirectUpdateVector_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &DirectUpdateVectorReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(DirectIndexService).DirectUpdateVector(ctx, reqbody.(*DirectUpdateVectorReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func DirectIndexService_DirectSearchVector_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &DirectSearchVectorReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(DirectIndexService).DirectSearchVector(ctx, reqbody.(*DirectSearchVectorReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func DirectIndexService_AddBigDataElastic_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &AddBigDataElasticReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(DirectIndexService).AddBigDataElastic(ctx, reqbody.(*AddBigDataElasticReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func DirectIndexService_DeleteBigDataElastic_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &DeleteBigDataElasticReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(DirectIndexService).DeleteBigDataElastic(ctx, reqbody.(*DeleteBigDataElasticReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func DirectIndexService_RecoverBigDataElastic_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &RecoverBigDataElasticReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(DirectIndexService).RecoverBigDataElastic(ctx, reqbody.(*RecoverBigDataElasticReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func DirectIndexService_BatchGetBigDataESByRobotBigDataID_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &BatchGetBigDataESByRobotBigDataIDReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(DirectIndexService).BatchGetBigDataESByRobotBigDataID(ctx, reqbody.(*BatchGetBigDataESByRobotBigDataIDReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func DirectIndexService_AddKnowledge_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &AddKnowledgeReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(DirectIndexService).AddKnowledge(ctx, reqbody.(*AddKnowledgeReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func DirectIndexService_BatchAddKnowledge_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &BatchAddKnowledgeReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(DirectIndexService).BatchAddKnowledge(ctx, reqbody.(*BatchAddKnowledgeReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func DirectIndexService_DeleteKnowledge_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &DeleteKnowledgeReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(DirectIndexService).DeleteKnowledge(ctx, reqbody.(*DeleteKnowledgeReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func DirectIndexService_BatchDeleteKnowledge_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &BatchDeleteKnowledgeReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(DirectIndexService).BatchDeleteKnowledge(ctx, reqbody.(*BatchDeleteKnowledgeReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func DirectIndexService_UpdateKnowledge_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &UpdateKnowledgeReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(DirectIndexService).UpdateKnowledge(ctx, reqbody.(*UpdateKnowledgeReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func DirectIndexService_AddText2SQL_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &AddText2SQLReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(DirectIndexService).AddText2SQL(ctx, reqbody.(*AddText2SQLReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func DirectIndexService_DeleteText2SQL_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &DeleteText2SQLReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(DirectIndexService).DeleteText2SQL(ctx, reqbody.(*DeleteText2SQLReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func DirectIndexService_AddRealTimeKnowledge_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &AddRealTimeKnowledgeReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(DirectIndexService).AddRealTimeKnowledge(ctx, reqbody.(*AddRealTimeKnowledgeReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func DirectIndexService_DeleteRealTimeKnowledge_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &DeleteRealTimeKnowledgeReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(DirectIndexService).DeleteRealTimeKnowledge(ctx, reqbody.(*DeleteRealTimeKnowledgeReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func DirectIndexService_RetrievalRealTime_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &RetrievalRealTimeReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(DirectIndexService).RetrievalRealTime(ctx, reqbody.(*RetrievalRealTimeReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func DirectIndexService_AddDBText2SQL_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &AddDBText2SQLReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(DirectIndexService).AddDBText2SQL(ctx, reqbody.(*AddDBText2SQLReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func DirectIndexService_DeleteDBText2SQL_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &DeleteDBText2SQLReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(DirectIndexService).DeleteDBText2SQL(ctx, reqbody.(*DeleteDBText2SQLReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func DirectIndexService_AddAndUpdateESData_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &AddESDataReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(DirectIndexService).AddAndUpdateESData(ctx, reqbody.(*AddESDataReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func DirectIndexService_DeleteESData_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &DeleteESDataReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(DirectIndexService).DeleteESData(ctx, reqbody.(*DeleteESDataReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func DirectIndexService_SearchES_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &SearchESReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(DirectIndexService).SearchES(ctx, reqbody.(*SearchESReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

// DirectIndexServer_ServiceDesc descriptor for server.RegisterService.
var DirectIndexServer_ServiceDesc = server.ServiceDesc{
	ServiceName: "trpc.KEP.bot_retrieval_server.DirectIndex",
	HandlerType: ((*DirectIndexService)(nil)),
	Methods: []server.Method{
		{
			Name: "/trpc.KEP.bot_retrieval_server.DirectIndex/CreateIndex",
			Func: DirectIndexService_CreateIndex_Handler,
		},
		{
			Name: "/trpc.KEP.bot_retrieval_server.DirectIndex/DeleteIndex",
			Func: DirectIndexService_DeleteIndex_Handler,
		},
		{
			Name: "/trpc.KEP.bot_retrieval_server.DirectIndex/AddVector",
			Func: DirectIndexService_AddVector_Handler,
		},
		{
			Name: "/trpc.KEP.bot_retrieval_server.DirectIndex/DeleteVector",
			Func: DirectIndexService_DeleteVector_Handler,
		},
		{
			Name: "/trpc.KEP.bot_retrieval_server.DirectIndex/UpdateVector",
			Func: DirectIndexService_UpdateVector_Handler,
		},
		{
			Name: "/trpc.KEP.bot_retrieval_server.DirectIndex/SearchVector",
			Func: DirectIndexService_SearchVector_Handler,
		},
		{
			Name: "/trpc.KEP.bot_retrieval_server.DirectIndex/SearchMultiKnowledge",
			Func: DirectIndexService_SearchMultiKnowledge_Handler,
		},
		{
			Name: "/trpc.KEP.bot_retrieval_server.DirectIndex/DirectCreateIndex",
			Func: DirectIndexService_DirectCreateIndex_Handler,
		},
		{
			Name: "/trpc.KEP.bot_retrieval_server.DirectIndex/DirectDeleteIndex",
			Func: DirectIndexService_DirectDeleteIndex_Handler,
		},
		{
			Name: "/trpc.KEP.bot_retrieval_server.DirectIndex/DirectAddVector",
			Func: DirectIndexService_DirectAddVector_Handler,
		},
		{
			Name: "/trpc.KEP.bot_retrieval_server.DirectIndex/DirectDeleteVector",
			Func: DirectIndexService_DirectDeleteVector_Handler,
		},
		{
			Name: "/trpc.KEP.bot_retrieval_server.DirectIndex/DirectUpdateVector",
			Func: DirectIndexService_DirectUpdateVector_Handler,
		},
		{
			Name: "/trpc.KEP.bot_retrieval_server.DirectIndex/DirectSearchVector",
			Func: DirectIndexService_DirectSearchVector_Handler,
		},
		{
			Name: "/trpc.KEP.bot_retrieval_server.DirectIndex/AddBigDataElastic",
			Func: DirectIndexService_AddBigDataElastic_Handler,
		},
		{
			Name: "/trpc.KEP.bot_retrieval_server.DirectIndex/DeleteBigDataElastic",
			Func: DirectIndexService_DeleteBigDataElastic_Handler,
		},
		{
			Name: "/trpc.KEP.bot_retrieval_server.DirectIndex/RecoverBigDataElastic",
			Func: DirectIndexService_RecoverBigDataElastic_Handler,
		},
		{
			Name: "/trpc.KEP.bot_retrieval_server.DirectIndex/BatchGetBigDataESByRobotBigDataID",
			Func: DirectIndexService_BatchGetBigDataESByRobotBigDataID_Handler,
		},
		{
			Name: "/trpc.KEP.bot_retrieval_server.DirectIndex/AddKnowledge",
			Func: DirectIndexService_AddKnowledge_Handler,
		},
		{
			Name: "/trpc.KEP.bot_retrieval_server.DirectIndex/BatchAddKnowledge",
			Func: DirectIndexService_BatchAddKnowledge_Handler,
		},
		{
			Name: "/trpc.KEP.bot_retrieval_server.DirectIndex/DeleteKnowledge",
			Func: DirectIndexService_DeleteKnowledge_Handler,
		},
		{
			Name: "/trpc.KEP.bot_retrieval_server.DirectIndex/BatchDeleteKnowledge",
			Func: DirectIndexService_BatchDeleteKnowledge_Handler,
		},
		{
			Name: "/trpc.KEP.bot_retrieval_server.DirectIndex/UpdateKnowledge",
			Func: DirectIndexService_UpdateKnowledge_Handler,
		},
		{
			Name: "/trpc.KEP.bot_retrieval_server.DirectIndex/AddText2SQL",
			Func: DirectIndexService_AddText2SQL_Handler,
		},
		{
			Name: "/trpc.KEP.bot_retrieval_server.DirectIndex/DeleteText2SQL",
			Func: DirectIndexService_DeleteText2SQL_Handler,
		},
		{
			Name: "/trpc.KEP.bot_retrieval_server.DirectIndex/AddRealTimeKnowledge",
			Func: DirectIndexService_AddRealTimeKnowledge_Handler,
		},
		{
			Name: "/trpc.KEP.bot_retrieval_server.DirectIndex/DeleteRealTimeKnowledge",
			Func: DirectIndexService_DeleteRealTimeKnowledge_Handler,
		},
		{
			Name: "/trpc.KEP.bot_retrieval_server.DirectIndex/RetrievalRealTime",
			Func: DirectIndexService_RetrievalRealTime_Handler,
		},
		{
			Name: "/trpc.KEP.bot_retrieval_server.DirectIndex/AddDBText2SQL",
			Func: DirectIndexService_AddDBText2SQL_Handler,
		},
		{
			Name: "/trpc.KEP.bot_retrieval_server.DirectIndex/DeleteDBText2SQL",
			Func: DirectIndexService_DeleteDBText2SQL_Handler,
		},
		{
			Name: "/trpc.KEP.bot_retrieval_server.DirectIndex/AddAndUpdateESData",
			Func: DirectIndexService_AddAndUpdateESData_Handler,
		},
		{
			Name: "/trpc.KEP.bot_retrieval_server.DirectIndex/DeleteESData",
			Func: DirectIndexService_DeleteESData_Handler,
		},
		{
			Name: "/trpc.KEP.bot_retrieval_server.DirectIndex/SearchES",
			Func: DirectIndexService_SearchES_Handler,
		},
	},
}

// RegisterDirectIndexService registers service.
func RegisterDirectIndexService(s server.Service, svr DirectIndexService) {
	if err := s.Register(&DirectIndexServer_ServiceDesc, svr); err != nil {
		panic(fmt.Sprintf("DirectIndex register error:%v", err))
	}
}

// START --------------------------------- Default Unimplemented Server Service --------------------------------- START

type UnimplementedRetrieval struct{}

// Search 查找
func (s *UnimplementedRetrieval) Search(ctx context.Context, req *SearchReq) (*SearchRsp, error) {
	return nil, errors.New("rpc Search of service Retrieval is not implemented")
}

// Publish 发布版本
func (s *UnimplementedRetrieval) Publish(ctx context.Context, req *PublishReq) (*PublishRsp, error) {
	return nil, errors.New("rpc Publish of service Retrieval is not implemented")
}

// Similarity 计算相似度
func (s *UnimplementedRetrieval) Similarity(ctx context.Context, req *SimilarityReq) (*SimilarityRsp, error) {
	return nil, errors.New("rpc Similarity of service Retrieval is not implemented")
}

// IndexRebuild 索引库重建
func (s *UnimplementedRetrieval) IndexRebuild(ctx context.Context, req *IndexRebuildReq) (*IndexRebuildRsp, error) {
	return nil, errors.New("rpc IndexRebuild of service Retrieval is not implemented")
}

// ContinueTerminatedTask 继续已经终止的任务
func (s *UnimplementedRetrieval) ContinueTerminatedTask(ctx context.Context, req *ContinueTerminatedTaskReq) (*ContinueTerminatedTaskRsp, error) {
	return nil, errors.New("rpc ContinueTerminatedTask of service Retrieval is not implemented")
}

// UpgradeEmbedding 线上库embedding升级任务
func (s *UnimplementedRetrieval) UpgradeEmbedding(ctx context.Context, req *UpgradeEmbeddingReq) (*UpgradeEmbeddingRsp, error) {
	return nil, errors.New("rpc UpgradeEmbedding of service Retrieval is not implemented")
}

// CheckVersion vector发布后校验版本号是否有效
func (s *UnimplementedRetrieval) CheckVersion(ctx context.Context, req *CheckVersionReq) (*CheckVersionRsp, error) {
	return nil, errors.New("rpc CheckVersion of service Retrieval is not implemented")
}

// BatchDeleteAllKnowledgeProd 批量删除发布库的所有知识（包括QA/文档/混合检索/text2sql等）
func (s *UnimplementedRetrieval) BatchDeleteAllKnowledgeProd(ctx context.Context, req *BatchDeleteAllKnowledgeProdReq) (*BatchDeleteAllKnowledgeProdRsp, error) {
	return nil, errors.New("rpc BatchDeleteAllKnowledgeProd of service Retrieval is not implemented")
}

// ClearAppVectorResource 按照引用维度删除数据[es,vector,db]
func (s *UnimplementedRetrieval) ClearAppVectorResource(ctx context.Context, req *ClearAppVectorResourceReq) (*ClearAppVectorResourceRsp, error) {
	return nil, errors.New("rpc ClearAppVectorResource of service Retrieval is not implemented")
}

type UnimplementedDirectIndex struct{}

// CreateIndex 增加检索库
func (s *UnimplementedDirectIndex) CreateIndex(ctx context.Context, req *CreateIndexReq) (*CreateIndexRsp, error) {
	return nil, errors.New("rpc CreateIndex of service DirectIndex is not implemented")
}

// DeleteIndex 删除检索库
func (s *UnimplementedDirectIndex) DeleteIndex(ctx context.Context, req *DeleteIndexReq) (*DeleteIndexRsp, error) {
	return nil, errors.New("rpc DeleteIndex of service DirectIndex is not implemented")
}

// AddVector 增加特征
func (s *UnimplementedDirectIndex) AddVector(ctx context.Context, req *AddVectorReq) (*AddVectorRsp, error) {
	return nil, errors.New("rpc AddVector of service DirectIndex is not implemented")
}

// DeleteVector 删除特征
func (s *UnimplementedDirectIndex) DeleteVector(ctx context.Context, req *DeleteVectorReq) (*DeleteVectorRsp, error) {
	return nil, errors.New("rpc DeleteVector of service DirectIndex is not implemented")
}

// UpdateVector 修改特征
func (s *UnimplementedDirectIndex) UpdateVector(ctx context.Context, req *UpdateVectorReq) (*UpdateVectorRsp, error) {
	return nil, errors.New("rpc UpdateVector of service DirectIndex is not implemented")
}

// SearchVector 检索特征
func (s *UnimplementedDirectIndex) SearchVector(ctx context.Context, req *SearchVectorReq) (*SearchVectorRsp, error) {
	return nil, errors.New("rpc SearchVector of service DirectIndex is not implemented")
}

// SearchMultiKnowledge 检索多个知识库
func (s *UnimplementedDirectIndex) SearchMultiKnowledge(ctx context.Context, req *SearchMultiKnowledgeReq) (*SearchVectorRsp, error) {
	return nil, errors.New("rpc SearchMultiKnowledge of service DirectIndex is not implemented")
}

// DirectCreateIndex 增加检索库(无用户关系绑定)
func (s *UnimplementedDirectIndex) DirectCreateIndex(ctx context.Context, req *DirectCreateIndexReq) (*DirectCreateIndexRsp, error) {
	return nil, errors.New("rpc DirectCreateIndex of service DirectIndex is not implemented")
}

// DirectDeleteIndex 删除检索库(无用户关系绑定)
func (s *UnimplementedDirectIndex) DirectDeleteIndex(ctx context.Context, req *DirectDeleteIndexReq) (*DirectDeleteIndexRsp, error) {
	return nil, errors.New("rpc DirectDeleteIndex of service DirectIndex is not implemented")
}

// DirectAddVector 增加特征(无用户关系绑定)
func (s *UnimplementedDirectIndex) DirectAddVector(ctx context.Context, req *DirectAddVectorReq) (*DirectAddVectorRsp, error) {
	return nil, errors.New("rpc DirectAddVector of service DirectIndex is not implemented")
}

// DirectDeleteVector 删除特征(无用户关系绑定)
func (s *UnimplementedDirectIndex) DirectDeleteVector(ctx context.Context, req *DirectDeleteVectorReq) (*DirectDeleteVectorRsp, error) {
	return nil, errors.New("rpc DirectDeleteVector of service DirectIndex is not implemented")
}

// DirectUpdateVector 修改特征(无用户关系绑定)
func (s *UnimplementedDirectIndex) DirectUpdateVector(ctx context.Context, req *DirectUpdateVectorReq) (*DirectUpdateVectorRsp, error) {
	return nil, errors.New("rpc DirectUpdateVector of service DirectIndex is not implemented")
}

// DirectSearchVector 检索特征(无用户关系绑定)
func (s *UnimplementedDirectIndex) DirectSearchVector(ctx context.Context, req *DirectSearchVectorReq) (*DirectSearchVectorRsp, error) {
	return nil, errors.New("rpc DirectSearchVector of service DirectIndex is not implemented")
}

// AddBigDataElastic 新建或更新BigData数据到ES
func (s *UnimplementedDirectIndex) AddBigDataElastic(ctx context.Context, req *AddBigDataElasticReq) (*AddBigDataElasticRsp, error) {
	return nil, errors.New("rpc AddBigDataElastic of service DirectIndex is not implemented")
}

// DeleteBigDataElastic 从ES里删除BigData
func (s *UnimplementedDirectIndex) DeleteBigDataElastic(ctx context.Context, req *DeleteBigDataElasticReq) (*DeleteBigDataElasticRsp, error) {
	return nil, errors.New("rpc DeleteBigDataElastic of service DirectIndex is not implemented")
}

// RecoverBigDataElastic 从ES恢复离线知识库的BigData
func (s *UnimplementedDirectIndex) RecoverBigDataElastic(ctx context.Context, req *RecoverBigDataElasticReq) (*RecoverBigDataElasticRsp, error) {
	return nil, errors.New("rpc RecoverBigDataElastic of service DirectIndex is not implemented")
}

// BatchGetBigDataESByRobotBigDataID 根据robot_id, big_data_id 从bigDataEs中批量查找数据
func (s *UnimplementedDirectIndex) BatchGetBigDataESByRobotBigDataID(ctx context.Context, req *BatchGetBigDataESByRobotBigDataIDReq) (*BatchGetBigDataESByRobotBigDataIDResp, error) {
	return nil, errors.New("rpc BatchGetBigDataESByRobotBigDataID of service DirectIndex is not implemented")
}

// AddKnowledge 增加知识
func (s *UnimplementedDirectIndex) AddKnowledge(ctx context.Context, req *AddKnowledgeReq) (*AddKnowledgeRsp, error) {
	return nil, errors.New("rpc AddKnowledge of service DirectIndex is not implemented")
}

// BatchAddKnowledge 批量增加知识
func (s *UnimplementedDirectIndex) BatchAddKnowledge(ctx context.Context, req *BatchAddKnowledgeReq) (*BatchAddKnowledgeRsp, error) {
	return nil, errors.New("rpc BatchAddKnowledge of service DirectIndex is not implemented")
}

// DeleteKnowledge 删除知识
func (s *UnimplementedDirectIndex) DeleteKnowledge(ctx context.Context, req *DeleteKnowledgeReq) (*DeleteKnowledgeRsp, error) {
	return nil, errors.New("rpc DeleteKnowledge of service DirectIndex is not implemented")
}

// BatchDeleteKnowledge 批量删除知识
func (s *UnimplementedDirectIndex) BatchDeleteKnowledge(ctx context.Context, req *BatchDeleteKnowledgeReq) (*BatchDeleteKnowledgeRsp, error) {
	return nil, errors.New("rpc BatchDeleteKnowledge of service DirectIndex is not implemented")
}

// UpdateKnowledge 修改知识
func (s *UnimplementedDirectIndex) UpdateKnowledge(ctx context.Context, req *UpdateKnowledgeReq) (*UpdateKnowledgeRsp, error) {
	return nil, errors.New("rpc UpdateKnowledge of service DirectIndex is not implemented")
}

// AddText2SQL 批量增加或修改text2sql
func (s *UnimplementedDirectIndex) AddText2SQL(ctx context.Context, req *AddText2SQLReq) (*AddText2SQLRsp, error) {
	return nil, errors.New("rpc AddText2SQL of service DirectIndex is not implemented")
}

// DeleteText2SQL 批量删除text2sql
func (s *UnimplementedDirectIndex) DeleteText2SQL(ctx context.Context, req *DeleteText2SQLReq) (*DeleteText2SQLRsp, error) {
	return nil, errors.New("rpc DeleteText2SQL of service DirectIndex is not implemented")
}

// AddRealTimeKnowledge 实时文档添加切片
func (s *UnimplementedDirectIndex) AddRealTimeKnowledge(ctx context.Context, req *AddRealTimeKnowledgeReq) (*AddRealTimeKnowledgeRsp, error) {
	return nil, errors.New("rpc AddRealTimeKnowledge of service DirectIndex is not implemented")
}

// DeleteRealTimeKnowledge 实时文档删除切片
func (s *UnimplementedDirectIndex) DeleteRealTimeKnowledge(ctx context.Context, req *DeleteRealTimeKnowledgeReq) (*DeleteRealTimeKnowledgeRsp, error) {
	return nil, errors.New("rpc DeleteRealTimeKnowledge of service DirectIndex is not implemented")
}

// RetrievalRealTime 实时文档检索接口
func (s *UnimplementedDirectIndex) RetrievalRealTime(ctx context.Context, req *RetrievalRealTimeReq) (*RetrievalRealTimeRsp, error) {
	return nil, errors.New("rpc RetrievalRealTime of service DirectIndex is not implemented")
}

// AddDBText2SQL 批量增加或修改外部数据库的Text2SQL信息
func (s *UnimplementedDirectIndex) AddDBText2SQL(ctx context.Context, req *AddDBText2SQLReq) (*AddDBText2SQLRsp, error) {
	return nil, errors.New("rpc AddDBText2SQL of service DirectIndex is not implemented")
}

// DeleteDBText2SQL 批量删除外部数据库的Text2SQL信息
func (s *UnimplementedDirectIndex) DeleteDBText2SQL(ctx context.Context, req *DeleteDBText2SQLReq) (*DeleteDBText2SQLRsp, error) {
	return nil, errors.New("rpc DeleteDBText2SQL of service DirectIndex is not implemented")
}

// AddAndUpdateESData 向ES中添加或者更新用于普通关键字检索的数据
func (s *UnimplementedDirectIndex) AddAndUpdateESData(ctx context.Context, req *AddESDataReq) (*AddESDataReqRsp, error) {
	return nil, errors.New("rpc AddAndUpdateESData of service DirectIndex is not implemented")
}

// DeleteESData 从ES中删除用于普通关键字检索的数据
func (s *UnimplementedDirectIndex) DeleteESData(ctx context.Context, req *DeleteESDataReq) (*DeleteESDataRsp, error) {
	return nil, errors.New("rpc DeleteESData of service DirectIndex is not implemented")
}

// SearchES 在ES中做关键字搜索
func (s *UnimplementedDirectIndex) SearchES(ctx context.Context, req *SearchESReq) (*SearchESRsp, error) {
	return nil, errors.New("rpc SearchES of service DirectIndex is not implemented")
}

// END --------------------------------- Default Unimplemented Server Service --------------------------------- END

// END ======================================= Server Service Definition ======================================= END

// START ======================================= Client Service Definition ======================================= START

// RetrievalClientProxy defines service client proxy
type RetrievalClientProxy interface {
	// Search 查找
	Search(ctx context.Context, req *SearchReq, opts ...client.Option) (rsp *SearchRsp, err error)

	// Publish 发布版本
	Publish(ctx context.Context, req *PublishReq, opts ...client.Option) (rsp *PublishRsp, err error)

	// Similarity 计算相似度
	Similarity(ctx context.Context, req *SimilarityReq, opts ...client.Option) (rsp *SimilarityRsp, err error)

	// IndexRebuild 索引库重建
	IndexRebuild(ctx context.Context, req *IndexRebuildReq, opts ...client.Option) (rsp *IndexRebuildRsp, err error)

	// ContinueTerminatedTask 继续已经终止的任务
	ContinueTerminatedTask(ctx context.Context, req *ContinueTerminatedTaskReq, opts ...client.Option) (rsp *ContinueTerminatedTaskRsp, err error)

	// UpgradeEmbedding 线上库embedding升级任务
	UpgradeEmbedding(ctx context.Context, req *UpgradeEmbeddingReq, opts ...client.Option) (rsp *UpgradeEmbeddingRsp, err error)

	// CheckVersion vector发布后校验版本号是否有效
	CheckVersion(ctx context.Context, req *CheckVersionReq, opts ...client.Option) (rsp *CheckVersionRsp, err error)

	// BatchDeleteAllKnowledgeProd 批量删除发布库的所有知识（包括QA/文档/混合检索/text2sql等）
	BatchDeleteAllKnowledgeProd(ctx context.Context, req *BatchDeleteAllKnowledgeProdReq, opts ...client.Option) (rsp *BatchDeleteAllKnowledgeProdRsp, err error)

	// ClearAppVectorResource 按照引用维度删除数据[es,vector,db]
	ClearAppVectorResource(ctx context.Context, req *ClearAppVectorResourceReq, opts ...client.Option) (rsp *ClearAppVectorResourceRsp, err error)
}

type RetrievalClientProxyImpl struct {
	client client.Client
	opts   []client.Option
}

var NewRetrievalClientProxy = func(opts ...client.Option) RetrievalClientProxy {
	return &RetrievalClientProxyImpl{client: client.DefaultClient, opts: opts}
}

func (c *RetrievalClientProxyImpl) Search(ctx context.Context, req *SearchReq, opts ...client.Option) (*SearchRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_retrieval_server.Retrieval/Search")
	msg.WithCalleeServiceName(RetrievalServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_retrieval_server")
	msg.WithCalleeService("Retrieval")
	msg.WithCalleeMethod("Search")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &SearchRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *RetrievalClientProxyImpl) Publish(ctx context.Context, req *PublishReq, opts ...client.Option) (*PublishRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_retrieval_server.Retrieval/Publish")
	msg.WithCalleeServiceName(RetrievalServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_retrieval_server")
	msg.WithCalleeService("Retrieval")
	msg.WithCalleeMethod("Publish")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &PublishRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *RetrievalClientProxyImpl) Similarity(ctx context.Context, req *SimilarityReq, opts ...client.Option) (*SimilarityRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_retrieval_server.Retrieval/Similarity")
	msg.WithCalleeServiceName(RetrievalServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_retrieval_server")
	msg.WithCalleeService("Retrieval")
	msg.WithCalleeMethod("Similarity")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &SimilarityRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *RetrievalClientProxyImpl) IndexRebuild(ctx context.Context, req *IndexRebuildReq, opts ...client.Option) (*IndexRebuildRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_retrieval_server.Retrieval/IndexRebuild")
	msg.WithCalleeServiceName(RetrievalServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_retrieval_server")
	msg.WithCalleeService("Retrieval")
	msg.WithCalleeMethod("IndexRebuild")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &IndexRebuildRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *RetrievalClientProxyImpl) ContinueTerminatedTask(ctx context.Context, req *ContinueTerminatedTaskReq, opts ...client.Option) (*ContinueTerminatedTaskRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_retrieval_server.Retrieval/ContinueTerminatedTask")
	msg.WithCalleeServiceName(RetrievalServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_retrieval_server")
	msg.WithCalleeService("Retrieval")
	msg.WithCalleeMethod("ContinueTerminatedTask")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &ContinueTerminatedTaskRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *RetrievalClientProxyImpl) UpgradeEmbedding(ctx context.Context, req *UpgradeEmbeddingReq, opts ...client.Option) (*UpgradeEmbeddingRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_retrieval_server.Retrieval/UpgradeEmbedding")
	msg.WithCalleeServiceName(RetrievalServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_retrieval_server")
	msg.WithCalleeService("Retrieval")
	msg.WithCalleeMethod("UpgradeEmbedding")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &UpgradeEmbeddingRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *RetrievalClientProxyImpl) CheckVersion(ctx context.Context, req *CheckVersionReq, opts ...client.Option) (*CheckVersionRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_retrieval_server.Retrieval/CheckVersion")
	msg.WithCalleeServiceName(RetrievalServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_retrieval_server")
	msg.WithCalleeService("Retrieval")
	msg.WithCalleeMethod("CheckVersion")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &CheckVersionRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *RetrievalClientProxyImpl) BatchDeleteAllKnowledgeProd(ctx context.Context, req *BatchDeleteAllKnowledgeProdReq, opts ...client.Option) (*BatchDeleteAllKnowledgeProdRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_retrieval_server.Retrieval/BatchDeleteAllKnowledgeProd")
	msg.WithCalleeServiceName(RetrievalServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_retrieval_server")
	msg.WithCalleeService("Retrieval")
	msg.WithCalleeMethod("BatchDeleteAllKnowledgeProd")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &BatchDeleteAllKnowledgeProdRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *RetrievalClientProxyImpl) ClearAppVectorResource(ctx context.Context, req *ClearAppVectorResourceReq, opts ...client.Option) (*ClearAppVectorResourceRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_retrieval_server.Retrieval/ClearAppVectorResource")
	msg.WithCalleeServiceName(RetrievalServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_retrieval_server")
	msg.WithCalleeService("Retrieval")
	msg.WithCalleeMethod("ClearAppVectorResource")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &ClearAppVectorResourceRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

// DirectIndexClientProxy defines service client proxy
type DirectIndexClientProxy interface {
	// CreateIndex 增加检索库
	CreateIndex(ctx context.Context, req *CreateIndexReq, opts ...client.Option) (rsp *CreateIndexRsp, err error)

	// DeleteIndex 删除检索库
	DeleteIndex(ctx context.Context, req *DeleteIndexReq, opts ...client.Option) (rsp *DeleteIndexRsp, err error)

	// AddVector 增加特征
	AddVector(ctx context.Context, req *AddVectorReq, opts ...client.Option) (rsp *AddVectorRsp, err error)

	// DeleteVector 删除特征
	DeleteVector(ctx context.Context, req *DeleteVectorReq, opts ...client.Option) (rsp *DeleteVectorRsp, err error)

	// UpdateVector 修改特征
	UpdateVector(ctx context.Context, req *UpdateVectorReq, opts ...client.Option) (rsp *UpdateVectorRsp, err error)

	// SearchVector 检索特征
	SearchVector(ctx context.Context, req *SearchVectorReq, opts ...client.Option) (rsp *SearchVectorRsp, err error)

	// SearchMultiKnowledge 检索多个知识库
	SearchMultiKnowledge(ctx context.Context, req *SearchMultiKnowledgeReq, opts ...client.Option) (rsp *SearchVectorRsp, err error)

	// DirectCreateIndex 增加检索库(无用户关系绑定)
	DirectCreateIndex(ctx context.Context, req *DirectCreateIndexReq, opts ...client.Option) (rsp *DirectCreateIndexRsp, err error)

	// DirectDeleteIndex 删除检索库(无用户关系绑定)
	DirectDeleteIndex(ctx context.Context, req *DirectDeleteIndexReq, opts ...client.Option) (rsp *DirectDeleteIndexRsp, err error)

	// DirectAddVector 增加特征(无用户关系绑定)
	DirectAddVector(ctx context.Context, req *DirectAddVectorReq, opts ...client.Option) (rsp *DirectAddVectorRsp, err error)

	// DirectDeleteVector 删除特征(无用户关系绑定)
	DirectDeleteVector(ctx context.Context, req *DirectDeleteVectorReq, opts ...client.Option) (rsp *DirectDeleteVectorRsp, err error)

	// DirectUpdateVector 修改特征(无用户关系绑定)
	DirectUpdateVector(ctx context.Context, req *DirectUpdateVectorReq, opts ...client.Option) (rsp *DirectUpdateVectorRsp, err error)

	// DirectSearchVector 检索特征(无用户关系绑定)
	DirectSearchVector(ctx context.Context, req *DirectSearchVectorReq, opts ...client.Option) (rsp *DirectSearchVectorRsp, err error)

	// AddBigDataElastic 新建或更新BigData数据到ES
	AddBigDataElastic(ctx context.Context, req *AddBigDataElasticReq, opts ...client.Option) (rsp *AddBigDataElasticRsp, err error)

	// DeleteBigDataElastic 从ES里删除BigData
	DeleteBigDataElastic(ctx context.Context, req *DeleteBigDataElasticReq, opts ...client.Option) (rsp *DeleteBigDataElasticRsp, err error)

	// RecoverBigDataElastic 从ES恢复离线知识库的BigData
	RecoverBigDataElastic(ctx context.Context, req *RecoverBigDataElasticReq, opts ...client.Option) (rsp *RecoverBigDataElasticRsp, err error)

	// BatchGetBigDataESByRobotBigDataID 根据robot_id, big_data_id 从bigDataEs中批量查找数据
	BatchGetBigDataESByRobotBigDataID(ctx context.Context, req *BatchGetBigDataESByRobotBigDataIDReq, opts ...client.Option) (rsp *BatchGetBigDataESByRobotBigDataIDResp, err error)

	// AddKnowledge 增加知识
	AddKnowledge(ctx context.Context, req *AddKnowledgeReq, opts ...client.Option) (rsp *AddKnowledgeRsp, err error)

	// BatchAddKnowledge 批量增加知识
	BatchAddKnowledge(ctx context.Context, req *BatchAddKnowledgeReq, opts ...client.Option) (rsp *BatchAddKnowledgeRsp, err error)

	// DeleteKnowledge 删除知识
	DeleteKnowledge(ctx context.Context, req *DeleteKnowledgeReq, opts ...client.Option) (rsp *DeleteKnowledgeRsp, err error)

	// BatchDeleteKnowledge 批量删除知识
	BatchDeleteKnowledge(ctx context.Context, req *BatchDeleteKnowledgeReq, opts ...client.Option) (rsp *BatchDeleteKnowledgeRsp, err error)

	// UpdateKnowledge 修改知识
	UpdateKnowledge(ctx context.Context, req *UpdateKnowledgeReq, opts ...client.Option) (rsp *UpdateKnowledgeRsp, err error)

	// AddText2SQL 批量增加或修改text2sql
	AddText2SQL(ctx context.Context, req *AddText2SQLReq, opts ...client.Option) (rsp *AddText2SQLRsp, err error)

	// DeleteText2SQL 批量删除text2sql
	DeleteText2SQL(ctx context.Context, req *DeleteText2SQLReq, opts ...client.Option) (rsp *DeleteText2SQLRsp, err error)

	// AddRealTimeKnowledge 实时文档添加切片
	AddRealTimeKnowledge(ctx context.Context, req *AddRealTimeKnowledgeReq, opts ...client.Option) (rsp *AddRealTimeKnowledgeRsp, err error)

	// DeleteRealTimeKnowledge 实时文档删除切片
	DeleteRealTimeKnowledge(ctx context.Context, req *DeleteRealTimeKnowledgeReq, opts ...client.Option) (rsp *DeleteRealTimeKnowledgeRsp, err error)

	// RetrievalRealTime 实时文档检索接口
	RetrievalRealTime(ctx context.Context, req *RetrievalRealTimeReq, opts ...client.Option) (rsp *RetrievalRealTimeRsp, err error)

	// AddDBText2SQL 批量增加或修改外部数据库的Text2SQL信息
	AddDBText2SQL(ctx context.Context, req *AddDBText2SQLReq, opts ...client.Option) (rsp *AddDBText2SQLRsp, err error)

	// DeleteDBText2SQL 批量删除外部数据库的Text2SQL信息
	DeleteDBText2SQL(ctx context.Context, req *DeleteDBText2SQLReq, opts ...client.Option) (rsp *DeleteDBText2SQLRsp, err error)

	// AddAndUpdateESData 向ES中添加或者更新用于普通关键字检索的数据
	AddAndUpdateESData(ctx context.Context, req *AddESDataReq, opts ...client.Option) (rsp *AddESDataReqRsp, err error)

	// DeleteESData 从ES中删除用于普通关键字检索的数据
	DeleteESData(ctx context.Context, req *DeleteESDataReq, opts ...client.Option) (rsp *DeleteESDataRsp, err error)

	// SearchES 在ES中做关键字搜索
	SearchES(ctx context.Context, req *SearchESReq, opts ...client.Option) (rsp *SearchESRsp, err error)
}

type DirectIndexClientProxyImpl struct {
	client client.Client
	opts   []client.Option
}

var NewDirectIndexClientProxy = func(opts ...client.Option) DirectIndexClientProxy {
	return &DirectIndexClientProxyImpl{client: client.DefaultClient, opts: opts}
}

func (c *DirectIndexClientProxyImpl) CreateIndex(ctx context.Context, req *CreateIndexReq, opts ...client.Option) (*CreateIndexRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_retrieval_server.DirectIndex/CreateIndex")
	msg.WithCalleeServiceName(DirectIndexServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_retrieval_server")
	msg.WithCalleeService("DirectIndex")
	msg.WithCalleeMethod("CreateIndex")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &CreateIndexRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *DirectIndexClientProxyImpl) DeleteIndex(ctx context.Context, req *DeleteIndexReq, opts ...client.Option) (*DeleteIndexRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_retrieval_server.DirectIndex/DeleteIndex")
	msg.WithCalleeServiceName(DirectIndexServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_retrieval_server")
	msg.WithCalleeService("DirectIndex")
	msg.WithCalleeMethod("DeleteIndex")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &DeleteIndexRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *DirectIndexClientProxyImpl) AddVector(ctx context.Context, req *AddVectorReq, opts ...client.Option) (*AddVectorRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_retrieval_server.DirectIndex/AddVector")
	msg.WithCalleeServiceName(DirectIndexServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_retrieval_server")
	msg.WithCalleeService("DirectIndex")
	msg.WithCalleeMethod("AddVector")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &AddVectorRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *DirectIndexClientProxyImpl) DeleteVector(ctx context.Context, req *DeleteVectorReq, opts ...client.Option) (*DeleteVectorRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_retrieval_server.DirectIndex/DeleteVector")
	msg.WithCalleeServiceName(DirectIndexServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_retrieval_server")
	msg.WithCalleeService("DirectIndex")
	msg.WithCalleeMethod("DeleteVector")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &DeleteVectorRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *DirectIndexClientProxyImpl) UpdateVector(ctx context.Context, req *UpdateVectorReq, opts ...client.Option) (*UpdateVectorRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_retrieval_server.DirectIndex/UpdateVector")
	msg.WithCalleeServiceName(DirectIndexServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_retrieval_server")
	msg.WithCalleeService("DirectIndex")
	msg.WithCalleeMethod("UpdateVector")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &UpdateVectorRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *DirectIndexClientProxyImpl) SearchVector(ctx context.Context, req *SearchVectorReq, opts ...client.Option) (*SearchVectorRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_retrieval_server.DirectIndex/SearchVector")
	msg.WithCalleeServiceName(DirectIndexServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_retrieval_server")
	msg.WithCalleeService("DirectIndex")
	msg.WithCalleeMethod("SearchVector")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &SearchVectorRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *DirectIndexClientProxyImpl) SearchMultiKnowledge(ctx context.Context, req *SearchMultiKnowledgeReq, opts ...client.Option) (*SearchVectorRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_retrieval_server.DirectIndex/SearchMultiKnowledge")
	msg.WithCalleeServiceName(DirectIndexServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_retrieval_server")
	msg.WithCalleeService("DirectIndex")
	msg.WithCalleeMethod("SearchMultiKnowledge")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &SearchVectorRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *DirectIndexClientProxyImpl) DirectCreateIndex(ctx context.Context, req *DirectCreateIndexReq, opts ...client.Option) (*DirectCreateIndexRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_retrieval_server.DirectIndex/DirectCreateIndex")
	msg.WithCalleeServiceName(DirectIndexServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_retrieval_server")
	msg.WithCalleeService("DirectIndex")
	msg.WithCalleeMethod("DirectCreateIndex")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &DirectCreateIndexRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *DirectIndexClientProxyImpl) DirectDeleteIndex(ctx context.Context, req *DirectDeleteIndexReq, opts ...client.Option) (*DirectDeleteIndexRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_retrieval_server.DirectIndex/DirectDeleteIndex")
	msg.WithCalleeServiceName(DirectIndexServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_retrieval_server")
	msg.WithCalleeService("DirectIndex")
	msg.WithCalleeMethod("DirectDeleteIndex")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &DirectDeleteIndexRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *DirectIndexClientProxyImpl) DirectAddVector(ctx context.Context, req *DirectAddVectorReq, opts ...client.Option) (*DirectAddVectorRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_retrieval_server.DirectIndex/DirectAddVector")
	msg.WithCalleeServiceName(DirectIndexServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_retrieval_server")
	msg.WithCalleeService("DirectIndex")
	msg.WithCalleeMethod("DirectAddVector")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &DirectAddVectorRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *DirectIndexClientProxyImpl) DirectDeleteVector(ctx context.Context, req *DirectDeleteVectorReq, opts ...client.Option) (*DirectDeleteVectorRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_retrieval_server.DirectIndex/DirectDeleteVector")
	msg.WithCalleeServiceName(DirectIndexServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_retrieval_server")
	msg.WithCalleeService("DirectIndex")
	msg.WithCalleeMethod("DirectDeleteVector")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &DirectDeleteVectorRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *DirectIndexClientProxyImpl) DirectUpdateVector(ctx context.Context, req *DirectUpdateVectorReq, opts ...client.Option) (*DirectUpdateVectorRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_retrieval_server.DirectIndex/DirectUpdateVector")
	msg.WithCalleeServiceName(DirectIndexServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_retrieval_server")
	msg.WithCalleeService("DirectIndex")
	msg.WithCalleeMethod("DirectUpdateVector")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &DirectUpdateVectorRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *DirectIndexClientProxyImpl) DirectSearchVector(ctx context.Context, req *DirectSearchVectorReq, opts ...client.Option) (*DirectSearchVectorRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_retrieval_server.DirectIndex/DirectSearchVector")
	msg.WithCalleeServiceName(DirectIndexServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_retrieval_server")
	msg.WithCalleeService("DirectIndex")
	msg.WithCalleeMethod("DirectSearchVector")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &DirectSearchVectorRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *DirectIndexClientProxyImpl) AddBigDataElastic(ctx context.Context, req *AddBigDataElasticReq, opts ...client.Option) (*AddBigDataElasticRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_retrieval_server.DirectIndex/AddBigDataElastic")
	msg.WithCalleeServiceName(DirectIndexServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_retrieval_server")
	msg.WithCalleeService("DirectIndex")
	msg.WithCalleeMethod("AddBigDataElastic")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &AddBigDataElasticRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *DirectIndexClientProxyImpl) DeleteBigDataElastic(ctx context.Context, req *DeleteBigDataElasticReq, opts ...client.Option) (*DeleteBigDataElasticRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_retrieval_server.DirectIndex/DeleteBigDataElastic")
	msg.WithCalleeServiceName(DirectIndexServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_retrieval_server")
	msg.WithCalleeService("DirectIndex")
	msg.WithCalleeMethod("DeleteBigDataElastic")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &DeleteBigDataElasticRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *DirectIndexClientProxyImpl) RecoverBigDataElastic(ctx context.Context, req *RecoverBigDataElasticReq, opts ...client.Option) (*RecoverBigDataElasticRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_retrieval_server.DirectIndex/RecoverBigDataElastic")
	msg.WithCalleeServiceName(DirectIndexServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_retrieval_server")
	msg.WithCalleeService("DirectIndex")
	msg.WithCalleeMethod("RecoverBigDataElastic")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &RecoverBigDataElasticRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *DirectIndexClientProxyImpl) BatchGetBigDataESByRobotBigDataID(ctx context.Context, req *BatchGetBigDataESByRobotBigDataIDReq, opts ...client.Option) (*BatchGetBigDataESByRobotBigDataIDResp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_retrieval_server.DirectIndex/BatchGetBigDataESByRobotBigDataID")
	msg.WithCalleeServiceName(DirectIndexServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_retrieval_server")
	msg.WithCalleeService("DirectIndex")
	msg.WithCalleeMethod("BatchGetBigDataESByRobotBigDataID")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &BatchGetBigDataESByRobotBigDataIDResp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *DirectIndexClientProxyImpl) AddKnowledge(ctx context.Context, req *AddKnowledgeReq, opts ...client.Option) (*AddKnowledgeRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_retrieval_server.DirectIndex/AddKnowledge")
	msg.WithCalleeServiceName(DirectIndexServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_retrieval_server")
	msg.WithCalleeService("DirectIndex")
	msg.WithCalleeMethod("AddKnowledge")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &AddKnowledgeRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *DirectIndexClientProxyImpl) BatchAddKnowledge(ctx context.Context, req *BatchAddKnowledgeReq, opts ...client.Option) (*BatchAddKnowledgeRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_retrieval_server.DirectIndex/BatchAddKnowledge")
	msg.WithCalleeServiceName(DirectIndexServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_retrieval_server")
	msg.WithCalleeService("DirectIndex")
	msg.WithCalleeMethod("BatchAddKnowledge")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &BatchAddKnowledgeRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *DirectIndexClientProxyImpl) DeleteKnowledge(ctx context.Context, req *DeleteKnowledgeReq, opts ...client.Option) (*DeleteKnowledgeRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_retrieval_server.DirectIndex/DeleteKnowledge")
	msg.WithCalleeServiceName(DirectIndexServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_retrieval_server")
	msg.WithCalleeService("DirectIndex")
	msg.WithCalleeMethod("DeleteKnowledge")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &DeleteKnowledgeRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *DirectIndexClientProxyImpl) BatchDeleteKnowledge(ctx context.Context, req *BatchDeleteKnowledgeReq, opts ...client.Option) (*BatchDeleteKnowledgeRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_retrieval_server.DirectIndex/BatchDeleteKnowledge")
	msg.WithCalleeServiceName(DirectIndexServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_retrieval_server")
	msg.WithCalleeService("DirectIndex")
	msg.WithCalleeMethod("BatchDeleteKnowledge")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &BatchDeleteKnowledgeRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *DirectIndexClientProxyImpl) UpdateKnowledge(ctx context.Context, req *UpdateKnowledgeReq, opts ...client.Option) (*UpdateKnowledgeRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_retrieval_server.DirectIndex/UpdateKnowledge")
	msg.WithCalleeServiceName(DirectIndexServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_retrieval_server")
	msg.WithCalleeService("DirectIndex")
	msg.WithCalleeMethod("UpdateKnowledge")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &UpdateKnowledgeRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *DirectIndexClientProxyImpl) AddText2SQL(ctx context.Context, req *AddText2SQLReq, opts ...client.Option) (*AddText2SQLRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_retrieval_server.DirectIndex/AddText2SQL")
	msg.WithCalleeServiceName(DirectIndexServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_retrieval_server")
	msg.WithCalleeService("DirectIndex")
	msg.WithCalleeMethod("AddText2SQL")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &AddText2SQLRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *DirectIndexClientProxyImpl) DeleteText2SQL(ctx context.Context, req *DeleteText2SQLReq, opts ...client.Option) (*DeleteText2SQLRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_retrieval_server.DirectIndex/DeleteText2SQL")
	msg.WithCalleeServiceName(DirectIndexServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_retrieval_server")
	msg.WithCalleeService("DirectIndex")
	msg.WithCalleeMethod("DeleteText2SQL")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &DeleteText2SQLRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *DirectIndexClientProxyImpl) AddRealTimeKnowledge(ctx context.Context, req *AddRealTimeKnowledgeReq, opts ...client.Option) (*AddRealTimeKnowledgeRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_retrieval_server.DirectIndex/AddRealTimeKnowledge")
	msg.WithCalleeServiceName(DirectIndexServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_retrieval_server")
	msg.WithCalleeService("DirectIndex")
	msg.WithCalleeMethod("AddRealTimeKnowledge")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &AddRealTimeKnowledgeRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *DirectIndexClientProxyImpl) DeleteRealTimeKnowledge(ctx context.Context, req *DeleteRealTimeKnowledgeReq, opts ...client.Option) (*DeleteRealTimeKnowledgeRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_retrieval_server.DirectIndex/DeleteRealTimeKnowledge")
	msg.WithCalleeServiceName(DirectIndexServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_retrieval_server")
	msg.WithCalleeService("DirectIndex")
	msg.WithCalleeMethod("DeleteRealTimeKnowledge")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &DeleteRealTimeKnowledgeRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *DirectIndexClientProxyImpl) RetrievalRealTime(ctx context.Context, req *RetrievalRealTimeReq, opts ...client.Option) (*RetrievalRealTimeRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_retrieval_server.DirectIndex/RetrievalRealTime")
	msg.WithCalleeServiceName(DirectIndexServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_retrieval_server")
	msg.WithCalleeService("DirectIndex")
	msg.WithCalleeMethod("RetrievalRealTime")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &RetrievalRealTimeRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *DirectIndexClientProxyImpl) AddDBText2SQL(ctx context.Context, req *AddDBText2SQLReq, opts ...client.Option) (*AddDBText2SQLRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_retrieval_server.DirectIndex/AddDBText2SQL")
	msg.WithCalleeServiceName(DirectIndexServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_retrieval_server")
	msg.WithCalleeService("DirectIndex")
	msg.WithCalleeMethod("AddDBText2SQL")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &AddDBText2SQLRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *DirectIndexClientProxyImpl) DeleteDBText2SQL(ctx context.Context, req *DeleteDBText2SQLReq, opts ...client.Option) (*DeleteDBText2SQLRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_retrieval_server.DirectIndex/DeleteDBText2SQL")
	msg.WithCalleeServiceName(DirectIndexServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_retrieval_server")
	msg.WithCalleeService("DirectIndex")
	msg.WithCalleeMethod("DeleteDBText2SQL")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &DeleteDBText2SQLRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *DirectIndexClientProxyImpl) AddAndUpdateESData(ctx context.Context, req *AddESDataReq, opts ...client.Option) (*AddESDataReqRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_retrieval_server.DirectIndex/AddAndUpdateESData")
	msg.WithCalleeServiceName(DirectIndexServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_retrieval_server")
	msg.WithCalleeService("DirectIndex")
	msg.WithCalleeMethod("AddAndUpdateESData")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &AddESDataReqRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *DirectIndexClientProxyImpl) DeleteESData(ctx context.Context, req *DeleteESDataReq, opts ...client.Option) (*DeleteESDataRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_retrieval_server.DirectIndex/DeleteESData")
	msg.WithCalleeServiceName(DirectIndexServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_retrieval_server")
	msg.WithCalleeService("DirectIndex")
	msg.WithCalleeMethod("DeleteESData")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &DeleteESDataRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *DirectIndexClientProxyImpl) SearchES(ctx context.Context, req *SearchESReq, opts ...client.Option) (*SearchESRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_retrieval_server.DirectIndex/SearchES")
	msg.WithCalleeServiceName(DirectIndexServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_retrieval_server")
	msg.WithCalleeService("DirectIndex")
	msg.WithCalleeMethod("SearchES")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &SearchESRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

// END ======================================= Client Service Definition ======================================= END
