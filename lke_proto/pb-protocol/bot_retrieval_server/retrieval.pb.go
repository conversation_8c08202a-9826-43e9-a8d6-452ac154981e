// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.1
// 	protoc        v3.15.8
// source: retrieval.proto

package bot_retrieval_server

import (
	reflect "reflect"
	sync "sync"

	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type ESDataType int32

const (
	ESDataType_ORG_DATA ESDataType = 0
	// 用于解析干预临时的org_data
	ESDataType_TMP_ORG_DATA ESDataType = 1
)

// Enum value maps for ESDataType.
var (
	ESDataType_name = map[int32]string{
		0: "ORG_DATA",
		1: "TMP_ORG_DATA",
	}
	ESDataType_value = map[string]int32{
		"ORG_DATA":     0,
		"TMP_ORG_DATA": 1,
	}
)

func (x ESDataType) Enum() *ESDataType {
	p := new(ESDataType)
	*p = x
	return p
}

func (x ESDataType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ESDataType) Descriptor() protoreflect.EnumDescriptor {
	return file_retrieval_proto_enumTypes[0].Descriptor()
}

func (ESDataType) Type() protoreflect.EnumType {
	return &file_retrieval_proto_enumTypes[0]
}

func (x ESDataType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ESDataType.Descriptor instead.
func (ESDataType) EnumDescriptor() ([]byte, []int) {
	return file_retrieval_proto_rawDescGZIP(), []int{0}
}

// 数据库类型
type DBType int32

const (
	DBType_MYSQL      DBType = 0
	DBType_SQL_SERVER DBType = 1
)

// Enum value maps for DBType.
var (
	DBType_name = map[int32]string{
		0: "MYSQL",
		1: "SQL_SERVER",
	}
	DBType_value = map[string]int32{
		"MYSQL":      0,
		"SQL_SERVER": 1,
	}
)

func (x DBType) Enum() *DBType {
	p := new(DBType)
	*p = x
	return p
}

func (x DBType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (DBType) Descriptor() protoreflect.EnumDescriptor {
	return file_retrieval_proto_enumTypes[1].Descriptor()
}

func (DBType) Type() protoreflect.EnumType {
	return &file_retrieval_proto_enumTypes[1]
}

func (x DBType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use DBType.Descriptor instead.
func (DBType) EnumDescriptor() ([]byte, []int) {
	return file_retrieval_proto_rawDescGZIP(), []int{1}
}

// 检索策略类型
type SearchStrategyTypeEnum int32

const (
	SearchStrategyTypeEnum_Mixing    SearchStrategyTypeEnum = 0 // 混合检索: 向量 + ES
	SearchStrategyTypeEnum_Semantics SearchStrategyTypeEnum = 1 // 语义检索： 向量
	// 没有语义和向量，只能通过table_enhancement和doc_type 2去控制excel的text2sql，通过doc_type为数据库控制数据库的text2sql
	SearchStrategyTypeEnum_NoneSearch SearchStrategyTypeEnum = 2
)

// Enum value maps for SearchStrategyTypeEnum.
var (
	SearchStrategyTypeEnum_name = map[int32]string{
		0: "Mixing",
		1: "Semantics",
		2: "NoneSearch",
	}
	SearchStrategyTypeEnum_value = map[string]int32{
		"Mixing":     0,
		"Semantics":  1,
		"NoneSearch": 2,
	}
)

func (x SearchStrategyTypeEnum) Enum() *SearchStrategyTypeEnum {
	p := new(SearchStrategyTypeEnum)
	*p = x
	return p
}

func (x SearchStrategyTypeEnum) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (SearchStrategyTypeEnum) Descriptor() protoreflect.EnumDescriptor {
	return file_retrieval_proto_enumTypes[2].Descriptor()
}

func (SearchStrategyTypeEnum) Type() protoreflect.EnumType {
	return &file_retrieval_proto_enumTypes[2]
}

func (x SearchStrategyTypeEnum) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use SearchStrategyTypeEnum.Descriptor instead.
func (SearchStrategyTypeEnum) EnumDescriptor() ([]byte, []int) {
	return file_retrieval_proto_rawDescGZIP(), []int{2}
}

// 知识库类型，0：离线知识库，1：C端实时文档
type KnowledgeType int32

const (
	KnowledgeType_KNOWLEDGE KnowledgeType = 0 // 离线知识库
	KnowledgeType_REALTIME  KnowledgeType = 1 // C端实时文档
)

// Enum value maps for KnowledgeType.
var (
	KnowledgeType_name = map[int32]string{
		0: "KNOWLEDGE",
		1: "REALTIME",
	}
	KnowledgeType_value = map[string]int32{
		"KNOWLEDGE": 0,
		"REALTIME":  1,
	}
)

func (x KnowledgeType) Enum() *KnowledgeType {
	p := new(KnowledgeType)
	*p = x
	return p
}

func (x KnowledgeType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (KnowledgeType) Descriptor() protoreflect.EnumDescriptor {
	return file_retrieval_proto_enumTypes[3].Descriptor()
}

func (KnowledgeType) Type() protoreflect.EnumType {
	return &file_retrieval_proto_enumTypes[3]
}

func (x KnowledgeType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use KnowledgeType.Descriptor instead.
func (KnowledgeType) EnumDescriptor() ([]byte, []int) {
	return file_retrieval_proto_rawDescGZIP(), []int{3}
}

// 检索结果类型
type RetrievalResultType int32

const (
	RetrievalResultType_RETRIEVAL          RetrievalResultType = 0 // 向量/混合检索的结果
	RetrievalResultType_TEXT2SQL           RetrievalResultType = 1 // text2sql的结果
	RetrievalResultType_IMAGE_SEARCH_IMAGE RetrievalResultType = 2 // 图搜图
	RetrievalResultType_TEXT_SEARCH_IMAGE  RetrievalResultType = 3 // 文搜图
)

// Enum value maps for RetrievalResultType.
var (
	RetrievalResultType_name = map[int32]string{
		0: "RETRIEVAL",
		1: "TEXT2SQL",
		2: "IMAGE_SEARCH_IMAGE",
		3: "TEXT_SEARCH_IMAGE",
	}
	RetrievalResultType_value = map[string]int32{
		"RETRIEVAL":          0,
		"TEXT2SQL":           1,
		"IMAGE_SEARCH_IMAGE": 2,
		"TEXT_SEARCH_IMAGE":  3,
	}
)

func (x RetrievalResultType) Enum() *RetrievalResultType {
	p := new(RetrievalResultType)
	*p = x
	return p
}

func (x RetrievalResultType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (RetrievalResultType) Descriptor() protoreflect.EnumDescriptor {
	return file_retrieval_proto_enumTypes[4].Descriptor()
}

func (RetrievalResultType) Type() protoreflect.EnumType {
	return &file_retrieval_proto_enumTypes[4]
}

func (x RetrievalResultType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use RetrievalResultType.Descriptor instead.
func (RetrievalResultType) EnumDescriptor() ([]byte, []int) {
	return file_retrieval_proto_rawDescGZIP(), []int{4}
}

// 数据类型枚举
type CellDataType int32

const (
	CellDataType_DATA_TYPE_STRING   CellDataType = 0 // 字符串类型
	CellDataType_DATA_TYPE_INTEGER  CellDataType = 1 // 整数类型
	CellDataType_DATA_TYPE_FLOAT    CellDataType = 2 // 浮点数类型
	CellDataType_DATA_TYPE_DATE     CellDataType = 3 // 日期类型
	CellDataType_DATA_TYPE_TIME     CellDataType = 4 // 时间类型
	CellDataType_DATA_TYPE_DATETIME CellDataType = 5 // 日期时间类型
	CellDataType_DATA_TYPE_BOOLEAN  CellDataType = 6 // 布尔类型
)

// Enum value maps for CellDataType.
var (
	CellDataType_name = map[int32]string{
		0: "DATA_TYPE_STRING",
		1: "DATA_TYPE_INTEGER",
		2: "DATA_TYPE_FLOAT",
		3: "DATA_TYPE_DATE",
		4: "DATA_TYPE_TIME",
		5: "DATA_TYPE_DATETIME",
		6: "DATA_TYPE_BOOLEAN",
	}
	CellDataType_value = map[string]int32{
		"DATA_TYPE_STRING":   0,
		"DATA_TYPE_INTEGER":  1,
		"DATA_TYPE_FLOAT":    2,
		"DATA_TYPE_DATE":     3,
		"DATA_TYPE_TIME":     4,
		"DATA_TYPE_DATETIME": 5,
		"DATA_TYPE_BOOLEAN":  6,
	}
)

func (x CellDataType) Enum() *CellDataType {
	p := new(CellDataType)
	*p = x
	return p
}

func (x CellDataType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (CellDataType) Descriptor() protoreflect.EnumDescriptor {
	return file_retrieval_proto_enumTypes[5].Descriptor()
}

func (CellDataType) Type() protoreflect.EnumType {
	return &file_retrieval_proto_enumTypes[5]
}

func (x CellDataType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use CellDataType.Descriptor instead.
func (CellDataType) EnumDescriptor() ([]byte, []int) {
	return file_retrieval_proto_rawDescGZIP(), []int{5}
}

// 逻辑运算符
type LabelExpression_LogicOpr int32

const (
	LabelExpression_NOOP LabelExpression_LogicOpr = 0
	LabelExpression_AND  LabelExpression_LogicOpr = 1
	LabelExpression_OR   LabelExpression_LogicOpr = 2
)

// Enum value maps for LabelExpression_LogicOpr.
var (
	LabelExpression_LogicOpr_name = map[int32]string{
		0: "NOOP",
		1: "AND",
		2: "OR",
	}
	LabelExpression_LogicOpr_value = map[string]int32{
		"NOOP": 0,
		"AND":  1,
		"OR":   2,
	}
)

func (x LabelExpression_LogicOpr) Enum() *LabelExpression_LogicOpr {
	p := new(LabelExpression_LogicOpr)
	*p = x
	return p
}

func (x LabelExpression_LogicOpr) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (LabelExpression_LogicOpr) Descriptor() protoreflect.EnumDescriptor {
	return file_retrieval_proto_enumTypes[6].Descriptor()
}

func (LabelExpression_LogicOpr) Type() protoreflect.EnumType {
	return &file_retrieval_proto_enumTypes[6]
}

func (x LabelExpression_LogicOpr) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use LabelExpression_LogicOpr.Descriptor instead.
func (LabelExpression_LogicOpr) EnumDescriptor() ([]byte, []int) {
	return file_retrieval_proto_rawDescGZIP(), []int{15, 0}
}

// 数据类型，general_vector标签为string类型，用户配置的标签默认为array类型，转换的表达式不同
type LabelExpression_Condition_DataType int32

const (
	LabelExpression_Condition_ARRAY  LabelExpression_Condition_DataType = 0
	LabelExpression_Condition_STRING LabelExpression_Condition_DataType = 1
)

// Enum value maps for LabelExpression_Condition_DataType.
var (
	LabelExpression_Condition_DataType_name = map[int32]string{
		0: "ARRAY",
		1: "STRING",
	}
	LabelExpression_Condition_DataType_value = map[string]int32{
		"ARRAY":  0,
		"STRING": 1,
	}
)

func (x LabelExpression_Condition_DataType) Enum() *LabelExpression_Condition_DataType {
	p := new(LabelExpression_Condition_DataType)
	*p = x
	return p
}

func (x LabelExpression_Condition_DataType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (LabelExpression_Condition_DataType) Descriptor() protoreflect.EnumDescriptor {
	return file_retrieval_proto_enumTypes[7].Descriptor()
}

func (LabelExpression_Condition_DataType) Type() protoreflect.EnumType {
	return &file_retrieval_proto_enumTypes[7]
}

func (x LabelExpression_Condition_DataType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use LabelExpression_Condition_DataType.Descriptor instead.
func (LabelExpression_Condition_DataType) EnumDescriptor() ([]byte, []int) {
	return file_retrieval_proto_rawDescGZIP(), []int{15, 0, 0}
}

// 运算符
type VectorLabelExpr_Operator int32

const (
	VectorLabelExpr_NOOP          VectorLabelExpr_Operator = 0
	VectorLabelExpr_SCALE         VectorLabelExpr_Operator = 1
	VectorLabelExpr_AND           VectorLabelExpr_Operator = 2
	VectorLabelExpr_OR            VectorLabelExpr_Operator = 3
	VectorLabelExpr_NOT           VectorLabelExpr_Operator = 4
	VectorLabelExpr_IN            VectorLabelExpr_Operator = 5
	VectorLabelExpr_NOT_IN        VectorLabelExpr_Operator = 6
	VectorLabelExpr_ALL_IN        VectorLabelExpr_Operator = 7
	VectorLabelExpr_EQUAL         VectorLabelExpr_Operator = 8
	VectorLabelExpr_NOT_EQUAL     VectorLabelExpr_Operator = 9
	VectorLabelExpr_GREATER       VectorLabelExpr_Operator = 10
	VectorLabelExpr_GREATER_EQUAL VectorLabelExpr_Operator = 11
	VectorLabelExpr_LESS          VectorLabelExpr_Operator = 12
	VectorLabelExpr_LESS_EQUAL    VectorLabelExpr_Operator = 13
)

// Enum value maps for VectorLabelExpr_Operator.
var (
	VectorLabelExpr_Operator_name = map[int32]string{
		0:  "NOOP",
		1:  "SCALE",
		2:  "AND",
		3:  "OR",
		4:  "NOT",
		5:  "IN",
		6:  "NOT_IN",
		7:  "ALL_IN",
		8:  "EQUAL",
		9:  "NOT_EQUAL",
		10: "GREATER",
		11: "GREATER_EQUAL",
		12: "LESS",
		13: "LESS_EQUAL",
	}
	VectorLabelExpr_Operator_value = map[string]int32{
		"NOOP":          0,
		"SCALE":         1,
		"AND":           2,
		"OR":            3,
		"NOT":           4,
		"IN":            5,
		"NOT_IN":        6,
		"ALL_IN":        7,
		"EQUAL":         8,
		"NOT_EQUAL":     9,
		"GREATER":       10,
		"GREATER_EQUAL": 11,
		"LESS":          12,
		"LESS_EQUAL":    13,
	}
)

func (x VectorLabelExpr_Operator) Enum() *VectorLabelExpr_Operator {
	p := new(VectorLabelExpr_Operator)
	*p = x
	return p
}

func (x VectorLabelExpr_Operator) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (VectorLabelExpr_Operator) Descriptor() protoreflect.EnumDescriptor {
	return file_retrieval_proto_enumTypes[8].Descriptor()
}

func (VectorLabelExpr_Operator) Type() protoreflect.EnumType {
	return &file_retrieval_proto_enumTypes[8]
}

func (x VectorLabelExpr_Operator) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use VectorLabelExpr_Operator.Descriptor instead.
func (VectorLabelExpr_Operator) EnumDescriptor() ([]byte, []int) {
	return file_retrieval_proto_rawDescGZIP(), []int{18, 0}
}

type VectorLabelExpr_Type int32

const (
	VectorLabelExpr_INT    VectorLabelExpr_Type = 0
	VectorLabelExpr_STRING VectorLabelExpr_Type = 1
)

// Enum value maps for VectorLabelExpr_Type.
var (
	VectorLabelExpr_Type_name = map[int32]string{
		0: "INT",
		1: "STRING",
	}
	VectorLabelExpr_Type_value = map[string]int32{
		"INT":    0,
		"STRING": 1,
	}
)

func (x VectorLabelExpr_Type) Enum() *VectorLabelExpr_Type {
	p := new(VectorLabelExpr_Type)
	*p = x
	return p
}

func (x VectorLabelExpr_Type) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (VectorLabelExpr_Type) Descriptor() protoreflect.EnumDescriptor {
	return file_retrieval_proto_enumTypes[9].Descriptor()
}

func (VectorLabelExpr_Type) Type() protoreflect.EnumType {
	return &file_retrieval_proto_enumTypes[9]
}

func (x VectorLabelExpr_Type) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use VectorLabelExpr_Type.Descriptor instead.
func (VectorLabelExpr_Type) EnumDescriptor() ([]byte, []int) {
	return file_retrieval_proto_rawDescGZIP(), []int{18, 1}
}

// 表头类型
type Text2SQLMeta_Header_HeaderType int32

const (
	Text2SQLMeta_Header_HEADER_TYPE_UNKNOWN Text2SQLMeta_Header_HeaderType = 0
	Text2SQLMeta_Header_HEADER_TYPE_COLUMN  Text2SQLMeta_Header_HeaderType = 1 // 列表头
	Text2SQLMeta_Header_HEADER_TYPE_ROW     Text2SQLMeta_Header_HeaderType = 2 // 行表头
)

// Enum value maps for Text2SQLMeta_Header_HeaderType.
var (
	Text2SQLMeta_Header_HeaderType_name = map[int32]string{
		0: "HEADER_TYPE_UNKNOWN",
		1: "HEADER_TYPE_COLUMN",
		2: "HEADER_TYPE_ROW",
	}
	Text2SQLMeta_Header_HeaderType_value = map[string]int32{
		"HEADER_TYPE_UNKNOWN": 0,
		"HEADER_TYPE_COLUMN":  1,
		"HEADER_TYPE_ROW":     2,
	}
)

func (x Text2SQLMeta_Header_HeaderType) Enum() *Text2SQLMeta_Header_HeaderType {
	p := new(Text2SQLMeta_Header_HeaderType)
	*p = x
	return p
}

func (x Text2SQLMeta_Header_HeaderType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (Text2SQLMeta_Header_HeaderType) Descriptor() protoreflect.EnumDescriptor {
	return file_retrieval_proto_enumTypes[10].Descriptor()
}

func (Text2SQLMeta_Header_HeaderType) Type() protoreflect.EnumType {
	return &file_retrieval_proto_enumTypes[10]
}

func (x Text2SQLMeta_Header_HeaderType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use Text2SQLMeta_Header_HeaderType.Descriptor instead.
func (Text2SQLMeta_Header_HeaderType) EnumDescriptor() ([]byte, []int) {
	return file_retrieval_proto_rawDescGZIP(), []int{85, 0, 0}
}

type SearchESReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RobotId    uint64 `protobuf:"varint,1,opt,name=robot_id,json=robotId,proto3" json:"robot_id,omitempty"`
	DocId      uint64 `protobuf:"varint,2,opt,name=doc_id,json=docId,proto3" json:"doc_id,omitempty"`
	Question   string `protobuf:"bytes,3,opt,name=question,proto3" json:"question,omitempty"`
	PageNumber uint32 `protobuf:"varint,4,opt,name=page_number,json=pageNumber,proto3" json:"page_number,omitempty"`
	PageSize   uint32 `protobuf:"varint,5,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
}

func (x *SearchESReq) Reset() {
	*x = SearchESReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_retrieval_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SearchESReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SearchESReq) ProtoMessage() {}

func (x *SearchESReq) ProtoReflect() protoreflect.Message {
	mi := &file_retrieval_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SearchESReq.ProtoReflect.Descriptor instead.
func (*SearchESReq) Descriptor() ([]byte, []int) {
	return file_retrieval_proto_rawDescGZIP(), []int{0}
}

func (x *SearchESReq) GetRobotId() uint64 {
	if x != nil {
		return x.RobotId
	}
	return 0
}

func (x *SearchESReq) GetDocId() uint64 {
	if x != nil {
		return x.DocId
	}
	return 0
}

func (x *SearchESReq) GetQuestion() string {
	if x != nil {
		return x.Question
	}
	return ""
}

func (x *SearchESReq) GetPageNumber() uint32 {
	if x != nil {
		return x.PageNumber
	}
	return 0
}

func (x *SearchESReq) GetPageSize() uint32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

type SearchESRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Total uint32 `protobuf:"varint,1,opt,name=total,proto3" json:"total,omitempty"`
	// 搜索返回的数据，没有数据内容
	Data []*ESData `protobuf:"bytes,2,rep,name=data,proto3" json:"data,omitempty"`
}

func (x *SearchESRsp) Reset() {
	*x = SearchESRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_retrieval_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SearchESRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SearchESRsp) ProtoMessage() {}

func (x *SearchESRsp) ProtoReflect() protoreflect.Message {
	mi := &file_retrieval_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SearchESRsp.ProtoReflect.Descriptor instead.
func (*SearchESRsp) Descriptor() ([]byte, []int) {
	return file_retrieval_proto_rawDescGZIP(), []int{1}
}

func (x *SearchESRsp) GetTotal() uint32 {
	if x != nil {
		return x.Total
	}
	return 0
}

func (x *SearchESRsp) GetData() []*ESData {
	if x != nil {
		return x.Data
	}
	return nil
}

type DeleteESDataReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 必选。应用id
	RobotId uint64 `protobuf:"varint,1,opt,name=robot_id,json=robotId,proto3" json:"robot_id,omitempty"`
	// 必选。文档ID，如果只填文档ID，不填data，表示删除这个文档所有的关联数据
	DocId string `protobuf:"bytes,2,opt,name=doc_id,json=docId,proto3" json:"doc_id,omitempty"`
	// 非必选。要删除的数据类型和ID。如果不填此参数，按照文档删除
	DeleteIds []*ESDataIDType `protobuf:"bytes,3,rep,name=delete_ids,json=deleteIds,proto3" json:"delete_ids,omitempty"`
}

func (x *DeleteESDataReq) Reset() {
	*x = DeleteESDataReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_retrieval_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteESDataReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteESDataReq) ProtoMessage() {}

func (x *DeleteESDataReq) ProtoReflect() protoreflect.Message {
	mi := &file_retrieval_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteESDataReq.ProtoReflect.Descriptor instead.
func (*DeleteESDataReq) Descriptor() ([]byte, []int) {
	return file_retrieval_proto_rawDescGZIP(), []int{2}
}

func (x *DeleteESDataReq) GetRobotId() uint64 {
	if x != nil {
		return x.RobotId
	}
	return 0
}

func (x *DeleteESDataReq) GetDocId() string {
	if x != nil {
		return x.DocId
	}
	return ""
}

func (x *DeleteESDataReq) GetDeleteIds() []*ESDataIDType {
	if x != nil {
		return x.DeleteIds
	}
	return nil
}

type ESDataIDType struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	DataType ESDataType `protobuf:"varint,1,opt,name=data_type,json=dataType,proto3,enum=trpc.KEP.bot_retrieval_server.ESDataType" json:"data_type,omitempty"`
	// org_data 的biz id转的字符串，或者临时org_data的biz id
	BizId string `protobuf:"bytes,2,opt,name=biz_id,json=bizId,proto3" json:"biz_id,omitempty"`
}

func (x *ESDataIDType) Reset() {
	*x = ESDataIDType{}
	if protoimpl.UnsafeEnabled {
		mi := &file_retrieval_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ESDataIDType) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ESDataIDType) ProtoMessage() {}

func (x *ESDataIDType) ProtoReflect() protoreflect.Message {
	mi := &file_retrieval_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ESDataIDType.ProtoReflect.Descriptor instead.
func (*ESDataIDType) Descriptor() ([]byte, []int) {
	return file_retrieval_proto_rawDescGZIP(), []int{3}
}

func (x *ESDataIDType) GetDataType() ESDataType {
	if x != nil {
		return x.DataType
	}
	return ESDataType_ORG_DATA
}

func (x *ESDataIDType) GetBizId() string {
	if x != nil {
		return x.BizId
	}
	return ""
}

type DeleteESDataRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *DeleteESDataRsp) Reset() {
	*x = DeleteESDataRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_retrieval_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteESDataRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteESDataRsp) ProtoMessage() {}

func (x *DeleteESDataRsp) ProtoReflect() protoreflect.Message {
	mi := &file_retrieval_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteESDataRsp.ProtoReflect.Descriptor instead.
func (*DeleteESDataRsp) Descriptor() ([]byte, []int) {
	return file_retrieval_proto_rawDescGZIP(), []int{4}
}

type AddESDataReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 应用id
	RobotId uint64    `protobuf:"varint,1,opt,name=robot_id,json=robotId,proto3" json:"robot_id,omitempty"`
	Data    []*ESData `protobuf:"bytes,2,rep,name=data,proto3" json:"data,omitempty"`
}

func (x *AddESDataReq) Reset() {
	*x = AddESDataReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_retrieval_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AddESDataReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddESDataReq) ProtoMessage() {}

func (x *AddESDataReq) ProtoReflect() protoreflect.Message {
	mi := &file_retrieval_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddESDataReq.ProtoReflect.Descriptor instead.
func (*AddESDataReq) Descriptor() ([]byte, []int) {
	return file_retrieval_proto_rawDescGZIP(), []int{5}
}

func (x *AddESDataReq) GetRobotId() uint64 {
	if x != nil {
		return x.RobotId
	}
	return 0
}

func (x *AddESDataReq) GetData() []*ESData {
	if x != nil {
		return x.Data
	}
	return nil
}

type ESData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	DataType ESDataType `protobuf:"varint,1,opt,name=data_type,json=dataType,proto3,enum=trpc.KEP.bot_retrieval_server.ESDataType" json:"data_type,omitempty"`
	// org_data 的biz id转的字符串，或者临时org_data的biz id
	BizId string `protobuf:"bytes,2,opt,name=biz_id,json=bizId,proto3" json:"biz_id,omitempty"`
	DocId string `protobuf:"bytes,3,opt,name=doc_id,json=docId,proto3" json:"doc_id,omitempty"`
	// 数据内容
	Content string `protobuf:"bytes,4,opt,name=content,proto3" json:"content,omitempty"`
}

func (x *ESData) Reset() {
	*x = ESData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_retrieval_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ESData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ESData) ProtoMessage() {}

func (x *ESData) ProtoReflect() protoreflect.Message {
	mi := &file_retrieval_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ESData.ProtoReflect.Descriptor instead.
func (*ESData) Descriptor() ([]byte, []int) {
	return file_retrieval_proto_rawDescGZIP(), []int{6}
}

func (x *ESData) GetDataType() ESDataType {
	if x != nil {
		return x.DataType
	}
	return ESDataType_ORG_DATA
}

func (x *ESData) GetBizId() string {
	if x != nil {
		return x.BizId
	}
	return ""
}

func (x *ESData) GetDocId() string {
	if x != nil {
		return x.DocId
	}
	return ""
}

func (x *ESData) GetContent() string {
	if x != nil {
		return x.Content
	}
	return ""
}

type AddESDataReqRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *AddESDataReqRsp) Reset() {
	*x = AddESDataReqRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_retrieval_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AddESDataReqRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddESDataReqRsp) ProtoMessage() {}

func (x *AddESDataReqRsp) ProtoReflect() protoreflect.Message {
	mi := &file_retrieval_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddESDataReqRsp.ProtoReflect.Descriptor instead.
func (*AddESDataReqRsp) Descriptor() ([]byte, []int) {
	return file_retrieval_proto_rawDescGZIP(), []int{7}
}

type AddDBText2SQLReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 机器人ID
	RobotId       uint64 `protobuf:"varint,1,opt,name=robot_id,json=robotId,proto3" json:"robot_id,omitempty"`
	DbSourceBizId uint64 `protobuf:"varint,2,opt,name=db_source_biz_id,json=dbSourceBizId,proto3" json:"db_source_biz_id,omitempty"`
	DbType        DBType `protobuf:"varint,3,opt,name=db_type,json=dbType,proto3,enum=trpc.KEP.bot_retrieval_server.DBType" json:"db_type,omitempty"`
	// 库描述信息，格式：库名|库别名|库描述
	DbDesc       string `protobuf:"bytes,4,opt,name=db_desc,json=dbDesc,proto3" json:"db_desc,omitempty"`
	DbTableBizId uint64 `protobuf:"varint,5,opt,name=db_table_biz_id,json=dbTableBizId,proto3" json:"db_table_biz_id,omitempty"`
	// 表描述信息，格式：表名|表别名|表描述
	TableDesc string       `protobuf:"bytes,6,opt,name=table_desc,json=tableDesc,proto3" json:"table_desc,omitempty"`
	Rows      []*DBRowData `protobuf:"bytes,7,rep,name=rows,proto3" json:"rows,omitempty"`
}

func (x *AddDBText2SQLReq) Reset() {
	*x = AddDBText2SQLReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_retrieval_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AddDBText2SQLReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddDBText2SQLReq) ProtoMessage() {}

func (x *AddDBText2SQLReq) ProtoReflect() protoreflect.Message {
	mi := &file_retrieval_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddDBText2SQLReq.ProtoReflect.Descriptor instead.
func (*AddDBText2SQLReq) Descriptor() ([]byte, []int) {
	return file_retrieval_proto_rawDescGZIP(), []int{8}
}

func (x *AddDBText2SQLReq) GetRobotId() uint64 {
	if x != nil {
		return x.RobotId
	}
	return 0
}

func (x *AddDBText2SQLReq) GetDbSourceBizId() uint64 {
	if x != nil {
		return x.DbSourceBizId
	}
	return 0
}

func (x *AddDBText2SQLReq) GetDbType() DBType {
	if x != nil {
		return x.DbType
	}
	return DBType_MYSQL
}

func (x *AddDBText2SQLReq) GetDbDesc() string {
	if x != nil {
		return x.DbDesc
	}
	return ""
}

func (x *AddDBText2SQLReq) GetDbTableBizId() uint64 {
	if x != nil {
		return x.DbTableBizId
	}
	return 0
}

func (x *AddDBText2SQLReq) GetTableDesc() string {
	if x != nil {
		return x.TableDesc
	}
	return ""
}

func (x *AddDBText2SQLReq) GetRows() []*DBRowData {
	if x != nil {
		return x.Rows
	}
	return nil
}

type DBRowData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Cells []*DBCell `protobuf:"bytes,1,rep,name=cells,proto3" json:"cells,omitempty"`
}

func (x *DBRowData) Reset() {
	*x = DBRowData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_retrieval_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DBRowData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DBRowData) ProtoMessage() {}

func (x *DBRowData) ProtoReflect() protoreflect.Message {
	mi := &file_retrieval_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DBRowData.ProtoReflect.Descriptor instead.
func (*DBRowData) Descriptor() ([]byte, []int) {
	return file_retrieval_proto_rawDescGZIP(), []int{9}
}

func (x *DBRowData) GetCells() []*DBCell {
	if x != nil {
		return x.Cells
	}
	return nil
}

type DBCell struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ColumnName      string `protobuf:"bytes,1,opt,name=column_name,json=columnName,proto3" json:"column_name,omitempty"`
	ColumnAliasName string `protobuf:"bytes,2,opt,name=column_alias_name,json=columnAliasName,proto3" json:"column_alias_name,omitempty"`
	ColumnDesc      string `protobuf:"bytes,3,opt,name=column_desc,json=columnDesc,proto3" json:"column_desc,omitempty"`
	DataType        string `protobuf:"bytes,4,opt,name=data_type,json=dataType,proto3" json:"data_type,omitempty"`
	Value           string `protobuf:"bytes,5,opt,name=value,proto3" json:"value,omitempty"`
}

func (x *DBCell) Reset() {
	*x = DBCell{}
	if protoimpl.UnsafeEnabled {
		mi := &file_retrieval_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DBCell) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DBCell) ProtoMessage() {}

func (x *DBCell) ProtoReflect() protoreflect.Message {
	mi := &file_retrieval_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DBCell.ProtoReflect.Descriptor instead.
func (*DBCell) Descriptor() ([]byte, []int) {
	return file_retrieval_proto_rawDescGZIP(), []int{10}
}

func (x *DBCell) GetColumnName() string {
	if x != nil {
		return x.ColumnName
	}
	return ""
}

func (x *DBCell) GetColumnAliasName() string {
	if x != nil {
		return x.ColumnAliasName
	}
	return ""
}

func (x *DBCell) GetColumnDesc() string {
	if x != nil {
		return x.ColumnDesc
	}
	return ""
}

func (x *DBCell) GetDataType() string {
	if x != nil {
		return x.DataType
	}
	return ""
}

func (x *DBCell) GetValue() string {
	if x != nil {
		return x.Value
	}
	return ""
}

type AddDBText2SQLRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *AddDBText2SQLRsp) Reset() {
	*x = AddDBText2SQLRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_retrieval_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AddDBText2SQLRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddDBText2SQLRsp) ProtoMessage() {}

func (x *AddDBText2SQLRsp) ProtoReflect() protoreflect.Message {
	mi := &file_retrieval_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddDBText2SQLRsp.ProtoReflect.Descriptor instead.
func (*AddDBText2SQLRsp) Descriptor() ([]byte, []int) {
	return file_retrieval_proto_rawDescGZIP(), []int{11}
}

type DeleteDBText2SQLReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 机器人ID
	RobotId      uint64   `protobuf:"varint,1,opt,name=robot_id,json=robotId,proto3" json:"robot_id,omitempty"`
	DbTableBizId []uint64 `protobuf:"varint,2,rep,packed,name=db_table_biz_id,json=dbTableBizId,proto3" json:"db_table_biz_id,omitempty"`
}

func (x *DeleteDBText2SQLReq) Reset() {
	*x = DeleteDBText2SQLReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_retrieval_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteDBText2SQLReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteDBText2SQLReq) ProtoMessage() {}

func (x *DeleteDBText2SQLReq) ProtoReflect() protoreflect.Message {
	mi := &file_retrieval_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteDBText2SQLReq.ProtoReflect.Descriptor instead.
func (*DeleteDBText2SQLReq) Descriptor() ([]byte, []int) {
	return file_retrieval_proto_rawDescGZIP(), []int{12}
}

func (x *DeleteDBText2SQLReq) GetRobotId() uint64 {
	if x != nil {
		return x.RobotId
	}
	return 0
}

func (x *DeleteDBText2SQLReq) GetDbTableBizId() []uint64 {
	if x != nil {
		return x.DbTableBizId
	}
	return nil
}

type DeleteDBText2SQLRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *DeleteDBText2SQLRsp) Reset() {
	*x = DeleteDBText2SQLRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_retrieval_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteDBText2SQLRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteDBText2SQLRsp) ProtoMessage() {}

func (x *DeleteDBText2SQLRsp) ProtoReflect() protoreflect.Message {
	mi := &file_retrieval_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteDBText2SQLRsp.ProtoReflect.Descriptor instead.
func (*DeleteDBText2SQLRsp) Descriptor() ([]byte, []int) {
	return file_retrieval_proto_rawDescGZIP(), []int{13}
}

// 检索策略配置
type SearchStrategy struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 检索策略类型 0:混合检索，1：语义检索
	StrategyType SearchStrategyTypeEnum `protobuf:"varint,1,opt,name=strategy_type,json=strategyType,proto3,enum=trpc.KEP.bot_retrieval_server.SearchStrategyTypeEnum" json:"strategy_type,omitempty"`
	// excel检索增强，默认关闭
	TableEnhancement bool `protobuf:"varint,2,opt,name=table_enhancement,json=tableEnhancement,proto3" json:"table_enhancement,omitempty"`
}

func (x *SearchStrategy) Reset() {
	*x = SearchStrategy{}
	if protoimpl.UnsafeEnabled {
		mi := &file_retrieval_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SearchStrategy) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SearchStrategy) ProtoMessage() {}

func (x *SearchStrategy) ProtoReflect() protoreflect.Message {
	mi := &file_retrieval_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SearchStrategy.ProtoReflect.Descriptor instead.
func (*SearchStrategy) Descriptor() ([]byte, []int) {
	return file_retrieval_proto_rawDescGZIP(), []int{14}
}

func (x *SearchStrategy) GetStrategyType() SearchStrategyTypeEnum {
	if x != nil {
		return x.StrategyType
	}
	return SearchStrategyTypeEnum_Mixing
}

func (x *SearchStrategy) GetTableEnhancement() bool {
	if x != nil {
		return x.TableEnhancement
	}
	return false
}

// 标签表达式
type LabelExpression struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// AND或OR
	Operator LabelExpression_LogicOpr `protobuf:"varint,1,opt,name=operator,proto3,enum=trpc.KEP.bot_retrieval_server.LabelExpression_LogicOpr" json:"operator,omitempty"`
	// 嵌套逻辑表达式
	Expressions []*LabelExpression `protobuf:"bytes,2,rep,name=expressions,proto3" json:"expressions,omitempty"`
	// 基本条件表达式
	Condition *LabelExpression_Condition `protobuf:"bytes,3,opt,name=condition,proto3" json:"condition,omitempty"`
}

func (x *LabelExpression) Reset() {
	*x = LabelExpression{}
	if protoimpl.UnsafeEnabled {
		mi := &file_retrieval_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LabelExpression) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LabelExpression) ProtoMessage() {}

func (x *LabelExpression) ProtoReflect() protoreflect.Message {
	mi := &file_retrieval_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LabelExpression.ProtoReflect.Descriptor instead.
func (*LabelExpression) Descriptor() ([]byte, []int) {
	return file_retrieval_proto_rawDescGZIP(), []int{15}
}

func (x *LabelExpression) GetOperator() LabelExpression_LogicOpr {
	if x != nil {
		return x.Operator
	}
	return LabelExpression_NOOP
}

func (x *LabelExpression) GetExpressions() []*LabelExpression {
	if x != nil {
		return x.Expressions
	}
	return nil
}

func (x *LabelExpression) GetCondition() *LabelExpression_Condition {
	if x != nil {
		return x.Condition
	}
	return nil
}

// 特征标签
type VectorLabel struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 标签名
	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	// 标签值
	Value string `protobuf:"bytes,2,opt,name=value,proto3" json:"value,omitempty"`
}

func (x *VectorLabel) Reset() {
	*x = VectorLabel{}
	if protoimpl.UnsafeEnabled {
		mi := &file_retrieval_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *VectorLabel) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VectorLabel) ProtoMessage() {}

func (x *VectorLabel) ProtoReflect() protoreflect.Message {
	mi := &file_retrieval_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VectorLabel.ProtoReflect.Descriptor instead.
func (*VectorLabel) Descriptor() ([]byte, []int) {
	return file_retrieval_proto_rawDescGZIP(), []int{16}
}

func (x *VectorLabel) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *VectorLabel) GetValue() string {
	if x != nil {
		return x.Value
	}
	return ""
}

// 检索的特征标签
type SearchVectorLabel struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 标签名
	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	// 标签值，一个标签多个标签值
	Values []string `protobuf:"bytes,2,rep,name=values,proto3" json:"values,omitempty"`
}

func (x *SearchVectorLabel) Reset() {
	*x = SearchVectorLabel{}
	if protoimpl.UnsafeEnabled {
		mi := &file_retrieval_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SearchVectorLabel) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SearchVectorLabel) ProtoMessage() {}

func (x *SearchVectorLabel) ProtoReflect() protoreflect.Message {
	mi := &file_retrieval_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SearchVectorLabel.ProtoReflect.Descriptor instead.
func (*SearchVectorLabel) Descriptor() ([]byte, []int) {
	return file_retrieval_proto_rawDescGZIP(), []int{17}
}

func (x *SearchVectorLabel) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *SearchVectorLabel) GetValues() []string {
	if x != nil {
		return x.Values
	}
	return nil
}

// 特征标签表达式
type VectorLabelExpr struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 运算符
	Op VectorLabelExpr_Operator `protobuf:"varint,1,opt,name=op,proto3,enum=trpc.KEP.bot_retrieval_server.VectorLabelExpr_Operator" json:"op,omitempty"`
	// 表达式
	Expressions []*VectorLabelExpr `protobuf:"bytes,2,rep,name=expressions,proto3" json:"expressions,omitempty"`
	// 类型, 0 int, 1 string
	Type VectorLabelExpr_Type `protobuf:"varint,3,opt,name=type,proto3,enum=trpc.KEP.bot_retrieval_server.VectorLabelExpr_Type" json:"type,omitempty"`
	// 值
	Value string `protobuf:"bytes,4,opt,name=value,proto3" json:"value,omitempty"`
}

func (x *VectorLabelExpr) Reset() {
	*x = VectorLabelExpr{}
	if protoimpl.UnsafeEnabled {
		mi := &file_retrieval_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *VectorLabelExpr) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VectorLabelExpr) ProtoMessage() {}

func (x *VectorLabelExpr) ProtoReflect() protoreflect.Message {
	mi := &file_retrieval_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VectorLabelExpr.ProtoReflect.Descriptor instead.
func (*VectorLabelExpr) Descriptor() ([]byte, []int) {
	return file_retrieval_proto_rawDescGZIP(), []int{18}
}

func (x *VectorLabelExpr) GetOp() VectorLabelExpr_Operator {
	if x != nil {
		return x.Op
	}
	return VectorLabelExpr_NOOP
}

func (x *VectorLabelExpr) GetExpressions() []*VectorLabelExpr {
	if x != nil {
		return x.Expressions
	}
	return nil
}

func (x *VectorLabelExpr) GetType() VectorLabelExpr_Type {
	if x != nil {
		return x.Type
	}
	return VectorLabelExpr_INT
}

func (x *VectorLabelExpr) GetValue() string {
	if x != nil {
		return x.Value
	}
	return ""
}

// 检索的额外信息，如排序和分数等字段
type RetrievalExtra struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	EmbRank     int32   `protobuf:"varint,1,opt,name=emb_rank,json=embRank,proto3" json:"emb_rank,omitempty"`
	EsScore     float32 `protobuf:"fixed32,2,opt,name=es_score,json=esScore,proto3" json:"es_score,omitempty"`
	EsRank      int32   `protobuf:"varint,3,opt,name=es_rank,json=esRank,proto3" json:"es_rank,omitempty"`
	RerankScore float32 `protobuf:"fixed32,4,opt,name=rerank_score,json=rerankScore,proto3" json:"rerank_score,omitempty"`
	RerankRank  int32   `protobuf:"varint,5,opt,name=rerank_rank,json=rerankRank,proto3" json:"rerank_rank,omitempty"`
	RrfScore    float32 `protobuf:"fixed32,6,opt,name=rrf_score,json=rrfScore,proto3" json:"rrf_score,omitempty"`
	RrfRank     int32   `protobuf:"varint,7,opt,name=rrf_rank,json=rrfRank,proto3" json:"rrf_rank,omitempty"`
}

func (x *RetrievalExtra) Reset() {
	*x = RetrievalExtra{}
	if protoimpl.UnsafeEnabled {
		mi := &file_retrieval_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RetrievalExtra) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RetrievalExtra) ProtoMessage() {}

func (x *RetrievalExtra) ProtoReflect() protoreflect.Message {
	mi := &file_retrieval_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RetrievalExtra.ProtoReflect.Descriptor instead.
func (*RetrievalExtra) Descriptor() ([]byte, []int) {
	return file_retrieval_proto_rawDescGZIP(), []int{19}
}

func (x *RetrievalExtra) GetEmbRank() int32 {
	if x != nil {
		return x.EmbRank
	}
	return 0
}

func (x *RetrievalExtra) GetEsScore() float32 {
	if x != nil {
		return x.EsScore
	}
	return 0
}

func (x *RetrievalExtra) GetEsRank() int32 {
	if x != nil {
		return x.EsRank
	}
	return 0
}

func (x *RetrievalExtra) GetRerankScore() float32 {
	if x != nil {
		return x.RerankScore
	}
	return 0
}

func (x *RetrievalExtra) GetRerankRank() int32 {
	if x != nil {
		return x.RerankRank
	}
	return 0
}

func (x *RetrievalExtra) GetRrfScore() float32 {
	if x != nil {
		return x.RrfScore
	}
	return 0
}

func (x *RetrievalExtra) GetRrfRank() int32 {
	if x != nil {
		return x.RrfRank
	}
	return 0
}

// 问题查询请求
type SearchReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 机器人ID
	RobotId uint64 `protobuf:"varint,1,opt,name=robot_id,json=robotId,proto3" json:"robot_id,omitempty"`
	// 问答库版本
	VersionId uint64 `protobuf:"varint,2,opt,name=version_id,json=versionId,proto3" json:"version_id,omitempty"`
	// 问题
	Question string `protobuf:"bytes,3,opt,name=question,proto3" json:"question,omitempty"`
	// 筛选器
	Filters []*SearchReq_Filter `protobuf:"bytes,4,rep,name=filters,proto3" json:"filters,omitempty"`
	// 取前 n 条 (默认3)
	TopN   uint32            `protobuf:"varint,5,opt,name=top_n,json=topN,proto3" json:"top_n,omitempty"`
	Rerank *SearchReq_Rerank `protobuf:"bytes,7,opt,name=rerank,proto3" json:"rerank,omitempty"`
	// 过滤器名称
	FilterKey string `protobuf:"bytes,8,opt,name=filter_key,json=filterKey,proto3" json:"filter_key,omitempty"`
	// 废弃字段，以LabelExpression为准
	// labels 标签
	Labels []*SearchVectorLabel `protobuf:"bytes,9,rep,name=labels,proto3" json:"labels,omitempty"`
	// 请求query带图片URL，用于图搜图
	ImageUrls []string `protobuf:"bytes,10,rep,name=image_urls,json=imageUrls,proto3" json:"image_urls,omitempty"`
	// 应用id
	BotBizId uint64 `protobuf:"varint,11,opt,name=bot_biz_id,json=botBizId,proto3" json:"bot_biz_id,omitempty"`
	// 标签表达式
	LabelExpression *LabelExpression `protobuf:"bytes,12,opt,name=label_expression,json=labelExpression,proto3" json:"label_expression,omitempty"`
	// 拆解的子问题，为空表示没有子问题
	SubQuestions []string `protobuf:"bytes,13,rep,name=sub_questions,json=subQuestions,proto3" json:"sub_questions,omitempty"`
	// 知识检索策略配置
	SearchStrategy *SearchStrategy `protobuf:"bytes,14,opt,name=search_strategy,json=searchStrategy,proto3" json:"search_strategy,omitempty"`
	// 模型名称
	ModelName string `protobuf:"bytes,15,opt,name=model_name,json=modelName,proto3" json:"model_name,omitempty"`
}

func (x *SearchReq) Reset() {
	*x = SearchReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_retrieval_proto_msgTypes[20]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SearchReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SearchReq) ProtoMessage() {}

func (x *SearchReq) ProtoReflect() protoreflect.Message {
	mi := &file_retrieval_proto_msgTypes[20]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SearchReq.ProtoReflect.Descriptor instead.
func (*SearchReq) Descriptor() ([]byte, []int) {
	return file_retrieval_proto_rawDescGZIP(), []int{20}
}

func (x *SearchReq) GetRobotId() uint64 {
	if x != nil {
		return x.RobotId
	}
	return 0
}

func (x *SearchReq) GetVersionId() uint64 {
	if x != nil {
		return x.VersionId
	}
	return 0
}

func (x *SearchReq) GetQuestion() string {
	if x != nil {
		return x.Question
	}
	return ""
}

func (x *SearchReq) GetFilters() []*SearchReq_Filter {
	if x != nil {
		return x.Filters
	}
	return nil
}

func (x *SearchReq) GetTopN() uint32 {
	if x != nil {
		return x.TopN
	}
	return 0
}

func (x *SearchReq) GetRerank() *SearchReq_Rerank {
	if x != nil {
		return x.Rerank
	}
	return nil
}

func (x *SearchReq) GetFilterKey() string {
	if x != nil {
		return x.FilterKey
	}
	return ""
}

func (x *SearchReq) GetLabels() []*SearchVectorLabel {
	if x != nil {
		return x.Labels
	}
	return nil
}

func (x *SearchReq) GetImageUrls() []string {
	if x != nil {
		return x.ImageUrls
	}
	return nil
}

func (x *SearchReq) GetBotBizId() uint64 {
	if x != nil {
		return x.BotBizId
	}
	return 0
}

func (x *SearchReq) GetLabelExpression() *LabelExpression {
	if x != nil {
		return x.LabelExpression
	}
	return nil
}

func (x *SearchReq) GetSubQuestions() []string {
	if x != nil {
		return x.SubQuestions
	}
	return nil
}

func (x *SearchReq) GetSearchStrategy() *SearchStrategy {
	if x != nil {
		return x.SearchStrategy
	}
	return nil
}

func (x *SearchReq) GetModelName() string {
	if x != nil {
		return x.ModelName
	}
	return ""
}

// 问题查询响应
type SearchRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 文档数据
	Docs []*SearchRsp_Doc `protobuf:"bytes,1,rep,name=docs,proto3" json:"docs,omitempty"`
}

func (x *SearchRsp) Reset() {
	*x = SearchRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_retrieval_proto_msgTypes[21]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SearchRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SearchRsp) ProtoMessage() {}

func (x *SearchRsp) ProtoReflect() protoreflect.Message {
	mi := &file_retrieval_proto_msgTypes[21]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SearchRsp.ProtoReflect.Descriptor instead.
func (*SearchRsp) Descriptor() ([]byte, []int) {
	return file_retrieval_proto_rawDescGZIP(), []int{21}
}

func (x *SearchRsp) GetDocs() []*SearchRsp_Doc {
	if x != nil {
		return x.Docs
	}
	return nil
}

// 发布版本请求
type PublishReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 机器人ID
	RobotId uint64 `protobuf:"varint,1,opt,name=robot_id,json=robotId,proto3" json:"robot_id,omitempty"`
	// 版本
	VersionId uint64 `protobuf:"varint,2,opt,name=version_id,json=versionId,proto3" json:"version_id,omitempty"`
	// 版本名称
	VersionName string `protobuf:"bytes,3,opt,name=version_name,json=versionName,proto3" json:"version_name,omitempty"`
	// embedding 版本
	EmbeddingVersion uint64 `protobuf:"varint,4,opt,name=embedding_version,json=embeddingVersion,proto3" json:"embedding_version,omitempty"`
	// 应用id
	BotBizId uint64 `protobuf:"varint,5,opt,name=bot_biz_id,json=botBizId,proto3" json:"bot_biz_id,omitempty"`
	// 上一个版本(发布前版本号)
	LastQaVersion uint64 `protobuf:"varint,6,opt,name=last_qa_version,json=lastQaVersion,proto3" json:"last_qa_version,omitempty"`
}

func (x *PublishReq) Reset() {
	*x = PublishReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_retrieval_proto_msgTypes[22]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PublishReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PublishReq) ProtoMessage() {}

func (x *PublishReq) ProtoReflect() protoreflect.Message {
	mi := &file_retrieval_proto_msgTypes[22]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PublishReq.ProtoReflect.Descriptor instead.
func (*PublishReq) Descriptor() ([]byte, []int) {
	return file_retrieval_proto_rawDescGZIP(), []int{22}
}

func (x *PublishReq) GetRobotId() uint64 {
	if x != nil {
		return x.RobotId
	}
	return 0
}

func (x *PublishReq) GetVersionId() uint64 {
	if x != nil {
		return x.VersionId
	}
	return 0
}

func (x *PublishReq) GetVersionName() string {
	if x != nil {
		return x.VersionName
	}
	return ""
}

func (x *PublishReq) GetEmbeddingVersion() uint64 {
	if x != nil {
		return x.EmbeddingVersion
	}
	return 0
}

func (x *PublishReq) GetBotBizId() uint64 {
	if x != nil {
		return x.BotBizId
	}
	return 0
}

func (x *PublishReq) GetLastQaVersion() uint64 {
	if x != nil {
		return x.LastQaVersion
	}
	return 0
}

// 发布版本响应
type PublishRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *PublishRsp) Reset() {
	*x = PublishRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_retrieval_proto_msgTypes[23]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PublishRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PublishRsp) ProtoMessage() {}

func (x *PublishRsp) ProtoReflect() protoreflect.Message {
	mi := &file_retrieval_proto_msgTypes[23]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PublishRsp.ProtoReflect.Descriptor instead.
func (*PublishRsp) Descriptor() ([]byte, []int) {
	return file_retrieval_proto_rawDescGZIP(), []int{23}
}

// 获取相似度
type SimilarityReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ori  string               `protobuf:"bytes,1,opt,name=ori,proto3" json:"ori,omitempty"`
	Docs []*SimilarityReq_Doc `protobuf:"bytes,2,rep,name=docs,proto3" json:"docs,omitempty"`
	// 机器人ID
	RobotId uint64 `protobuf:"varint,3,opt,name=robot_id,json=robotId,proto3" json:"robot_id,omitempty"`
	// 版本
	VersionId uint64 `protobuf:"varint,4,opt,name=version_id,json=versionId,proto3" json:"version_id,omitempty"`
	// embedding 版本(相似/评测 直接指定 embedding_version, 否则使用 robot_id + version_id 获取发布时使用的 embedding)
	EmbeddingVersion uint64 `protobuf:"varint,5,opt,name=embedding_version,json=embeddingVersion,proto3" json:"embedding_version,omitempty"`
	// 应用id
	BotBizId uint64 `protobuf:"varint,6,opt,name=bot_biz_id,json=botBizId,proto3" json:"bot_biz_id,omitempty"`
}

func (x *SimilarityReq) Reset() {
	*x = SimilarityReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_retrieval_proto_msgTypes[24]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SimilarityReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SimilarityReq) ProtoMessage() {}

func (x *SimilarityReq) ProtoReflect() protoreflect.Message {
	mi := &file_retrieval_proto_msgTypes[24]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SimilarityReq.ProtoReflect.Descriptor instead.
func (*SimilarityReq) Descriptor() ([]byte, []int) {
	return file_retrieval_proto_rawDescGZIP(), []int{24}
}

func (x *SimilarityReq) GetOri() string {
	if x != nil {
		return x.Ori
	}
	return ""
}

func (x *SimilarityReq) GetDocs() []*SimilarityReq_Doc {
	if x != nil {
		return x.Docs
	}
	return nil
}

func (x *SimilarityReq) GetRobotId() uint64 {
	if x != nil {
		return x.RobotId
	}
	return 0
}

func (x *SimilarityReq) GetVersionId() uint64 {
	if x != nil {
		return x.VersionId
	}
	return 0
}

func (x *SimilarityReq) GetEmbeddingVersion() uint64 {
	if x != nil {
		return x.EmbeddingVersion
	}
	return 0
}

func (x *SimilarityReq) GetBotBizId() uint64 {
	if x != nil {
		return x.BotBizId
	}
	return 0
}

type SimilarityRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Similarities []float32 `protobuf:"fixed32,1,rep,packed,name=similarities,proto3" json:"similarities,omitempty"`
}

func (x *SimilarityRsp) Reset() {
	*x = SimilarityRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_retrieval_proto_msgTypes[25]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SimilarityRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SimilarityRsp) ProtoMessage() {}

func (x *SimilarityRsp) ProtoReflect() protoreflect.Message {
	mi := &file_retrieval_proto_msgTypes[25]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SimilarityRsp.ProtoReflect.Descriptor instead.
func (*SimilarityRsp) Descriptor() ([]byte, []int) {
	return file_retrieval_proto_rawDescGZIP(), []int{25}
}

func (x *SimilarityRsp) GetSimilarities() []float32 {
	if x != nil {
		return x.Similarities
	}
	return nil
}

// 索引库重建请求
type IndexRebuildReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 机器人ID
	RobotId uint64 `protobuf:"varint,1,opt,name=robot_id,json=robotId,proto3" json:"robot_id,omitempty"`
	// 机器人版本
	VersionId uint64 `protobuf:"varint,2,opt,name=version_id,json=versionId,proto3" json:"version_id,omitempty"`
	// 应用id
	BotBizId uint64 `protobuf:"varint,3,opt,name=bot_biz_id,json=botBizId,proto3" json:"bot_biz_id,omitempty"`
}

func (x *IndexRebuildReq) Reset() {
	*x = IndexRebuildReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_retrieval_proto_msgTypes[26]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *IndexRebuildReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*IndexRebuildReq) ProtoMessage() {}

func (x *IndexRebuildReq) ProtoReflect() protoreflect.Message {
	mi := &file_retrieval_proto_msgTypes[26]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use IndexRebuildReq.ProtoReflect.Descriptor instead.
func (*IndexRebuildReq) Descriptor() ([]byte, []int) {
	return file_retrieval_proto_rawDescGZIP(), []int{26}
}

func (x *IndexRebuildReq) GetRobotId() uint64 {
	if x != nil {
		return x.RobotId
	}
	return 0
}

func (x *IndexRebuildReq) GetVersionId() uint64 {
	if x != nil {
		return x.VersionId
	}
	return 0
}

func (x *IndexRebuildReq) GetBotBizId() uint64 {
	if x != nil {
		return x.BotBizId
	}
	return 0
}

// 索引库重建响应
type IndexRebuildRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *IndexRebuildRsp) Reset() {
	*x = IndexRebuildRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_retrieval_proto_msgTypes[27]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *IndexRebuildRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*IndexRebuildRsp) ProtoMessage() {}

func (x *IndexRebuildRsp) ProtoReflect() protoreflect.Message {
	mi := &file_retrieval_proto_msgTypes[27]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use IndexRebuildRsp.ProtoReflect.Descriptor instead.
func (*IndexRebuildRsp) Descriptor() ([]byte, []int) {
	return file_retrieval_proto_rawDescGZIP(), []int{27}
}

// 继续已经终止的任务请求
type ContinueTerminatedTaskReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 任务ID
	TaskId uint64 `protobuf:"varint,1,opt,name=task_id,json=taskId,proto3" json:"task_id,omitempty"`
	// 重试次数
	RetryTimes uint64 `protobuf:"varint,2,opt,name=retry_times,json=retryTimes,proto3" json:"retry_times,omitempty"`
	// 等待发起重试的时间(ms)
	WaitToStart uint64 `protobuf:"varint,3,opt,name=wait_to_start,json=waitToStart,proto3" json:"wait_to_start,omitempty"`
}

func (x *ContinueTerminatedTaskReq) Reset() {
	*x = ContinueTerminatedTaskReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_retrieval_proto_msgTypes[28]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ContinueTerminatedTaskReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ContinueTerminatedTaskReq) ProtoMessage() {}

func (x *ContinueTerminatedTaskReq) ProtoReflect() protoreflect.Message {
	mi := &file_retrieval_proto_msgTypes[28]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ContinueTerminatedTaskReq.ProtoReflect.Descriptor instead.
func (*ContinueTerminatedTaskReq) Descriptor() ([]byte, []int) {
	return file_retrieval_proto_rawDescGZIP(), []int{28}
}

func (x *ContinueTerminatedTaskReq) GetTaskId() uint64 {
	if x != nil {
		return x.TaskId
	}
	return 0
}

func (x *ContinueTerminatedTaskReq) GetRetryTimes() uint64 {
	if x != nil {
		return x.RetryTimes
	}
	return 0
}

func (x *ContinueTerminatedTaskReq) GetWaitToStart() uint64 {
	if x != nil {
		return x.WaitToStart
	}
	return 0
}

// 继续已经终止的任务响应
type ContinueTerminatedTaskRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *ContinueTerminatedTaskRsp) Reset() {
	*x = ContinueTerminatedTaskRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_retrieval_proto_msgTypes[29]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ContinueTerminatedTaskRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ContinueTerminatedTaskRsp) ProtoMessage() {}

func (x *ContinueTerminatedTaskRsp) ProtoReflect() protoreflect.Message {
	mi := &file_retrieval_proto_msgTypes[29]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ContinueTerminatedTaskRsp.ProtoReflect.Descriptor instead.
func (*ContinueTerminatedTaskRsp) Descriptor() ([]byte, []int) {
	return file_retrieval_proto_rawDescGZIP(), []int{29}
}

// 线上库embedding升级请求
type UpgradeEmbeddingReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 机器人ID
	RobotId uint64 `protobuf:"varint,1,opt,name=robot_id,json=robotId,proto3" json:"robot_id,omitempty"`
	// 升级的embedding版本id
	EmbeddingVersionId uint64 `protobuf:"varint,2,opt,name=embedding_version_id,json=embeddingVersionId,proto3" json:"embedding_version_id,omitempty"`
	// 应用id
	BotBizId uint64 `protobuf:"varint,3,opt,name=bot_biz_id,json=botBizId,proto3" json:"bot_biz_id,omitempty"`
}

func (x *UpgradeEmbeddingReq) Reset() {
	*x = UpgradeEmbeddingReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_retrieval_proto_msgTypes[30]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpgradeEmbeddingReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpgradeEmbeddingReq) ProtoMessage() {}

func (x *UpgradeEmbeddingReq) ProtoReflect() protoreflect.Message {
	mi := &file_retrieval_proto_msgTypes[30]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpgradeEmbeddingReq.ProtoReflect.Descriptor instead.
func (*UpgradeEmbeddingReq) Descriptor() ([]byte, []int) {
	return file_retrieval_proto_rawDescGZIP(), []int{30}
}

func (x *UpgradeEmbeddingReq) GetRobotId() uint64 {
	if x != nil {
		return x.RobotId
	}
	return 0
}

func (x *UpgradeEmbeddingReq) GetEmbeddingVersionId() uint64 {
	if x != nil {
		return x.EmbeddingVersionId
	}
	return 0
}

func (x *UpgradeEmbeddingReq) GetBotBizId() uint64 {
	if x != nil {
		return x.BotBizId
	}
	return 0
}

// 线上库embedding升级响应
type UpgradeEmbeddingRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *UpgradeEmbeddingRsp) Reset() {
	*x = UpgradeEmbeddingRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_retrieval_proto_msgTypes[31]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpgradeEmbeddingRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpgradeEmbeddingRsp) ProtoMessage() {}

func (x *UpgradeEmbeddingRsp) ProtoReflect() protoreflect.Message {
	mi := &file_retrieval_proto_msgTypes[31]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpgradeEmbeddingRsp.ProtoReflect.Descriptor instead.
func (*UpgradeEmbeddingRsp) Descriptor() ([]byte, []int) {
	return file_retrieval_proto_rawDescGZIP(), []int{31}
}

// 校验vector版本号是否有效的请求
type CheckVersionReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 机器人ID
	RobotId uint64 `protobuf:"varint,1,opt,name=robot_id,json=robotId,proto3" json:"robot_id,omitempty"`
	// 版本号
	VersionId uint64 `protobuf:"varint,2,opt,name=version_id,json=versionId,proto3" json:"version_id,omitempty"`
}

func (x *CheckVersionReq) Reset() {
	*x = CheckVersionReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_retrieval_proto_msgTypes[32]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CheckVersionReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CheckVersionReq) ProtoMessage() {}

func (x *CheckVersionReq) ProtoReflect() protoreflect.Message {
	mi := &file_retrieval_proto_msgTypes[32]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CheckVersionReq.ProtoReflect.Descriptor instead.
func (*CheckVersionReq) Descriptor() ([]byte, []int) {
	return file_retrieval_proto_rawDescGZIP(), []int{32}
}

func (x *CheckVersionReq) GetRobotId() uint64 {
	if x != nil {
		return x.RobotId
	}
	return 0
}

func (x *CheckVersionReq) GetVersionId() uint64 {
	if x != nil {
		return x.VersionId
	}
	return 0
}

// 校验vector版本号是否有效的响应
type CheckVersionRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 版本号是否有效
	IsValid bool `protobuf:"varint,1,opt,name=is_valid,json=isValid,proto3" json:"is_valid,omitempty"`
}

func (x *CheckVersionRsp) Reset() {
	*x = CheckVersionRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_retrieval_proto_msgTypes[33]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CheckVersionRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CheckVersionRsp) ProtoMessage() {}

func (x *CheckVersionRsp) ProtoReflect() protoreflect.Message {
	mi := &file_retrieval_proto_msgTypes[33]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CheckVersionRsp.ProtoReflect.Descriptor instead.
func (*CheckVersionRsp) Descriptor() ([]byte, []int) {
	return file_retrieval_proto_rawDescGZIP(), []int{33}
}

func (x *CheckVersionRsp) GetIsValid() bool {
	if x != nil {
		return x.IsValid
	}
	return false
}

// 增加检索库请求
type CreateIndexReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 机器人ID
	RobotId uint64 `protobuf:"varint,1,opt,name=robot_id,json=robotId,proto3" json:"robot_id,omitempty"`
	// 索引库ID, 业务自己定义保存
	IndexId uint64 `protobuf:"varint,2,opt,name=index_id,json=indexId,proto3" json:"index_id,omitempty"`
	// embedding 版本
	EmbeddingVersion uint64 `protobuf:"varint,3,opt,name=embedding_version,json=embeddingVersion,proto3" json:"embedding_version,omitempty"`
	// 文档类型 (1 QA, 2 文档段, 3 拒答问题, 4 搜索引擎)
	DocType uint32 `protobuf:"varint,4,opt,name=doc_type,json=docType,proto3" json:"doc_type,omitempty"`
	// 应用id
	BotBizId uint64 `protobuf:"varint,5,opt,name=bot_biz_id,json=botBizId,proto3" json:"bot_biz_id,omitempty"`
}

func (x *CreateIndexReq) Reset() {
	*x = CreateIndexReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_retrieval_proto_msgTypes[34]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateIndexReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateIndexReq) ProtoMessage() {}

func (x *CreateIndexReq) ProtoReflect() protoreflect.Message {
	mi := &file_retrieval_proto_msgTypes[34]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateIndexReq.ProtoReflect.Descriptor instead.
func (*CreateIndexReq) Descriptor() ([]byte, []int) {
	return file_retrieval_proto_rawDescGZIP(), []int{34}
}

func (x *CreateIndexReq) GetRobotId() uint64 {
	if x != nil {
		return x.RobotId
	}
	return 0
}

func (x *CreateIndexReq) GetIndexId() uint64 {
	if x != nil {
		return x.IndexId
	}
	return 0
}

func (x *CreateIndexReq) GetEmbeddingVersion() uint64 {
	if x != nil {
		return x.EmbeddingVersion
	}
	return 0
}

func (x *CreateIndexReq) GetDocType() uint32 {
	if x != nil {
		return x.DocType
	}
	return 0
}

func (x *CreateIndexReq) GetBotBizId() uint64 {
	if x != nil {
		return x.BotBizId
	}
	return 0
}

// 增加检索库响应
type CreateIndexRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *CreateIndexRsp) Reset() {
	*x = CreateIndexRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_retrieval_proto_msgTypes[35]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateIndexRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateIndexRsp) ProtoMessage() {}

func (x *CreateIndexRsp) ProtoReflect() protoreflect.Message {
	mi := &file_retrieval_proto_msgTypes[35]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateIndexRsp.ProtoReflect.Descriptor instead.
func (*CreateIndexRsp) Descriptor() ([]byte, []int) {
	return file_retrieval_proto_rawDescGZIP(), []int{35}
}

// 删除检索库请求
type DeleteIndexReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 机器人ID
	RobotId uint64 `protobuf:"varint,1,opt,name=robot_id,json=robotId,proto3" json:"robot_id,omitempty"`
	// 索引库ID, 业务自己定义保存
	IndexId uint64 `protobuf:"varint,2,opt,name=index_id,json=indexId,proto3" json:"index_id,omitempty"`
	// embedding 版本
	EmbeddingVersion uint64 `protobuf:"varint,3,opt,name=embedding_version,json=embeddingVersion,proto3" json:"embedding_version,omitempty"`
	// 应用id
	BotBizId uint64 `protobuf:"varint,4,opt,name=bot_biz_id,json=botBizId,proto3" json:"bot_biz_id,omitempty"`
}

func (x *DeleteIndexReq) Reset() {
	*x = DeleteIndexReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_retrieval_proto_msgTypes[36]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteIndexReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteIndexReq) ProtoMessage() {}

func (x *DeleteIndexReq) ProtoReflect() protoreflect.Message {
	mi := &file_retrieval_proto_msgTypes[36]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteIndexReq.ProtoReflect.Descriptor instead.
func (*DeleteIndexReq) Descriptor() ([]byte, []int) {
	return file_retrieval_proto_rawDescGZIP(), []int{36}
}

func (x *DeleteIndexReq) GetRobotId() uint64 {
	if x != nil {
		return x.RobotId
	}
	return 0
}

func (x *DeleteIndexReq) GetIndexId() uint64 {
	if x != nil {
		return x.IndexId
	}
	return 0
}

func (x *DeleteIndexReq) GetEmbeddingVersion() uint64 {
	if x != nil {
		return x.EmbeddingVersion
	}
	return 0
}

func (x *DeleteIndexReq) GetBotBizId() uint64 {
	if x != nil {
		return x.BotBizId
	}
	return 0
}

// 删除检索库响应
type DeleteIndexRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *DeleteIndexRsp) Reset() {
	*x = DeleteIndexRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_retrieval_proto_msgTypes[37]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteIndexRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteIndexRsp) ProtoMessage() {}

func (x *DeleteIndexRsp) ProtoReflect() protoreflect.Message {
	mi := &file_retrieval_proto_msgTypes[37]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteIndexRsp.ProtoReflect.Descriptor instead.
func (*DeleteIndexRsp) Descriptor() ([]byte, []int) {
	return file_retrieval_proto_rawDescGZIP(), []int{37}
}

// 增加特征请求
type AddVectorReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 机器人ID
	RobotId uint64 `protobuf:"varint,1,opt,name=robot_id,json=robotId,proto3" json:"robot_id,omitempty"`
	// 索引库ID, 业务自己定义保存
	IndexId uint64 `protobuf:"varint,2,opt,name=index_id,json=indexId,proto3" json:"index_id,omitempty"`
	// ID
	Id uint64 `protobuf:"varint,3,opt,name=id,proto3" json:"id,omitempty"`
	// 文档内容
	PageContent string `protobuf:"bytes,4,opt,name=page_content,json=pageContent,proto3" json:"page_content,omitempty"`
	// 文档类型 (1 QA, 2 文档段, 3 拒答问题, 4 搜索引擎)
	DocType uint32 `protobuf:"varint,5,opt,name=doc_type,json=docType,proto3" json:"doc_type,omitempty"`
	// embedding 版本
	EmbeddingVersion uint64 `protobuf:"varint,6,opt,name=embedding_version,json=embeddingVersion,proto3" json:"embedding_version,omitempty"`
	// labels 标签
	Labels []*VectorLabel `protobuf:"bytes,7,rep,name=labels,proto3" json:"labels,omitempty"`
	// 有效期，时间戳，秒。填0时不过期
	ExpireTime int64 `protobuf:"varint,8,opt,name=expire_time,json=expireTime,proto3" json:"expire_time,omitempty"`
	// 应用id
	BotBizId uint64 `protobuf:"varint,9,opt,name=bot_biz_id,json=botBizId,proto3" json:"bot_biz_id,omitempty"`
	// 知识库类型，区分离线知识库还是实时文档; embedding升级时调用AddVector需要区分
	Type KnowledgeType `protobuf:"varint,10,opt,name=type,proto3,enum=trpc.KEP.bot_retrieval_server.KnowledgeType" json:"type,omitempty"`
}

func (x *AddVectorReq) Reset() {
	*x = AddVectorReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_retrieval_proto_msgTypes[38]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AddVectorReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddVectorReq) ProtoMessage() {}

func (x *AddVectorReq) ProtoReflect() protoreflect.Message {
	mi := &file_retrieval_proto_msgTypes[38]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddVectorReq.ProtoReflect.Descriptor instead.
func (*AddVectorReq) Descriptor() ([]byte, []int) {
	return file_retrieval_proto_rawDescGZIP(), []int{38}
}

func (x *AddVectorReq) GetRobotId() uint64 {
	if x != nil {
		return x.RobotId
	}
	return 0
}

func (x *AddVectorReq) GetIndexId() uint64 {
	if x != nil {
		return x.IndexId
	}
	return 0
}

func (x *AddVectorReq) GetId() uint64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *AddVectorReq) GetPageContent() string {
	if x != nil {
		return x.PageContent
	}
	return ""
}

func (x *AddVectorReq) GetDocType() uint32 {
	if x != nil {
		return x.DocType
	}
	return 0
}

func (x *AddVectorReq) GetEmbeddingVersion() uint64 {
	if x != nil {
		return x.EmbeddingVersion
	}
	return 0
}

func (x *AddVectorReq) GetLabels() []*VectorLabel {
	if x != nil {
		return x.Labels
	}
	return nil
}

func (x *AddVectorReq) GetExpireTime() int64 {
	if x != nil {
		return x.ExpireTime
	}
	return 0
}

func (x *AddVectorReq) GetBotBizId() uint64 {
	if x != nil {
		return x.BotBizId
	}
	return 0
}

func (x *AddVectorReq) GetType() KnowledgeType {
	if x != nil {
		return x.Type
	}
	return KnowledgeType_KNOWLEDGE
}

// 增加特征响应
type AddVectorRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *AddVectorRsp) Reset() {
	*x = AddVectorRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_retrieval_proto_msgTypes[39]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AddVectorRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddVectorRsp) ProtoMessage() {}

func (x *AddVectorRsp) ProtoReflect() protoreflect.Message {
	mi := &file_retrieval_proto_msgTypes[39]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddVectorRsp.ProtoReflect.Descriptor instead.
func (*AddVectorRsp) Descriptor() ([]byte, []int) {
	return file_retrieval_proto_rawDescGZIP(), []int{39}
}

// 删除特征请求
type DeleteVectorReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 机器人ID
	RobotId uint64 `protobuf:"varint,1,opt,name=robot_id,json=robotId,proto3" json:"robot_id,omitempty"`
	// 索引库ID, 业务自己定义保存
	IndexId uint64 `protobuf:"varint,2,opt,name=index_id,json=indexId,proto3" json:"index_id,omitempty"`
	// ID
	Id uint64 `protobuf:"varint,3,opt,name=id,proto3" json:"id,omitempty"`
	// embedding 版本
	EmbeddingVersion uint64 `protobuf:"varint,4,opt,name=embedding_version,json=embeddingVersion,proto3" json:"embedding_version,omitempty"`
	// 应用id
	BotBizId uint64 `protobuf:"varint,5,opt,name=bot_biz_id,json=botBizId,proto3" json:"bot_biz_id,omitempty"`
}

func (x *DeleteVectorReq) Reset() {
	*x = DeleteVectorReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_retrieval_proto_msgTypes[40]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteVectorReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteVectorReq) ProtoMessage() {}

func (x *DeleteVectorReq) ProtoReflect() protoreflect.Message {
	mi := &file_retrieval_proto_msgTypes[40]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteVectorReq.ProtoReflect.Descriptor instead.
func (*DeleteVectorReq) Descriptor() ([]byte, []int) {
	return file_retrieval_proto_rawDescGZIP(), []int{40}
}

func (x *DeleteVectorReq) GetRobotId() uint64 {
	if x != nil {
		return x.RobotId
	}
	return 0
}

func (x *DeleteVectorReq) GetIndexId() uint64 {
	if x != nil {
		return x.IndexId
	}
	return 0
}

func (x *DeleteVectorReq) GetId() uint64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *DeleteVectorReq) GetEmbeddingVersion() uint64 {
	if x != nil {
		return x.EmbeddingVersion
	}
	return 0
}

func (x *DeleteVectorReq) GetBotBizId() uint64 {
	if x != nil {
		return x.BotBizId
	}
	return 0
}

// 删除特征响应
type DeleteVectorRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *DeleteVectorRsp) Reset() {
	*x = DeleteVectorRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_retrieval_proto_msgTypes[41]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteVectorRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteVectorRsp) ProtoMessage() {}

func (x *DeleteVectorRsp) ProtoReflect() protoreflect.Message {
	mi := &file_retrieval_proto_msgTypes[41]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteVectorRsp.ProtoReflect.Descriptor instead.
func (*DeleteVectorRsp) Descriptor() ([]byte, []int) {
	return file_retrieval_proto_rawDescGZIP(), []int{41}
}

// 修改特征请求
type UpdateVectorReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 机器人ID
	RobotId uint64 `protobuf:"varint,1,opt,name=robot_id,json=robotId,proto3" json:"robot_id,omitempty"`
	// 索引库ID, 业务自己定义保存
	IndexId uint64 `protobuf:"varint,2,opt,name=index_id,json=indexId,proto3" json:"index_id,omitempty"`
	// ID
	Id uint64 `protobuf:"varint,3,opt,name=id,proto3" json:"id,omitempty"`
	// 文档内容
	PageContent string `protobuf:"bytes,4,opt,name=page_content,json=pageContent,proto3" json:"page_content,omitempty"`
	// 文档类型 (1 QA, 2 文档段, 3 拒答问题, 4 搜索引擎)
	DocType uint32 `protobuf:"varint,5,opt,name=doc_type,json=docType,proto3" json:"doc_type,omitempty"`
	// embedding 版本
	EmbeddingVersion uint64 `protobuf:"varint,6,opt,name=embedding_version,json=embeddingVersion,proto3" json:"embedding_version,omitempty"`
	// labels 标签
	Labels []*VectorLabel `protobuf:"bytes,7,rep,name=labels,proto3" json:"labels,omitempty"`
	// 有效期，时间戳，秒。填0时不过期
	ExpireTime int64 `protobuf:"varint,8,opt,name=expire_time,json=expireTime,proto3" json:"expire_time,omitempty"`
	// 应用id
	BotBizId uint64 `protobuf:"varint,9,opt,name=bot_biz_id,json=botBizId,proto3" json:"bot_biz_id,omitempty"`
}

func (x *UpdateVectorReq) Reset() {
	*x = UpdateVectorReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_retrieval_proto_msgTypes[42]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateVectorReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateVectorReq) ProtoMessage() {}

func (x *UpdateVectorReq) ProtoReflect() protoreflect.Message {
	mi := &file_retrieval_proto_msgTypes[42]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateVectorReq.ProtoReflect.Descriptor instead.
func (*UpdateVectorReq) Descriptor() ([]byte, []int) {
	return file_retrieval_proto_rawDescGZIP(), []int{42}
}

func (x *UpdateVectorReq) GetRobotId() uint64 {
	if x != nil {
		return x.RobotId
	}
	return 0
}

func (x *UpdateVectorReq) GetIndexId() uint64 {
	if x != nil {
		return x.IndexId
	}
	return 0
}

func (x *UpdateVectorReq) GetId() uint64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *UpdateVectorReq) GetPageContent() string {
	if x != nil {
		return x.PageContent
	}
	return ""
}

func (x *UpdateVectorReq) GetDocType() uint32 {
	if x != nil {
		return x.DocType
	}
	return 0
}

func (x *UpdateVectorReq) GetEmbeddingVersion() uint64 {
	if x != nil {
		return x.EmbeddingVersion
	}
	return 0
}

func (x *UpdateVectorReq) GetLabels() []*VectorLabel {
	if x != nil {
		return x.Labels
	}
	return nil
}

func (x *UpdateVectorReq) GetExpireTime() int64 {
	if x != nil {
		return x.ExpireTime
	}
	return 0
}

func (x *UpdateVectorReq) GetBotBizId() uint64 {
	if x != nil {
		return x.BotBizId
	}
	return 0
}

// 修改特征响应
type UpdateVectorRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *UpdateVectorRsp) Reset() {
	*x = UpdateVectorRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_retrieval_proto_msgTypes[43]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateVectorRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateVectorRsp) ProtoMessage() {}

func (x *UpdateVectorRsp) ProtoReflect() protoreflect.Message {
	mi := &file_retrieval_proto_msgTypes[43]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateVectorRsp.ProtoReflect.Descriptor instead.
func (*UpdateVectorRsp) Descriptor() ([]byte, []int) {
	return file_retrieval_proto_rawDescGZIP(), []int{43}
}

// 搜索数据
type SearchData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 知识库自增ID(需与入库的RobotID保持一致)
	KnowledgeId uint64 `protobuf:"varint,1,opt,name=knowledge_id,json=knowledgeId,proto3" json:"knowledge_id,omitempty"`
	// 知识库业务ID
	KnowledgeBizId uint64          `protobuf:"varint,2,opt,name=knowledge_biz_id,json=knowledgeBizId,proto3" json:"knowledge_biz_id,omitempty"`
	Filters        []*SearchFilter `protobuf:"bytes,3,rep,name=filters,proto3" json:"filters,omitempty"`
	// embedding 版本 todo hjq 需要换成ID之类
	EmbeddingVersion uint64 `protobuf:"varint,4,opt,name=embedding_version,json=embeddingVersion,proto3" json:"embedding_version,omitempty"`
	// labels 标签
	Labels []*SearchVectorLabel `protobuf:"bytes,5,rep,name=labels,proto3" json:"labels,omitempty"`
	// 标签表达式
	LabelExpression *LabelExpression `protobuf:"bytes,6,opt,name=label_expression,json=labelExpression,proto3" json:"label_expression,omitempty"`
}

func (x *SearchData) Reset() {
	*x = SearchData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_retrieval_proto_msgTypes[44]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SearchData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SearchData) ProtoMessage() {}

func (x *SearchData) ProtoReflect() protoreflect.Message {
	mi := &file_retrieval_proto_msgTypes[44]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SearchData.ProtoReflect.Descriptor instead.
func (*SearchData) Descriptor() ([]byte, []int) {
	return file_retrieval_proto_rawDescGZIP(), []int{44}
}

func (x *SearchData) GetKnowledgeId() uint64 {
	if x != nil {
		return x.KnowledgeId
	}
	return 0
}

func (x *SearchData) GetKnowledgeBizId() uint64 {
	if x != nil {
		return x.KnowledgeBizId
	}
	return 0
}

func (x *SearchData) GetFilters() []*SearchFilter {
	if x != nil {
		return x.Filters
	}
	return nil
}

func (x *SearchData) GetEmbeddingVersion() uint64 {
	if x != nil {
		return x.EmbeddingVersion
	}
	return 0
}

func (x *SearchData) GetLabels() []*SearchVectorLabel {
	if x != nil {
		return x.Labels
	}
	return nil
}

func (x *SearchData) GetLabelExpression() *LabelExpression {
	if x != nil {
		return x.LabelExpression
	}
	return nil
}

type SearchFilter struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 索引库ID, 业务自己定义保存
	IndexId uint64 `protobuf:"varint,1,opt,name=index_id,json=indexId,proto3" json:"index_id,omitempty"`
	// 置信度
	Confidence float32 `protobuf:"fixed32,2,opt,name=confidence,proto3" json:"confidence,omitempty"`
	// 取 top_n
	TopN uint32 `protobuf:"varint,3,opt,name=top_n,json=topN,proto3" json:"top_n,omitempty"`
	// 文档类型 (1 QA, 2 文档段, 3 拒答问题, 4 搜索引擎)
	DocType uint32 `protobuf:"varint,4,opt,name=doc_type,json=docType,proto3" json:"doc_type,omitempty"`
	// 标签表达式字符串 key1="value1" and key2="value2"
	// 优先使用表达式字符串, 表达式字符串无值时使用表达式
	LabelExprString string `protobuf:"bytes,5,opt,name=label_expr_string,json=labelExprString,proto3" json:"label_expr_string,omitempty"`
	// 标签表达式
	LabelExpr *VectorLabelExpr `protobuf:"bytes,6,opt,name=label_expr,json=labelExpr,proto3" json:"label_expr,omitempty"`
}

func (x *SearchFilter) Reset() {
	*x = SearchFilter{}
	if protoimpl.UnsafeEnabled {
		mi := &file_retrieval_proto_msgTypes[45]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SearchFilter) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SearchFilter) ProtoMessage() {}

func (x *SearchFilter) ProtoReflect() protoreflect.Message {
	mi := &file_retrieval_proto_msgTypes[45]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SearchFilter.ProtoReflect.Descriptor instead.
func (*SearchFilter) Descriptor() ([]byte, []int) {
	return file_retrieval_proto_rawDescGZIP(), []int{45}
}

func (x *SearchFilter) GetIndexId() uint64 {
	if x != nil {
		return x.IndexId
	}
	return 0
}

func (x *SearchFilter) GetConfidence() float32 {
	if x != nil {
		return x.Confidence
	}
	return 0
}

func (x *SearchFilter) GetTopN() uint32 {
	if x != nil {
		return x.TopN
	}
	return 0
}

func (x *SearchFilter) GetDocType() uint32 {
	if x != nil {
		return x.DocType
	}
	return 0
}

func (x *SearchFilter) GetLabelExprString() string {
	if x != nil {
		return x.LabelExprString
	}
	return ""
}

func (x *SearchFilter) GetLabelExpr() *VectorLabelExpr {
	if x != nil {
		return x.LabelExpr
	}
	return nil
}

// 重排模型
type Rerank struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Model  string `protobuf:"bytes,1,opt,name=model,proto3" json:"model,omitempty"`            // 模型名称
	TopN   uint32 `protobuf:"varint,2,opt,name=top_n,json=topN,proto3" json:"top_n,omitempty"` // top_n
	Enable bool   `protobuf:"varint,3,opt,name=enable,proto3" json:"enable,omitempty"`         // 启用
}

func (x *Rerank) Reset() {
	*x = Rerank{}
	if protoimpl.UnsafeEnabled {
		mi := &file_retrieval_proto_msgTypes[46]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Rerank) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Rerank) ProtoMessage() {}

func (x *Rerank) ProtoReflect() protoreflect.Message {
	mi := &file_retrieval_proto_msgTypes[46]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Rerank.ProtoReflect.Descriptor instead.
func (*Rerank) Descriptor() ([]byte, []int) {
	return file_retrieval_proto_rawDescGZIP(), []int{46}
}

func (x *Rerank) GetModel() string {
	if x != nil {
		return x.Model
	}
	return ""
}

func (x *Rerank) GetTopN() uint32 {
	if x != nil {
		return x.TopN
	}
	return 0
}

func (x *Rerank) GetEnable() bool {
	if x != nil {
		return x.Enable
	}
	return false
}

// 多库检索请求
type SearchMultiKnowledgeReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 应用自增ID
	RobotId uint64 `protobuf:"varint,1,opt,name=robot_id,json=robotId,proto3" json:"robot_id,omitempty"`
	// 应用业务id
	BotBizId uint64 `protobuf:"varint,2,opt,name=bot_biz_id,json=botBizId,proto3" json:"bot_biz_id,omitempty"`
	// 问题
	Question string `protobuf:"bytes,3,opt,name=question,proto3" json:"question,omitempty"`
	// 请求query带图片URL，用于图搜图
	ImageUrls []string `protobuf:"bytes,4,rep,name=image_urls,json=imageUrls,proto3" json:"image_urls,omitempty"`
	// 拆解的子问题，为空表示没有子问题
	SubQuestions []string `protobuf:"bytes,5,rep,name=sub_questions,json=subQuestions,proto3" json:"sub_questions,omitempty"`
	// 取前 n 条 (默认3)
	TopN uint32 `protobuf:"varint,6,opt,name=top_n,json=topN,proto3" json:"top_n,omitempty"`
	// 重排模型
	Rerank *Rerank `protobuf:"bytes,7,opt,name=rerank,proto3" json:"rerank,omitempty"`
	// 过滤器名称
	FilterKey string `protobuf:"bytes,8,opt,name=filter_key,json=filterKey,proto3" json:"filter_key,omitempty"`
	// 知识检索策略配置
	SearchStrategy *SearchStrategy `protobuf:"bytes,9,opt,name=search_strategy,json=searchStrategy,proto3" json:"search_strategy,omitempty"`
	// 模型名称
	ModelName string `protobuf:"bytes,10,opt,name=model_name,json=modelName,proto3" json:"model_name,omitempty"`
	// 搜索数据
	SearchData []*SearchData `protobuf:"bytes,11,rep,name=search_data,json=searchData,proto3" json:"search_data,omitempty"`
}

func (x *SearchMultiKnowledgeReq) Reset() {
	*x = SearchMultiKnowledgeReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_retrieval_proto_msgTypes[47]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SearchMultiKnowledgeReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SearchMultiKnowledgeReq) ProtoMessage() {}

func (x *SearchMultiKnowledgeReq) ProtoReflect() protoreflect.Message {
	mi := &file_retrieval_proto_msgTypes[47]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SearchMultiKnowledgeReq.ProtoReflect.Descriptor instead.
func (*SearchMultiKnowledgeReq) Descriptor() ([]byte, []int) {
	return file_retrieval_proto_rawDescGZIP(), []int{47}
}

func (x *SearchMultiKnowledgeReq) GetRobotId() uint64 {
	if x != nil {
		return x.RobotId
	}
	return 0
}

func (x *SearchMultiKnowledgeReq) GetBotBizId() uint64 {
	if x != nil {
		return x.BotBizId
	}
	return 0
}

func (x *SearchMultiKnowledgeReq) GetQuestion() string {
	if x != nil {
		return x.Question
	}
	return ""
}

func (x *SearchMultiKnowledgeReq) GetImageUrls() []string {
	if x != nil {
		return x.ImageUrls
	}
	return nil
}

func (x *SearchMultiKnowledgeReq) GetSubQuestions() []string {
	if x != nil {
		return x.SubQuestions
	}
	return nil
}

func (x *SearchMultiKnowledgeReq) GetTopN() uint32 {
	if x != nil {
		return x.TopN
	}
	return 0
}

func (x *SearchMultiKnowledgeReq) GetRerank() *Rerank {
	if x != nil {
		return x.Rerank
	}
	return nil
}

func (x *SearchMultiKnowledgeReq) GetFilterKey() string {
	if x != nil {
		return x.FilterKey
	}
	return ""
}

func (x *SearchMultiKnowledgeReq) GetSearchStrategy() *SearchStrategy {
	if x != nil {
		return x.SearchStrategy
	}
	return nil
}

func (x *SearchMultiKnowledgeReq) GetModelName() string {
	if x != nil {
		return x.ModelName
	}
	return ""
}

func (x *SearchMultiKnowledgeReq) GetSearchData() []*SearchData {
	if x != nil {
		return x.SearchData
	}
	return nil
}

// 问题查询请求
type SearchVectorReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 机器人ID
	RobotId uint64 `protobuf:"varint,1,opt,name=robot_id,json=robotId,proto3" json:"robot_id,omitempty"`
	// 问题
	Question string                    `protobuf:"bytes,2,opt,name=question,proto3" json:"question,omitempty"`
	Filters  []*SearchVectorReq_Filter `protobuf:"bytes,3,rep,name=filters,proto3" json:"filters,omitempty"`
	// 取前 n 条 (默认3)
	TopN uint32 `protobuf:"varint,4,opt,name=top_n,json=topN,proto3" json:"top_n,omitempty"`
	// embedding 版本
	EmbeddingVersion uint64                  `protobuf:"varint,5,opt,name=embedding_version,json=embeddingVersion,proto3" json:"embedding_version,omitempty"`
	Rerank           *SearchVectorReq_Rerank `protobuf:"bytes,7,opt,name=rerank,proto3" json:"rerank,omitempty"`
	// 过滤器名称
	FilterKey string `protobuf:"bytes,8,opt,name=filter_key,json=filterKey,proto3" json:"filter_key,omitempty"`
	// labels 标签
	Labels []*SearchVectorLabel `protobuf:"bytes,9,rep,name=labels,proto3" json:"labels,omitempty"`
	// 请求query带图片URL，用于图搜图
	ImageUrls []string `protobuf:"bytes,10,rep,name=image_urls,json=imageUrls,proto3" json:"image_urls,omitempty"`
	// 应用id
	BotBizId uint64 `protobuf:"varint,11,opt,name=bot_biz_id,json=botBizId,proto3" json:"bot_biz_id,omitempty"`
	// 标签表达式
	LabelExpression *LabelExpression `protobuf:"bytes,12,opt,name=label_expression,json=labelExpression,proto3" json:"label_expression,omitempty"`
	// 拆解的子问题，为空表示没有子问题
	SubQuestions []string `protobuf:"bytes,13,rep,name=sub_questions,json=subQuestions,proto3" json:"sub_questions,omitempty"`
	// 知识检索策略配置
	SearchStrategy *SearchStrategy `protobuf:"bytes,14,opt,name=search_strategy,json=searchStrategy,proto3" json:"search_strategy,omitempty"`
	// 模型名称
	ModelName string `protobuf:"bytes,15,opt,name=model_name,json=modelName,proto3" json:"model_name,omitempty"`
}

func (x *SearchVectorReq) Reset() {
	*x = SearchVectorReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_retrieval_proto_msgTypes[48]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SearchVectorReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SearchVectorReq) ProtoMessage() {}

func (x *SearchVectorReq) ProtoReflect() protoreflect.Message {
	mi := &file_retrieval_proto_msgTypes[48]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SearchVectorReq.ProtoReflect.Descriptor instead.
func (*SearchVectorReq) Descriptor() ([]byte, []int) {
	return file_retrieval_proto_rawDescGZIP(), []int{48}
}

func (x *SearchVectorReq) GetRobotId() uint64 {
	if x != nil {
		return x.RobotId
	}
	return 0
}

func (x *SearchVectorReq) GetQuestion() string {
	if x != nil {
		return x.Question
	}
	return ""
}

func (x *SearchVectorReq) GetFilters() []*SearchVectorReq_Filter {
	if x != nil {
		return x.Filters
	}
	return nil
}

func (x *SearchVectorReq) GetTopN() uint32 {
	if x != nil {
		return x.TopN
	}
	return 0
}

func (x *SearchVectorReq) GetEmbeddingVersion() uint64 {
	if x != nil {
		return x.EmbeddingVersion
	}
	return 0
}

func (x *SearchVectorReq) GetRerank() *SearchVectorReq_Rerank {
	if x != nil {
		return x.Rerank
	}
	return nil
}

func (x *SearchVectorReq) GetFilterKey() string {
	if x != nil {
		return x.FilterKey
	}
	return ""
}

func (x *SearchVectorReq) GetLabels() []*SearchVectorLabel {
	if x != nil {
		return x.Labels
	}
	return nil
}

func (x *SearchVectorReq) GetImageUrls() []string {
	if x != nil {
		return x.ImageUrls
	}
	return nil
}

func (x *SearchVectorReq) GetBotBizId() uint64 {
	if x != nil {
		return x.BotBizId
	}
	return 0
}

func (x *SearchVectorReq) GetLabelExpression() *LabelExpression {
	if x != nil {
		return x.LabelExpression
	}
	return nil
}

func (x *SearchVectorReq) GetSubQuestions() []string {
	if x != nil {
		return x.SubQuestions
	}
	return nil
}

func (x *SearchVectorReq) GetSearchStrategy() *SearchStrategy {
	if x != nil {
		return x.SearchStrategy
	}
	return nil
}

func (x *SearchVectorReq) GetModelName() string {
	if x != nil {
		return x.ModelName
	}
	return ""
}

// 问题查询响应
type SearchVectorRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 文档数据
	Docs []*SearchVectorRsp_Doc `protobuf:"bytes,1,rep,name=docs,proto3" json:"docs,omitempty"`
}

func (x *SearchVectorRsp) Reset() {
	*x = SearchVectorRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_retrieval_proto_msgTypes[49]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SearchVectorRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SearchVectorRsp) ProtoMessage() {}

func (x *SearchVectorRsp) ProtoReflect() protoreflect.Message {
	mi := &file_retrieval_proto_msgTypes[49]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SearchVectorRsp.ProtoReflect.Descriptor instead.
func (*SearchVectorRsp) Descriptor() ([]byte, []int) {
	return file_retrieval_proto_rawDescGZIP(), []int{49}
}

func (x *SearchVectorRsp) GetDocs() []*SearchVectorRsp_Doc {
	if x != nil {
		return x.Docs
	}
	return nil
}

// 增加检索库请求
type DirectCreateIndexReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 检索库名称
	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	// 索引库ID, 业务自己定义保存
	IndexId uint64 `protobuf:"varint,2,opt,name=index_id,json=indexId,proto3" json:"index_id,omitempty"`
	// embedding 版本
	EmbeddingVersion uint64 `protobuf:"varint,3,opt,name=embedding_version,json=embeddingVersion,proto3" json:"embedding_version,omitempty"`
	// 文档类型 (1 QA, 2 文档段, 3 拒答问题, 4 搜索引擎)
	DocType uint32 `protobuf:"varint,4,opt,name=doc_type,json=docType,proto3" json:"doc_type,omitempty"`
}

func (x *DirectCreateIndexReq) Reset() {
	*x = DirectCreateIndexReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_retrieval_proto_msgTypes[50]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DirectCreateIndexReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DirectCreateIndexReq) ProtoMessage() {}

func (x *DirectCreateIndexReq) ProtoReflect() protoreflect.Message {
	mi := &file_retrieval_proto_msgTypes[50]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DirectCreateIndexReq.ProtoReflect.Descriptor instead.
func (*DirectCreateIndexReq) Descriptor() ([]byte, []int) {
	return file_retrieval_proto_rawDescGZIP(), []int{50}
}

func (x *DirectCreateIndexReq) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *DirectCreateIndexReq) GetIndexId() uint64 {
	if x != nil {
		return x.IndexId
	}
	return 0
}

func (x *DirectCreateIndexReq) GetEmbeddingVersion() uint64 {
	if x != nil {
		return x.EmbeddingVersion
	}
	return 0
}

func (x *DirectCreateIndexReq) GetDocType() uint32 {
	if x != nil {
		return x.DocType
	}
	return 0
}

// 增加检索库响应
type DirectCreateIndexRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *DirectCreateIndexRsp) Reset() {
	*x = DirectCreateIndexRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_retrieval_proto_msgTypes[51]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DirectCreateIndexRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DirectCreateIndexRsp) ProtoMessage() {}

func (x *DirectCreateIndexRsp) ProtoReflect() protoreflect.Message {
	mi := &file_retrieval_proto_msgTypes[51]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DirectCreateIndexRsp.ProtoReflect.Descriptor instead.
func (*DirectCreateIndexRsp) Descriptor() ([]byte, []int) {
	return file_retrieval_proto_rawDescGZIP(), []int{51}
}

// 删除检索库请求
type DirectDeleteIndexReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 检索库名称
	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	// 索引库ID, 业务自己定义保存
	IndexId uint64 `protobuf:"varint,2,opt,name=index_id,json=indexId,proto3" json:"index_id,omitempty"`
	// embedding 版本
	EmbeddingVersion uint64 `protobuf:"varint,3,opt,name=embedding_version,json=embeddingVersion,proto3" json:"embedding_version,omitempty"`
}

func (x *DirectDeleteIndexReq) Reset() {
	*x = DirectDeleteIndexReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_retrieval_proto_msgTypes[52]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DirectDeleteIndexReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DirectDeleteIndexReq) ProtoMessage() {}

func (x *DirectDeleteIndexReq) ProtoReflect() protoreflect.Message {
	mi := &file_retrieval_proto_msgTypes[52]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DirectDeleteIndexReq.ProtoReflect.Descriptor instead.
func (*DirectDeleteIndexReq) Descriptor() ([]byte, []int) {
	return file_retrieval_proto_rawDescGZIP(), []int{52}
}

func (x *DirectDeleteIndexReq) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *DirectDeleteIndexReq) GetIndexId() uint64 {
	if x != nil {
		return x.IndexId
	}
	return 0
}

func (x *DirectDeleteIndexReq) GetEmbeddingVersion() uint64 {
	if x != nil {
		return x.EmbeddingVersion
	}
	return 0
}

// 删除检索库响应
type DirectDeleteIndexRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *DirectDeleteIndexRsp) Reset() {
	*x = DirectDeleteIndexRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_retrieval_proto_msgTypes[53]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DirectDeleteIndexRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DirectDeleteIndexRsp) ProtoMessage() {}

func (x *DirectDeleteIndexRsp) ProtoReflect() protoreflect.Message {
	mi := &file_retrieval_proto_msgTypes[53]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DirectDeleteIndexRsp.ProtoReflect.Descriptor instead.
func (*DirectDeleteIndexRsp) Descriptor() ([]byte, []int) {
	return file_retrieval_proto_rawDescGZIP(), []int{53}
}

// 增加特征请求
type DirectAddVectorReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 检索库名称
	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	// 索引库ID, 业务自己定义保存
	IndexId uint64 `protobuf:"varint,2,opt,name=index_id,json=indexId,proto3" json:"index_id,omitempty"`
	// ID
	Id uint64 `protobuf:"varint,3,opt,name=id,proto3" json:"id,omitempty"`
	// 文档内容
	PageContent string `protobuf:"bytes,4,opt,name=page_content,json=pageContent,proto3" json:"page_content,omitempty"`
	// 文档类型 (1 QA, 2 文档段, 3 拒答问题, 4 搜索引擎)
	DocType uint32 `protobuf:"varint,5,opt,name=doc_type,json=docType,proto3" json:"doc_type,omitempty"`
	// embedding 版本
	EmbeddingVersion uint64 `protobuf:"varint,6,opt,name=embedding_version,json=embeddingVersion,proto3" json:"embedding_version,omitempty"`
	// labels 标签
	Labels []*VectorLabel `protobuf:"bytes,7,rep,name=labels,proto3" json:"labels,omitempty"`
	// 有效期，时间戳，秒。填0时不过期
	ExpireTime int64 `protobuf:"varint,8,opt,name=expire_time,json=expireTime,proto3" json:"expire_time,omitempty"`
}

func (x *DirectAddVectorReq) Reset() {
	*x = DirectAddVectorReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_retrieval_proto_msgTypes[54]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DirectAddVectorReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DirectAddVectorReq) ProtoMessage() {}

func (x *DirectAddVectorReq) ProtoReflect() protoreflect.Message {
	mi := &file_retrieval_proto_msgTypes[54]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DirectAddVectorReq.ProtoReflect.Descriptor instead.
func (*DirectAddVectorReq) Descriptor() ([]byte, []int) {
	return file_retrieval_proto_rawDescGZIP(), []int{54}
}

func (x *DirectAddVectorReq) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *DirectAddVectorReq) GetIndexId() uint64 {
	if x != nil {
		return x.IndexId
	}
	return 0
}

func (x *DirectAddVectorReq) GetId() uint64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *DirectAddVectorReq) GetPageContent() string {
	if x != nil {
		return x.PageContent
	}
	return ""
}

func (x *DirectAddVectorReq) GetDocType() uint32 {
	if x != nil {
		return x.DocType
	}
	return 0
}

func (x *DirectAddVectorReq) GetEmbeddingVersion() uint64 {
	if x != nil {
		return x.EmbeddingVersion
	}
	return 0
}

func (x *DirectAddVectorReq) GetLabels() []*VectorLabel {
	if x != nil {
		return x.Labels
	}
	return nil
}

func (x *DirectAddVectorReq) GetExpireTime() int64 {
	if x != nil {
		return x.ExpireTime
	}
	return 0
}

// 增加特征响应
type DirectAddVectorRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *DirectAddVectorRsp) Reset() {
	*x = DirectAddVectorRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_retrieval_proto_msgTypes[55]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DirectAddVectorRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DirectAddVectorRsp) ProtoMessage() {}

func (x *DirectAddVectorRsp) ProtoReflect() protoreflect.Message {
	mi := &file_retrieval_proto_msgTypes[55]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DirectAddVectorRsp.ProtoReflect.Descriptor instead.
func (*DirectAddVectorRsp) Descriptor() ([]byte, []int) {
	return file_retrieval_proto_rawDescGZIP(), []int{55}
}

// 删除特征请求
type DirectDeleteVectorReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 检索库名称
	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	// 索引库ID, 业务自己定义保存
	IndexId uint64 `protobuf:"varint,2,opt,name=index_id,json=indexId,proto3" json:"index_id,omitempty"`
	// ID
	Id uint64 `protobuf:"varint,3,opt,name=id,proto3" json:"id,omitempty"`
	// embedding 版本
	EmbeddingVersion uint64 `protobuf:"varint,4,opt,name=embedding_version,json=embeddingVersion,proto3" json:"embedding_version,omitempty"`
}

func (x *DirectDeleteVectorReq) Reset() {
	*x = DirectDeleteVectorReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_retrieval_proto_msgTypes[56]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DirectDeleteVectorReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DirectDeleteVectorReq) ProtoMessage() {}

func (x *DirectDeleteVectorReq) ProtoReflect() protoreflect.Message {
	mi := &file_retrieval_proto_msgTypes[56]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DirectDeleteVectorReq.ProtoReflect.Descriptor instead.
func (*DirectDeleteVectorReq) Descriptor() ([]byte, []int) {
	return file_retrieval_proto_rawDescGZIP(), []int{56}
}

func (x *DirectDeleteVectorReq) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *DirectDeleteVectorReq) GetIndexId() uint64 {
	if x != nil {
		return x.IndexId
	}
	return 0
}

func (x *DirectDeleteVectorReq) GetId() uint64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *DirectDeleteVectorReq) GetEmbeddingVersion() uint64 {
	if x != nil {
		return x.EmbeddingVersion
	}
	return 0
}

// 删除特征响应
type DirectDeleteVectorRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *DirectDeleteVectorRsp) Reset() {
	*x = DirectDeleteVectorRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_retrieval_proto_msgTypes[57]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DirectDeleteVectorRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DirectDeleteVectorRsp) ProtoMessage() {}

func (x *DirectDeleteVectorRsp) ProtoReflect() protoreflect.Message {
	mi := &file_retrieval_proto_msgTypes[57]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DirectDeleteVectorRsp.ProtoReflect.Descriptor instead.
func (*DirectDeleteVectorRsp) Descriptor() ([]byte, []int) {
	return file_retrieval_proto_rawDescGZIP(), []int{57}
}

// 修改特征请求
type DirectUpdateVectorReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 检索库名称
	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	// 索引库ID, 业务自己定义保存
	IndexId uint64 `protobuf:"varint,2,opt,name=index_id,json=indexId,proto3" json:"index_id,omitempty"`
	// ID
	Id uint64 `protobuf:"varint,3,opt,name=id,proto3" json:"id,omitempty"`
	// 文档内容
	PageContent string `protobuf:"bytes,4,opt,name=page_content,json=pageContent,proto3" json:"page_content,omitempty"`
	// 文档类型 (1 QA, 2 文档段, 3 拒答问题, 4 搜索引擎)
	DocType uint32 `protobuf:"varint,5,opt,name=doc_type,json=docType,proto3" json:"doc_type,omitempty"`
	// embedding 版本
	EmbeddingVersion uint64 `protobuf:"varint,6,opt,name=embedding_version,json=embeddingVersion,proto3" json:"embedding_version,omitempty"`
	// labels 标签
	Labels []*VectorLabel `protobuf:"bytes,7,rep,name=labels,proto3" json:"labels,omitempty"`
	// 有效期，时间戳，秒。填0时不过期
	ExpireTime int64 `protobuf:"varint,8,opt,name=expire_time,json=expireTime,proto3" json:"expire_time,omitempty"`
}

func (x *DirectUpdateVectorReq) Reset() {
	*x = DirectUpdateVectorReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_retrieval_proto_msgTypes[58]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DirectUpdateVectorReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DirectUpdateVectorReq) ProtoMessage() {}

func (x *DirectUpdateVectorReq) ProtoReflect() protoreflect.Message {
	mi := &file_retrieval_proto_msgTypes[58]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DirectUpdateVectorReq.ProtoReflect.Descriptor instead.
func (*DirectUpdateVectorReq) Descriptor() ([]byte, []int) {
	return file_retrieval_proto_rawDescGZIP(), []int{58}
}

func (x *DirectUpdateVectorReq) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *DirectUpdateVectorReq) GetIndexId() uint64 {
	if x != nil {
		return x.IndexId
	}
	return 0
}

func (x *DirectUpdateVectorReq) GetId() uint64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *DirectUpdateVectorReq) GetPageContent() string {
	if x != nil {
		return x.PageContent
	}
	return ""
}

func (x *DirectUpdateVectorReq) GetDocType() uint32 {
	if x != nil {
		return x.DocType
	}
	return 0
}

func (x *DirectUpdateVectorReq) GetEmbeddingVersion() uint64 {
	if x != nil {
		return x.EmbeddingVersion
	}
	return 0
}

func (x *DirectUpdateVectorReq) GetLabels() []*VectorLabel {
	if x != nil {
		return x.Labels
	}
	return nil
}

func (x *DirectUpdateVectorReq) GetExpireTime() int64 {
	if x != nil {
		return x.ExpireTime
	}
	return 0
}

// 修改特征响应
type DirectUpdateVectorRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *DirectUpdateVectorRsp) Reset() {
	*x = DirectUpdateVectorRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_retrieval_proto_msgTypes[59]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DirectUpdateVectorRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DirectUpdateVectorRsp) ProtoMessage() {}

func (x *DirectUpdateVectorRsp) ProtoReflect() protoreflect.Message {
	mi := &file_retrieval_proto_msgTypes[59]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DirectUpdateVectorRsp.ProtoReflect.Descriptor instead.
func (*DirectUpdateVectorRsp) Descriptor() ([]byte, []int) {
	return file_retrieval_proto_rawDescGZIP(), []int{59}
}

// 问题查询请求
type DirectSearchVectorReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 检索库名称
	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	// 问题
	Question string                          `protobuf:"bytes,2,opt,name=question,proto3" json:"question,omitempty"`
	Filters  []*DirectSearchVectorReq_Filter `protobuf:"bytes,3,rep,name=filters,proto3" json:"filters,omitempty"`
	// 取前 n 条 (默认3)
	TopN uint32 `protobuf:"varint,4,opt,name=top_n,json=topN,proto3" json:"top_n,omitempty"`
	// embedding 版本
	EmbeddingVersion uint64                        `protobuf:"varint,5,opt,name=embedding_version,json=embeddingVersion,proto3" json:"embedding_version,omitempty"`
	Rerank           *DirectSearchVectorReq_Rerank `protobuf:"bytes,7,opt,name=rerank,proto3" json:"rerank,omitempty"`
}

func (x *DirectSearchVectorReq) Reset() {
	*x = DirectSearchVectorReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_retrieval_proto_msgTypes[60]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DirectSearchVectorReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DirectSearchVectorReq) ProtoMessage() {}

func (x *DirectSearchVectorReq) ProtoReflect() protoreflect.Message {
	mi := &file_retrieval_proto_msgTypes[60]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DirectSearchVectorReq.ProtoReflect.Descriptor instead.
func (*DirectSearchVectorReq) Descriptor() ([]byte, []int) {
	return file_retrieval_proto_rawDescGZIP(), []int{60}
}

func (x *DirectSearchVectorReq) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *DirectSearchVectorReq) GetQuestion() string {
	if x != nil {
		return x.Question
	}
	return ""
}

func (x *DirectSearchVectorReq) GetFilters() []*DirectSearchVectorReq_Filter {
	if x != nil {
		return x.Filters
	}
	return nil
}

func (x *DirectSearchVectorReq) GetTopN() uint32 {
	if x != nil {
		return x.TopN
	}
	return 0
}

func (x *DirectSearchVectorReq) GetEmbeddingVersion() uint64 {
	if x != nil {
		return x.EmbeddingVersion
	}
	return 0
}

func (x *DirectSearchVectorReq) GetRerank() *DirectSearchVectorReq_Rerank {
	if x != nil {
		return x.Rerank
	}
	return nil
}

// 问题查询响应
type DirectSearchVectorRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 文档数据
	Docs []*DirectSearchVectorRsp_Doc `protobuf:"bytes,1,rep,name=docs,proto3" json:"docs,omitempty"`
}

func (x *DirectSearchVectorRsp) Reset() {
	*x = DirectSearchVectorRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_retrieval_proto_msgTypes[61]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DirectSearchVectorRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DirectSearchVectorRsp) ProtoMessage() {}

func (x *DirectSearchVectorRsp) ProtoReflect() protoreflect.Message {
	mi := &file_retrieval_proto_msgTypes[61]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DirectSearchVectorRsp.ProtoReflect.Descriptor instead.
func (*DirectSearchVectorRsp) Descriptor() ([]byte, []int) {
	return file_retrieval_proto_rawDescGZIP(), []int{61}
}

func (x *DirectSearchVectorRsp) GetDocs() []*DirectSearchVectorRsp_Doc {
	if x != nil {
		return x.Docs
	}
	return nil
}

// 添加Bigdata到ES的请求
// es的index 在vector维护
type AddBigDataElasticReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Data []*BigData `protobuf:"bytes,1,rep,name=data,proto3" json:"data,omitempty"`
	// 知识库类型，区分离线知识库还是实时文档
	Type KnowledgeType `protobuf:"varint,2,opt,name=type,proto3,enum=trpc.KEP.bot_retrieval_server.KnowledgeType" json:"type,omitempty"`
}

func (x *AddBigDataElasticReq) Reset() {
	*x = AddBigDataElasticReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_retrieval_proto_msgTypes[62]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AddBigDataElasticReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddBigDataElasticReq) ProtoMessage() {}

func (x *AddBigDataElasticReq) ProtoReflect() protoreflect.Message {
	mi := &file_retrieval_proto_msgTypes[62]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddBigDataElasticReq.ProtoReflect.Descriptor instead.
func (*AddBigDataElasticReq) Descriptor() ([]byte, []int) {
	return file_retrieval_proto_rawDescGZIP(), []int{62}
}

func (x *AddBigDataElasticReq) GetData() []*BigData {
	if x != nil {
		return x.Data
	}
	return nil
}

func (x *AddBigDataElasticReq) GetType() KnowledgeType {
	if x != nil {
		return x.Type
	}
	return KnowledgeType_KNOWLEDGE
}

type BigData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 机器人ID
	RobotId uint64 `protobuf:"varint,1,opt,name=robot_id,json=robotId,proto3" json:"robot_id,omitempty"`
	// 业务的文档ID
	DocId uint64 `protobuf:"varint,2,opt,name=doc_id,json=docId,proto3" json:"doc_id,omitempty"`
	// BigData的ID
	BigDataId string `protobuf:"bytes,3,opt,name=big_data_id,json=bigDataId,proto3" json:"big_data_id,omitempty"`
	// BigData 分片起始索引
	BigStart int32 `protobuf:"varint,4,opt,name=BigStart,proto3" json:"BigStart,omitempty"`
	// BigData 分片结束索引
	BigEnd int32 `protobuf:"varint,5,opt,name=BigEnd,proto3" json:"BigEnd,omitempty"`
	// BigData的内容
	BigString string `protobuf:"bytes,6,opt,name=BigString,proto3" json:"BigString,omitempty"`
}

func (x *BigData) Reset() {
	*x = BigData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_retrieval_proto_msgTypes[63]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BigData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BigData) ProtoMessage() {}

func (x *BigData) ProtoReflect() protoreflect.Message {
	mi := &file_retrieval_proto_msgTypes[63]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BigData.ProtoReflect.Descriptor instead.
func (*BigData) Descriptor() ([]byte, []int) {
	return file_retrieval_proto_rawDescGZIP(), []int{63}
}

func (x *BigData) GetRobotId() uint64 {
	if x != nil {
		return x.RobotId
	}
	return 0
}

func (x *BigData) GetDocId() uint64 {
	if x != nil {
		return x.DocId
	}
	return 0
}

func (x *BigData) GetBigDataId() string {
	if x != nil {
		return x.BigDataId
	}
	return ""
}

func (x *BigData) GetBigStart() int32 {
	if x != nil {
		return x.BigStart
	}
	return 0
}

func (x *BigData) GetBigEnd() int32 {
	if x != nil {
		return x.BigEnd
	}
	return 0
}

func (x *BigData) GetBigString() string {
	if x != nil {
		return x.BigString
	}
	return ""
}

// BatchGetBigDataESByRobotBigDataIDReq 根据robot_id, big_data_id 从bigDataEs中查找数据
type BatchGetBigDataESByRobotBigDataIDReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 机器人ID
	RobotId uint64 `protobuf:"varint,1,opt,name=robot_id,json=robotId,proto3" json:"robot_id,omitempty"`
	// BigData的ID
	BigDataIds []string `protobuf:"bytes,2,rep,name=big_data_ids,json=bigDataIds,proto3" json:"big_data_ids,omitempty"`
	// 知识库类型，区分离线知识库还是实时文档
	Type KnowledgeType `protobuf:"varint,3,opt,name=type,proto3,enum=trpc.KEP.bot_retrieval_server.KnowledgeType" json:"type,omitempty"`
}

func (x *BatchGetBigDataESByRobotBigDataIDReq) Reset() {
	*x = BatchGetBigDataESByRobotBigDataIDReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_retrieval_proto_msgTypes[64]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BatchGetBigDataESByRobotBigDataIDReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BatchGetBigDataESByRobotBigDataIDReq) ProtoMessage() {}

func (x *BatchGetBigDataESByRobotBigDataIDReq) ProtoReflect() protoreflect.Message {
	mi := &file_retrieval_proto_msgTypes[64]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BatchGetBigDataESByRobotBigDataIDReq.ProtoReflect.Descriptor instead.
func (*BatchGetBigDataESByRobotBigDataIDReq) Descriptor() ([]byte, []int) {
	return file_retrieval_proto_rawDescGZIP(), []int{64}
}

func (x *BatchGetBigDataESByRobotBigDataIDReq) GetRobotId() uint64 {
	if x != nil {
		return x.RobotId
	}
	return 0
}

func (x *BatchGetBigDataESByRobotBigDataIDReq) GetBigDataIds() []string {
	if x != nil {
		return x.BigDataIds
	}
	return nil
}

func (x *BatchGetBigDataESByRobotBigDataIDReq) GetType() KnowledgeType {
	if x != nil {
		return x.Type
	}
	return KnowledgeType_KNOWLEDGE
}

// BatchGetBigDataESByRobotBigDataIDResp 根据robot_id,  big_data_id 从bigDataEs中查找数据响应
type BatchGetBigDataESByRobotBigDataIDResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Data []*BigData `protobuf:"bytes,1,rep,name=data,proto3" json:"data,omitempty"`
}

func (x *BatchGetBigDataESByRobotBigDataIDResp) Reset() {
	*x = BatchGetBigDataESByRobotBigDataIDResp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_retrieval_proto_msgTypes[65]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BatchGetBigDataESByRobotBigDataIDResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BatchGetBigDataESByRobotBigDataIDResp) ProtoMessage() {}

func (x *BatchGetBigDataESByRobotBigDataIDResp) ProtoReflect() protoreflect.Message {
	mi := &file_retrieval_proto_msgTypes[65]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BatchGetBigDataESByRobotBigDataIDResp.ProtoReflect.Descriptor instead.
func (*BatchGetBigDataESByRobotBigDataIDResp) Descriptor() ([]byte, []int) {
	return file_retrieval_proto_rawDescGZIP(), []int{65}
}

func (x *BatchGetBigDataESByRobotBigDataIDResp) GetData() []*BigData {
	if x != nil {
		return x.Data
	}
	return nil
}

// 添加Bigdata到ES的响应
type AddBigDataElasticRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *AddBigDataElasticRsp) Reset() {
	*x = AddBigDataElasticRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_retrieval_proto_msgTypes[66]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AddBigDataElasticRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddBigDataElasticRsp) ProtoMessage() {}

func (x *AddBigDataElasticRsp) ProtoReflect() protoreflect.Message {
	mi := &file_retrieval_proto_msgTypes[66]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddBigDataElasticRsp.ProtoReflect.Descriptor instead.
func (*AddBigDataElasticRsp) Descriptor() ([]byte, []int) {
	return file_retrieval_proto_rawDescGZIP(), []int{66}
}

// 从ES删除Bigdata的请求
type DeleteBigDataElasticReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 机器人ID
	RobotId uint64 `protobuf:"varint,1,opt,name=robot_id,json=robotId,proto3" json:"robot_id,omitempty"`
	// 业务的文档ID
	DocId uint64 `protobuf:"varint,2,opt,name=doc_id,json=docId,proto3" json:"doc_id,omitempty"`
	// 知识库类型，区分离线知识库还是实时文档
	Type KnowledgeType `protobuf:"varint,3,opt,name=type,proto3,enum=trpc.KEP.bot_retrieval_server.KnowledgeType" json:"type,omitempty"`
	// 是否硬删除，默认否，只有在文档删除场景才硬删除
	HardDelete bool `protobuf:"varint,4,opt,name=hard_delete,json=hardDelete,proto3" json:"hard_delete,omitempty"`
}

func (x *DeleteBigDataElasticReq) Reset() {
	*x = DeleteBigDataElasticReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_retrieval_proto_msgTypes[67]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteBigDataElasticReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteBigDataElasticReq) ProtoMessage() {}

func (x *DeleteBigDataElasticReq) ProtoReflect() protoreflect.Message {
	mi := &file_retrieval_proto_msgTypes[67]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteBigDataElasticReq.ProtoReflect.Descriptor instead.
func (*DeleteBigDataElasticReq) Descriptor() ([]byte, []int) {
	return file_retrieval_proto_rawDescGZIP(), []int{67}
}

func (x *DeleteBigDataElasticReq) GetRobotId() uint64 {
	if x != nil {
		return x.RobotId
	}
	return 0
}

func (x *DeleteBigDataElasticReq) GetDocId() uint64 {
	if x != nil {
		return x.DocId
	}
	return 0
}

func (x *DeleteBigDataElasticReq) GetType() KnowledgeType {
	if x != nil {
		return x.Type
	}
	return KnowledgeType_KNOWLEDGE
}

func (x *DeleteBigDataElasticReq) GetHardDelete() bool {
	if x != nil {
		return x.HardDelete
	}
	return false
}

// 从ES删除Bigdata响应
type DeleteBigDataElasticRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *DeleteBigDataElasticRsp) Reset() {
	*x = DeleteBigDataElasticRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_retrieval_proto_msgTypes[68]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteBigDataElasticRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteBigDataElasticRsp) ProtoMessage() {}

func (x *DeleteBigDataElasticRsp) ProtoReflect() protoreflect.Message {
	mi := &file_retrieval_proto_msgTypes[68]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteBigDataElasticRsp.ProtoReflect.Descriptor instead.
func (*DeleteBigDataElasticRsp) Descriptor() ([]byte, []int) {
	return file_retrieval_proto_rawDescGZIP(), []int{68}
}

// 从ES恢复离线知识库的BigData的请求
type RecoverBigDataElasticReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 机器人ID
	RobotId uint64 `protobuf:"varint,1,opt,name=robot_id,json=robotId,proto3" json:"robot_id,omitempty"`
	// 业务的文档ID
	DocId uint64 `protobuf:"varint,2,opt,name=doc_id,json=docId,proto3" json:"doc_id,omitempty"`
}

func (x *RecoverBigDataElasticReq) Reset() {
	*x = RecoverBigDataElasticReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_retrieval_proto_msgTypes[69]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RecoverBigDataElasticReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RecoverBigDataElasticReq) ProtoMessage() {}

func (x *RecoverBigDataElasticReq) ProtoReflect() protoreflect.Message {
	mi := &file_retrieval_proto_msgTypes[69]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RecoverBigDataElasticReq.ProtoReflect.Descriptor instead.
func (*RecoverBigDataElasticReq) Descriptor() ([]byte, []int) {
	return file_retrieval_proto_rawDescGZIP(), []int{69}
}

func (x *RecoverBigDataElasticReq) GetRobotId() uint64 {
	if x != nil {
		return x.RobotId
	}
	return 0
}

func (x *RecoverBigDataElasticReq) GetDocId() uint64 {
	if x != nil {
		return x.DocId
	}
	return 0
}

// 从ES恢复离线知识库的BigData的响应
type RecoverBigDataElasticRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *RecoverBigDataElasticRsp) Reset() {
	*x = RecoverBigDataElasticRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_retrieval_proto_msgTypes[70]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RecoverBigDataElasticRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RecoverBigDataElasticRsp) ProtoMessage() {}

func (x *RecoverBigDataElasticRsp) ProtoReflect() protoreflect.Message {
	mi := &file_retrieval_proto_msgTypes[70]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RecoverBigDataElasticRsp.ProtoReflect.Descriptor instead.
func (*RecoverBigDataElasticRsp) Descriptor() ([]byte, []int) {
	return file_retrieval_proto_rawDescGZIP(), []int{70}
}

// 增加知识请求
type AddKnowledgeReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 机器人ID
	RobotId uint64 `protobuf:"varint,1,opt,name=robot_id,json=robotId,proto3" json:"robot_id,omitempty"`
	// 索引库ID, 业务自己定义保存
	IndexId uint64 `protobuf:"varint,2,opt,name=index_id,json=indexId,proto3" json:"index_id,omitempty"`
	// ID
	Id uint64 `protobuf:"varint,3,opt,name=id,proto3" json:"id,omitempty"`
	// 文档类型 (1 QA, 2 文档或者表格, 3 拒答问题, 4 搜索引擎)
	DocType uint32 `protobuf:"varint,4,opt,name=doc_type,json=docType,proto3" json:"doc_type,omitempty"`
	// 文档切片类型，segment-文档切片 table-表格，当doc_type=2时需要填写
	SegmentType string `protobuf:"bytes,5,opt,name=segment_type,json=segmentType,proto3" json:"segment_type,omitempty"`
	// 文档id，如果是文档则是对应的文档id，如果是qa，则用关联的文档id，即t_doc_qa表里的doc_id字段，没有则为0
	DocId uint64 `protobuf:"varint,6,opt,name=doc_id,json=docId,proto3" json:"doc_id,omitempty"`
	// 文档内容
	PageContent string `protobuf:"bytes,7,opt,name=page_content,json=pageContent,proto3" json:"page_content,omitempty"`
	// embedding 版本
	EmbeddingVersion uint64 `protobuf:"varint,8,opt,name=embedding_version,json=embeddingVersion,proto3" json:"embedding_version,omitempty"`
	// labels 标签
	Labels []*VectorLabel `protobuf:"bytes,9,rep,name=labels,proto3" json:"labels,omitempty"`
	// 有效期，时间戳，秒。填0时不过期
	ExpireTime int64 `protobuf:"varint,10,opt,name=expire_time,json=expireTime,proto3" json:"expire_time,omitempty"`
	// 应用id
	BotBizId uint64 `protobuf:"varint,11,opt,name=bot_biz_id,json=botBizId,proto3" json:"bot_biz_id,omitempty"`
}

func (x *AddKnowledgeReq) Reset() {
	*x = AddKnowledgeReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_retrieval_proto_msgTypes[71]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AddKnowledgeReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddKnowledgeReq) ProtoMessage() {}

func (x *AddKnowledgeReq) ProtoReflect() protoreflect.Message {
	mi := &file_retrieval_proto_msgTypes[71]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddKnowledgeReq.ProtoReflect.Descriptor instead.
func (*AddKnowledgeReq) Descriptor() ([]byte, []int) {
	return file_retrieval_proto_rawDescGZIP(), []int{71}
}

func (x *AddKnowledgeReq) GetRobotId() uint64 {
	if x != nil {
		return x.RobotId
	}
	return 0
}

func (x *AddKnowledgeReq) GetIndexId() uint64 {
	if x != nil {
		return x.IndexId
	}
	return 0
}

func (x *AddKnowledgeReq) GetId() uint64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *AddKnowledgeReq) GetDocType() uint32 {
	if x != nil {
		return x.DocType
	}
	return 0
}

func (x *AddKnowledgeReq) GetSegmentType() string {
	if x != nil {
		return x.SegmentType
	}
	return ""
}

func (x *AddKnowledgeReq) GetDocId() uint64 {
	if x != nil {
		return x.DocId
	}
	return 0
}

func (x *AddKnowledgeReq) GetPageContent() string {
	if x != nil {
		return x.PageContent
	}
	return ""
}

func (x *AddKnowledgeReq) GetEmbeddingVersion() uint64 {
	if x != nil {
		return x.EmbeddingVersion
	}
	return 0
}

func (x *AddKnowledgeReq) GetLabels() []*VectorLabel {
	if x != nil {
		return x.Labels
	}
	return nil
}

func (x *AddKnowledgeReq) GetExpireTime() int64 {
	if x != nil {
		return x.ExpireTime
	}
	return 0
}

func (x *AddKnowledgeReq) GetBotBizId() uint64 {
	if x != nil {
		return x.BotBizId
	}
	return 0
}

// 增加知识响应
type AddKnowledgeRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *AddKnowledgeRsp) Reset() {
	*x = AddKnowledgeRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_retrieval_proto_msgTypes[72]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AddKnowledgeRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddKnowledgeRsp) ProtoMessage() {}

func (x *AddKnowledgeRsp) ProtoReflect() protoreflect.Message {
	mi := &file_retrieval_proto_msgTypes[72]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddKnowledgeRsp.ProtoReflect.Descriptor instead.
func (*AddKnowledgeRsp) Descriptor() ([]byte, []int) {
	return file_retrieval_proto_rawDescGZIP(), []int{72}
}

// 批量增加知识请求
type BatchAddKnowledgeReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 机器人ID
	RobotId uint64 `protobuf:"varint,1,opt,name=robot_id,json=robotId,proto3" json:"robot_id,omitempty"`
	// 索引库ID, 业务自己定义保存
	IndexId uint64 `protobuf:"varint,2,opt,name=index_id,json=indexId,proto3" json:"index_id,omitempty"`
	// 文档类型 (1 QA, 2 文档或者表格, 3 拒答问题, 4 搜索引擎)
	DocType uint32 `protobuf:"varint,3,opt,name=doc_type,json=docType,proto3" json:"doc_type,omitempty"`
	// embedding 版本
	EmbeddingVersion uint64 `protobuf:"varint,4,opt,name=embedding_version,json=embeddingVersion,proto3" json:"embedding_version,omitempty"`
	// 文档切片数组 支持批量
	Knowledge []*KnowledgeData `protobuf:"bytes,5,rep,name=knowledge,proto3" json:"knowledge,omitempty"`
	// 应用id
	BotBizId uint64 `protobuf:"varint,6,opt,name=bot_biz_id,json=botBizId,proto3" json:"bot_biz_id,omitempty"`
}

func (x *BatchAddKnowledgeReq) Reset() {
	*x = BatchAddKnowledgeReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_retrieval_proto_msgTypes[73]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BatchAddKnowledgeReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BatchAddKnowledgeReq) ProtoMessage() {}

func (x *BatchAddKnowledgeReq) ProtoReflect() protoreflect.Message {
	mi := &file_retrieval_proto_msgTypes[73]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BatchAddKnowledgeReq.ProtoReflect.Descriptor instead.
func (*BatchAddKnowledgeReq) Descriptor() ([]byte, []int) {
	return file_retrieval_proto_rawDescGZIP(), []int{73}
}

func (x *BatchAddKnowledgeReq) GetRobotId() uint64 {
	if x != nil {
		return x.RobotId
	}
	return 0
}

func (x *BatchAddKnowledgeReq) GetIndexId() uint64 {
	if x != nil {
		return x.IndexId
	}
	return 0
}

func (x *BatchAddKnowledgeReq) GetDocType() uint32 {
	if x != nil {
		return x.DocType
	}
	return 0
}

func (x *BatchAddKnowledgeReq) GetEmbeddingVersion() uint64 {
	if x != nil {
		return x.EmbeddingVersion
	}
	return 0
}

func (x *BatchAddKnowledgeReq) GetKnowledge() []*KnowledgeData {
	if x != nil {
		return x.Knowledge
	}
	return nil
}

func (x *BatchAddKnowledgeReq) GetBotBizId() uint64 {
	if x != nil {
		return x.BotBizId
	}
	return 0
}

// 批量增加知识响应
type BatchAddKnowledgeRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *BatchAddKnowledgeRsp) Reset() {
	*x = BatchAddKnowledgeRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_retrieval_proto_msgTypes[74]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BatchAddKnowledgeRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BatchAddKnowledgeRsp) ProtoMessage() {}

func (x *BatchAddKnowledgeRsp) ProtoReflect() protoreflect.Message {
	mi := &file_retrieval_proto_msgTypes[74]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BatchAddKnowledgeRsp.ProtoReflect.Descriptor instead.
func (*BatchAddKnowledgeRsp) Descriptor() ([]byte, []int) {
	return file_retrieval_proto_rawDescGZIP(), []int{74}
}

// 删除知识请求
type DeleteKnowledgeReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 机器人ID
	RobotId uint64 `protobuf:"varint,1,opt,name=robot_id,json=robotId,proto3" json:"robot_id,omitempty"`
	// 索引库ID, 业务自己定义保存
	IndexId uint64 `protobuf:"varint,2,opt,name=index_id,json=indexId,proto3" json:"index_id,omitempty"`
	// ID
	Id uint64 `protobuf:"varint,3,opt,name=id,proto3" json:"id,omitempty"`
	// 文档类型 (1 QA, 2 文档或者表格, 3 拒答问题, 4 搜索引擎)
	DocType uint32 `protobuf:"varint,4,opt,name=doc_type,json=docType,proto3" json:"doc_type,omitempty"`
	// 文档切片类型，segment-文档切片 table-表格，当doc_type=2时需要填写
	SegmentType string `protobuf:"bytes,5,opt,name=segment_type,json=segmentType,proto3" json:"segment_type,omitempty"`
	// embedding 版本
	EmbeddingVersion uint64 `protobuf:"varint,6,opt,name=embedding_version,json=embeddingVersion,proto3" json:"embedding_version,omitempty"`
	// 应用id
	BotBizId uint64 `protobuf:"varint,7,opt,name=bot_biz_id,json=botBizId,proto3" json:"bot_biz_id,omitempty"`
}

func (x *DeleteKnowledgeReq) Reset() {
	*x = DeleteKnowledgeReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_retrieval_proto_msgTypes[75]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteKnowledgeReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteKnowledgeReq) ProtoMessage() {}

func (x *DeleteKnowledgeReq) ProtoReflect() protoreflect.Message {
	mi := &file_retrieval_proto_msgTypes[75]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteKnowledgeReq.ProtoReflect.Descriptor instead.
func (*DeleteKnowledgeReq) Descriptor() ([]byte, []int) {
	return file_retrieval_proto_rawDescGZIP(), []int{75}
}

func (x *DeleteKnowledgeReq) GetRobotId() uint64 {
	if x != nil {
		return x.RobotId
	}
	return 0
}

func (x *DeleteKnowledgeReq) GetIndexId() uint64 {
	if x != nil {
		return x.IndexId
	}
	return 0
}

func (x *DeleteKnowledgeReq) GetId() uint64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *DeleteKnowledgeReq) GetDocType() uint32 {
	if x != nil {
		return x.DocType
	}
	return 0
}

func (x *DeleteKnowledgeReq) GetSegmentType() string {
	if x != nil {
		return x.SegmentType
	}
	return ""
}

func (x *DeleteKnowledgeReq) GetEmbeddingVersion() uint64 {
	if x != nil {
		return x.EmbeddingVersion
	}
	return 0
}

func (x *DeleteKnowledgeReq) GetBotBizId() uint64 {
	if x != nil {
		return x.BotBizId
	}
	return 0
}

// 删除知识响应
type DeleteKnowledgeRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *DeleteKnowledgeRsp) Reset() {
	*x = DeleteKnowledgeRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_retrieval_proto_msgTypes[76]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteKnowledgeRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteKnowledgeRsp) ProtoMessage() {}

func (x *DeleteKnowledgeRsp) ProtoReflect() protoreflect.Message {
	mi := &file_retrieval_proto_msgTypes[76]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteKnowledgeRsp.ProtoReflect.Descriptor instead.
func (*DeleteKnowledgeRsp) Descriptor() ([]byte, []int) {
	return file_retrieval_proto_rawDescGZIP(), []int{76}
}

// 批量删除知识请求
type BatchDeleteKnowledgeReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 机器人ID
	RobotId uint64 `protobuf:"varint,1,opt,name=robot_id,json=robotId,proto3" json:"robot_id,omitempty"`
	// 索引库ID, 业务自己定义保存
	IndexId uint64 `protobuf:"varint,2,opt,name=index_id,json=indexId,proto3" json:"index_id,omitempty"`
	// 要删除的数据，文档片段ID和对应的切片类型
	Data []*KnowledgeIDType `protobuf:"bytes,3,rep,name=data,proto3" json:"data,omitempty"`
	// 文档类型 (1 QA, 2 文档或者表格, 3 拒答问题, 4 搜索引擎)
	DocType uint32 `protobuf:"varint,4,opt,name=doc_type,json=docType,proto3" json:"doc_type,omitempty"`
	// embedding 版本
	EmbeddingVersion uint64 `protobuf:"varint,5,opt,name=embedding_version,json=embeddingVersion,proto3" json:"embedding_version,omitempty"`
	// 应用id
	BotBizId uint64 `protobuf:"varint,6,opt,name=bot_biz_id,json=botBizId,proto3" json:"bot_biz_id,omitempty"`
}

func (x *BatchDeleteKnowledgeReq) Reset() {
	*x = BatchDeleteKnowledgeReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_retrieval_proto_msgTypes[77]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BatchDeleteKnowledgeReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BatchDeleteKnowledgeReq) ProtoMessage() {}

func (x *BatchDeleteKnowledgeReq) ProtoReflect() protoreflect.Message {
	mi := &file_retrieval_proto_msgTypes[77]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BatchDeleteKnowledgeReq.ProtoReflect.Descriptor instead.
func (*BatchDeleteKnowledgeReq) Descriptor() ([]byte, []int) {
	return file_retrieval_proto_rawDescGZIP(), []int{77}
}

func (x *BatchDeleteKnowledgeReq) GetRobotId() uint64 {
	if x != nil {
		return x.RobotId
	}
	return 0
}

func (x *BatchDeleteKnowledgeReq) GetIndexId() uint64 {
	if x != nil {
		return x.IndexId
	}
	return 0
}

func (x *BatchDeleteKnowledgeReq) GetData() []*KnowledgeIDType {
	if x != nil {
		return x.Data
	}
	return nil
}

func (x *BatchDeleteKnowledgeReq) GetDocType() uint32 {
	if x != nil {
		return x.DocType
	}
	return 0
}

func (x *BatchDeleteKnowledgeReq) GetEmbeddingVersion() uint64 {
	if x != nil {
		return x.EmbeddingVersion
	}
	return 0
}

func (x *BatchDeleteKnowledgeReq) GetBotBizId() uint64 {
	if x != nil {
		return x.BotBizId
	}
	return 0
}

// 批量删除知识响应
type BatchDeleteKnowledgeRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *BatchDeleteKnowledgeRsp) Reset() {
	*x = BatchDeleteKnowledgeRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_retrieval_proto_msgTypes[78]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BatchDeleteKnowledgeRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BatchDeleteKnowledgeRsp) ProtoMessage() {}

func (x *BatchDeleteKnowledgeRsp) ProtoReflect() protoreflect.Message {
	mi := &file_retrieval_proto_msgTypes[78]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BatchDeleteKnowledgeRsp.ProtoReflect.Descriptor instead.
func (*BatchDeleteKnowledgeRsp) Descriptor() ([]byte, []int) {
	return file_retrieval_proto_rawDescGZIP(), []int{78}
}

// 批量删除发布库的所有知识（包括QA/文档/混合检索/text2sql等）请求
type BatchDeleteAllKnowledgeProdReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 机器人ID
	RobotId uint64 `protobuf:"varint,1,opt,name=robot_id,json=robotId,proto3" json:"robot_id,omitempty"`
	// 文档库版本
	VersionId uint64 `protobuf:"varint,2,opt,name=version_id,json=versionId,proto3" json:"version_id,omitempty"`
	// 要删除的数据，文档片段ID和对应的切片类型
	Data []*KnowledgeIDType `protobuf:"bytes,3,rep,name=data,proto3" json:"data,omitempty"`
	// 文档类型 (1 QA, 2 文档或者表格, 3 拒答问题, 4 搜索引擎)
	DocType uint32 `protobuf:"varint,4,opt,name=doc_type,json=docType,proto3" json:"doc_type,omitempty"`
	// 应用id
	BotBizId uint64 `protobuf:"varint,5,opt,name=bot_biz_id,json=botBizId,proto3" json:"bot_biz_id,omitempty"`
	// 文档id，如果是文档则是对应的文档id，如果是qa，则用关联的文档id，即t_doc_qa表里的doc_id字段，没有则为0
	DocId uint64 `protobuf:"varint,6,opt,name=doc_id,json=docId,proto3" json:"doc_id,omitempty"`
}

func (x *BatchDeleteAllKnowledgeProdReq) Reset() {
	*x = BatchDeleteAllKnowledgeProdReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_retrieval_proto_msgTypes[79]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BatchDeleteAllKnowledgeProdReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BatchDeleteAllKnowledgeProdReq) ProtoMessage() {}

func (x *BatchDeleteAllKnowledgeProdReq) ProtoReflect() protoreflect.Message {
	mi := &file_retrieval_proto_msgTypes[79]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BatchDeleteAllKnowledgeProdReq.ProtoReflect.Descriptor instead.
func (*BatchDeleteAllKnowledgeProdReq) Descriptor() ([]byte, []int) {
	return file_retrieval_proto_rawDescGZIP(), []int{79}
}

func (x *BatchDeleteAllKnowledgeProdReq) GetRobotId() uint64 {
	if x != nil {
		return x.RobotId
	}
	return 0
}

func (x *BatchDeleteAllKnowledgeProdReq) GetVersionId() uint64 {
	if x != nil {
		return x.VersionId
	}
	return 0
}

func (x *BatchDeleteAllKnowledgeProdReq) GetData() []*KnowledgeIDType {
	if x != nil {
		return x.Data
	}
	return nil
}

func (x *BatchDeleteAllKnowledgeProdReq) GetDocType() uint32 {
	if x != nil {
		return x.DocType
	}
	return 0
}

func (x *BatchDeleteAllKnowledgeProdReq) GetBotBizId() uint64 {
	if x != nil {
		return x.BotBizId
	}
	return 0
}

func (x *BatchDeleteAllKnowledgeProdReq) GetDocId() uint64 {
	if x != nil {
		return x.DocId
	}
	return 0
}

// 批量删除发布库的知识响应
type BatchDeleteAllKnowledgeProdRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *BatchDeleteAllKnowledgeProdRsp) Reset() {
	*x = BatchDeleteAllKnowledgeProdRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_retrieval_proto_msgTypes[80]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BatchDeleteAllKnowledgeProdRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BatchDeleteAllKnowledgeProdRsp) ProtoMessage() {}

func (x *BatchDeleteAllKnowledgeProdRsp) ProtoReflect() protoreflect.Message {
	mi := &file_retrieval_proto_msgTypes[80]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BatchDeleteAllKnowledgeProdRsp.ProtoReflect.Descriptor instead.
func (*BatchDeleteAllKnowledgeProdRsp) Descriptor() ([]byte, []int) {
	return file_retrieval_proto_rawDescGZIP(), []int{80}
}

// 修改知识请求
type UpdateKnowledgeReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 机器人ID
	RobotId uint64 `protobuf:"varint,1,opt,name=robot_id,json=robotId,proto3" json:"robot_id,omitempty"`
	// 索引库ID, 业务自己定义保存
	IndexId uint64 `protobuf:"varint,2,opt,name=index_id,json=indexId,proto3" json:"index_id,omitempty"`
	// ID
	Id uint64 `protobuf:"varint,3,opt,name=id,proto3" json:"id,omitempty"`
	// 文档类型 (1 QA, 2 文档或者表格, 3 拒答问题, 4 搜索引擎)
	DocType uint32 `protobuf:"varint,4,opt,name=doc_type,json=docType,proto3" json:"doc_type,omitempty"`
	// 文档切片类型，segment-文档切片 table-表格，当doc_type=2时需要填写
	SegmentType string `protobuf:"bytes,5,opt,name=segment_type,json=segmentType,proto3" json:"segment_type,omitempty"`
	// 文档内容
	PageContent string `protobuf:"bytes,6,opt,name=page_content,json=pageContent,proto3" json:"page_content,omitempty"`
	// embedding 版本
	EmbeddingVersion uint64 `protobuf:"varint,7,opt,name=embedding_version,json=embeddingVersion,proto3" json:"embedding_version,omitempty"`
	// labels 标签
	Labels []*VectorLabel `protobuf:"bytes,8,rep,name=labels,proto3" json:"labels,omitempty"`
	// 有效期，时间戳，秒。填0时不过期
	ExpireTime int64 `protobuf:"varint,9,opt,name=expire_time,json=expireTime,proto3" json:"expire_time,omitempty"`
	// 文档id，如果是文档则是对应的文档id，如果是qa，则用关联的文档id，即t_doc_qa表里的doc_id字段，没有则为0
	DocId uint64 `protobuf:"varint,10,opt,name=doc_id,json=docId,proto3" json:"doc_id,omitempty"`
	// 应用id
	BotBizId uint64 `protobuf:"varint,11,opt,name=bot_biz_id,json=botBizId,proto3" json:"bot_biz_id,omitempty"`
}

func (x *UpdateKnowledgeReq) Reset() {
	*x = UpdateKnowledgeReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_retrieval_proto_msgTypes[81]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateKnowledgeReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateKnowledgeReq) ProtoMessage() {}

func (x *UpdateKnowledgeReq) ProtoReflect() protoreflect.Message {
	mi := &file_retrieval_proto_msgTypes[81]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateKnowledgeReq.ProtoReflect.Descriptor instead.
func (*UpdateKnowledgeReq) Descriptor() ([]byte, []int) {
	return file_retrieval_proto_rawDescGZIP(), []int{81}
}

func (x *UpdateKnowledgeReq) GetRobotId() uint64 {
	if x != nil {
		return x.RobotId
	}
	return 0
}

func (x *UpdateKnowledgeReq) GetIndexId() uint64 {
	if x != nil {
		return x.IndexId
	}
	return 0
}

func (x *UpdateKnowledgeReq) GetId() uint64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *UpdateKnowledgeReq) GetDocType() uint32 {
	if x != nil {
		return x.DocType
	}
	return 0
}

func (x *UpdateKnowledgeReq) GetSegmentType() string {
	if x != nil {
		return x.SegmentType
	}
	return ""
}

func (x *UpdateKnowledgeReq) GetPageContent() string {
	if x != nil {
		return x.PageContent
	}
	return ""
}

func (x *UpdateKnowledgeReq) GetEmbeddingVersion() uint64 {
	if x != nil {
		return x.EmbeddingVersion
	}
	return 0
}

func (x *UpdateKnowledgeReq) GetLabels() []*VectorLabel {
	if x != nil {
		return x.Labels
	}
	return nil
}

func (x *UpdateKnowledgeReq) GetExpireTime() int64 {
	if x != nil {
		return x.ExpireTime
	}
	return 0
}

func (x *UpdateKnowledgeReq) GetDocId() uint64 {
	if x != nil {
		return x.DocId
	}
	return 0
}

func (x *UpdateKnowledgeReq) GetBotBizId() uint64 {
	if x != nil {
		return x.BotBizId
	}
	return 0
}

// 修改知识响应
type UpdateKnowledgeRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *UpdateKnowledgeRsp) Reset() {
	*x = UpdateKnowledgeRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_retrieval_proto_msgTypes[82]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateKnowledgeRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateKnowledgeRsp) ProtoMessage() {}

func (x *UpdateKnowledgeRsp) ProtoReflect() protoreflect.Message {
	mi := &file_retrieval_proto_msgTypes[82]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateKnowledgeRsp.ProtoReflect.Descriptor instead.
func (*UpdateKnowledgeRsp) Descriptor() ([]byte, []int) {
	return file_retrieval_proto_rawDescGZIP(), []int{82}
}

// 单元格
type Cell struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Value        string       `protobuf:"bytes,1,opt,name=value,proto3" json:"value,omitempty"`
	CellDataType CellDataType `protobuf:"varint,2,opt,name=cell_data_type,json=cellDataType,proto3,enum=trpc.KEP.bot_retrieval_server.CellDataType" json:"cell_data_type,omitempty"`
}

func (x *Cell) Reset() {
	*x = Cell{}
	if protoimpl.UnsafeEnabled {
		mi := &file_retrieval_proto_msgTypes[83]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Cell) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Cell) ProtoMessage() {}

func (x *Cell) ProtoReflect() protoreflect.Message {
	mi := &file_retrieval_proto_msgTypes[83]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Cell.ProtoReflect.Descriptor instead.
func (*Cell) Descriptor() ([]byte, []int) {
	return file_retrieval_proto_rawDescGZIP(), []int{83}
}

func (x *Cell) GetValue() string {
	if x != nil {
		return x.Value
	}
	return ""
}

func (x *Cell) GetCellDataType() CellDataType {
	if x != nil {
		return x.CellDataType
	}
	return CellDataType_DATA_TYPE_STRING
}

// 行
type Row struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Cells []*Cell `protobuf:"bytes,1,rep,name=cells,proto3" json:"cells,omitempty"`
}

func (x *Row) Reset() {
	*x = Row{}
	if protoimpl.UnsafeEnabled {
		mi := &file_retrieval_proto_msgTypes[84]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Row) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Row) ProtoMessage() {}

func (x *Row) ProtoReflect() protoreflect.Message {
	mi := &file_retrieval_proto_msgTypes[84]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Row.ProtoReflect.Descriptor instead.
func (*Row) Descriptor() ([]byte, []int) {
	return file_retrieval_proto_rawDescGZIP(), []int{84}
}

func (x *Row) GetCells() []*Cell {
	if x != nil {
		return x.Cells
	}
	return nil
}

// text2sql meta信息
type Text2SQLMeta struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// table_id：对于excel表格，就是sheet id
	TableId string `protobuf:"bytes,1,opt,name=table_id,json=tableId,proto3" json:"table_id,omitempty"`
	// table_name：对于excel表格，就是sheet名
	TableName string `protobuf:"bytes,2,opt,name=table_name,json=tableName,proto3" json:"table_name,omitempty"`
	// 文档类型 (2 文档或者表格)
	DocType uint32 `protobuf:"varint,3,opt,name=doc_type,json=docType,proto3" json:"doc_type,omitempty"`
	// 表头
	Headers []*Text2SQLMeta_Header `protobuf:"bytes,4,rep,name=headers,proto3" json:"headers,omitempty"` // header取index 0
}

func (x *Text2SQLMeta) Reset() {
	*x = Text2SQLMeta{}
	if protoimpl.UnsafeEnabled {
		mi := &file_retrieval_proto_msgTypes[85]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Text2SQLMeta) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Text2SQLMeta) ProtoMessage() {}

func (x *Text2SQLMeta) ProtoReflect() protoreflect.Message {
	mi := &file_retrieval_proto_msgTypes[85]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Text2SQLMeta.ProtoReflect.Descriptor instead.
func (*Text2SQLMeta) Descriptor() ([]byte, []int) {
	return file_retrieval_proto_rawDescGZIP(), []int{85}
}

func (x *Text2SQLMeta) GetTableId() string {
	if x != nil {
		return x.TableId
	}
	return ""
}

func (x *Text2SQLMeta) GetTableName() string {
	if x != nil {
		return x.TableName
	}
	return ""
}

func (x *Text2SQLMeta) GetDocType() uint32 {
	if x != nil {
		return x.DocType
	}
	return 0
}

func (x *Text2SQLMeta) GetHeaders() []*Text2SQLMeta_Header {
	if x != nil {
		return x.Headers
	}
	return nil
}

// test2sql的表格某一行的data
type Text2SQLRowData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 分片ID
	Id uint64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// 文档切片类型，"text2sql_content"
	SegmentType string `protobuf:"bytes,2,opt,name=segment_type,json=segmentType,proto3" json:"segment_type,omitempty"`
	// 某个row对应的所有单元格
	Row *Row `protobuf:"bytes,3,opt,name=row,proto3" json:"row,omitempty"`
}

func (x *Text2SQLRowData) Reset() {
	*x = Text2SQLRowData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_retrieval_proto_msgTypes[86]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Text2SQLRowData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Text2SQLRowData) ProtoMessage() {}

func (x *Text2SQLRowData) ProtoReflect() protoreflect.Message {
	mi := &file_retrieval_proto_msgTypes[86]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Text2SQLRowData.ProtoReflect.Descriptor instead.
func (*Text2SQLRowData) Descriptor() ([]byte, []int) {
	return file_retrieval_proto_rawDescGZIP(), []int{86}
}

func (x *Text2SQLRowData) GetId() uint64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *Text2SQLRowData) GetSegmentType() string {
	if x != nil {
		return x.SegmentType
	}
	return ""
}

func (x *Text2SQLRowData) GetRow() *Row {
	if x != nil {
		return x.Row
	}
	return nil
}

// text2sql更新的请求
type AddText2SQLReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 机器人ID
	RobotId uint64 `protobuf:"varint,1,opt,name=robot_id,json=robotId,proto3" json:"robot_id,omitempty"`
	// 整个文档ID
	DocId uint64 `protobuf:"varint,2,opt,name=doc_id,json=docId,proto3" json:"doc_id,omitempty"`
	// meta信息
	Meta *Text2SQLMeta `protobuf:"bytes,3,opt,name=meta,proto3" json:"meta,omitempty"`
	// 文档内容
	Rows []*Text2SQLRowData `protobuf:"bytes,4,rep,name=rows,proto3" json:"rows,omitempty"`
	// labels 标签
	Labels []*VectorLabel `protobuf:"bytes,5,rep,name=labels,proto3" json:"labels,omitempty"`
	// 有效期，时间戳，秒。填0时不过期
	ExpireTime int64 `protobuf:"varint,6,opt,name=expire_time,json=expireTime,proto3" json:"expire_time,omitempty"`
	// 文档的名称
	FileName string `protobuf:"bytes,7,opt,name=file_name,json=fileName,proto3" json:"file_name,omitempty"`
	// 企业ID
	CorpId uint64 `protobuf:"varint,8,opt,name=corp_id,json=corpId,proto3" json:"corp_id,omitempty"`
}

func (x *AddText2SQLReq) Reset() {
	*x = AddText2SQLReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_retrieval_proto_msgTypes[87]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AddText2SQLReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddText2SQLReq) ProtoMessage() {}

func (x *AddText2SQLReq) ProtoReflect() protoreflect.Message {
	mi := &file_retrieval_proto_msgTypes[87]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddText2SQLReq.ProtoReflect.Descriptor instead.
func (*AddText2SQLReq) Descriptor() ([]byte, []int) {
	return file_retrieval_proto_rawDescGZIP(), []int{87}
}

func (x *AddText2SQLReq) GetRobotId() uint64 {
	if x != nil {
		return x.RobotId
	}
	return 0
}

func (x *AddText2SQLReq) GetDocId() uint64 {
	if x != nil {
		return x.DocId
	}
	return 0
}

func (x *AddText2SQLReq) GetMeta() *Text2SQLMeta {
	if x != nil {
		return x.Meta
	}
	return nil
}

func (x *AddText2SQLReq) GetRows() []*Text2SQLRowData {
	if x != nil {
		return x.Rows
	}
	return nil
}

func (x *AddText2SQLReq) GetLabels() []*VectorLabel {
	if x != nil {
		return x.Labels
	}
	return nil
}

func (x *AddText2SQLReq) GetExpireTime() int64 {
	if x != nil {
		return x.ExpireTime
	}
	return 0
}

func (x *AddText2SQLReq) GetFileName() string {
	if x != nil {
		return x.FileName
	}
	return ""
}

func (x *AddText2SQLReq) GetCorpId() uint64 {
	if x != nil {
		return x.CorpId
	}
	return 0
}

// text2sql更新的响应
type AddText2SQLRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *AddText2SQLRsp) Reset() {
	*x = AddText2SQLRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_retrieval_proto_msgTypes[88]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AddText2SQLRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddText2SQLRsp) ProtoMessage() {}

func (x *AddText2SQLRsp) ProtoReflect() protoreflect.Message {
	mi := &file_retrieval_proto_msgTypes[88]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddText2SQLRsp.ProtoReflect.Descriptor instead.
func (*AddText2SQLRsp) Descriptor() ([]byte, []int) {
	return file_retrieval_proto_rawDescGZIP(), []int{88}
}

// text2sql删除的请求
type DeleteText2SQLReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 机器人ID
	RobotId uint64 `protobuf:"varint,1,opt,name=robot_id,json=robotId,proto3" json:"robot_id,omitempty"`
	// 文档ID
	DocId uint64 `protobuf:"varint,2,opt,name=doc_id,json=docId,proto3" json:"doc_id,omitempty"`
	// 文档切片类型，"text2sql_content"
	SegmentType string `protobuf:"bytes,3,opt,name=segment_type,json=segmentType,proto3" json:"segment_type,omitempty"`
}

func (x *DeleteText2SQLReq) Reset() {
	*x = DeleteText2SQLReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_retrieval_proto_msgTypes[89]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteText2SQLReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteText2SQLReq) ProtoMessage() {}

func (x *DeleteText2SQLReq) ProtoReflect() protoreflect.Message {
	mi := &file_retrieval_proto_msgTypes[89]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteText2SQLReq.ProtoReflect.Descriptor instead.
func (*DeleteText2SQLReq) Descriptor() ([]byte, []int) {
	return file_retrieval_proto_rawDescGZIP(), []int{89}
}

func (x *DeleteText2SQLReq) GetRobotId() uint64 {
	if x != nil {
		return x.RobotId
	}
	return 0
}

func (x *DeleteText2SQLReq) GetDocId() uint64 {
	if x != nil {
		return x.DocId
	}
	return 0
}

func (x *DeleteText2SQLReq) GetSegmentType() string {
	if x != nil {
		return x.SegmentType
	}
	return ""
}

// text2sql删除的响应
type DeleteText2SQLRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *DeleteText2SQLRsp) Reset() {
	*x = DeleteText2SQLRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_retrieval_proto_msgTypes[90]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteText2SQLRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteText2SQLRsp) ProtoMessage() {}

func (x *DeleteText2SQLRsp) ProtoReflect() protoreflect.Message {
	mi := &file_retrieval_proto_msgTypes[90]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteText2SQLRsp.ProtoReflect.Descriptor instead.
func (*DeleteText2SQLRsp) Descriptor() ([]byte, []int) {
	return file_retrieval_proto_rawDescGZIP(), []int{90}
}

// 文档切片等知识数据
type KnowledgeData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// ID
	Id uint64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// 文档切片类型，segment-文档切片 table-表格，当doc_type=2时需要填写
	SegmentType string `protobuf:"bytes,2,opt,name=segment_type,json=segmentType,proto3" json:"segment_type,omitempty"`
	// 文档id，如果是文档则是对应的文档id，如果是qa，则用关联的文档id，即t_doc_qa表里的doc_id字段，没有则为0
	DocId uint64 `protobuf:"varint,3,opt,name=doc_id,json=docId,proto3" json:"doc_id,omitempty"`
	// 文档内容
	PageContent string `protobuf:"bytes,4,opt,name=page_content,json=pageContent,proto3" json:"page_content,omitempty"`
	// labels 标签
	Labels []*VectorLabel `protobuf:"bytes,5,rep,name=labels,proto3" json:"labels,omitempty"`
	// 有效期，时间戳，秒。填0时不过期
	ExpireTime int64 `protobuf:"varint,6,opt,name=expire_time,json=expireTime,proto3" json:"expire_time,omitempty"`
}

func (x *KnowledgeData) Reset() {
	*x = KnowledgeData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_retrieval_proto_msgTypes[91]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *KnowledgeData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*KnowledgeData) ProtoMessage() {}

func (x *KnowledgeData) ProtoReflect() protoreflect.Message {
	mi := &file_retrieval_proto_msgTypes[91]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use KnowledgeData.ProtoReflect.Descriptor instead.
func (*KnowledgeData) Descriptor() ([]byte, []int) {
	return file_retrieval_proto_rawDescGZIP(), []int{91}
}

func (x *KnowledgeData) GetId() uint64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *KnowledgeData) GetSegmentType() string {
	if x != nil {
		return x.SegmentType
	}
	return ""
}

func (x *KnowledgeData) GetDocId() uint64 {
	if x != nil {
		return x.DocId
	}
	return 0
}

func (x *KnowledgeData) GetPageContent() string {
	if x != nil {
		return x.PageContent
	}
	return ""
}

func (x *KnowledgeData) GetLabels() []*VectorLabel {
	if x != nil {
		return x.Labels
	}
	return nil
}

func (x *KnowledgeData) GetExpireTime() int64 {
	if x != nil {
		return x.ExpireTime
	}
	return 0
}

// 实时文档添加切片请求
type AddRealTimeKnowledgeReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 机器人ID
	RobotId uint64 `protobuf:"varint,1,opt,name=robot_id,json=robotId,proto3" json:"robot_id,omitempty"`
	// 索引库ID, 业务自己定义保存
	IndexId uint64 `protobuf:"varint,2,opt,name=index_id,json=indexId,proto3" json:"index_id,omitempty"`
	// 文档类型 (1 QA, 2 文档或者表格, 3 拒答问题, 4 搜索引擎)
	DocType uint32 `protobuf:"varint,3,opt,name=doc_type,json=docType,proto3" json:"doc_type,omitempty"`
	// embedding 版本
	EmbeddingVersion uint64 `protobuf:"varint,4,opt,name=embedding_version,json=embeddingVersion,proto3" json:"embedding_version,omitempty"`
	// 文档切片数组 支持批量
	Knowledge []*KnowledgeData `protobuf:"bytes,5,rep,name=knowledge,proto3" json:"knowledge,omitempty"`
	// 应用id
	BotBizId uint64 `protobuf:"varint,6,opt,name=bot_biz_id,json=botBizId,proto3" json:"bot_biz_id,omitempty"`
}

func (x *AddRealTimeKnowledgeReq) Reset() {
	*x = AddRealTimeKnowledgeReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_retrieval_proto_msgTypes[92]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AddRealTimeKnowledgeReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddRealTimeKnowledgeReq) ProtoMessage() {}

func (x *AddRealTimeKnowledgeReq) ProtoReflect() protoreflect.Message {
	mi := &file_retrieval_proto_msgTypes[92]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddRealTimeKnowledgeReq.ProtoReflect.Descriptor instead.
func (*AddRealTimeKnowledgeReq) Descriptor() ([]byte, []int) {
	return file_retrieval_proto_rawDescGZIP(), []int{92}
}

func (x *AddRealTimeKnowledgeReq) GetRobotId() uint64 {
	if x != nil {
		return x.RobotId
	}
	return 0
}

func (x *AddRealTimeKnowledgeReq) GetIndexId() uint64 {
	if x != nil {
		return x.IndexId
	}
	return 0
}

func (x *AddRealTimeKnowledgeReq) GetDocType() uint32 {
	if x != nil {
		return x.DocType
	}
	return 0
}

func (x *AddRealTimeKnowledgeReq) GetEmbeddingVersion() uint64 {
	if x != nil {
		return x.EmbeddingVersion
	}
	return 0
}

func (x *AddRealTimeKnowledgeReq) GetKnowledge() []*KnowledgeData {
	if x != nil {
		return x.Knowledge
	}
	return nil
}

func (x *AddRealTimeKnowledgeReq) GetBotBizId() uint64 {
	if x != nil {
		return x.BotBizId
	}
	return 0
}

// 实时文档添加切片响应
type AddRealTimeKnowledgeRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *AddRealTimeKnowledgeRsp) Reset() {
	*x = AddRealTimeKnowledgeRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_retrieval_proto_msgTypes[93]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AddRealTimeKnowledgeRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddRealTimeKnowledgeRsp) ProtoMessage() {}

func (x *AddRealTimeKnowledgeRsp) ProtoReflect() protoreflect.Message {
	mi := &file_retrieval_proto_msgTypes[93]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddRealTimeKnowledgeRsp.ProtoReflect.Descriptor instead.
func (*AddRealTimeKnowledgeRsp) Descriptor() ([]byte, []int) {
	return file_retrieval_proto_rawDescGZIP(), []int{93}
}

type KnowledgeIDType struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 文档片段ID
	Id uint64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// 文档切片类型，segment-文档切片 table-表格，当doc_type=2时需要填写
	SegmentType string `protobuf:"bytes,2,opt,name=segment_type,json=segmentType,proto3" json:"segment_type,omitempty"`
}

func (x *KnowledgeIDType) Reset() {
	*x = KnowledgeIDType{}
	if protoimpl.UnsafeEnabled {
		mi := &file_retrieval_proto_msgTypes[94]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *KnowledgeIDType) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*KnowledgeIDType) ProtoMessage() {}

func (x *KnowledgeIDType) ProtoReflect() protoreflect.Message {
	mi := &file_retrieval_proto_msgTypes[94]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use KnowledgeIDType.ProtoReflect.Descriptor instead.
func (*KnowledgeIDType) Descriptor() ([]byte, []int) {
	return file_retrieval_proto_rawDescGZIP(), []int{94}
}

func (x *KnowledgeIDType) GetId() uint64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *KnowledgeIDType) GetSegmentType() string {
	if x != nil {
		return x.SegmentType
	}
	return ""
}

// 实时文档删除切片请求
type DeleteRealTimeKnowledgeReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 机器人ID
	RobotId uint64 `protobuf:"varint,1,opt,name=robot_id,json=robotId,proto3" json:"robot_id,omitempty"`
	// 索引库ID, 业务自己定义保存
	IndexId uint64 `protobuf:"varint,2,opt,name=index_id,json=indexId,proto3" json:"index_id,omitempty"`
	// embedding 版本
	EmbeddingVersion uint64 `protobuf:"varint,3,opt,name=embedding_version,json=embeddingVersion,proto3" json:"embedding_version,omitempty"`
	// 文档类型 (1 QA, 2 文档或者表格, 3 拒答问题, 4 搜索引擎)
	DocType uint32 `protobuf:"varint,4,opt,name=doc_type,json=docType,proto3" json:"doc_type,omitempty"`
	// 要删除的数据，文档片段ID和对应的切片类型
	Data []*KnowledgeIDType `protobuf:"bytes,5,rep,name=data,proto3" json:"data,omitempty"`
	// 应用id
	BotBizId uint64 `protobuf:"varint,6,opt,name=bot_biz_id,json=botBizId,proto3" json:"bot_biz_id,omitempty"`
}

func (x *DeleteRealTimeKnowledgeReq) Reset() {
	*x = DeleteRealTimeKnowledgeReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_retrieval_proto_msgTypes[95]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteRealTimeKnowledgeReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteRealTimeKnowledgeReq) ProtoMessage() {}

func (x *DeleteRealTimeKnowledgeReq) ProtoReflect() protoreflect.Message {
	mi := &file_retrieval_proto_msgTypes[95]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteRealTimeKnowledgeReq.ProtoReflect.Descriptor instead.
func (*DeleteRealTimeKnowledgeReq) Descriptor() ([]byte, []int) {
	return file_retrieval_proto_rawDescGZIP(), []int{95}
}

func (x *DeleteRealTimeKnowledgeReq) GetRobotId() uint64 {
	if x != nil {
		return x.RobotId
	}
	return 0
}

func (x *DeleteRealTimeKnowledgeReq) GetIndexId() uint64 {
	if x != nil {
		return x.IndexId
	}
	return 0
}

func (x *DeleteRealTimeKnowledgeReq) GetEmbeddingVersion() uint64 {
	if x != nil {
		return x.EmbeddingVersion
	}
	return 0
}

func (x *DeleteRealTimeKnowledgeReq) GetDocType() uint32 {
	if x != nil {
		return x.DocType
	}
	return 0
}

func (x *DeleteRealTimeKnowledgeReq) GetData() []*KnowledgeIDType {
	if x != nil {
		return x.Data
	}
	return nil
}

func (x *DeleteRealTimeKnowledgeReq) GetBotBizId() uint64 {
	if x != nil {
		return x.BotBizId
	}
	return 0
}

// 实时文档删除切片响应
type DeleteRealTimeKnowledgeRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *DeleteRealTimeKnowledgeRsp) Reset() {
	*x = DeleteRealTimeKnowledgeRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_retrieval_proto_msgTypes[96]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteRealTimeKnowledgeRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteRealTimeKnowledgeRsp) ProtoMessage() {}

func (x *DeleteRealTimeKnowledgeRsp) ProtoReflect() protoreflect.Message {
	mi := &file_retrieval_proto_msgTypes[96]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteRealTimeKnowledgeRsp.ProtoReflect.Descriptor instead.
func (*DeleteRealTimeKnowledgeRsp) Descriptor() ([]byte, []int) {
	return file_retrieval_proto_rawDescGZIP(), []int{96}
}

// 实时文档检索接口请求
type RetrievalRealTimeReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 机器人ID
	RobotId uint64 `protobuf:"varint,1,opt,name=robot_id,json=robotId,proto3" json:"robot_id,omitempty"`
	// 问题
	Question string                         `protobuf:"bytes,2,opt,name=question,proto3" json:"question,omitempty"`
	Filters  []*RetrievalRealTimeReq_Filter `protobuf:"bytes,3,rep,name=filters,proto3" json:"filters,omitempty"`
	// 取前 n 条 (默认3)
	TopN uint32 `protobuf:"varint,4,opt,name=top_n,json=topN,proto3" json:"top_n,omitempty"`
	// embedding 版本
	EmbeddingVersion uint64                       `protobuf:"varint,5,opt,name=embedding_version,json=embeddingVersion,proto3" json:"embedding_version,omitempty"`
	Rerank           *RetrievalRealTimeReq_Rerank `protobuf:"bytes,7,opt,name=rerank,proto3" json:"rerank,omitempty"`
	// 过滤器名称
	FilterKey string `protobuf:"bytes,8,opt,name=filter_key,json=filterKey,proto3" json:"filter_key,omitempty"`
	// labels 标签
	Labels []*SearchVectorLabel `protobuf:"bytes,9,rep,name=labels,proto3" json:"labels,omitempty"`
	// 请求query带图片URL，用于图搜图
	ImageUrls []string `protobuf:"bytes,10,rep,name=image_urls,json=imageUrls,proto3" json:"image_urls,omitempty"`
	// 应用id
	BotBizId uint64 `protobuf:"varint,11,opt,name=bot_biz_id,json=botBizId,proto3" json:"bot_biz_id,omitempty"`
	// 标签表达式
	LabelExpression *LabelExpression `protobuf:"bytes,12,opt,name=label_expression,json=labelExpression,proto3" json:"label_expression,omitempty"`
	// 拆解的子问题，为空表示没有子问题
	SubQuestions []string `protobuf:"bytes,13,rep,name=sub_questions,json=subQuestions,proto3" json:"sub_questions,omitempty"`
	// 知识检索策略配置
	SearchStrategy *SearchStrategy `protobuf:"bytes,14,opt,name=search_strategy,json=searchStrategy,proto3" json:"search_strategy,omitempty"`
	// 模型名称
	ModelName string `protobuf:"bytes,15,opt,name=model_name,json=modelName,proto3" json:"model_name,omitempty"`
}

func (x *RetrievalRealTimeReq) Reset() {
	*x = RetrievalRealTimeReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_retrieval_proto_msgTypes[97]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RetrievalRealTimeReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RetrievalRealTimeReq) ProtoMessage() {}

func (x *RetrievalRealTimeReq) ProtoReflect() protoreflect.Message {
	mi := &file_retrieval_proto_msgTypes[97]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RetrievalRealTimeReq.ProtoReflect.Descriptor instead.
func (*RetrievalRealTimeReq) Descriptor() ([]byte, []int) {
	return file_retrieval_proto_rawDescGZIP(), []int{97}
}

func (x *RetrievalRealTimeReq) GetRobotId() uint64 {
	if x != nil {
		return x.RobotId
	}
	return 0
}

func (x *RetrievalRealTimeReq) GetQuestion() string {
	if x != nil {
		return x.Question
	}
	return ""
}

func (x *RetrievalRealTimeReq) GetFilters() []*RetrievalRealTimeReq_Filter {
	if x != nil {
		return x.Filters
	}
	return nil
}

func (x *RetrievalRealTimeReq) GetTopN() uint32 {
	if x != nil {
		return x.TopN
	}
	return 0
}

func (x *RetrievalRealTimeReq) GetEmbeddingVersion() uint64 {
	if x != nil {
		return x.EmbeddingVersion
	}
	return 0
}

func (x *RetrievalRealTimeReq) GetRerank() *RetrievalRealTimeReq_Rerank {
	if x != nil {
		return x.Rerank
	}
	return nil
}

func (x *RetrievalRealTimeReq) GetFilterKey() string {
	if x != nil {
		return x.FilterKey
	}
	return ""
}

func (x *RetrievalRealTimeReq) GetLabels() []*SearchVectorLabel {
	if x != nil {
		return x.Labels
	}
	return nil
}

func (x *RetrievalRealTimeReq) GetImageUrls() []string {
	if x != nil {
		return x.ImageUrls
	}
	return nil
}

func (x *RetrievalRealTimeReq) GetBotBizId() uint64 {
	if x != nil {
		return x.BotBizId
	}
	return 0
}

func (x *RetrievalRealTimeReq) GetLabelExpression() *LabelExpression {
	if x != nil {
		return x.LabelExpression
	}
	return nil
}

func (x *RetrievalRealTimeReq) GetSubQuestions() []string {
	if x != nil {
		return x.SubQuestions
	}
	return nil
}

func (x *RetrievalRealTimeReq) GetSearchStrategy() *SearchStrategy {
	if x != nil {
		return x.SearchStrategy
	}
	return nil
}

func (x *RetrievalRealTimeReq) GetModelName() string {
	if x != nil {
		return x.ModelName
	}
	return ""
}

// 实时文档检索接口响应
type RetrievalRealTimeRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 文档数据
	Docs []*RetrievalRealTimeRsp_Doc `protobuf:"bytes,1,rep,name=docs,proto3" json:"docs,omitempty"`
}

func (x *RetrievalRealTimeRsp) Reset() {
	*x = RetrievalRealTimeRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_retrieval_proto_msgTypes[98]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RetrievalRealTimeRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RetrievalRealTimeRsp) ProtoMessage() {}

func (x *RetrievalRealTimeRsp) ProtoReflect() protoreflect.Message {
	mi := &file_retrieval_proto_msgTypes[98]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RetrievalRealTimeRsp.ProtoReflect.Descriptor instead.
func (*RetrievalRealTimeRsp) Descriptor() ([]byte, []int) {
	return file_retrieval_proto_rawDescGZIP(), []int{98}
}

func (x *RetrievalRealTimeRsp) GetDocs() []*RetrievalRealTimeRsp_Doc {
	if x != nil {
		return x.Docs
	}
	return nil
}

// 清理应用向量库资源请求
type ClearAppVectorResourceReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 应用ID[表中的主键ID]
	RobotId uint64 `protobuf:"varint,1,opt,name=robot_id,json=robotId,proto3" json:"robot_id,omitempty"`
	// 应用业务ID
	BotBizId string `protobuf:"bytes,2,opt,name=bot_biz_id,json=botBizId,proto3" json:"bot_biz_id,omitempty"`
	// 任务ID
	TaskId uint64 `protobuf:"varint,3,opt,name=task_id,json=taskId,proto3" json:"task_id,omitempty"`
}

func (x *ClearAppVectorResourceReq) Reset() {
	*x = ClearAppVectorResourceReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_retrieval_proto_msgTypes[99]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ClearAppVectorResourceReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ClearAppVectorResourceReq) ProtoMessage() {}

func (x *ClearAppVectorResourceReq) ProtoReflect() protoreflect.Message {
	mi := &file_retrieval_proto_msgTypes[99]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ClearAppVectorResourceReq.ProtoReflect.Descriptor instead.
func (*ClearAppVectorResourceReq) Descriptor() ([]byte, []int) {
	return file_retrieval_proto_rawDescGZIP(), []int{99}
}

func (x *ClearAppVectorResourceReq) GetRobotId() uint64 {
	if x != nil {
		return x.RobotId
	}
	return 0
}

func (x *ClearAppVectorResourceReq) GetBotBizId() string {
	if x != nil {
		return x.BotBizId
	}
	return ""
}

func (x *ClearAppVectorResourceReq) GetTaskId() uint64 {
	if x != nil {
		return x.TaskId
	}
	return 0
}

type ClearAppVectorResourceRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *ClearAppVectorResourceRsp) Reset() {
	*x = ClearAppVectorResourceRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_retrieval_proto_msgTypes[100]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ClearAppVectorResourceRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ClearAppVectorResourceRsp) ProtoMessage() {}

func (x *ClearAppVectorResourceRsp) ProtoReflect() protoreflect.Message {
	mi := &file_retrieval_proto_msgTypes[100]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ClearAppVectorResourceRsp.ProtoReflect.Descriptor instead.
func (*ClearAppVectorResourceRsp) Descriptor() ([]byte, []int) {
	return file_retrieval_proto_rawDescGZIP(), []int{100}
}

// 定义基本条件表达式
type LabelExpression_Condition struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Type LabelExpression_Condition_DataType `protobuf:"varint,1,opt,name=type,proto3,enum=trpc.KEP.bot_retrieval_server.LabelExpression_Condition_DataType" json:"type,omitempty"`
	// 标签名
	Name string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	// 标签值
	Values []string `protobuf:"bytes,3,rep,name=values,proto3" json:"values,omitempty"`
}

func (x *LabelExpression_Condition) Reset() {
	*x = LabelExpression_Condition{}
	if protoimpl.UnsafeEnabled {
		mi := &file_retrieval_proto_msgTypes[101]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LabelExpression_Condition) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LabelExpression_Condition) ProtoMessage() {}

func (x *LabelExpression_Condition) ProtoReflect() protoreflect.Message {
	mi := &file_retrieval_proto_msgTypes[101]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LabelExpression_Condition.ProtoReflect.Descriptor instead.
func (*LabelExpression_Condition) Descriptor() ([]byte, []int) {
	return file_retrieval_proto_rawDescGZIP(), []int{15, 0}
}

func (x *LabelExpression_Condition) GetType() LabelExpression_Condition_DataType {
	if x != nil {
		return x.Type
	}
	return LabelExpression_Condition_ARRAY
}

func (x *LabelExpression_Condition) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *LabelExpression_Condition) GetValues() []string {
	if x != nil {
		return x.Values
	}
	return nil
}

type SearchReq_Filter struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 文档类型 (1 QA, 2 文档段, 3 拒答问题, 4 搜索引擎)
	DocType uint32 `protobuf:"varint,1,opt,name=doc_type,json=docType,proto3" json:"doc_type,omitempty"`
	// 置信度
	Confidence float32 `protobuf:"fixed32,2,opt,name=confidence,proto3" json:"confidence,omitempty"`
	// 取 top_n
	TopN uint32 `protobuf:"varint,3,opt,name=top_n,json=topN,proto3" json:"top_n,omitempty"`
	// 废弃字段，以LabelExpression为准
	// 标签表达式字符串 key1="value1" and key2="value2"
	// 优先使用表达式字符串, 表达式字符串无值时使用表达式
	LabelExprString string `protobuf:"bytes,4,opt,name=label_expr_string,json=labelExprString,proto3" json:"label_expr_string,omitempty"`
	// 标签表达式
	LabelExpr *VectorLabelExpr `protobuf:"bytes,5,opt,name=label_expr,json=labelExpr,proto3" json:"label_expr,omitempty"`
}

func (x *SearchReq_Filter) Reset() {
	*x = SearchReq_Filter{}
	if protoimpl.UnsafeEnabled {
		mi := &file_retrieval_proto_msgTypes[102]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SearchReq_Filter) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SearchReq_Filter) ProtoMessage() {}

func (x *SearchReq_Filter) ProtoReflect() protoreflect.Message {
	mi := &file_retrieval_proto_msgTypes[102]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SearchReq_Filter.ProtoReflect.Descriptor instead.
func (*SearchReq_Filter) Descriptor() ([]byte, []int) {
	return file_retrieval_proto_rawDescGZIP(), []int{20, 0}
}

func (x *SearchReq_Filter) GetDocType() uint32 {
	if x != nil {
		return x.DocType
	}
	return 0
}

func (x *SearchReq_Filter) GetConfidence() float32 {
	if x != nil {
		return x.Confidence
	}
	return 0
}

func (x *SearchReq_Filter) GetTopN() uint32 {
	if x != nil {
		return x.TopN
	}
	return 0
}

func (x *SearchReq_Filter) GetLabelExprString() string {
	if x != nil {
		return x.LabelExprString
	}
	return ""
}

func (x *SearchReq_Filter) GetLabelExpr() *VectorLabelExpr {
	if x != nil {
		return x.LabelExpr
	}
	return nil
}

// 重排模型
type SearchReq_Rerank struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Model  string `protobuf:"bytes,1,opt,name=model,proto3" json:"model,omitempty"`            // 模型名称
	TopN   uint32 `protobuf:"varint,2,opt,name=top_n,json=topN,proto3" json:"top_n,omitempty"` // top_n
	Enable bool   `protobuf:"varint,3,opt,name=enable,proto3" json:"enable,omitempty"`         // 启用
}

func (x *SearchReq_Rerank) Reset() {
	*x = SearchReq_Rerank{}
	if protoimpl.UnsafeEnabled {
		mi := &file_retrieval_proto_msgTypes[103]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SearchReq_Rerank) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SearchReq_Rerank) ProtoMessage() {}

func (x *SearchReq_Rerank) ProtoReflect() protoreflect.Message {
	mi := &file_retrieval_proto_msgTypes[103]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SearchReq_Rerank.ProtoReflect.Descriptor instead.
func (*SearchReq_Rerank) Descriptor() ([]byte, []int) {
	return file_retrieval_proto_rawDescGZIP(), []int{20, 1}
}

func (x *SearchReq_Rerank) GetModel() string {
	if x != nil {
		return x.Model
	}
	return ""
}

func (x *SearchReq_Rerank) GetTopN() uint32 {
	if x != nil {
		return x.TopN
	}
	return 0
}

func (x *SearchReq_Rerank) GetEnable() bool {
	if x != nil {
		return x.Enable
	}
	return false
}

type SearchRsp_Doc struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 文档类型 (1 QA, 2 文档段, 3 拒答问题, 4 搜索引擎)
	DocType uint32 `protobuf:"varint,1,opt,name=doc_type,json=docType,proto3" json:"doc_type,omitempty"`
	// 文档ID
	DocId uint64 `protobuf:"varint,2,opt,name=doc_id,json=docId,proto3" json:"doc_id,omitempty"`
	// 对应文档类型关联的数据ID, 类型为 QA 时为 QAID, 类型为 文档段 时为 文档段ID
	RelatedId uint64 `protobuf:"varint,3,opt,name=related_id,json=relatedId,proto3" json:"related_id,omitempty"`
	// 文档段, 当文档类型为 文档段(2) 时有效
	PageContent string `protobuf:"bytes,4,opt,name=page_content,json=pageContent,proto3" json:"page_content,omitempty"`
	// 问题, 当文档类型为 QA(1) 时有效
	Question string `protobuf:"bytes,5,opt,name=question,proto3" json:"question,omitempty"`
	// 答案, 当文档类型为 QA(1) 时有效
	Answer string `protobuf:"bytes,6,opt,name=answer,proto3" json:"answer,omitempty"`
	// 置信度
	Confidence float32 `protobuf:"fixed32,7,opt,name=confidence,proto3" json:"confidence,omitempty"`
	// 文档段原文, 当文档类型为 文档段(2) 时有效
	OrgData string `protobuf:"bytes,8,opt,name=org_data,json=orgData,proto3" json:"org_data,omitempty"`
	// 自定义参数
	CustomParam string `protobuf:"bytes,9,opt,name=custom_param,json=customParam,proto3" json:"custom_param,omitempty"`
	// 是否big_data true-表示org_data是由big_data填充
	IsBigData bool `protobuf:"varint,10,opt,name=is_big_data,json=isBigData,proto3" json:"is_big_data,omitempty"`
	// 算法扩展信息
	Extra *RetrievalExtra `protobuf:"bytes,11,opt,name=extra,proto3" json:"extra,omitempty"`
	// 检索结果类型
	ResultType RetrievalResultType `protobuf:"varint,12,opt,name=result_type,json=resultType,proto3,enum=trpc.KEP.bot_retrieval_server.RetrievalResultType" json:"result_type,omitempty"`
	// 检索命中的图片URL
	ImageUrls            []string                            `protobuf:"bytes,13,rep,name=image_urls,json=imageUrls,proto3" json:"image_urls,omitempty"`
	SimilarQuestionExtra *SearchRsp_Doc_SimilarQuestionExtra `protobuf:"bytes,14,opt,name=similar_question_extra,json=similarQuestionExtra,proto3" json:"similar_question_extra,omitempty"`
	Text2SqlExtra        *SearchRsp_Doc_Text2SQLExtra        `protobuf:"bytes,15,opt,name=text2sql_extra,json=text2sqlExtra,proto3" json:"text2sql_extra,omitempty"`
	// 问题描述 qa意图的描述
	QuestionDesc string `protobuf:"bytes,16,opt,name=question_desc,json=questionDesc,proto3" json:"question_desc,omitempty"`
}

func (x *SearchRsp_Doc) Reset() {
	*x = SearchRsp_Doc{}
	if protoimpl.UnsafeEnabled {
		mi := &file_retrieval_proto_msgTypes[104]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SearchRsp_Doc) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SearchRsp_Doc) ProtoMessage() {}

func (x *SearchRsp_Doc) ProtoReflect() protoreflect.Message {
	mi := &file_retrieval_proto_msgTypes[104]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SearchRsp_Doc.ProtoReflect.Descriptor instead.
func (*SearchRsp_Doc) Descriptor() ([]byte, []int) {
	return file_retrieval_proto_rawDescGZIP(), []int{21, 0}
}

func (x *SearchRsp_Doc) GetDocType() uint32 {
	if x != nil {
		return x.DocType
	}
	return 0
}

func (x *SearchRsp_Doc) GetDocId() uint64 {
	if x != nil {
		return x.DocId
	}
	return 0
}

func (x *SearchRsp_Doc) GetRelatedId() uint64 {
	if x != nil {
		return x.RelatedId
	}
	return 0
}

func (x *SearchRsp_Doc) GetPageContent() string {
	if x != nil {
		return x.PageContent
	}
	return ""
}

func (x *SearchRsp_Doc) GetQuestion() string {
	if x != nil {
		return x.Question
	}
	return ""
}

func (x *SearchRsp_Doc) GetAnswer() string {
	if x != nil {
		return x.Answer
	}
	return ""
}

func (x *SearchRsp_Doc) GetConfidence() float32 {
	if x != nil {
		return x.Confidence
	}
	return 0
}

func (x *SearchRsp_Doc) GetOrgData() string {
	if x != nil {
		return x.OrgData
	}
	return ""
}

func (x *SearchRsp_Doc) GetCustomParam() string {
	if x != nil {
		return x.CustomParam
	}
	return ""
}

func (x *SearchRsp_Doc) GetIsBigData() bool {
	if x != nil {
		return x.IsBigData
	}
	return false
}

func (x *SearchRsp_Doc) GetExtra() *RetrievalExtra {
	if x != nil {
		return x.Extra
	}
	return nil
}

func (x *SearchRsp_Doc) GetResultType() RetrievalResultType {
	if x != nil {
		return x.ResultType
	}
	return RetrievalResultType_RETRIEVAL
}

func (x *SearchRsp_Doc) GetImageUrls() []string {
	if x != nil {
		return x.ImageUrls
	}
	return nil
}

func (x *SearchRsp_Doc) GetSimilarQuestionExtra() *SearchRsp_Doc_SimilarQuestionExtra {
	if x != nil {
		return x.SimilarQuestionExtra
	}
	return nil
}

func (x *SearchRsp_Doc) GetText2SqlExtra() *SearchRsp_Doc_Text2SQLExtra {
	if x != nil {
		return x.Text2SqlExtra
	}
	return nil
}

func (x *SearchRsp_Doc) GetQuestionDesc() string {
	if x != nil {
		return x.QuestionDesc
	}
	return ""
}

// 相似问相关的额外信息
type SearchRsp_Doc_SimilarQuestionExtra struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 当检索到的是相似问时，返回该相似问ID
	SimilarId uint64 `protobuf:"varint,1,opt,name=similar_id,json=similarId,proto3" json:"similar_id,omitempty"`
	// 当检索到的是相似问时，返回该相似问的问题
	SimilarQuestion string `protobuf:"bytes,2,opt,name=similar_question,json=similarQuestion,proto3" json:"similar_question,omitempty"`
}

func (x *SearchRsp_Doc_SimilarQuestionExtra) Reset() {
	*x = SearchRsp_Doc_SimilarQuestionExtra{}
	if protoimpl.UnsafeEnabled {
		mi := &file_retrieval_proto_msgTypes[105]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SearchRsp_Doc_SimilarQuestionExtra) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SearchRsp_Doc_SimilarQuestionExtra) ProtoMessage() {}

func (x *SearchRsp_Doc_SimilarQuestionExtra) ProtoReflect() protoreflect.Message {
	mi := &file_retrieval_proto_msgTypes[105]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SearchRsp_Doc_SimilarQuestionExtra.ProtoReflect.Descriptor instead.
func (*SearchRsp_Doc_SimilarQuestionExtra) Descriptor() ([]byte, []int) {
	return file_retrieval_proto_rawDescGZIP(), []int{21, 0, 0}
}

func (x *SearchRsp_Doc_SimilarQuestionExtra) GetSimilarId() uint64 {
	if x != nil {
		return x.SimilarId
	}
	return 0
}

func (x *SearchRsp_Doc_SimilarQuestionExtra) GetSimilarQuestion() string {
	if x != nil {
		return x.SimilarQuestion
	}
	return ""
}

// text2sql检索结果带上sheet名称，参考来源溯源用到
type SearchRsp_Doc_Text2SQLExtra struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TableInfos []*SearchRsp_Doc_Text2SQLExtra_TableInfo `protobuf:"bytes,1,rep,name=table_infos,json=tableInfos,proto3" json:"table_infos,omitempty"`
}

func (x *SearchRsp_Doc_Text2SQLExtra) Reset() {
	*x = SearchRsp_Doc_Text2SQLExtra{}
	if protoimpl.UnsafeEnabled {
		mi := &file_retrieval_proto_msgTypes[106]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SearchRsp_Doc_Text2SQLExtra) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SearchRsp_Doc_Text2SQLExtra) ProtoMessage() {}

func (x *SearchRsp_Doc_Text2SQLExtra) ProtoReflect() protoreflect.Message {
	mi := &file_retrieval_proto_msgTypes[106]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SearchRsp_Doc_Text2SQLExtra.ProtoReflect.Descriptor instead.
func (*SearchRsp_Doc_Text2SQLExtra) Descriptor() ([]byte, []int) {
	return file_retrieval_proto_rawDescGZIP(), []int{21, 0, 1}
}

func (x *SearchRsp_Doc_Text2SQLExtra) GetTableInfos() []*SearchRsp_Doc_Text2SQLExtra_TableInfo {
	if x != nil {
		return x.TableInfos
	}
	return nil
}

type SearchRsp_Doc_Text2SQLExtra_TableInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TableName string `protobuf:"bytes,1,opt,name=table_name,json=tableName,proto3" json:"table_name,omitempty"`
	DocId     uint64 `protobuf:"varint,2,opt,name=doc_id,json=docId,proto3" json:"doc_id,omitempty"`
}

func (x *SearchRsp_Doc_Text2SQLExtra_TableInfo) Reset() {
	*x = SearchRsp_Doc_Text2SQLExtra_TableInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_retrieval_proto_msgTypes[107]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SearchRsp_Doc_Text2SQLExtra_TableInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SearchRsp_Doc_Text2SQLExtra_TableInfo) ProtoMessage() {}

func (x *SearchRsp_Doc_Text2SQLExtra_TableInfo) ProtoReflect() protoreflect.Message {
	mi := &file_retrieval_proto_msgTypes[107]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SearchRsp_Doc_Text2SQLExtra_TableInfo.ProtoReflect.Descriptor instead.
func (*SearchRsp_Doc_Text2SQLExtra_TableInfo) Descriptor() ([]byte, []int) {
	return file_retrieval_proto_rawDescGZIP(), []int{21, 0, 1, 0}
}

func (x *SearchRsp_Doc_Text2SQLExtra_TableInfo) GetTableName() string {
	if x != nil {
		return x.TableName
	}
	return ""
}

func (x *SearchRsp_Doc_Text2SQLExtra_TableInfo) GetDocId() uint64 {
	if x != nil {
		return x.DocId
	}
	return 0
}

type SimilarityReq_Doc struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 文档类型 (1 QA, 2 文档段, 3 拒答问题, 4 搜索引擎)
	DocType uint32 `protobuf:"varint,1,opt,name=doc_type,json=docType,proto3" json:"doc_type,omitempty"`
	// 文本内容
	Content string `protobuf:"bytes,2,opt,name=content,proto3" json:"content,omitempty"`
}

func (x *SimilarityReq_Doc) Reset() {
	*x = SimilarityReq_Doc{}
	if protoimpl.UnsafeEnabled {
		mi := &file_retrieval_proto_msgTypes[108]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SimilarityReq_Doc) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SimilarityReq_Doc) ProtoMessage() {}

func (x *SimilarityReq_Doc) ProtoReflect() protoreflect.Message {
	mi := &file_retrieval_proto_msgTypes[108]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SimilarityReq_Doc.ProtoReflect.Descriptor instead.
func (*SimilarityReq_Doc) Descriptor() ([]byte, []int) {
	return file_retrieval_proto_rawDescGZIP(), []int{24, 0}
}

func (x *SimilarityReq_Doc) GetDocType() uint32 {
	if x != nil {
		return x.DocType
	}
	return 0
}

func (x *SimilarityReq_Doc) GetContent() string {
	if x != nil {
		return x.Content
	}
	return ""
}

type SearchVectorReq_Filter struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 索引库ID, 业务自己定义保存
	IndexId uint64 `protobuf:"varint,1,opt,name=index_id,json=indexId,proto3" json:"index_id,omitempty"`
	// 置信度
	Confidence float32 `protobuf:"fixed32,2,opt,name=confidence,proto3" json:"confidence,omitempty"`
	// 取 top_n
	TopN uint32 `protobuf:"varint,3,opt,name=top_n,json=topN,proto3" json:"top_n,omitempty"`
	// 文档类型 (1 QA, 2 文档段, 3 拒答问题, 4 搜索引擎)
	DocType uint32 `protobuf:"varint,4,opt,name=doc_type,json=docType,proto3" json:"doc_type,omitempty"`
	// 标签表达式字符串 key1="value1" and key2="value2"
	// 优先使用表达式字符串, 表达式字符串无值时使用表达式
	LabelExprString string `protobuf:"bytes,5,opt,name=label_expr_string,json=labelExprString,proto3" json:"label_expr_string,omitempty"`
	// 标签表达式
	LabelExpr *VectorLabelExpr `protobuf:"bytes,6,opt,name=label_expr,json=labelExpr,proto3" json:"label_expr,omitempty"`
}

func (x *SearchVectorReq_Filter) Reset() {
	*x = SearchVectorReq_Filter{}
	if protoimpl.UnsafeEnabled {
		mi := &file_retrieval_proto_msgTypes[109]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SearchVectorReq_Filter) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SearchVectorReq_Filter) ProtoMessage() {}

func (x *SearchVectorReq_Filter) ProtoReflect() protoreflect.Message {
	mi := &file_retrieval_proto_msgTypes[109]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SearchVectorReq_Filter.ProtoReflect.Descriptor instead.
func (*SearchVectorReq_Filter) Descriptor() ([]byte, []int) {
	return file_retrieval_proto_rawDescGZIP(), []int{48, 0}
}

func (x *SearchVectorReq_Filter) GetIndexId() uint64 {
	if x != nil {
		return x.IndexId
	}
	return 0
}

func (x *SearchVectorReq_Filter) GetConfidence() float32 {
	if x != nil {
		return x.Confidence
	}
	return 0
}

func (x *SearchVectorReq_Filter) GetTopN() uint32 {
	if x != nil {
		return x.TopN
	}
	return 0
}

func (x *SearchVectorReq_Filter) GetDocType() uint32 {
	if x != nil {
		return x.DocType
	}
	return 0
}

func (x *SearchVectorReq_Filter) GetLabelExprString() string {
	if x != nil {
		return x.LabelExprString
	}
	return ""
}

func (x *SearchVectorReq_Filter) GetLabelExpr() *VectorLabelExpr {
	if x != nil {
		return x.LabelExpr
	}
	return nil
}

// 重排模型
type SearchVectorReq_Rerank struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Model  string `protobuf:"bytes,1,opt,name=model,proto3" json:"model,omitempty"`            // 模型名称
	TopN   uint32 `protobuf:"varint,2,opt,name=top_n,json=topN,proto3" json:"top_n,omitempty"` // top_n
	Enable bool   `protobuf:"varint,3,opt,name=enable,proto3" json:"enable,omitempty"`         // 启用
}

func (x *SearchVectorReq_Rerank) Reset() {
	*x = SearchVectorReq_Rerank{}
	if protoimpl.UnsafeEnabled {
		mi := &file_retrieval_proto_msgTypes[110]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SearchVectorReq_Rerank) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SearchVectorReq_Rerank) ProtoMessage() {}

func (x *SearchVectorReq_Rerank) ProtoReflect() protoreflect.Message {
	mi := &file_retrieval_proto_msgTypes[110]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SearchVectorReq_Rerank.ProtoReflect.Descriptor instead.
func (*SearchVectorReq_Rerank) Descriptor() ([]byte, []int) {
	return file_retrieval_proto_rawDescGZIP(), []int{48, 1}
}

func (x *SearchVectorReq_Rerank) GetModel() string {
	if x != nil {
		return x.Model
	}
	return ""
}

func (x *SearchVectorReq_Rerank) GetTopN() uint32 {
	if x != nil {
		return x.TopN
	}
	return 0
}

func (x *SearchVectorReq_Rerank) GetEnable() bool {
	if x != nil {
		return x.Enable
	}
	return false
}

type SearchVectorRsp_Doc struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 索引库ID, 业务自己定义保存
	IndexId uint64 `protobuf:"varint,1,opt,name=index_id,json=indexId,proto3" json:"index_id,omitempty"`
	// 对应文档类型关联的数据ID, 类型为 QA 时为 QAID, 类型为 文档段 时为 文档段ID
	Id uint64 `protobuf:"varint,2,opt,name=id,proto3" json:"id,omitempty"`
	// 置信度
	Confidence float32 `protobuf:"fixed32,3,opt,name=confidence,proto3" json:"confidence,omitempty"`
	// question
	Question string `protobuf:"bytes,4,opt,name=question,proto3" json:"question,omitempty"`
	// answer
	Answer string `protobuf:"bytes,5,opt,name=answer,proto3" json:"answer,omitempty"`
	// 内容
	PageContent string `protobuf:"bytes,6,opt,name=page_content,json=pageContent,proto3" json:"page_content,omitempty"`
	// 原始内容
	OrgData string `protobuf:"bytes,7,opt,name=org_data,json=orgData,proto3" json:"org_data,omitempty"`
	// 文档类型 (1 QA, 2 文档段, 3 拒答问题, 4 搜索引擎)
	DocType uint32 `protobuf:"varint,8,opt,name=doc_type,json=docType,proto3" json:"doc_type,omitempty"`
	// 是否big_data true-表示org_data是由big_data填充
	IsBigData bool `protobuf:"varint,9,opt,name=is_big_data,json=isBigData,proto3" json:"is_big_data,omitempty"`
	// 算法扩展信息
	Extra *RetrievalExtra `protobuf:"bytes,10,opt,name=extra,proto3" json:"extra,omitempty"`
	// 检索结果类型
	ResultType RetrievalResultType `protobuf:"varint,11,opt,name=result_type,json=resultType,proto3,enum=trpc.KEP.bot_retrieval_server.RetrievalResultType" json:"result_type,omitempty"`
	// 文档ID
	DocId uint64 `protobuf:"varint,12,opt,name=doc_id,json=docId,proto3" json:"doc_id,omitempty"`
	// 检索命中的图片URL
	ImageUrls            []string                                  `protobuf:"bytes,13,rep,name=image_urls,json=imageUrls,proto3" json:"image_urls,omitempty"`
	SimilarQuestionExtra *SearchVectorRsp_Doc_SimilarQuestionExtra `protobuf:"bytes,14,opt,name=similar_question_extra,json=similarQuestionExtra,proto3" json:"similar_question_extra,omitempty"`
	Text2SqlExtra        *SearchVectorRsp_Doc_Text2SQLExtra        `protobuf:"bytes,15,opt,name=text2sql_extra,json=text2sqlExtra,proto3" json:"text2sql_extra,omitempty"`
	// 问题描述 qa意图的描述
	QuestionDesc string `protobuf:"bytes,16,opt,name=question_desc,json=questionDesc,proto3" json:"question_desc,omitempty"`
}

func (x *SearchVectorRsp_Doc) Reset() {
	*x = SearchVectorRsp_Doc{}
	if protoimpl.UnsafeEnabled {
		mi := &file_retrieval_proto_msgTypes[111]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SearchVectorRsp_Doc) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SearchVectorRsp_Doc) ProtoMessage() {}

func (x *SearchVectorRsp_Doc) ProtoReflect() protoreflect.Message {
	mi := &file_retrieval_proto_msgTypes[111]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SearchVectorRsp_Doc.ProtoReflect.Descriptor instead.
func (*SearchVectorRsp_Doc) Descriptor() ([]byte, []int) {
	return file_retrieval_proto_rawDescGZIP(), []int{49, 0}
}

func (x *SearchVectorRsp_Doc) GetIndexId() uint64 {
	if x != nil {
		return x.IndexId
	}
	return 0
}

func (x *SearchVectorRsp_Doc) GetId() uint64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *SearchVectorRsp_Doc) GetConfidence() float32 {
	if x != nil {
		return x.Confidence
	}
	return 0
}

func (x *SearchVectorRsp_Doc) GetQuestion() string {
	if x != nil {
		return x.Question
	}
	return ""
}

func (x *SearchVectorRsp_Doc) GetAnswer() string {
	if x != nil {
		return x.Answer
	}
	return ""
}

func (x *SearchVectorRsp_Doc) GetPageContent() string {
	if x != nil {
		return x.PageContent
	}
	return ""
}

func (x *SearchVectorRsp_Doc) GetOrgData() string {
	if x != nil {
		return x.OrgData
	}
	return ""
}

func (x *SearchVectorRsp_Doc) GetDocType() uint32 {
	if x != nil {
		return x.DocType
	}
	return 0
}

func (x *SearchVectorRsp_Doc) GetIsBigData() bool {
	if x != nil {
		return x.IsBigData
	}
	return false
}

func (x *SearchVectorRsp_Doc) GetExtra() *RetrievalExtra {
	if x != nil {
		return x.Extra
	}
	return nil
}

func (x *SearchVectorRsp_Doc) GetResultType() RetrievalResultType {
	if x != nil {
		return x.ResultType
	}
	return RetrievalResultType_RETRIEVAL
}

func (x *SearchVectorRsp_Doc) GetDocId() uint64 {
	if x != nil {
		return x.DocId
	}
	return 0
}

func (x *SearchVectorRsp_Doc) GetImageUrls() []string {
	if x != nil {
		return x.ImageUrls
	}
	return nil
}

func (x *SearchVectorRsp_Doc) GetSimilarQuestionExtra() *SearchVectorRsp_Doc_SimilarQuestionExtra {
	if x != nil {
		return x.SimilarQuestionExtra
	}
	return nil
}

func (x *SearchVectorRsp_Doc) GetText2SqlExtra() *SearchVectorRsp_Doc_Text2SQLExtra {
	if x != nil {
		return x.Text2SqlExtra
	}
	return nil
}

func (x *SearchVectorRsp_Doc) GetQuestionDesc() string {
	if x != nil {
		return x.QuestionDesc
	}
	return ""
}

// 相似问相关的额外信息
type SearchVectorRsp_Doc_SimilarQuestionExtra struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 当检索到的是相似问时，返回该相似问ID
	SimilarId uint64 `protobuf:"varint,1,opt,name=similar_id,json=similarId,proto3" json:"similar_id,omitempty"`
	// 当检索到的是相似问时，返回该相似问的问题
	SimilarQuestion string `protobuf:"bytes,2,opt,name=similar_question,json=similarQuestion,proto3" json:"similar_question,omitempty"`
}

func (x *SearchVectorRsp_Doc_SimilarQuestionExtra) Reset() {
	*x = SearchVectorRsp_Doc_SimilarQuestionExtra{}
	if protoimpl.UnsafeEnabled {
		mi := &file_retrieval_proto_msgTypes[112]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SearchVectorRsp_Doc_SimilarQuestionExtra) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SearchVectorRsp_Doc_SimilarQuestionExtra) ProtoMessage() {}

func (x *SearchVectorRsp_Doc_SimilarQuestionExtra) ProtoReflect() protoreflect.Message {
	mi := &file_retrieval_proto_msgTypes[112]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SearchVectorRsp_Doc_SimilarQuestionExtra.ProtoReflect.Descriptor instead.
func (*SearchVectorRsp_Doc_SimilarQuestionExtra) Descriptor() ([]byte, []int) {
	return file_retrieval_proto_rawDescGZIP(), []int{49, 0, 0}
}

func (x *SearchVectorRsp_Doc_SimilarQuestionExtra) GetSimilarId() uint64 {
	if x != nil {
		return x.SimilarId
	}
	return 0
}

func (x *SearchVectorRsp_Doc_SimilarQuestionExtra) GetSimilarQuestion() string {
	if x != nil {
		return x.SimilarQuestion
	}
	return ""
}

// text2sql检索结果带上sheet名称，参考来源溯源用到
type SearchVectorRsp_Doc_Text2SQLExtra struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TableInfos []*SearchVectorRsp_Doc_Text2SQLExtra_TableInfo `protobuf:"bytes,1,rep,name=table_infos,json=tableInfos,proto3" json:"table_infos,omitempty"`
}

func (x *SearchVectorRsp_Doc_Text2SQLExtra) Reset() {
	*x = SearchVectorRsp_Doc_Text2SQLExtra{}
	if protoimpl.UnsafeEnabled {
		mi := &file_retrieval_proto_msgTypes[113]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SearchVectorRsp_Doc_Text2SQLExtra) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SearchVectorRsp_Doc_Text2SQLExtra) ProtoMessage() {}

func (x *SearchVectorRsp_Doc_Text2SQLExtra) ProtoReflect() protoreflect.Message {
	mi := &file_retrieval_proto_msgTypes[113]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SearchVectorRsp_Doc_Text2SQLExtra.ProtoReflect.Descriptor instead.
func (*SearchVectorRsp_Doc_Text2SQLExtra) Descriptor() ([]byte, []int) {
	return file_retrieval_proto_rawDescGZIP(), []int{49, 0, 1}
}

func (x *SearchVectorRsp_Doc_Text2SQLExtra) GetTableInfos() []*SearchVectorRsp_Doc_Text2SQLExtra_TableInfo {
	if x != nil {
		return x.TableInfos
	}
	return nil
}

type SearchVectorRsp_Doc_Text2SQLExtra_TableInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TableName string `protobuf:"bytes,1,opt,name=table_name,json=tableName,proto3" json:"table_name,omitempty"`
	DocId     uint64 `protobuf:"varint,2,opt,name=doc_id,json=docId,proto3" json:"doc_id,omitempty"`
}

func (x *SearchVectorRsp_Doc_Text2SQLExtra_TableInfo) Reset() {
	*x = SearchVectorRsp_Doc_Text2SQLExtra_TableInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_retrieval_proto_msgTypes[114]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SearchVectorRsp_Doc_Text2SQLExtra_TableInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SearchVectorRsp_Doc_Text2SQLExtra_TableInfo) ProtoMessage() {}

func (x *SearchVectorRsp_Doc_Text2SQLExtra_TableInfo) ProtoReflect() protoreflect.Message {
	mi := &file_retrieval_proto_msgTypes[114]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SearchVectorRsp_Doc_Text2SQLExtra_TableInfo.ProtoReflect.Descriptor instead.
func (*SearchVectorRsp_Doc_Text2SQLExtra_TableInfo) Descriptor() ([]byte, []int) {
	return file_retrieval_proto_rawDescGZIP(), []int{49, 0, 1, 0}
}

func (x *SearchVectorRsp_Doc_Text2SQLExtra_TableInfo) GetTableName() string {
	if x != nil {
		return x.TableName
	}
	return ""
}

func (x *SearchVectorRsp_Doc_Text2SQLExtra_TableInfo) GetDocId() uint64 {
	if x != nil {
		return x.DocId
	}
	return 0
}

type DirectSearchVectorReq_Filter struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 索引库ID, 业务自己定义保存
	IndexId uint64 `protobuf:"varint,1,opt,name=index_id,json=indexId,proto3" json:"index_id,omitempty"`
	// 置信度
	Confidence float32 `protobuf:"fixed32,2,opt,name=confidence,proto3" json:"confidence,omitempty"`
	// 取 top_n
	TopN uint32 `protobuf:"varint,3,opt,name=top_n,json=topN,proto3" json:"top_n,omitempty"`
	// 文档类型 (1 QA, 2 文档段, 3 拒答问题, 4 搜索引擎)
	DocType uint32 `protobuf:"varint,4,opt,name=doc_type,json=docType,proto3" json:"doc_type,omitempty"`
	// 标签表达式字符串 key1="value1" and key2="value2"
	// 优先使用表达式字符串, 表达式字符串无值时使用表达式
	LabelExprString string `protobuf:"bytes,5,opt,name=label_expr_string,json=labelExprString,proto3" json:"label_expr_string,omitempty"`
	// 标签表达式
	LabelExpr *VectorLabelExpr `protobuf:"bytes,6,opt,name=label_expr,json=labelExpr,proto3" json:"label_expr,omitempty"`
}

func (x *DirectSearchVectorReq_Filter) Reset() {
	*x = DirectSearchVectorReq_Filter{}
	if protoimpl.UnsafeEnabled {
		mi := &file_retrieval_proto_msgTypes[115]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DirectSearchVectorReq_Filter) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DirectSearchVectorReq_Filter) ProtoMessage() {}

func (x *DirectSearchVectorReq_Filter) ProtoReflect() protoreflect.Message {
	mi := &file_retrieval_proto_msgTypes[115]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DirectSearchVectorReq_Filter.ProtoReflect.Descriptor instead.
func (*DirectSearchVectorReq_Filter) Descriptor() ([]byte, []int) {
	return file_retrieval_proto_rawDescGZIP(), []int{60, 0}
}

func (x *DirectSearchVectorReq_Filter) GetIndexId() uint64 {
	if x != nil {
		return x.IndexId
	}
	return 0
}

func (x *DirectSearchVectorReq_Filter) GetConfidence() float32 {
	if x != nil {
		return x.Confidence
	}
	return 0
}

func (x *DirectSearchVectorReq_Filter) GetTopN() uint32 {
	if x != nil {
		return x.TopN
	}
	return 0
}

func (x *DirectSearchVectorReq_Filter) GetDocType() uint32 {
	if x != nil {
		return x.DocType
	}
	return 0
}

func (x *DirectSearchVectorReq_Filter) GetLabelExprString() string {
	if x != nil {
		return x.LabelExprString
	}
	return ""
}

func (x *DirectSearchVectorReq_Filter) GetLabelExpr() *VectorLabelExpr {
	if x != nil {
		return x.LabelExpr
	}
	return nil
}

// 重排模型
type DirectSearchVectorReq_Rerank struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Model  string `protobuf:"bytes,1,opt,name=model,proto3" json:"model,omitempty"`            // 模型名称
	TopN   uint32 `protobuf:"varint,2,opt,name=top_n,json=topN,proto3" json:"top_n,omitempty"` // top_n
	Enable bool   `protobuf:"varint,3,opt,name=enable,proto3" json:"enable,omitempty"`         // 启用
}

func (x *DirectSearchVectorReq_Rerank) Reset() {
	*x = DirectSearchVectorReq_Rerank{}
	if protoimpl.UnsafeEnabled {
		mi := &file_retrieval_proto_msgTypes[116]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DirectSearchVectorReq_Rerank) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DirectSearchVectorReq_Rerank) ProtoMessage() {}

func (x *DirectSearchVectorReq_Rerank) ProtoReflect() protoreflect.Message {
	mi := &file_retrieval_proto_msgTypes[116]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DirectSearchVectorReq_Rerank.ProtoReflect.Descriptor instead.
func (*DirectSearchVectorReq_Rerank) Descriptor() ([]byte, []int) {
	return file_retrieval_proto_rawDescGZIP(), []int{60, 1}
}

func (x *DirectSearchVectorReq_Rerank) GetModel() string {
	if x != nil {
		return x.Model
	}
	return ""
}

func (x *DirectSearchVectorReq_Rerank) GetTopN() uint32 {
	if x != nil {
		return x.TopN
	}
	return 0
}

func (x *DirectSearchVectorReq_Rerank) GetEnable() bool {
	if x != nil {
		return x.Enable
	}
	return false
}

type DirectSearchVectorRsp_Doc struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 索引库ID, 业务自己定义保存
	IndexId uint64 `protobuf:"varint,1,opt,name=index_id,json=indexId,proto3" json:"index_id,omitempty"`
	// 对应文档类型关联的数据ID, 类型为 QA 时为 QAID, 类型为 文档段 时为 文档段ID
	Id uint64 `protobuf:"varint,2,opt,name=id,proto3" json:"id,omitempty"`
	// 置信度
	Confidence float32 `protobuf:"fixed32,3,opt,name=confidence,proto3" json:"confidence,omitempty"`
	// question
	Question string `protobuf:"bytes,4,opt,name=question,proto3" json:"question,omitempty"`
	// answer
	Answer string `protobuf:"bytes,5,opt,name=answer,proto3" json:"answer,omitempty"`
	// 内容
	PageContent string `protobuf:"bytes,6,opt,name=page_content,json=pageContent,proto3" json:"page_content,omitempty"`
	// 原始内容
	OrgData string `protobuf:"bytes,7,opt,name=org_data,json=orgData,proto3" json:"org_data,omitempty"`
	// 文档类型 (1 QA, 2 文档段, 3 拒答问题, 4 搜索引擎)
	DocType uint32 `protobuf:"varint,8,opt,name=doc_type,json=docType,proto3" json:"doc_type,omitempty"`
}

func (x *DirectSearchVectorRsp_Doc) Reset() {
	*x = DirectSearchVectorRsp_Doc{}
	if protoimpl.UnsafeEnabled {
		mi := &file_retrieval_proto_msgTypes[117]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DirectSearchVectorRsp_Doc) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DirectSearchVectorRsp_Doc) ProtoMessage() {}

func (x *DirectSearchVectorRsp_Doc) ProtoReflect() protoreflect.Message {
	mi := &file_retrieval_proto_msgTypes[117]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DirectSearchVectorRsp_Doc.ProtoReflect.Descriptor instead.
func (*DirectSearchVectorRsp_Doc) Descriptor() ([]byte, []int) {
	return file_retrieval_proto_rawDescGZIP(), []int{61, 0}
}

func (x *DirectSearchVectorRsp_Doc) GetIndexId() uint64 {
	if x != nil {
		return x.IndexId
	}
	return 0
}

func (x *DirectSearchVectorRsp_Doc) GetId() uint64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *DirectSearchVectorRsp_Doc) GetConfidence() float32 {
	if x != nil {
		return x.Confidence
	}
	return 0
}

func (x *DirectSearchVectorRsp_Doc) GetQuestion() string {
	if x != nil {
		return x.Question
	}
	return ""
}

func (x *DirectSearchVectorRsp_Doc) GetAnswer() string {
	if x != nil {
		return x.Answer
	}
	return ""
}

func (x *DirectSearchVectorRsp_Doc) GetPageContent() string {
	if x != nil {
		return x.PageContent
	}
	return ""
}

func (x *DirectSearchVectorRsp_Doc) GetOrgData() string {
	if x != nil {
		return x.OrgData
	}
	return ""
}

func (x *DirectSearchVectorRsp_Doc) GetDocType() uint32 {
	if x != nil {
		return x.DocType
	}
	return 0
}

// 表头
type Text2SQLMeta_Header struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Type Text2SQLMeta_Header_HeaderType `protobuf:"varint,1,opt,name=type,proto3,enum=trpc.KEP.bot_retrieval_server.Text2SQLMeta_Header_HeaderType" json:"type,omitempty"` // 表头类型
	Rows []*Row                         `protobuf:"bytes,2,rep,name=rows,proto3" json:"rows,omitempty"`                                                                    // 表头数据，可能有多行，取index 0
}

func (x *Text2SQLMeta_Header) Reset() {
	*x = Text2SQLMeta_Header{}
	if protoimpl.UnsafeEnabled {
		mi := &file_retrieval_proto_msgTypes[118]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Text2SQLMeta_Header) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Text2SQLMeta_Header) ProtoMessage() {}

func (x *Text2SQLMeta_Header) ProtoReflect() protoreflect.Message {
	mi := &file_retrieval_proto_msgTypes[118]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Text2SQLMeta_Header.ProtoReflect.Descriptor instead.
func (*Text2SQLMeta_Header) Descriptor() ([]byte, []int) {
	return file_retrieval_proto_rawDescGZIP(), []int{85, 0}
}

func (x *Text2SQLMeta_Header) GetType() Text2SQLMeta_Header_HeaderType {
	if x != nil {
		return x.Type
	}
	return Text2SQLMeta_Header_HEADER_TYPE_UNKNOWN
}

func (x *Text2SQLMeta_Header) GetRows() []*Row {
	if x != nil {
		return x.Rows
	}
	return nil
}

type RetrievalRealTimeReq_Filter struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 索引库ID, 业务自己定义保存
	IndexId uint64 `protobuf:"varint,1,opt,name=index_id,json=indexId,proto3" json:"index_id,omitempty"`
	// 置信度
	Confidence float32 `protobuf:"fixed32,2,opt,name=confidence,proto3" json:"confidence,omitempty"`
	// 取 top_n
	TopN uint32 `protobuf:"varint,3,opt,name=top_n,json=topN,proto3" json:"top_n,omitempty"`
	// 文档类型 (1 QA, 2 文档段, 3 拒答问题, 4 搜索引擎)
	DocType uint32 `protobuf:"varint,4,opt,name=doc_type,json=docType,proto3" json:"doc_type,omitempty"`
}

func (x *RetrievalRealTimeReq_Filter) Reset() {
	*x = RetrievalRealTimeReq_Filter{}
	if protoimpl.UnsafeEnabled {
		mi := &file_retrieval_proto_msgTypes[119]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RetrievalRealTimeReq_Filter) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RetrievalRealTimeReq_Filter) ProtoMessage() {}

func (x *RetrievalRealTimeReq_Filter) ProtoReflect() protoreflect.Message {
	mi := &file_retrieval_proto_msgTypes[119]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RetrievalRealTimeReq_Filter.ProtoReflect.Descriptor instead.
func (*RetrievalRealTimeReq_Filter) Descriptor() ([]byte, []int) {
	return file_retrieval_proto_rawDescGZIP(), []int{97, 0}
}

func (x *RetrievalRealTimeReq_Filter) GetIndexId() uint64 {
	if x != nil {
		return x.IndexId
	}
	return 0
}

func (x *RetrievalRealTimeReq_Filter) GetConfidence() float32 {
	if x != nil {
		return x.Confidence
	}
	return 0
}

func (x *RetrievalRealTimeReq_Filter) GetTopN() uint32 {
	if x != nil {
		return x.TopN
	}
	return 0
}

func (x *RetrievalRealTimeReq_Filter) GetDocType() uint32 {
	if x != nil {
		return x.DocType
	}
	return 0
}

// 重排模型
type RetrievalRealTimeReq_Rerank struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Model  string `protobuf:"bytes,1,opt,name=model,proto3" json:"model,omitempty"`            // 模型名称
	TopN   uint32 `protobuf:"varint,2,opt,name=top_n,json=topN,proto3" json:"top_n,omitempty"` // top_n
	Enable bool   `protobuf:"varint,3,opt,name=enable,proto3" json:"enable,omitempty"`         // 启用
}

func (x *RetrievalRealTimeReq_Rerank) Reset() {
	*x = RetrievalRealTimeReq_Rerank{}
	if protoimpl.UnsafeEnabled {
		mi := &file_retrieval_proto_msgTypes[120]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RetrievalRealTimeReq_Rerank) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RetrievalRealTimeReq_Rerank) ProtoMessage() {}

func (x *RetrievalRealTimeReq_Rerank) ProtoReflect() protoreflect.Message {
	mi := &file_retrieval_proto_msgTypes[120]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RetrievalRealTimeReq_Rerank.ProtoReflect.Descriptor instead.
func (*RetrievalRealTimeReq_Rerank) Descriptor() ([]byte, []int) {
	return file_retrieval_proto_rawDescGZIP(), []int{97, 1}
}

func (x *RetrievalRealTimeReq_Rerank) GetModel() string {
	if x != nil {
		return x.Model
	}
	return ""
}

func (x *RetrievalRealTimeReq_Rerank) GetTopN() uint32 {
	if x != nil {
		return x.TopN
	}
	return 0
}

func (x *RetrievalRealTimeReq_Rerank) GetEnable() bool {
	if x != nil {
		return x.Enable
	}
	return false
}

type RetrievalRealTimeRsp_Doc struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 索引库ID, 业务自己定义保存
	IndexId uint64 `protobuf:"varint,1,opt,name=index_id,json=indexId,proto3" json:"index_id,omitempty"`
	// 对应文档类型关联的数据ID, 类型为 QA 时为 QAID, 类型为 文档段 时为 文档段ID
	Id uint64 `protobuf:"varint,2,opt,name=id,proto3" json:"id,omitempty"`
	// 置信度
	Confidence float32 `protobuf:"fixed32,3,opt,name=confidence,proto3" json:"confidence,omitempty"`
	// question
	Question string `protobuf:"bytes,4,opt,name=question,proto3" json:"question,omitempty"`
	// answer
	Answer string `protobuf:"bytes,5,opt,name=answer,proto3" json:"answer,omitempty"`
	// 内容
	PageContent string `protobuf:"bytes,6,opt,name=page_content,json=pageContent,proto3" json:"page_content,omitempty"`
	// 原始内容
	OrgData string `protobuf:"bytes,7,opt,name=org_data,json=orgData,proto3" json:"org_data,omitempty"`
	// 文档类型 (1 QA, 2 文档段, 3 拒答问题, 4 搜索引擎)
	DocType uint32 `protobuf:"varint,8,opt,name=doc_type,json=docType,proto3" json:"doc_type,omitempty"`
	// 是否big_data true-表示org_data是由big_data填充
	IsBigData bool `protobuf:"varint,9,opt,name=is_big_data,json=isBigData,proto3" json:"is_big_data,omitempty"`
	// 算法扩展信息
	Extra *RetrievalExtra `protobuf:"bytes,10,opt,name=extra,proto3" json:"extra,omitempty"`
	// 文档ID
	DocId uint64 `protobuf:"varint,11,opt,name=doc_id,json=docId,proto3" json:"doc_id,omitempty"`
	// 检索结果类型
	ResultType RetrievalResultType `protobuf:"varint,12,opt,name=result_type,json=resultType,proto3,enum=trpc.KEP.bot_retrieval_server.RetrievalResultType" json:"result_type,omitempty"`
	// 检索命中的图片URL
	ImageUrls []string `protobuf:"bytes,13,rep,name=image_urls,json=imageUrls,proto3" json:"image_urls,omitempty"`
}

func (x *RetrievalRealTimeRsp_Doc) Reset() {
	*x = RetrievalRealTimeRsp_Doc{}
	if protoimpl.UnsafeEnabled {
		mi := &file_retrieval_proto_msgTypes[121]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RetrievalRealTimeRsp_Doc) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RetrievalRealTimeRsp_Doc) ProtoMessage() {}

func (x *RetrievalRealTimeRsp_Doc) ProtoReflect() protoreflect.Message {
	mi := &file_retrieval_proto_msgTypes[121]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RetrievalRealTimeRsp_Doc.ProtoReflect.Descriptor instead.
func (*RetrievalRealTimeRsp_Doc) Descriptor() ([]byte, []int) {
	return file_retrieval_proto_rawDescGZIP(), []int{98, 0}
}

func (x *RetrievalRealTimeRsp_Doc) GetIndexId() uint64 {
	if x != nil {
		return x.IndexId
	}
	return 0
}

func (x *RetrievalRealTimeRsp_Doc) GetId() uint64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *RetrievalRealTimeRsp_Doc) GetConfidence() float32 {
	if x != nil {
		return x.Confidence
	}
	return 0
}

func (x *RetrievalRealTimeRsp_Doc) GetQuestion() string {
	if x != nil {
		return x.Question
	}
	return ""
}

func (x *RetrievalRealTimeRsp_Doc) GetAnswer() string {
	if x != nil {
		return x.Answer
	}
	return ""
}

func (x *RetrievalRealTimeRsp_Doc) GetPageContent() string {
	if x != nil {
		return x.PageContent
	}
	return ""
}

func (x *RetrievalRealTimeRsp_Doc) GetOrgData() string {
	if x != nil {
		return x.OrgData
	}
	return ""
}

func (x *RetrievalRealTimeRsp_Doc) GetDocType() uint32 {
	if x != nil {
		return x.DocType
	}
	return 0
}

func (x *RetrievalRealTimeRsp_Doc) GetIsBigData() bool {
	if x != nil {
		return x.IsBigData
	}
	return false
}

func (x *RetrievalRealTimeRsp_Doc) GetExtra() *RetrievalExtra {
	if x != nil {
		return x.Extra
	}
	return nil
}

func (x *RetrievalRealTimeRsp_Doc) GetDocId() uint64 {
	if x != nil {
		return x.DocId
	}
	return 0
}

func (x *RetrievalRealTimeRsp_Doc) GetResultType() RetrievalResultType {
	if x != nil {
		return x.ResultType
	}
	return RetrievalResultType_RETRIEVAL
}

func (x *RetrievalRealTimeRsp_Doc) GetImageUrls() []string {
	if x != nil {
		return x.ImageUrls
	}
	return nil
}

var File_retrieval_proto protoreflect.FileDescriptor

var file_retrieval_proto_rawDesc = []byte{
	0x0a, 0x0f, 0x72, 0x65, 0x74, 0x72, 0x69, 0x65, 0x76, 0x61, 0x6c, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x12, 0x1d, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f,
	0x72, 0x65, 0x74, 0x72, 0x69, 0x65, 0x76, 0x61, 0x6c, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72,
	0x1a, 0x0e, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x22, 0x99, 0x01, 0x0a, 0x0b, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x45, 0x53, 0x52, 0x65, 0x71,
	0x12, 0x19, 0x0a, 0x08, 0x72, 0x6f, 0x62, 0x6f, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x04, 0x52, 0x07, 0x72, 0x6f, 0x62, 0x6f, 0x74, 0x49, 0x64, 0x12, 0x15, 0x0a, 0x06, 0x64,
	0x6f, 0x63, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x04, 0x52, 0x05, 0x64, 0x6f, 0x63,
	0x49, 0x64, 0x12, 0x1a, 0x0a, 0x08, 0x71, 0x75, 0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x71, 0x75, 0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x1f,
	0x0a, 0x0b, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x0d, 0x52, 0x0a, 0x70, 0x61, 0x67, 0x65, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x12,
	0x1b, 0x0a, 0x09, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x0d, 0x52, 0x08, 0x70, 0x61, 0x67, 0x65, 0x53, 0x69, 0x7a, 0x65, 0x22, 0x5e, 0x0a, 0x0b,
	0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x45, 0x53, 0x52, 0x73, 0x70, 0x12, 0x14, 0x0a, 0x05, 0x74,
	0x6f, 0x74, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x05, 0x74, 0x6f, 0x74, 0x61,
	0x6c, 0x12, 0x39, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x25, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x72,
	0x65, 0x74, 0x72, 0x69, 0x65, 0x76, 0x61, 0x6c, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e,
	0x45, 0x53, 0x44, 0x61, 0x74, 0x61, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x22, 0x8f, 0x01, 0x0a,
	0x0f, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x45, 0x53, 0x44, 0x61, 0x74, 0x61, 0x52, 0x65, 0x71,
	0x12, 0x19, 0x0a, 0x08, 0x72, 0x6f, 0x62, 0x6f, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x04, 0x52, 0x07, 0x72, 0x6f, 0x62, 0x6f, 0x74, 0x49, 0x64, 0x12, 0x15, 0x0a, 0x06, 0x64,
	0x6f, 0x63, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x64, 0x6f, 0x63,
	0x49, 0x64, 0x12, 0x4a, 0x0a, 0x0a, 0x64, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x5f, 0x69, 0x64, 0x73,
	0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2b, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45,
	0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x72, 0x65, 0x74, 0x72, 0x69, 0x65, 0x76, 0x61, 0x6c, 0x5f,
	0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x45, 0x53, 0x44, 0x61, 0x74, 0x61, 0x49, 0x44, 0x54,
	0x79, 0x70, 0x65, 0x52, 0x09, 0x64, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x49, 0x64, 0x73, 0x22, 0x6d,
	0x0a, 0x0c, 0x45, 0x53, 0x44, 0x61, 0x74, 0x61, 0x49, 0x44, 0x54, 0x79, 0x70, 0x65, 0x12, 0x46,
	0x0a, 0x09, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x29, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74,
	0x5f, 0x72, 0x65, 0x74, 0x72, 0x69, 0x65, 0x76, 0x61, 0x6c, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65,
	0x72, 0x2e, 0x45, 0x53, 0x44, 0x61, 0x74, 0x61, 0x54, 0x79, 0x70, 0x65, 0x52, 0x08, 0x64, 0x61,
	0x74, 0x61, 0x54, 0x79, 0x70, 0x65, 0x12, 0x15, 0x0a, 0x06, 0x62, 0x69, 0x7a, 0x5f, 0x69, 0x64,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x62, 0x69, 0x7a, 0x49, 0x64, 0x22, 0x11, 0x0a,
	0x0f, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x45, 0x53, 0x44, 0x61, 0x74, 0x61, 0x52, 0x73, 0x70,
	0x22, 0x64, 0x0a, 0x0c, 0x41, 0x64, 0x64, 0x45, 0x53, 0x44, 0x61, 0x74, 0x61, 0x52, 0x65, 0x71,
	0x12, 0x19, 0x0a, 0x08, 0x72, 0x6f, 0x62, 0x6f, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x04, 0x52, 0x07, 0x72, 0x6f, 0x62, 0x6f, 0x74, 0x49, 0x64, 0x12, 0x39, 0x0a, 0x04, 0x64,
	0x61, 0x74, 0x61, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x25, 0x2e, 0x74, 0x72, 0x70, 0x63,
	0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x72, 0x65, 0x74, 0x72, 0x69, 0x65, 0x76,
	0x61, 0x6c, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x45, 0x53, 0x44, 0x61, 0x74, 0x61,
	0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x22, 0x98, 0x01, 0x0a, 0x06, 0x45, 0x53, 0x44, 0x61, 0x74,
	0x61, 0x12, 0x46, 0x0a, 0x09, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x29, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e,
	0x62, 0x6f, 0x74, 0x5f, 0x72, 0x65, 0x74, 0x72, 0x69, 0x65, 0x76, 0x61, 0x6c, 0x5f, 0x73, 0x65,
	0x72, 0x76, 0x65, 0x72, 0x2e, 0x45, 0x53, 0x44, 0x61, 0x74, 0x61, 0x54, 0x79, 0x70, 0x65, 0x52,
	0x08, 0x64, 0x61, 0x74, 0x61, 0x54, 0x79, 0x70, 0x65, 0x12, 0x15, 0x0a, 0x06, 0x62, 0x69, 0x7a,
	0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x62, 0x69, 0x7a, 0x49, 0x64,
	0x12, 0x15, 0x0a, 0x06, 0x64, 0x6f, 0x63, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x05, 0x64, 0x6f, 0x63, 0x49, 0x64, 0x12, 0x18, 0x0a, 0x07, 0x63, 0x6f, 0x6e, 0x74, 0x65,
	0x6e, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e,
	0x74, 0x22, 0x11, 0x0a, 0x0f, 0x41, 0x64, 0x64, 0x45, 0x53, 0x44, 0x61, 0x74, 0x61, 0x52, 0x65,
	0x71, 0x52, 0x73, 0x70, 0x22, 0xd8, 0x02, 0x0a, 0x10, 0x41, 0x64, 0x64, 0x44, 0x42, 0x54, 0x65,
	0x78, 0x74, 0x32, 0x53, 0x51, 0x4c, 0x52, 0x65, 0x71, 0x12, 0x22, 0x0a, 0x08, 0x72, 0x6f, 0x62,
	0x6f, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x42, 0x07, 0xfa, 0x42, 0x04,
	0x32, 0x02, 0x28, 0x01, 0x52, 0x07, 0x72, 0x6f, 0x62, 0x6f, 0x74, 0x49, 0x64, 0x12, 0x30, 0x0a,
	0x10, 0x64, 0x62, 0x5f, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x62, 0x69, 0x7a, 0x5f, 0x69,
	0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x04, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x32, 0x02, 0x28, 0x01,
	0x52, 0x0d, 0x64, 0x62, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x42, 0x69, 0x7a, 0x49, 0x64, 0x12,
	0x3e, 0x0a, 0x07, 0x64, 0x62, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e,
	0x32, 0x25, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f,
	0x72, 0x65, 0x74, 0x72, 0x69, 0x65, 0x76, 0x61, 0x6c, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72,
	0x2e, 0x44, 0x42, 0x54, 0x79, 0x70, 0x65, 0x52, 0x06, 0x64, 0x62, 0x54, 0x79, 0x70, 0x65, 0x12,
	0x17, 0x0a, 0x07, 0x64, 0x62, 0x5f, 0x64, 0x65, 0x73, 0x63, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x06, 0x64, 0x62, 0x44, 0x65, 0x73, 0x63, 0x12, 0x2e, 0x0a, 0x0f, 0x64, 0x62, 0x5f, 0x74,
	0x61, 0x62, 0x6c, 0x65, 0x5f, 0x62, 0x69, 0x7a, 0x5f, 0x69, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x04, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x32, 0x02, 0x28, 0x01, 0x52, 0x0c, 0x64, 0x62, 0x54, 0x61,
	0x62, 0x6c, 0x65, 0x42, 0x69, 0x7a, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x74, 0x61, 0x62, 0x6c,
	0x65, 0x5f, 0x64, 0x65, 0x73, 0x63, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x74, 0x61,
	0x62, 0x6c, 0x65, 0x44, 0x65, 0x73, 0x63, 0x12, 0x46, 0x0a, 0x04, 0x72, 0x6f, 0x77, 0x73, 0x18,
	0x07, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x28, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50,
	0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x72, 0x65, 0x74, 0x72, 0x69, 0x65, 0x76, 0x61, 0x6c, 0x5f, 0x73,
	0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x44, 0x42, 0x52, 0x6f, 0x77, 0x44, 0x61, 0x74, 0x61, 0x42,
	0x08, 0xfa, 0x42, 0x05, 0x92, 0x01, 0x02, 0x08, 0x01, 0x52, 0x04, 0x72, 0x6f, 0x77, 0x73, 0x22,
	0x52, 0x0a, 0x09, 0x44, 0x42, 0x52, 0x6f, 0x77, 0x44, 0x61, 0x74, 0x61, 0x12, 0x45, 0x0a, 0x05,
	0x63, 0x65, 0x6c, 0x6c, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x25, 0x2e, 0x74, 0x72,
	0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x72, 0x65, 0x74, 0x72, 0x69,
	0x65, 0x76, 0x61, 0x6c, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x44, 0x42, 0x43, 0x65,
	0x6c, 0x6c, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x92, 0x01, 0x02, 0x08, 0x01, 0x52, 0x05, 0x63, 0x65,
	0x6c, 0x6c, 0x73, 0x22, 0xa9, 0x01, 0x0a, 0x06, 0x44, 0x42, 0x43, 0x65, 0x6c, 0x6c, 0x12, 0x1f,
	0x0a, 0x0b, 0x63, 0x6f, 0x6c, 0x75, 0x6d, 0x6e, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0a, 0x63, 0x6f, 0x6c, 0x75, 0x6d, 0x6e, 0x4e, 0x61, 0x6d, 0x65, 0x12,
	0x2a, 0x0a, 0x11, 0x63, 0x6f, 0x6c, 0x75, 0x6d, 0x6e, 0x5f, 0x61, 0x6c, 0x69, 0x61, 0x73, 0x5f,
	0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x63, 0x6f, 0x6c, 0x75,
	0x6d, 0x6e, 0x41, 0x6c, 0x69, 0x61, 0x73, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x63,
	0x6f, 0x6c, 0x75, 0x6d, 0x6e, 0x5f, 0x64, 0x65, 0x73, 0x63, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0a, 0x63, 0x6f, 0x6c, 0x75, 0x6d, 0x6e, 0x44, 0x65, 0x73, 0x63, 0x12, 0x1b, 0x0a, 0x09,
	0x64, 0x61, 0x74, 0x61, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x08, 0x64, 0x61, 0x74, 0x61, 0x54, 0x79, 0x70, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c,
	0x75, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x22,
	0x12, 0x0a, 0x10, 0x41, 0x64, 0x64, 0x44, 0x42, 0x54, 0x65, 0x78, 0x74, 0x32, 0x53, 0x51, 0x4c,
	0x52, 0x73, 0x70, 0x22, 0x6a, 0x0a, 0x13, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x44, 0x42, 0x54,
	0x65, 0x78, 0x74, 0x32, 0x53, 0x51, 0x4c, 0x52, 0x65, 0x71, 0x12, 0x22, 0x0a, 0x08, 0x72, 0x6f,
	0x62, 0x6f, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x42, 0x07, 0xfa, 0x42,
	0x04, 0x32, 0x02, 0x28, 0x01, 0x52, 0x07, 0x72, 0x6f, 0x62, 0x6f, 0x74, 0x49, 0x64, 0x12, 0x2f,
	0x0a, 0x0f, 0x64, 0x62, 0x5f, 0x74, 0x61, 0x62, 0x6c, 0x65, 0x5f, 0x62, 0x69, 0x7a, 0x5f, 0x69,
	0x64, 0x18, 0x02, 0x20, 0x03, 0x28, 0x04, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x92, 0x01, 0x02, 0x08,
	0x01, 0x52, 0x0c, 0x64, 0x62, 0x54, 0x61, 0x62, 0x6c, 0x65, 0x42, 0x69, 0x7a, 0x49, 0x64, 0x22,
	0x15, 0x0a, 0x13, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x44, 0x42, 0x54, 0x65, 0x78, 0x74, 0x32,
	0x53, 0x51, 0x4c, 0x52, 0x73, 0x70, 0x22, 0x99, 0x01, 0x0a, 0x0e, 0x53, 0x65, 0x61, 0x72, 0x63,
	0x68, 0x53, 0x74, 0x72, 0x61, 0x74, 0x65, 0x67, 0x79, 0x12, 0x5a, 0x0a, 0x0d, 0x73, 0x74, 0x72,
	0x61, 0x74, 0x65, 0x67, 0x79, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e,
	0x32, 0x35, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f,
	0x72, 0x65, 0x74, 0x72, 0x69, 0x65, 0x76, 0x61, 0x6c, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72,
	0x2e, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x53, 0x74, 0x72, 0x61, 0x74, 0x65, 0x67, 0x79, 0x54,
	0x79, 0x70, 0x65, 0x45, 0x6e, 0x75, 0x6d, 0x52, 0x0c, 0x73, 0x74, 0x72, 0x61, 0x74, 0x65, 0x67,
	0x79, 0x54, 0x79, 0x70, 0x65, 0x12, 0x2b, 0x0a, 0x11, 0x74, 0x61, 0x62, 0x6c, 0x65, 0x5f, 0x65,
	0x6e, 0x68, 0x61, 0x6e, 0x63, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08,
	0x52, 0x10, 0x74, 0x61, 0x62, 0x6c, 0x65, 0x45, 0x6e, 0x68, 0x61, 0x6e, 0x63, 0x65, 0x6d, 0x65,
	0x6e, 0x74, 0x22, 0xeb, 0x03, 0x0a, 0x0f, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x45, 0x78, 0x70, 0x72,
	0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x53, 0x0a, 0x08, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74,
	0x6f, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x37, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e,
	0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x72, 0x65, 0x74, 0x72, 0x69, 0x65, 0x76, 0x61,
	0x6c, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x45, 0x78,
	0x70, 0x72, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x2e, 0x4c, 0x6f, 0x67, 0x69, 0x63, 0x4f, 0x70,
	0x72, 0x52, 0x08, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x12, 0x50, 0x0a, 0x0b, 0x65,
	0x78, 0x70, 0x72, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x2e, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f,
	0x72, 0x65, 0x74, 0x72, 0x69, 0x65, 0x76, 0x61, 0x6c, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72,
	0x2e, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x45, 0x78, 0x70, 0x72, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e,
	0x52, 0x0b, 0x65, 0x78, 0x70, 0x72, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x56, 0x0a,
	0x09, 0x63, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x38, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f,
	0x72, 0x65, 0x74, 0x72, 0x69, 0x65, 0x76, 0x61, 0x6c, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72,
	0x2e, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x45, 0x78, 0x70, 0x72, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e,
	0x2e, 0x43, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x09, 0x63, 0x6f, 0x6e, 0x64,
	0x69, 0x74, 0x69, 0x6f, 0x6e, 0x1a, 0xb1, 0x01, 0x0a, 0x09, 0x43, 0x6f, 0x6e, 0x64, 0x69, 0x74,
	0x69, 0x6f, 0x6e, 0x12, 0x55, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x41, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74,
	0x5f, 0x72, 0x65, 0x74, 0x72, 0x69, 0x65, 0x76, 0x61, 0x6c, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65,
	0x72, 0x2e, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x45, 0x78, 0x70, 0x72, 0x65, 0x73, 0x73, 0x69, 0x6f,
	0x6e, 0x2e, 0x43, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x44, 0x61, 0x74, 0x61,
	0x54, 0x79, 0x70, 0x65, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61,
	0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x16,
	0x0a, 0x06, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x09, 0x52, 0x06,
	0x76, 0x61, 0x6c, 0x75, 0x65, 0x73, 0x22, 0x21, 0x0a, 0x08, 0x44, 0x61, 0x74, 0x61, 0x54, 0x79,
	0x70, 0x65, 0x12, 0x09, 0x0a, 0x05, 0x41, 0x52, 0x52, 0x41, 0x59, 0x10, 0x00, 0x12, 0x0a, 0x0a,
	0x06, 0x53, 0x54, 0x52, 0x49, 0x4e, 0x47, 0x10, 0x01, 0x22, 0x25, 0x0a, 0x08, 0x4c, 0x6f, 0x67,
	0x69, 0x63, 0x4f, 0x70, 0x72, 0x12, 0x08, 0x0a, 0x04, 0x4e, 0x4f, 0x4f, 0x50, 0x10, 0x00, 0x12,
	0x07, 0x0a, 0x03, 0x41, 0x4e, 0x44, 0x10, 0x01, 0x12, 0x06, 0x0a, 0x02, 0x4f, 0x52, 0x10, 0x02,
	0x22, 0x37, 0x0a, 0x0b, 0x56, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x12,
	0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e,
	0x61, 0x6d, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x22, 0x3f, 0x0a, 0x11, 0x53, 0x65, 0x61,
	0x72, 0x63, 0x68, 0x56, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x12, 0x12,
	0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61,
	0x6d, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x73, 0x18, 0x02, 0x20, 0x03,
	0x28, 0x09, 0x52, 0x06, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x73, 0x22, 0xd8, 0x03, 0x0a, 0x0f, 0x56,
	0x65, 0x63, 0x74, 0x6f, 0x72, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x45, 0x78, 0x70, 0x72, 0x12, 0x47,
	0x0a, 0x02, 0x6f, 0x70, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x37, 0x2e, 0x74, 0x72, 0x70,
	0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x72, 0x65, 0x74, 0x72, 0x69, 0x65,
	0x76, 0x61, 0x6c, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x56, 0x65, 0x63, 0x74, 0x6f,
	0x72, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x45, 0x78, 0x70, 0x72, 0x2e, 0x4f, 0x70, 0x65, 0x72, 0x61,
	0x74, 0x6f, 0x72, 0x52, 0x02, 0x6f, 0x70, 0x12, 0x50, 0x0a, 0x0b, 0x65, 0x78, 0x70, 0x72, 0x65,
	0x73, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2e, 0x2e, 0x74,
	0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x72, 0x65, 0x74, 0x72,
	0x69, 0x65, 0x76, 0x61, 0x6c, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x56, 0x65, 0x63,
	0x74, 0x6f, 0x72, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x45, 0x78, 0x70, 0x72, 0x52, 0x0b, 0x65, 0x78,
	0x70, 0x72, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x47, 0x0a, 0x04, 0x74, 0x79, 0x70,
	0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x33, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b,
	0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x72, 0x65, 0x74, 0x72, 0x69, 0x65, 0x76, 0x61, 0x6c,
	0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x56, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x4c, 0x61,
	0x62, 0x65, 0x6c, 0x45, 0x78, 0x70, 0x72, 0x2e, 0x54, 0x79, 0x70, 0x65, 0x52, 0x04, 0x74, 0x79,
	0x70, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x22, 0xad, 0x01, 0x0a, 0x08, 0x4f, 0x70, 0x65,
	0x72, 0x61, 0x74, 0x6f, 0x72, 0x12, 0x08, 0x0a, 0x04, 0x4e, 0x4f, 0x4f, 0x50, 0x10, 0x00, 0x12,
	0x09, 0x0a, 0x05, 0x53, 0x43, 0x41, 0x4c, 0x45, 0x10, 0x01, 0x12, 0x07, 0x0a, 0x03, 0x41, 0x4e,
	0x44, 0x10, 0x02, 0x12, 0x06, 0x0a, 0x02, 0x4f, 0x52, 0x10, 0x03, 0x12, 0x07, 0x0a, 0x03, 0x4e,
	0x4f, 0x54, 0x10, 0x04, 0x12, 0x06, 0x0a, 0x02, 0x49, 0x4e, 0x10, 0x05, 0x12, 0x0a, 0x0a, 0x06,
	0x4e, 0x4f, 0x54, 0x5f, 0x49, 0x4e, 0x10, 0x06, 0x12, 0x0a, 0x0a, 0x06, 0x41, 0x4c, 0x4c, 0x5f,
	0x49, 0x4e, 0x10, 0x07, 0x12, 0x09, 0x0a, 0x05, 0x45, 0x51, 0x55, 0x41, 0x4c, 0x10, 0x08, 0x12,
	0x0d, 0x0a, 0x09, 0x4e, 0x4f, 0x54, 0x5f, 0x45, 0x51, 0x55, 0x41, 0x4c, 0x10, 0x09, 0x12, 0x0b,
	0x0a, 0x07, 0x47, 0x52, 0x45, 0x41, 0x54, 0x45, 0x52, 0x10, 0x0a, 0x12, 0x11, 0x0a, 0x0d, 0x47,
	0x52, 0x45, 0x41, 0x54, 0x45, 0x52, 0x5f, 0x45, 0x51, 0x55, 0x41, 0x4c, 0x10, 0x0b, 0x12, 0x08,
	0x0a, 0x04, 0x4c, 0x45, 0x53, 0x53, 0x10, 0x0c, 0x12, 0x0e, 0x0a, 0x0a, 0x4c, 0x45, 0x53, 0x53,
	0x5f, 0x45, 0x51, 0x55, 0x41, 0x4c, 0x10, 0x0d, 0x22, 0x1b, 0x0a, 0x04, 0x54, 0x79, 0x70, 0x65,
	0x12, 0x07, 0x0a, 0x03, 0x49, 0x4e, 0x54, 0x10, 0x00, 0x12, 0x0a, 0x0a, 0x06, 0x53, 0x54, 0x52,
	0x49, 0x4e, 0x47, 0x10, 0x01, 0x22, 0xdb, 0x01, 0x0a, 0x0e, 0x52, 0x65, 0x74, 0x72, 0x69, 0x65,
	0x76, 0x61, 0x6c, 0x45, 0x78, 0x74, 0x72, 0x61, 0x12, 0x19, 0x0a, 0x08, 0x65, 0x6d, 0x62, 0x5f,
	0x72, 0x61, 0x6e, 0x6b, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x07, 0x65, 0x6d, 0x62, 0x52,
	0x61, 0x6e, 0x6b, 0x12, 0x19, 0x0a, 0x08, 0x65, 0x73, 0x5f, 0x73, 0x63, 0x6f, 0x72, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x02, 0x52, 0x07, 0x65, 0x73, 0x53, 0x63, 0x6f, 0x72, 0x65, 0x12, 0x17,
	0x0a, 0x07, 0x65, 0x73, 0x5f, 0x72, 0x61, 0x6e, 0x6b, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x06, 0x65, 0x73, 0x52, 0x61, 0x6e, 0x6b, 0x12, 0x21, 0x0a, 0x0c, 0x72, 0x65, 0x72, 0x61, 0x6e,
	0x6b, 0x5f, 0x73, 0x63, 0x6f, 0x72, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x02, 0x52, 0x0b, 0x72,
	0x65, 0x72, 0x61, 0x6e, 0x6b, 0x53, 0x63, 0x6f, 0x72, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x72, 0x65,
	0x72, 0x61, 0x6e, 0x6b, 0x5f, 0x72, 0x61, 0x6e, 0x6b, 0x18, 0x05, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x0a, 0x72, 0x65, 0x72, 0x61, 0x6e, 0x6b, 0x52, 0x61, 0x6e, 0x6b, 0x12, 0x1b, 0x0a, 0x09, 0x72,
	0x72, 0x66, 0x5f, 0x73, 0x63, 0x6f, 0x72, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x02, 0x52, 0x08,
	0x72, 0x72, 0x66, 0x53, 0x63, 0x6f, 0x72, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x72, 0x72, 0x66, 0x5f,
	0x72, 0x61, 0x6e, 0x6b, 0x18, 0x07, 0x20, 0x01, 0x28, 0x05, 0x52, 0x07, 0x72, 0x72, 0x66, 0x52,
	0x61, 0x6e, 0x6b, 0x22, 0xe5, 0x07, 0x0a, 0x09, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x52, 0x65,
	0x71, 0x12, 0x22, 0x0a, 0x08, 0x72, 0x6f, 0x62, 0x6f, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x04, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x32, 0x02, 0x28, 0x01, 0x52, 0x07, 0x72, 0x6f,
	0x62, 0x6f, 0x74, 0x49, 0x64, 0x12, 0x26, 0x0a, 0x0a, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e,
	0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x04, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x32, 0x02,
	0x28, 0x01, 0x52, 0x09, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x12, 0x23, 0x0a,
	0x08, 0x71, 0x75, 0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x42,
	0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x10, 0x01, 0x52, 0x08, 0x71, 0x75, 0x65, 0x73, 0x74, 0x69,
	0x6f, 0x6e, 0x12, 0x49, 0x0a, 0x07, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x73, 0x18, 0x04, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x2f, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62,
	0x6f, 0x74, 0x5f, 0x72, 0x65, 0x74, 0x72, 0x69, 0x65, 0x76, 0x61, 0x6c, 0x5f, 0x73, 0x65, 0x72,
	0x76, 0x65, 0x72, 0x2e, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x52, 0x65, 0x71, 0x2e, 0x46, 0x69,
	0x6c, 0x74, 0x65, 0x72, 0x52, 0x07, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x73, 0x12, 0x13, 0x0a,
	0x05, 0x74, 0x6f, 0x70, 0x5f, 0x6e, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x04, 0x74, 0x6f,
	0x70, 0x4e, 0x12, 0x47, 0x0a, 0x06, 0x72, 0x65, 0x72, 0x61, 0x6e, 0x6b, 0x18, 0x07, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x2f, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f,
	0x74, 0x5f, 0x72, 0x65, 0x74, 0x72, 0x69, 0x65, 0x76, 0x61, 0x6c, 0x5f, 0x73, 0x65, 0x72, 0x76,
	0x65, 0x72, 0x2e, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x52, 0x65, 0x71, 0x2e, 0x52, 0x65, 0x72,
	0x61, 0x6e, 0x6b, 0x52, 0x06, 0x72, 0x65, 0x72, 0x61, 0x6e, 0x6b, 0x12, 0x1d, 0x0a, 0x0a, 0x66,
	0x69, 0x6c, 0x74, 0x65, 0x72, 0x5f, 0x6b, 0x65, 0x79, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x09, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x4b, 0x65, 0x79, 0x12, 0x48, 0x0a, 0x06, 0x6c, 0x61,
	0x62, 0x65, 0x6c, 0x73, 0x18, 0x09, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x30, 0x2e, 0x74, 0x72, 0x70,
	0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x72, 0x65, 0x74, 0x72, 0x69, 0x65,
	0x76, 0x61, 0x6c, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x53, 0x65, 0x61, 0x72, 0x63,
	0x68, 0x56, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x52, 0x06, 0x6c, 0x61,
	0x62, 0x65, 0x6c, 0x73, 0x12, 0x1d, 0x0a, 0x0a, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x5f, 0x75, 0x72,
	0x6c, 0x73, 0x18, 0x0a, 0x20, 0x03, 0x28, 0x09, 0x52, 0x09, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x55,
	0x72, 0x6c, 0x73, 0x12, 0x1c, 0x0a, 0x0a, 0x62, 0x6f, 0x74, 0x5f, 0x62, 0x69, 0x7a, 0x5f, 0x69,
	0x64, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x04, 0x52, 0x08, 0x62, 0x6f, 0x74, 0x42, 0x69, 0x7a, 0x49,
	0x64, 0x12, 0x59, 0x0a, 0x10, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x5f, 0x65, 0x78, 0x70, 0x72, 0x65,
	0x73, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2e, 0x2e, 0x74, 0x72,
	0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x72, 0x65, 0x74, 0x72, 0x69,
	0x65, 0x76, 0x61, 0x6c, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x4c, 0x61, 0x62, 0x65,
	0x6c, 0x45, 0x78, 0x70, 0x72, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x52, 0x0f, 0x6c, 0x61, 0x62,
	0x65, 0x6c, 0x45, 0x78, 0x70, 0x72, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x23, 0x0a, 0x0d,
	0x73, 0x75, 0x62, 0x5f, 0x71, 0x75, 0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x0d, 0x20,
	0x03, 0x28, 0x09, 0x52, 0x0c, 0x73, 0x75, 0x62, 0x51, 0x75, 0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e,
	0x73, 0x12, 0x56, 0x0a, 0x0f, 0x73, 0x65, 0x61, 0x72, 0x63, 0x68, 0x5f, 0x73, 0x74, 0x72, 0x61,
	0x74, 0x65, 0x67, 0x79, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2d, 0x2e, 0x74, 0x72, 0x70,
	0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x72, 0x65, 0x74, 0x72, 0x69, 0x65,
	0x76, 0x61, 0x6c, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x53, 0x65, 0x61, 0x72, 0x63,
	0x68, 0x53, 0x74, 0x72, 0x61, 0x74, 0x65, 0x67, 0x79, 0x52, 0x0e, 0x73, 0x65, 0x61, 0x72, 0x63,
	0x68, 0x53, 0x74, 0x72, 0x61, 0x74, 0x65, 0x67, 0x79, 0x12, 0x1d, 0x0a, 0x0a, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x4e, 0x61, 0x6d, 0x65, 0x1a, 0xd3, 0x01, 0x0a, 0x06, 0x46, 0x69, 0x6c,
	0x74, 0x65, 0x72, 0x12, 0x19, 0x0a, 0x08, 0x64, 0x6f, 0x63, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x07, 0x64, 0x6f, 0x63, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1e,
	0x0a, 0x0a, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x64, 0x65, 0x6e, 0x63, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x02, 0x52, 0x0a, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x64, 0x65, 0x6e, 0x63, 0x65, 0x12, 0x13,
	0x0a, 0x05, 0x74, 0x6f, 0x70, 0x5f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x04, 0x74,
	0x6f, 0x70, 0x4e, 0x12, 0x2a, 0x0a, 0x11, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x5f, 0x65, 0x78, 0x70,
	0x72, 0x5f, 0x73, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f,
	0x6c, 0x61, 0x62, 0x65, 0x6c, 0x45, 0x78, 0x70, 0x72, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x12,
	0x4d, 0x0a, 0x0a, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x5f, 0x65, 0x78, 0x70, 0x72, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x2e, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62,
	0x6f, 0x74, 0x5f, 0x72, 0x65, 0x74, 0x72, 0x69, 0x65, 0x76, 0x61, 0x6c, 0x5f, 0x73, 0x65, 0x72,
	0x76, 0x65, 0x72, 0x2e, 0x56, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x45,
	0x78, 0x70, 0x72, 0x52, 0x09, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x45, 0x78, 0x70, 0x72, 0x1a, 0x4b,
	0x0a, 0x06, 0x52, 0x65, 0x72, 0x61, 0x6e, 0x6b, 0x12, 0x14, 0x0a, 0x05, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x12, 0x13,
	0x0a, 0x05, 0x74, 0x6f, 0x70, 0x5f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x04, 0x74,
	0x6f, 0x70, 0x4e, 0x12, 0x16, 0x0a, 0x06, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x08, 0x52, 0x06, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x22, 0xd3, 0x08, 0x0a, 0x09,
	0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x52, 0x73, 0x70, 0x12, 0x40, 0x0a, 0x04, 0x64, 0x6f, 0x63,
	0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2c, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b,
	0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x72, 0x65, 0x74, 0x72, 0x69, 0x65, 0x76, 0x61, 0x6c,
	0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x52, 0x73,
	0x70, 0x2e, 0x44, 0x6f, 0x63, 0x52, 0x04, 0x64, 0x6f, 0x63, 0x73, 0x1a, 0x83, 0x08, 0x0a, 0x03,
	0x44, 0x6f, 0x63, 0x12, 0x19, 0x0a, 0x08, 0x64, 0x6f, 0x63, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x07, 0x64, 0x6f, 0x63, 0x54, 0x79, 0x70, 0x65, 0x12, 0x15,
	0x0a, 0x06, 0x64, 0x6f, 0x63, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x04, 0x52, 0x05,
	0x64, 0x6f, 0x63, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x72, 0x65, 0x6c, 0x61, 0x74, 0x65, 0x64,
	0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x04, 0x52, 0x09, 0x72, 0x65, 0x6c, 0x61, 0x74,
	0x65, 0x64, 0x49, 0x64, 0x12, 0x21, 0x0a, 0x0c, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x63, 0x6f, 0x6e,
	0x74, 0x65, 0x6e, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x70, 0x61, 0x67, 0x65,
	0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x12, 0x1a, 0x0a, 0x08, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x69, 0x6f, 0x6e, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x69, 0x6f, 0x6e, 0x12, 0x16, 0x0a, 0x06, 0x61, 0x6e, 0x73, 0x77, 0x65, 0x72, 0x18, 0x06, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x06, 0x61, 0x6e, 0x73, 0x77, 0x65, 0x72, 0x12, 0x1e, 0x0a, 0x0a, 0x63,
	0x6f, 0x6e, 0x66, 0x69, 0x64, 0x65, 0x6e, 0x63, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x02, 0x52,
	0x0a, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x64, 0x65, 0x6e, 0x63, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x6f,
	0x72, 0x67, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6f,
	0x72, 0x67, 0x44, 0x61, 0x74, 0x61, 0x12, 0x21, 0x0a, 0x0c, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d,
	0x5f, 0x70, 0x61, 0x72, 0x61, 0x6d, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x63, 0x75,
	0x73, 0x74, 0x6f, 0x6d, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x12, 0x1e, 0x0a, 0x0b, 0x69, 0x73, 0x5f,
	0x62, 0x69, 0x67, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x08, 0x52, 0x09,
	0x69, 0x73, 0x42, 0x69, 0x67, 0x44, 0x61, 0x74, 0x61, 0x12, 0x43, 0x0a, 0x05, 0x65, 0x78, 0x74,
	0x72, 0x61, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2d, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e,
	0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x72, 0x65, 0x74, 0x72, 0x69, 0x65, 0x76, 0x61,
	0x6c, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x52, 0x65, 0x74, 0x72, 0x69, 0x65, 0x76,
	0x61, 0x6c, 0x45, 0x78, 0x74, 0x72, 0x61, 0x52, 0x05, 0x65, 0x78, 0x74, 0x72, 0x61, 0x12, 0x53,
	0x0a, 0x0b, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x0c, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x32, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62,
	0x6f, 0x74, 0x5f, 0x72, 0x65, 0x74, 0x72, 0x69, 0x65, 0x76, 0x61, 0x6c, 0x5f, 0x73, 0x65, 0x72,
	0x76, 0x65, 0x72, 0x2e, 0x52, 0x65, 0x74, 0x72, 0x69, 0x65, 0x76, 0x61, 0x6c, 0x52, 0x65, 0x73,
	0x75, 0x6c, 0x74, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0a, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x54,
	0x79, 0x70, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x5f, 0x75, 0x72, 0x6c,
	0x73, 0x18, 0x0d, 0x20, 0x03, 0x28, 0x09, 0x52, 0x09, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x55, 0x72,
	0x6c, 0x73, 0x12, 0x77, 0x0a, 0x16, 0x73, 0x69, 0x6d, 0x69, 0x6c, 0x61, 0x72, 0x5f, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x65, 0x78, 0x74, 0x72, 0x61, 0x18, 0x0e, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x41, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f,
	0x74, 0x5f, 0x72, 0x65, 0x74, 0x72, 0x69, 0x65, 0x76, 0x61, 0x6c, 0x5f, 0x73, 0x65, 0x72, 0x76,
	0x65, 0x72, 0x2e, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x52, 0x73, 0x70, 0x2e, 0x44, 0x6f, 0x63,
	0x2e, 0x53, 0x69, 0x6d, 0x69, 0x6c, 0x61, 0x72, 0x51, 0x75, 0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e,
	0x45, 0x78, 0x74, 0x72, 0x61, 0x52, 0x14, 0x73, 0x69, 0x6d, 0x69, 0x6c, 0x61, 0x72, 0x51, 0x75,
	0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x45, 0x78, 0x74, 0x72, 0x61, 0x12, 0x61, 0x0a, 0x0e, 0x74,
	0x65, 0x78, 0x74, 0x32, 0x73, 0x71, 0x6c, 0x5f, 0x65, 0x78, 0x74, 0x72, 0x61, 0x18, 0x0f, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x3a, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62,
	0x6f, 0x74, 0x5f, 0x72, 0x65, 0x74, 0x72, 0x69, 0x65, 0x76, 0x61, 0x6c, 0x5f, 0x73, 0x65, 0x72,
	0x76, 0x65, 0x72, 0x2e, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x52, 0x73, 0x70, 0x2e, 0x44, 0x6f,
	0x63, 0x2e, 0x54, 0x65, 0x78, 0x74, 0x32, 0x53, 0x51, 0x4c, 0x45, 0x78, 0x74, 0x72, 0x61, 0x52,
	0x0d, 0x74, 0x65, 0x78, 0x74, 0x32, 0x73, 0x71, 0x6c, 0x45, 0x78, 0x74, 0x72, 0x61, 0x12, 0x23,
	0x0a, 0x0d, 0x71, 0x75, 0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x64, 0x65, 0x73, 0x63, 0x18,
	0x10, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x71, 0x75, 0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x44,
	0x65, 0x73, 0x63, 0x1a, 0x60, 0x0a, 0x14, 0x53, 0x69, 0x6d, 0x69, 0x6c, 0x61, 0x72, 0x51, 0x75,
	0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x45, 0x78, 0x74, 0x72, 0x61, 0x12, 0x1d, 0x0a, 0x0a, 0x73,
	0x69, 0x6d, 0x69, 0x6c, 0x61, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52,
	0x09, 0x73, 0x69, 0x6d, 0x69, 0x6c, 0x61, 0x72, 0x49, 0x64, 0x12, 0x29, 0x0a, 0x10, 0x73, 0x69,
	0x6d, 0x69, 0x6c, 0x61, 0x72, 0x5f, 0x71, 0x75, 0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x73, 0x69, 0x6d, 0x69, 0x6c, 0x61, 0x72, 0x51, 0x75, 0x65,
	0x73, 0x74, 0x69, 0x6f, 0x6e, 0x1a, 0xb9, 0x01, 0x0a, 0x0d, 0x54, 0x65, 0x78, 0x74, 0x32, 0x53,
	0x51, 0x4c, 0x45, 0x78, 0x74, 0x72, 0x61, 0x12, 0x65, 0x0a, 0x0b, 0x74, 0x61, 0x62, 0x6c, 0x65,
	0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x44, 0x2e, 0x74,
	0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x72, 0x65, 0x74, 0x72,
	0x69, 0x65, 0x76, 0x61, 0x6c, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x53, 0x65, 0x61,
	0x72, 0x63, 0x68, 0x52, 0x73, 0x70, 0x2e, 0x44, 0x6f, 0x63, 0x2e, 0x54, 0x65, 0x78, 0x74, 0x32,
	0x53, 0x51, 0x4c, 0x45, 0x78, 0x74, 0x72, 0x61, 0x2e, 0x54, 0x61, 0x62, 0x6c, 0x65, 0x49, 0x6e,
	0x66, 0x6f, 0x52, 0x0a, 0x74, 0x61, 0x62, 0x6c, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x73, 0x1a, 0x41,
	0x0a, 0x09, 0x54, 0x61, 0x62, 0x6c, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x1d, 0x0a, 0x0a, 0x74,
	0x61, 0x62, 0x6c, 0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x09, 0x74, 0x61, 0x62, 0x6c, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x15, 0x0a, 0x06, 0x64, 0x6f,
	0x63, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x04, 0x52, 0x05, 0x64, 0x6f, 0x63, 0x49,
	0x64, 0x22, 0xfa, 0x01, 0x0a, 0x0a, 0x50, 0x75, 0x62, 0x6c, 0x69, 0x73, 0x68, 0x52, 0x65, 0x71,
	0x12, 0x22, 0x0a, 0x08, 0x72, 0x6f, 0x62, 0x6f, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x04, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x32, 0x02, 0x28, 0x01, 0x52, 0x07, 0x72, 0x6f, 0x62,
	0x6f, 0x74, 0x49, 0x64, 0x12, 0x26, 0x0a, 0x0a, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x5f,
	0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x04, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x32, 0x02, 0x28,
	0x01, 0x52, 0x09, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x12, 0x2d, 0x0a, 0x0c,
	0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x09, 0x42, 0x0a, 0xfa, 0x42, 0x07, 0x72, 0x05, 0x10, 0x01, 0x18, 0x80, 0x08, 0x52, 0x0b,
	0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x2b, 0x0a, 0x11, 0x65,
	0x6d, 0x62, 0x65, 0x64, 0x64, 0x69, 0x6e, 0x67, 0x5f, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x04, 0x52, 0x10, 0x65, 0x6d, 0x62, 0x65, 0x64, 0x64, 0x69, 0x6e,
	0x67, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x1c, 0x0a, 0x0a, 0x62, 0x6f, 0x74, 0x5f,
	0x62, 0x69, 0x7a, 0x5f, 0x69, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x04, 0x52, 0x08, 0x62, 0x6f,
	0x74, 0x42, 0x69, 0x7a, 0x49, 0x64, 0x12, 0x26, 0x0a, 0x0f, 0x6c, 0x61, 0x73, 0x74, 0x5f, 0x71,
	0x61, 0x5f, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x06, 0x20, 0x01, 0x28, 0x04, 0x52,
	0x0d, 0x6c, 0x61, 0x73, 0x74, 0x51, 0x61, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x22, 0x0c,
	0x0a, 0x0a, 0x50, 0x75, 0x62, 0x6c, 0x69, 0x73, 0x68, 0x52, 0x73, 0x70, 0x22, 0xbd, 0x02, 0x0a,
	0x0d, 0x53, 0x69, 0x6d, 0x69, 0x6c, 0x61, 0x72, 0x69, 0x74, 0x79, 0x52, 0x65, 0x71, 0x12, 0x1c,
	0x0a, 0x03, 0x6f, 0x72, 0x69, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0a, 0xfa, 0x42, 0x07,
	0x72, 0x05, 0x10, 0x01, 0x18, 0xd0, 0x0f, 0x52, 0x03, 0x6f, 0x72, 0x69, 0x12, 0x44, 0x0a, 0x04,
	0x64, 0x6f, 0x63, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x30, 0x2e, 0x74, 0x72, 0x70,
	0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x72, 0x65, 0x74, 0x72, 0x69, 0x65,
	0x76, 0x61, 0x6c, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x53, 0x69, 0x6d, 0x69, 0x6c,
	0x61, 0x72, 0x69, 0x74, 0x79, 0x52, 0x65, 0x71, 0x2e, 0x44, 0x6f, 0x63, 0x52, 0x04, 0x64, 0x6f,
	0x63, 0x73, 0x12, 0x22, 0x0a, 0x08, 0x72, 0x6f, 0x62, 0x6f, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x04, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x32, 0x02, 0x28, 0x01, 0x52, 0x07, 0x72,
	0x6f, 0x62, 0x6f, 0x74, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f,
	0x6e, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x04, 0x52, 0x09, 0x76, 0x65, 0x72, 0x73,
	0x69, 0x6f, 0x6e, 0x49, 0x64, 0x12, 0x2b, 0x0a, 0x11, 0x65, 0x6d, 0x62, 0x65, 0x64, 0x64, 0x69,
	0x6e, 0x67, 0x5f, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x05, 0x20, 0x01, 0x28, 0x04,
	0x52, 0x10, 0x65, 0x6d, 0x62, 0x65, 0x64, 0x64, 0x69, 0x6e, 0x67, 0x56, 0x65, 0x72, 0x73, 0x69,
	0x6f, 0x6e, 0x12, 0x1c, 0x0a, 0x0a, 0x62, 0x6f, 0x74, 0x5f, 0x62, 0x69, 0x7a, 0x5f, 0x69, 0x64,
	0x18, 0x06, 0x20, 0x01, 0x28, 0x04, 0x52, 0x08, 0x62, 0x6f, 0x74, 0x42, 0x69, 0x7a, 0x49, 0x64,
	0x1a, 0x3a, 0x0a, 0x03, 0x44, 0x6f, 0x63, 0x12, 0x19, 0x0a, 0x08, 0x64, 0x6f, 0x63, 0x5f, 0x74,
	0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x07, 0x64, 0x6f, 0x63, 0x54, 0x79,
	0x70, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x07, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x22, 0x33, 0x0a, 0x0d,
	0x53, 0x69, 0x6d, 0x69, 0x6c, 0x61, 0x72, 0x69, 0x74, 0x79, 0x52, 0x73, 0x70, 0x12, 0x22, 0x0a,
	0x0c, 0x73, 0x69, 0x6d, 0x69, 0x6c, 0x61, 0x72, 0x69, 0x74, 0x69, 0x65, 0x73, 0x18, 0x01, 0x20,
	0x03, 0x28, 0x02, 0x52, 0x0c, 0x73, 0x69, 0x6d, 0x69, 0x6c, 0x61, 0x72, 0x69, 0x74, 0x69, 0x65,
	0x73, 0x22, 0x7b, 0x0a, 0x0f, 0x49, 0x6e, 0x64, 0x65, 0x78, 0x52, 0x65, 0x62, 0x75, 0x69, 0x6c,
	0x64, 0x52, 0x65, 0x71, 0x12, 0x22, 0x0a, 0x08, 0x72, 0x6f, 0x62, 0x6f, 0x74, 0x5f, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x32, 0x02, 0x28, 0x01, 0x52,
	0x07, 0x72, 0x6f, 0x62, 0x6f, 0x74, 0x49, 0x64, 0x12, 0x26, 0x0a, 0x0a, 0x76, 0x65, 0x72, 0x73,
	0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x04, 0x42, 0x07, 0xfa, 0x42,
	0x04, 0x32, 0x02, 0x28, 0x01, 0x52, 0x09, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x49, 0x64,
	0x12, 0x1c, 0x0a, 0x0a, 0x62, 0x6f, 0x74, 0x5f, 0x62, 0x69, 0x7a, 0x5f, 0x69, 0x64, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x04, 0x52, 0x08, 0x62, 0x6f, 0x74, 0x42, 0x69, 0x7a, 0x49, 0x64, 0x22, 0x11,
	0x0a, 0x0f, 0x49, 0x6e, 0x64, 0x65, 0x78, 0x52, 0x65, 0x62, 0x75, 0x69, 0x6c, 0x64, 0x52, 0x73,
	0x70, 0x22, 0x8b, 0x01, 0x0a, 0x19, 0x43, 0x6f, 0x6e, 0x74, 0x69, 0x6e, 0x75, 0x65, 0x54, 0x65,
	0x72, 0x6d, 0x69, 0x6e, 0x61, 0x74, 0x65, 0x64, 0x54, 0x61, 0x73, 0x6b, 0x52, 0x65, 0x71, 0x12,
	0x20, 0x0a, 0x07, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04,
	0x42, 0x07, 0xfa, 0x42, 0x04, 0x32, 0x02, 0x28, 0x01, 0x52, 0x06, 0x74, 0x61, 0x73, 0x6b, 0x49,
	0x64, 0x12, 0x28, 0x0a, 0x0b, 0x72, 0x65, 0x74, 0x72, 0x79, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x73,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x04, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x32, 0x02, 0x28, 0x01, 0x52,
	0x0a, 0x72, 0x65, 0x74, 0x72, 0x79, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x12, 0x22, 0x0a, 0x0d, 0x77,
	0x61, 0x69, 0x74, 0x5f, 0x74, 0x6f, 0x5f, 0x73, 0x74, 0x61, 0x72, 0x74, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x04, 0x52, 0x0b, 0x77, 0x61, 0x69, 0x74, 0x54, 0x6f, 0x53, 0x74, 0x61, 0x72, 0x74, 0x22,
	0x1b, 0x0a, 0x19, 0x43, 0x6f, 0x6e, 0x74, 0x69, 0x6e, 0x75, 0x65, 0x54, 0x65, 0x72, 0x6d, 0x69,
	0x6e, 0x61, 0x74, 0x65, 0x64, 0x54, 0x61, 0x73, 0x6b, 0x52, 0x73, 0x70, 0x22, 0x92, 0x01, 0x0a,
	0x13, 0x55, 0x70, 0x67, 0x72, 0x61, 0x64, 0x65, 0x45, 0x6d, 0x62, 0x65, 0x64, 0x64, 0x69, 0x6e,
	0x67, 0x52, 0x65, 0x71, 0x12, 0x22, 0x0a, 0x08, 0x72, 0x6f, 0x62, 0x6f, 0x74, 0x5f, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x32, 0x02, 0x28, 0x01, 0x52,
	0x07, 0x72, 0x6f, 0x62, 0x6f, 0x74, 0x49, 0x64, 0x12, 0x39, 0x0a, 0x14, 0x65, 0x6d, 0x62, 0x65,
	0x64, 0x64, 0x69, 0x6e, 0x67, 0x5f, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x04, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x32, 0x02, 0x28, 0x01, 0x52,
	0x12, 0x65, 0x6d, 0x62, 0x65, 0x64, 0x64, 0x69, 0x6e, 0x67, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f,
	0x6e, 0x49, 0x64, 0x12, 0x1c, 0x0a, 0x0a, 0x62, 0x6f, 0x74, 0x5f, 0x62, 0x69, 0x7a, 0x5f, 0x69,
	0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x04, 0x52, 0x08, 0x62, 0x6f, 0x74, 0x42, 0x69, 0x7a, 0x49,
	0x64, 0x22, 0x15, 0x0a, 0x13, 0x55, 0x70, 0x67, 0x72, 0x61, 0x64, 0x65, 0x45, 0x6d, 0x62, 0x65,
	0x64, 0x64, 0x69, 0x6e, 0x67, 0x52, 0x73, 0x70, 0x22, 0x5d, 0x0a, 0x0f, 0x43, 0x68, 0x65, 0x63,
	0x6b, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x12, 0x22, 0x0a, 0x08, 0x72,
	0x6f, 0x62, 0x6f, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x42, 0x07, 0xfa,
	0x42, 0x04, 0x32, 0x02, 0x28, 0x01, 0x52, 0x07, 0x72, 0x6f, 0x62, 0x6f, 0x74, 0x49, 0x64, 0x12,
	0x26, 0x0a, 0x0a, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x04, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x32, 0x02, 0x28, 0x01, 0x52, 0x09, 0x76, 0x65,
	0x72, 0x73, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x22, 0x2c, 0x0a, 0x0f, 0x43, 0x68, 0x65, 0x63, 0x6b,
	0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x52, 0x73, 0x70, 0x12, 0x19, 0x0a, 0x08, 0x69, 0x73,
	0x5f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x07, 0x69, 0x73,
	0x56, 0x61, 0x6c, 0x69, 0x64, 0x22, 0xbe, 0x01, 0x0a, 0x0e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65,
	0x49, 0x6e, 0x64, 0x65, 0x78, 0x52, 0x65, 0x71, 0x12, 0x22, 0x0a, 0x08, 0x72, 0x6f, 0x62, 0x6f,
	0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x32,
	0x02, 0x28, 0x01, 0x52, 0x07, 0x72, 0x6f, 0x62, 0x6f, 0x74, 0x49, 0x64, 0x12, 0x22, 0x0a, 0x08,
	0x69, 0x6e, 0x64, 0x65, 0x78, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x04, 0x42, 0x07,
	0xfa, 0x42, 0x04, 0x32, 0x02, 0x28, 0x01, 0x52, 0x07, 0x69, 0x6e, 0x64, 0x65, 0x78, 0x49, 0x64,
	0x12, 0x2b, 0x0a, 0x11, 0x65, 0x6d, 0x62, 0x65, 0x64, 0x64, 0x69, 0x6e, 0x67, 0x5f, 0x76, 0x65,
	0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x04, 0x52, 0x10, 0x65, 0x6d, 0x62,
	0x65, 0x64, 0x64, 0x69, 0x6e, 0x67, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x19, 0x0a,
	0x08, 0x64, 0x6f, 0x63, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0d, 0x52,
	0x07, 0x64, 0x6f, 0x63, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1c, 0x0a, 0x0a, 0x62, 0x6f, 0x74, 0x5f,
	0x62, 0x69, 0x7a, 0x5f, 0x69, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x04, 0x52, 0x08, 0x62, 0x6f,
	0x74, 0x42, 0x69, 0x7a, 0x49, 0x64, 0x22, 0x10, 0x0a, 0x0e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65,
	0x49, 0x6e, 0x64, 0x65, 0x78, 0x52, 0x73, 0x70, 0x22, 0xa3, 0x01, 0x0a, 0x0e, 0x44, 0x65, 0x6c,
	0x65, 0x74, 0x65, 0x49, 0x6e, 0x64, 0x65, 0x78, 0x52, 0x65, 0x71, 0x12, 0x22, 0x0a, 0x08, 0x72,
	0x6f, 0x62, 0x6f, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x42, 0x07, 0xfa,
	0x42, 0x04, 0x32, 0x02, 0x28, 0x01, 0x52, 0x07, 0x72, 0x6f, 0x62, 0x6f, 0x74, 0x49, 0x64, 0x12,
	0x22, 0x0a, 0x08, 0x69, 0x6e, 0x64, 0x65, 0x78, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x04, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x32, 0x02, 0x28, 0x01, 0x52, 0x07, 0x69, 0x6e, 0x64, 0x65,
	0x78, 0x49, 0x64, 0x12, 0x2b, 0x0a, 0x11, 0x65, 0x6d, 0x62, 0x65, 0x64, 0x64, 0x69, 0x6e, 0x67,
	0x5f, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x04, 0x52, 0x10,
	0x65, 0x6d, 0x62, 0x65, 0x64, 0x64, 0x69, 0x6e, 0x67, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e,
	0x12, 0x1c, 0x0a, 0x0a, 0x62, 0x6f, 0x74, 0x5f, 0x62, 0x69, 0x7a, 0x5f, 0x69, 0x64, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x04, 0x52, 0x08, 0x62, 0x6f, 0x74, 0x42, 0x69, 0x7a, 0x49, 0x64, 0x22, 0x10,
	0x0a, 0x0e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x49, 0x6e, 0x64, 0x65, 0x78, 0x52, 0x73, 0x70,
	0x22, 0xab, 0x03, 0x0a, 0x0c, 0x41, 0x64, 0x64, 0x56, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x52, 0x65,
	0x71, 0x12, 0x22, 0x0a, 0x08, 0x72, 0x6f, 0x62, 0x6f, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x04, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x32, 0x02, 0x28, 0x01, 0x52, 0x07, 0x72, 0x6f,
	0x62, 0x6f, 0x74, 0x49, 0x64, 0x12, 0x22, 0x0a, 0x08, 0x69, 0x6e, 0x64, 0x65, 0x78, 0x5f, 0x69,
	0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x04, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x32, 0x02, 0x28, 0x01,
	0x52, 0x07, 0x69, 0x6e, 0x64, 0x65, 0x78, 0x49, 0x64, 0x12, 0x17, 0x0a, 0x02, 0x69, 0x64, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x04, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x32, 0x02, 0x28, 0x01, 0x52, 0x02,
	0x69, 0x64, 0x12, 0x2d, 0x0a, 0x0c, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x65,
	0x6e, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0a, 0xfa, 0x42, 0x07, 0x72, 0x05, 0x10,
	0x01, 0x18, 0xd0, 0x0f, 0x52, 0x0b, 0x70, 0x61, 0x67, 0x65, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e,
	0x74, 0x12, 0x19, 0x0a, 0x08, 0x64, 0x6f, 0x63, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x0d, 0x52, 0x07, 0x64, 0x6f, 0x63, 0x54, 0x79, 0x70, 0x65, 0x12, 0x2b, 0x0a, 0x11,
	0x65, 0x6d, 0x62, 0x65, 0x64, 0x64, 0x69, 0x6e, 0x67, 0x5f, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f,
	0x6e, 0x18, 0x06, 0x20, 0x01, 0x28, 0x04, 0x52, 0x10, 0x65, 0x6d, 0x62, 0x65, 0x64, 0x64, 0x69,
	0x6e, 0x67, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x42, 0x0a, 0x06, 0x6c, 0x61, 0x62,
	0x65, 0x6c, 0x73, 0x18, 0x07, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2a, 0x2e, 0x74, 0x72, 0x70, 0x63,
	0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x72, 0x65, 0x74, 0x72, 0x69, 0x65, 0x76,
	0x61, 0x6c, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x56, 0x65, 0x63, 0x74, 0x6f, 0x72,
	0x4c, 0x61, 0x62, 0x65, 0x6c, 0x52, 0x06, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x73, 0x12, 0x1f, 0x0a,
	0x0b, 0x65, 0x78, 0x70, 0x69, 0x72, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x08, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x0a, 0x65, 0x78, 0x70, 0x69, 0x72, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x1c,
	0x0a, 0x0a, 0x62, 0x6f, 0x74, 0x5f, 0x62, 0x69, 0x7a, 0x5f, 0x69, 0x64, 0x18, 0x09, 0x20, 0x01,
	0x28, 0x04, 0x52, 0x08, 0x62, 0x6f, 0x74, 0x42, 0x69, 0x7a, 0x49, 0x64, 0x12, 0x40, 0x0a, 0x04,
	0x74, 0x79, 0x70, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x2c, 0x2e, 0x74, 0x72, 0x70,
	0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x72, 0x65, 0x74, 0x72, 0x69, 0x65,
	0x76, 0x61, 0x6c, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x4b, 0x6e, 0x6f, 0x77, 0x6c,
	0x65, 0x64, 0x67, 0x65, 0x54, 0x79, 0x70, 0x65, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x22, 0x0e,
	0x0a, 0x0c, 0x41, 0x64, 0x64, 0x56, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x52, 0x73, 0x70, 0x22, 0xbd,
	0x01, 0x0a, 0x0f, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x56, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x52,
	0x65, 0x71, 0x12, 0x22, 0x0a, 0x08, 0x72, 0x6f, 0x62, 0x6f, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x04, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x32, 0x02, 0x28, 0x01, 0x52, 0x07, 0x72,
	0x6f, 0x62, 0x6f, 0x74, 0x49, 0x64, 0x12, 0x22, 0x0a, 0x08, 0x69, 0x6e, 0x64, 0x65, 0x78, 0x5f,
	0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x04, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x32, 0x02, 0x28,
	0x01, 0x52, 0x07, 0x69, 0x6e, 0x64, 0x65, 0x78, 0x49, 0x64, 0x12, 0x17, 0x0a, 0x02, 0x69, 0x64,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x04, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x32, 0x02, 0x28, 0x01, 0x52,
	0x02, 0x69, 0x64, 0x12, 0x2b, 0x0a, 0x11, 0x65, 0x6d, 0x62, 0x65, 0x64, 0x64, 0x69, 0x6e, 0x67,
	0x5f, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x04, 0x52, 0x10,
	0x65, 0x6d, 0x62, 0x65, 0x64, 0x64, 0x69, 0x6e, 0x67, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e,
	0x12, 0x1c, 0x0a, 0x0a, 0x62, 0x6f, 0x74, 0x5f, 0x62, 0x69, 0x7a, 0x5f, 0x69, 0x64, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x04, 0x52, 0x08, 0x62, 0x6f, 0x74, 0x42, 0x69, 0x7a, 0x49, 0x64, 0x22, 0x11,
	0x0a, 0x0f, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x56, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x52, 0x73,
	0x70, 0x22, 0xec, 0x02, 0x0a, 0x0f, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x56, 0x65, 0x63, 0x74,
	0x6f, 0x72, 0x52, 0x65, 0x71, 0x12, 0x22, 0x0a, 0x08, 0x72, 0x6f, 0x62, 0x6f, 0x74, 0x5f, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x32, 0x02, 0x28, 0x01,
	0x52, 0x07, 0x72, 0x6f, 0x62, 0x6f, 0x74, 0x49, 0x64, 0x12, 0x22, 0x0a, 0x08, 0x69, 0x6e, 0x64,
	0x65, 0x78, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x04, 0x42, 0x07, 0xfa, 0x42, 0x04,
	0x32, 0x02, 0x28, 0x01, 0x52, 0x07, 0x69, 0x6e, 0x64, 0x65, 0x78, 0x49, 0x64, 0x12, 0x17, 0x0a,
	0x02, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x04, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x32, 0x02,
	0x28, 0x01, 0x52, 0x02, 0x69, 0x64, 0x12, 0x2d, 0x0a, 0x0c, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x63,
	0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0a, 0xfa, 0x42,
	0x07, 0x72, 0x05, 0x10, 0x01, 0x18, 0xd0, 0x0f, 0x52, 0x0b, 0x70, 0x61, 0x67, 0x65, 0x43, 0x6f,
	0x6e, 0x74, 0x65, 0x6e, 0x74, 0x12, 0x19, 0x0a, 0x08, 0x64, 0x6f, 0x63, 0x5f, 0x74, 0x79, 0x70,
	0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x07, 0x64, 0x6f, 0x63, 0x54, 0x79, 0x70, 0x65,
	0x12, 0x2b, 0x0a, 0x11, 0x65, 0x6d, 0x62, 0x65, 0x64, 0x64, 0x69, 0x6e, 0x67, 0x5f, 0x76, 0x65,
	0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x06, 0x20, 0x01, 0x28, 0x04, 0x52, 0x10, 0x65, 0x6d, 0x62,
	0x65, 0x64, 0x64, 0x69, 0x6e, 0x67, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x42, 0x0a,
	0x06, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x73, 0x18, 0x07, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2a, 0x2e,
	0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x72, 0x65, 0x74,
	0x72, 0x69, 0x65, 0x76, 0x61, 0x6c, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x56, 0x65,
	0x63, 0x74, 0x6f, 0x72, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x52, 0x06, 0x6c, 0x61, 0x62, 0x65, 0x6c,
	0x73, 0x12, 0x1f, 0x0a, 0x0b, 0x65, 0x78, 0x70, 0x69, 0x72, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65,
	0x18, 0x08, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x65, 0x78, 0x70, 0x69, 0x72, 0x65, 0x54, 0x69,
	0x6d, 0x65, 0x12, 0x1c, 0x0a, 0x0a, 0x62, 0x6f, 0x74, 0x5f, 0x62, 0x69, 0x7a, 0x5f, 0x69, 0x64,
	0x18, 0x09, 0x20, 0x01, 0x28, 0x04, 0x52, 0x08, 0x62, 0x6f, 0x74, 0x42, 0x69, 0x7a, 0x49, 0x64,
	0x22, 0x11, 0x0a, 0x0f, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x56, 0x65, 0x63, 0x74, 0x6f, 0x72,
	0x52, 0x73, 0x70, 0x22, 0xfb, 0x02, 0x0a, 0x0a, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x44, 0x61,
	0x74, 0x61, 0x12, 0x2a, 0x0a, 0x0c, 0x6b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x5f,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x32, 0x02, 0x28,
	0x01, 0x52, 0x0b, 0x6b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x49, 0x64, 0x12, 0x28,
	0x0a, 0x10, 0x6b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x5f, 0x62, 0x69, 0x7a, 0x5f,
	0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0e, 0x6b, 0x6e, 0x6f, 0x77, 0x6c, 0x65,
	0x64, 0x67, 0x65, 0x42, 0x69, 0x7a, 0x49, 0x64, 0x12, 0x45, 0x0a, 0x07, 0x66, 0x69, 0x6c, 0x74,
	0x65, 0x72, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2b, 0x2e, 0x74, 0x72, 0x70, 0x63,
	0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x72, 0x65, 0x74, 0x72, 0x69, 0x65, 0x76,
	0x61, 0x6c, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68,
	0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x52, 0x07, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x73, 0x12,
	0x2b, 0x0a, 0x11, 0x65, 0x6d, 0x62, 0x65, 0x64, 0x64, 0x69, 0x6e, 0x67, 0x5f, 0x76, 0x65, 0x72,
	0x73, 0x69, 0x6f, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x04, 0x52, 0x10, 0x65, 0x6d, 0x62, 0x65,
	0x64, 0x64, 0x69, 0x6e, 0x67, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x48, 0x0a, 0x06,
	0x6c, 0x61, 0x62, 0x65, 0x6c, 0x73, 0x18, 0x05, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x30, 0x2e, 0x74,
	0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x72, 0x65, 0x74, 0x72,
	0x69, 0x65, 0x76, 0x61, 0x6c, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x53, 0x65, 0x61,
	0x72, 0x63, 0x68, 0x56, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x52, 0x06,
	0x6c, 0x61, 0x62, 0x65, 0x6c, 0x73, 0x12, 0x59, 0x0a, 0x10, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x5f,
	0x65, 0x78, 0x70, 0x72, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x2e, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f,
	0x72, 0x65, 0x74, 0x72, 0x69, 0x65, 0x76, 0x61, 0x6c, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72,
	0x2e, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x45, 0x78, 0x70, 0x72, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e,
	0x52, 0x0f, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x45, 0x78, 0x70, 0x72, 0x65, 0x73, 0x73, 0x69, 0x6f,
	0x6e, 0x22, 0xf4, 0x01, 0x0a, 0x0c, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x46, 0x69, 0x6c, 0x74,
	0x65, 0x72, 0x12, 0x19, 0x0a, 0x08, 0x69, 0x6e, 0x64, 0x65, 0x78, 0x5f, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x04, 0x52, 0x07, 0x69, 0x6e, 0x64, 0x65, 0x78, 0x49, 0x64, 0x12, 0x1e, 0x0a,
	0x0a, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x64, 0x65, 0x6e, 0x63, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x02, 0x52, 0x0a, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x64, 0x65, 0x6e, 0x63, 0x65, 0x12, 0x13, 0x0a,
	0x05, 0x74, 0x6f, 0x70, 0x5f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x04, 0x74, 0x6f,
	0x70, 0x4e, 0x12, 0x19, 0x0a, 0x08, 0x64, 0x6f, 0x63, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x0d, 0x52, 0x07, 0x64, 0x6f, 0x63, 0x54, 0x79, 0x70, 0x65, 0x12, 0x2a, 0x0a,
	0x11, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x5f, 0x65, 0x78, 0x70, 0x72, 0x5f, 0x73, 0x74, 0x72, 0x69,
	0x6e, 0x67, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x45,
	0x78, 0x70, 0x72, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x12, 0x4d, 0x0a, 0x0a, 0x6c, 0x61, 0x62,
	0x65, 0x6c, 0x5f, 0x65, 0x78, 0x70, 0x72, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2e, 0x2e,
	0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x72, 0x65, 0x74,
	0x72, 0x69, 0x65, 0x76, 0x61, 0x6c, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x56, 0x65,
	0x63, 0x74, 0x6f, 0x72, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x45, 0x78, 0x70, 0x72, 0x52, 0x09, 0x6c,
	0x61, 0x62, 0x65, 0x6c, 0x45, 0x78, 0x70, 0x72, 0x22, 0x4b, 0x0a, 0x06, 0x52, 0x65, 0x72, 0x61,
	0x6e, 0x6b, 0x12, 0x14, 0x0a, 0x05, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x05, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x12, 0x13, 0x0a, 0x05, 0x74, 0x6f, 0x70, 0x5f,
	0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x04, 0x74, 0x6f, 0x70, 0x4e, 0x12, 0x16, 0x0a,
	0x06, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x08, 0x52, 0x06, 0x65,
	0x6e, 0x61, 0x62, 0x6c, 0x65, 0x22, 0xfa, 0x03, 0x0a, 0x17, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68,
	0x4d, 0x75, 0x6c, 0x74, 0x69, 0x4b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x52, 0x65,
	0x71, 0x12, 0x22, 0x0a, 0x08, 0x72, 0x6f, 0x62, 0x6f, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x04, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x32, 0x02, 0x28, 0x01, 0x52, 0x07, 0x72, 0x6f,
	0x62, 0x6f, 0x74, 0x49, 0x64, 0x12, 0x1c, 0x0a, 0x0a, 0x62, 0x6f, 0x74, 0x5f, 0x62, 0x69, 0x7a,
	0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x04, 0x52, 0x08, 0x62, 0x6f, 0x74, 0x42, 0x69,
	0x7a, 0x49, 0x64, 0x12, 0x23, 0x0a, 0x08, 0x71, 0x75, 0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x10, 0x01, 0x52, 0x08,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x1d, 0x0a, 0x0a, 0x69, 0x6d, 0x61, 0x67,
	0x65, 0x5f, 0x75, 0x72, 0x6c, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x09, 0x52, 0x09, 0x69, 0x6d,
	0x61, 0x67, 0x65, 0x55, 0x72, 0x6c, 0x73, 0x12, 0x23, 0x0a, 0x0d, 0x73, 0x75, 0x62, 0x5f, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x05, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0c,
	0x73, 0x75, 0x62, 0x51, 0x75, 0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x13, 0x0a, 0x05,
	0x74, 0x6f, 0x70, 0x5f, 0x6e, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x04, 0x74, 0x6f, 0x70,
	0x4e, 0x12, 0x3d, 0x0a, 0x06, 0x72, 0x65, 0x72, 0x61, 0x6e, 0x6b, 0x18, 0x07, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x25, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74,
	0x5f, 0x72, 0x65, 0x74, 0x72, 0x69, 0x65, 0x76, 0x61, 0x6c, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65,
	0x72, 0x2e, 0x52, 0x65, 0x72, 0x61, 0x6e, 0x6b, 0x52, 0x06, 0x72, 0x65, 0x72, 0x61, 0x6e, 0x6b,
	0x12, 0x1d, 0x0a, 0x0a, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x5f, 0x6b, 0x65, 0x79, 0x18, 0x08,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x4b, 0x65, 0x79, 0x12,
	0x56, 0x0a, 0x0f, 0x73, 0x65, 0x61, 0x72, 0x63, 0x68, 0x5f, 0x73, 0x74, 0x72, 0x61, 0x74, 0x65,
	0x67, 0x79, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2d, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e,
	0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x72, 0x65, 0x74, 0x72, 0x69, 0x65, 0x76, 0x61,
	0x6c, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x53,
	0x74, 0x72, 0x61, 0x74, 0x65, 0x67, 0x79, 0x52, 0x0e, 0x73, 0x65, 0x61, 0x72, 0x63, 0x68, 0x53,
	0x74, 0x72, 0x61, 0x74, 0x65, 0x67, 0x79, 0x12, 0x1d, 0x0a, 0x0a, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x4a, 0x0a, 0x0b, 0x73, 0x65, 0x61, 0x72, 0x63, 0x68,
	0x5f, 0x64, 0x61, 0x74, 0x61, 0x18, 0x0b, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x29, 0x2e, 0x74, 0x72,
	0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x72, 0x65, 0x74, 0x72, 0x69,
	0x65, 0x76, 0x61, 0x6c, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x53, 0x65, 0x61, 0x72,
	0x63, 0x68, 0x44, 0x61, 0x74, 0x61, 0x52, 0x0a, 0x73, 0x65, 0x61, 0x72, 0x63, 0x68, 0x44, 0x61,
	0x74, 0x61, 0x22, 0x97, 0x08, 0x0a, 0x0f, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x56, 0x65, 0x63,
	0x74, 0x6f, 0x72, 0x52, 0x65, 0x71, 0x12, 0x22, 0x0a, 0x08, 0x72, 0x6f, 0x62, 0x6f, 0x74, 0x5f,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x32, 0x02, 0x28,
	0x01, 0x52, 0x07, 0x72, 0x6f, 0x62, 0x6f, 0x74, 0x49, 0x64, 0x12, 0x23, 0x0a, 0x08, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42,
	0x04, 0x72, 0x02, 0x10, 0x01, 0x52, 0x08, 0x71, 0x75, 0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x12,
	0x4f, 0x0a, 0x07, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x35, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f,
	0x72, 0x65, 0x74, 0x72, 0x69, 0x65, 0x76, 0x61, 0x6c, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72,
	0x2e, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x56, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x52, 0x65, 0x71,
	0x2e, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x52, 0x07, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x73,
	0x12, 0x13, 0x0a, 0x05, 0x74, 0x6f, 0x70, 0x5f, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0d, 0x52,
	0x04, 0x74, 0x6f, 0x70, 0x4e, 0x12, 0x2b, 0x0a, 0x11, 0x65, 0x6d, 0x62, 0x65, 0x64, 0x64, 0x69,
	0x6e, 0x67, 0x5f, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x05, 0x20, 0x01, 0x28, 0x04,
	0x52, 0x10, 0x65, 0x6d, 0x62, 0x65, 0x64, 0x64, 0x69, 0x6e, 0x67, 0x56, 0x65, 0x72, 0x73, 0x69,
	0x6f, 0x6e, 0x12, 0x4d, 0x0a, 0x06, 0x72, 0x65, 0x72, 0x61, 0x6e, 0x6b, 0x18, 0x07, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x35, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f,
	0x74, 0x5f, 0x72, 0x65, 0x74, 0x72, 0x69, 0x65, 0x76, 0x61, 0x6c, 0x5f, 0x73, 0x65, 0x72, 0x76,
	0x65, 0x72, 0x2e, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x56, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x52,
	0x65, 0x71, 0x2e, 0x52, 0x65, 0x72, 0x61, 0x6e, 0x6b, 0x52, 0x06, 0x72, 0x65, 0x72, 0x61, 0x6e,
	0x6b, 0x12, 0x1d, 0x0a, 0x0a, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x5f, 0x6b, 0x65, 0x79, 0x18,
	0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x4b, 0x65, 0x79,
	0x12, 0x48, 0x0a, 0x06, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x73, 0x18, 0x09, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x30, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f,
	0x72, 0x65, 0x74, 0x72, 0x69, 0x65, 0x76, 0x61, 0x6c, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72,
	0x2e, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x56, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x4c, 0x61, 0x62,
	0x65, 0x6c, 0x52, 0x06, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x73, 0x12, 0x1d, 0x0a, 0x0a, 0x69, 0x6d,
	0x61, 0x67, 0x65, 0x5f, 0x75, 0x72, 0x6c, 0x73, 0x18, 0x0a, 0x20, 0x03, 0x28, 0x09, 0x52, 0x09,
	0x69, 0x6d, 0x61, 0x67, 0x65, 0x55, 0x72, 0x6c, 0x73, 0x12, 0x1c, 0x0a, 0x0a, 0x62, 0x6f, 0x74,
	0x5f, 0x62, 0x69, 0x7a, 0x5f, 0x69, 0x64, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x04, 0x52, 0x08, 0x62,
	0x6f, 0x74, 0x42, 0x69, 0x7a, 0x49, 0x64, 0x12, 0x59, 0x0a, 0x10, 0x6c, 0x61, 0x62, 0x65, 0x6c,
	0x5f, 0x65, 0x78, 0x70, 0x72, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x0c, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x2e, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74,
	0x5f, 0x72, 0x65, 0x74, 0x72, 0x69, 0x65, 0x76, 0x61, 0x6c, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65,
	0x72, 0x2e, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x45, 0x78, 0x70, 0x72, 0x65, 0x73, 0x73, 0x69, 0x6f,
	0x6e, 0x52, 0x0f, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x45, 0x78, 0x70, 0x72, 0x65, 0x73, 0x73, 0x69,
	0x6f, 0x6e, 0x12, 0x23, 0x0a, 0x0d, 0x73, 0x75, 0x62, 0x5f, 0x71, 0x75, 0x65, 0x73, 0x74, 0x69,
	0x6f, 0x6e, 0x73, 0x18, 0x0d, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0c, 0x73, 0x75, 0x62, 0x51, 0x75,
	0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x56, 0x0a, 0x0f, 0x73, 0x65, 0x61, 0x72, 0x63,
	0x68, 0x5f, 0x73, 0x74, 0x72, 0x61, 0x74, 0x65, 0x67, 0x79, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x2d, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f,
	0x72, 0x65, 0x74, 0x72, 0x69, 0x65, 0x76, 0x61, 0x6c, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72,
	0x2e, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x53, 0x74, 0x72, 0x61, 0x74, 0x65, 0x67, 0x79, 0x52,
	0x0e, 0x73, 0x65, 0x61, 0x72, 0x63, 0x68, 0x53, 0x74, 0x72, 0x61, 0x74, 0x65, 0x67, 0x79, 0x12,
	0x1d, 0x0a, 0x0a, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x0f, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x09, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x4e, 0x61, 0x6d, 0x65, 0x1a, 0xee,
	0x01, 0x0a, 0x06, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x12, 0x19, 0x0a, 0x08, 0x69, 0x6e, 0x64,
	0x65, 0x78, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x07, 0x69, 0x6e, 0x64,
	0x65, 0x78, 0x49, 0x64, 0x12, 0x1e, 0x0a, 0x0a, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x64, 0x65, 0x6e,
	0x63, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x02, 0x52, 0x0a, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x64,
	0x65, 0x6e, 0x63, 0x65, 0x12, 0x13, 0x0a, 0x05, 0x74, 0x6f, 0x70, 0x5f, 0x6e, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x0d, 0x52, 0x04, 0x74, 0x6f, 0x70, 0x4e, 0x12, 0x19, 0x0a, 0x08, 0x64, 0x6f, 0x63,
	0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x07, 0x64, 0x6f, 0x63,
	0x54, 0x79, 0x70, 0x65, 0x12, 0x2a, 0x0a, 0x11, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x5f, 0x65, 0x78,
	0x70, 0x72, 0x5f, 0x73, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0f, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x45, 0x78, 0x70, 0x72, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67,
	0x12, 0x4d, 0x0a, 0x0a, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x5f, 0x65, 0x78, 0x70, 0x72, 0x18, 0x06,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x2e, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e,
	0x62, 0x6f, 0x74, 0x5f, 0x72, 0x65, 0x74, 0x72, 0x69, 0x65, 0x76, 0x61, 0x6c, 0x5f, 0x73, 0x65,
	0x72, 0x76, 0x65, 0x72, 0x2e, 0x56, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x4c, 0x61, 0x62, 0x65, 0x6c,
	0x45, 0x78, 0x70, 0x72, 0x52, 0x09, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x45, 0x78, 0x70, 0x72, 0x1a,
	0x4b, 0x0a, 0x06, 0x52, 0x65, 0x72, 0x61, 0x6e, 0x6b, 0x12, 0x14, 0x0a, 0x05, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x12,
	0x13, 0x0a, 0x05, 0x74, 0x6f, 0x70, 0x5f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x04,
	0x74, 0x6f, 0x70, 0x4e, 0x12, 0x16, 0x0a, 0x06, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x08, 0x52, 0x06, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x22, 0xda, 0x08, 0x0a,
	0x0f, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x56, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x52, 0x73, 0x70,
	0x12, 0x46, 0x0a, 0x04, 0x64, 0x6f, 0x63, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x32,
	0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x72, 0x65,
	0x74, 0x72, 0x69, 0x65, 0x76, 0x61, 0x6c, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x53,
	0x65, 0x61, 0x72, 0x63, 0x68, 0x56, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x52, 0x73, 0x70, 0x2e, 0x44,
	0x6f, 0x63, 0x52, 0x04, 0x64, 0x6f, 0x63, 0x73, 0x1a, 0xfe, 0x07, 0x0a, 0x03, 0x44, 0x6f, 0x63,
	0x12, 0x19, 0x0a, 0x08, 0x69, 0x6e, 0x64, 0x65, 0x78, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x04, 0x52, 0x07, 0x69, 0x6e, 0x64, 0x65, 0x78, 0x49, 0x64, 0x12, 0x0e, 0x0a, 0x02, 0x69,
	0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x04, 0x52, 0x02, 0x69, 0x64, 0x12, 0x1e, 0x0a, 0x0a, 0x63,
	0x6f, 0x6e, 0x66, 0x69, 0x64, 0x65, 0x6e, 0x63, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x02, 0x52,
	0x0a, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x64, 0x65, 0x6e, 0x63, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x16, 0x0a, 0x06, 0x61, 0x6e, 0x73, 0x77, 0x65,
	0x72, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x61, 0x6e, 0x73, 0x77, 0x65, 0x72, 0x12,
	0x21, 0x0a, 0x0c, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x18,
	0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x70, 0x61, 0x67, 0x65, 0x43, 0x6f, 0x6e, 0x74, 0x65,
	0x6e, 0x74, 0x12, 0x19, 0x0a, 0x08, 0x6f, 0x72, 0x67, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x18, 0x07,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6f, 0x72, 0x67, 0x44, 0x61, 0x74, 0x61, 0x12, 0x19, 0x0a,
	0x08, 0x64, 0x6f, 0x63, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0d, 0x52,
	0x07, 0x64, 0x6f, 0x63, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1e, 0x0a, 0x0b, 0x69, 0x73, 0x5f, 0x62,
	0x69, 0x67, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x18, 0x09, 0x20, 0x01, 0x28, 0x08, 0x52, 0x09, 0x69,
	0x73, 0x42, 0x69, 0x67, 0x44, 0x61, 0x74, 0x61, 0x12, 0x43, 0x0a, 0x05, 0x65, 0x78, 0x74, 0x72,
	0x61, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2d, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b,
	0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x72, 0x65, 0x74, 0x72, 0x69, 0x65, 0x76, 0x61, 0x6c,
	0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x52, 0x65, 0x74, 0x72, 0x69, 0x65, 0x76, 0x61,
	0x6c, 0x45, 0x78, 0x74, 0x72, 0x61, 0x52, 0x05, 0x65, 0x78, 0x74, 0x72, 0x61, 0x12, 0x53, 0x0a,
	0x0b, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x0b, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x32, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f,
	0x74, 0x5f, 0x72, 0x65, 0x74, 0x72, 0x69, 0x65, 0x76, 0x61, 0x6c, 0x5f, 0x73, 0x65, 0x72, 0x76,
	0x65, 0x72, 0x2e, 0x52, 0x65, 0x74, 0x72, 0x69, 0x65, 0x76, 0x61, 0x6c, 0x52, 0x65, 0x73, 0x75,
	0x6c, 0x74, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0a, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x54, 0x79,
	0x70, 0x65, 0x12, 0x15, 0x0a, 0x06, 0x64, 0x6f, 0x63, 0x5f, 0x69, 0x64, 0x18, 0x0c, 0x20, 0x01,
	0x28, 0x04, 0x52, 0x05, 0x64, 0x6f, 0x63, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x69, 0x6d, 0x61,
	0x67, 0x65, 0x5f, 0x75, 0x72, 0x6c, 0x73, 0x18, 0x0d, 0x20, 0x03, 0x28, 0x09, 0x52, 0x09, 0x69,
	0x6d, 0x61, 0x67, 0x65, 0x55, 0x72, 0x6c, 0x73, 0x12, 0x7d, 0x0a, 0x16, 0x73, 0x69, 0x6d, 0x69,
	0x6c, 0x61, 0x72, 0x5f, 0x71, 0x75, 0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x65, 0x78, 0x74,
	0x72, 0x61, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x47, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e,
	0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x72, 0x65, 0x74, 0x72, 0x69, 0x65, 0x76, 0x61,
	0x6c, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x56,
	0x65, 0x63, 0x74, 0x6f, 0x72, 0x52, 0x73, 0x70, 0x2e, 0x44, 0x6f, 0x63, 0x2e, 0x53, 0x69, 0x6d,
	0x69, 0x6c, 0x61, 0x72, 0x51, 0x75, 0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x45, 0x78, 0x74, 0x72,
	0x61, 0x52, 0x14, 0x73, 0x69, 0x6d, 0x69, 0x6c, 0x61, 0x72, 0x51, 0x75, 0x65, 0x73, 0x74, 0x69,
	0x6f, 0x6e, 0x45, 0x78, 0x74, 0x72, 0x61, 0x12, 0x67, 0x0a, 0x0e, 0x74, 0x65, 0x78, 0x74, 0x32,
	0x73, 0x71, 0x6c, 0x5f, 0x65, 0x78, 0x74, 0x72, 0x61, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x40, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x72,
	0x65, 0x74, 0x72, 0x69, 0x65, 0x76, 0x61, 0x6c, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e,
	0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x56, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x52, 0x73, 0x70, 0x2e,
	0x44, 0x6f, 0x63, 0x2e, 0x54, 0x65, 0x78, 0x74, 0x32, 0x53, 0x51, 0x4c, 0x45, 0x78, 0x74, 0x72,
	0x61, 0x52, 0x0d, 0x74, 0x65, 0x78, 0x74, 0x32, 0x73, 0x71, 0x6c, 0x45, 0x78, 0x74, 0x72, 0x61,
	0x12, 0x23, 0x0a, 0x0d, 0x71, 0x75, 0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x64, 0x65, 0x73,
	0x63, 0x18, 0x10, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x71, 0x75, 0x65, 0x73, 0x74, 0x69, 0x6f,
	0x6e, 0x44, 0x65, 0x73, 0x63, 0x1a, 0x60, 0x0a, 0x14, 0x53, 0x69, 0x6d, 0x69, 0x6c, 0x61, 0x72,
	0x51, 0x75, 0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x45, 0x78, 0x74, 0x72, 0x61, 0x12, 0x1d, 0x0a,
	0x0a, 0x73, 0x69, 0x6d, 0x69, 0x6c, 0x61, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x04, 0x52, 0x09, 0x73, 0x69, 0x6d, 0x69, 0x6c, 0x61, 0x72, 0x49, 0x64, 0x12, 0x29, 0x0a, 0x10,
	0x73, 0x69, 0x6d, 0x69, 0x6c, 0x61, 0x72, 0x5f, 0x71, 0x75, 0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x73, 0x69, 0x6d, 0x69, 0x6c, 0x61, 0x72, 0x51,
	0x75, 0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x1a, 0xbf, 0x01, 0x0a, 0x0d, 0x54, 0x65, 0x78, 0x74,
	0x32, 0x53, 0x51, 0x4c, 0x45, 0x78, 0x74, 0x72, 0x61, 0x12, 0x6b, 0x0a, 0x0b, 0x74, 0x61, 0x62,
	0x6c, 0x65, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x4a,
	0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x72, 0x65,
	0x74, 0x72, 0x69, 0x65, 0x76, 0x61, 0x6c, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x53,
	0x65, 0x61, 0x72, 0x63, 0x68, 0x56, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x52, 0x73, 0x70, 0x2e, 0x44,
	0x6f, 0x63, 0x2e, 0x54, 0x65, 0x78, 0x74, 0x32, 0x53, 0x51, 0x4c, 0x45, 0x78, 0x74, 0x72, 0x61,
	0x2e, 0x54, 0x61, 0x62, 0x6c, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x0a, 0x74, 0x61, 0x62, 0x6c,
	0x65, 0x49, 0x6e, 0x66, 0x6f, 0x73, 0x1a, 0x41, 0x0a, 0x09, 0x54, 0x61, 0x62, 0x6c, 0x65, 0x49,
	0x6e, 0x66, 0x6f, 0x12, 0x1d, 0x0a, 0x0a, 0x74, 0x61, 0x62, 0x6c, 0x65, 0x5f, 0x6e, 0x61, 0x6d,
	0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x74, 0x61, 0x62, 0x6c, 0x65, 0x4e, 0x61,
	0x6d, 0x65, 0x12, 0x15, 0x0a, 0x06, 0x64, 0x6f, 0x63, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x04, 0x52, 0x05, 0x64, 0x6f, 0x63, 0x49, 0x64, 0x22, 0x9f, 0x01, 0x0a, 0x14, 0x44, 0x69,
	0x72, 0x65, 0x63, 0x74, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x49, 0x6e, 0x64, 0x65, 0x78, 0x52,
	0x65, 0x71, 0x12, 0x1b, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x10, 0x01, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12,
	0x22, 0x0a, 0x08, 0x69, 0x6e, 0x64, 0x65, 0x78, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x04, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x32, 0x02, 0x28, 0x01, 0x52, 0x07, 0x69, 0x6e, 0x64, 0x65,
	0x78, 0x49, 0x64, 0x12, 0x2b, 0x0a, 0x11, 0x65, 0x6d, 0x62, 0x65, 0x64, 0x64, 0x69, 0x6e, 0x67,
	0x5f, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x04, 0x52, 0x10,
	0x65, 0x6d, 0x62, 0x65, 0x64, 0x64, 0x69, 0x6e, 0x67, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e,
	0x12, 0x19, 0x0a, 0x08, 0x64, 0x6f, 0x63, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x0d, 0x52, 0x07, 0x64, 0x6f, 0x63, 0x54, 0x79, 0x70, 0x65, 0x22, 0x16, 0x0a, 0x14, 0x44,
	0x69, 0x72, 0x65, 0x63, 0x74, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x49, 0x6e, 0x64, 0x65, 0x78,
	0x52, 0x73, 0x70, 0x22, 0x84, 0x01, 0x0a, 0x14, 0x44, 0x69, 0x72, 0x65, 0x63, 0x74, 0x44, 0x65,
	0x6c, 0x65, 0x74, 0x65, 0x49, 0x6e, 0x64, 0x65, 0x78, 0x52, 0x65, 0x71, 0x12, 0x1b, 0x0a, 0x04,
	0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72,
	0x02, 0x10, 0x01, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x22, 0x0a, 0x08, 0x69, 0x6e, 0x64,
	0x65, 0x78, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x04, 0x42, 0x07, 0xfa, 0x42, 0x04,
	0x32, 0x02, 0x28, 0x01, 0x52, 0x07, 0x69, 0x6e, 0x64, 0x65, 0x78, 0x49, 0x64, 0x12, 0x2b, 0x0a,
	0x11, 0x65, 0x6d, 0x62, 0x65, 0x64, 0x64, 0x69, 0x6e, 0x67, 0x5f, 0x76, 0x65, 0x72, 0x73, 0x69,
	0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x04, 0x52, 0x10, 0x65, 0x6d, 0x62, 0x65, 0x64, 0x64,
	0x69, 0x6e, 0x67, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x22, 0x16, 0x0a, 0x14, 0x44, 0x69,
	0x72, 0x65, 0x63, 0x74, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x49, 0x6e, 0x64, 0x65, 0x78, 0x52,
	0x73, 0x70, 0x22, 0xca, 0x02, 0x0a, 0x12, 0x44, 0x69, 0x72, 0x65, 0x63, 0x74, 0x41, 0x64, 0x64,
	0x56, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x52, 0x65, 0x71, 0x12, 0x1b, 0x0a, 0x04, 0x6e, 0x61, 0x6d,
	0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x10, 0x01,
	0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x22, 0x0a, 0x08, 0x69, 0x6e, 0x64, 0x65, 0x78, 0x5f,
	0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x04, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x32, 0x02, 0x28,
	0x01, 0x52, 0x07, 0x69, 0x6e, 0x64, 0x65, 0x78, 0x49, 0x64, 0x12, 0x17, 0x0a, 0x02, 0x69, 0x64,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x04, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x32, 0x02, 0x28, 0x01, 0x52,
	0x02, 0x69, 0x64, 0x12, 0x2d, 0x0a, 0x0c, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x63, 0x6f, 0x6e, 0x74,
	0x65, 0x6e, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0a, 0xfa, 0x42, 0x07, 0x72, 0x05,
	0x10, 0x01, 0x18, 0xd0, 0x0f, 0x52, 0x0b, 0x70, 0x61, 0x67, 0x65, 0x43, 0x6f, 0x6e, 0x74, 0x65,
	0x6e, 0x74, 0x12, 0x19, 0x0a, 0x08, 0x64, 0x6f, 0x63, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x0d, 0x52, 0x07, 0x64, 0x6f, 0x63, 0x54, 0x79, 0x70, 0x65, 0x12, 0x2b, 0x0a,
	0x11, 0x65, 0x6d, 0x62, 0x65, 0x64, 0x64, 0x69, 0x6e, 0x67, 0x5f, 0x76, 0x65, 0x72, 0x73, 0x69,
	0x6f, 0x6e, 0x18, 0x06, 0x20, 0x01, 0x28, 0x04, 0x52, 0x10, 0x65, 0x6d, 0x62, 0x65, 0x64, 0x64,
	0x69, 0x6e, 0x67, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x42, 0x0a, 0x06, 0x6c, 0x61,
	0x62, 0x65, 0x6c, 0x73, 0x18, 0x07, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2a, 0x2e, 0x74, 0x72, 0x70,
	0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x72, 0x65, 0x74, 0x72, 0x69, 0x65,
	0x76, 0x61, 0x6c, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x56, 0x65, 0x63, 0x74, 0x6f,
	0x72, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x52, 0x06, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x73, 0x12, 0x1f,
	0x0a, 0x0b, 0x65, 0x78, 0x70, 0x69, 0x72, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x08, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x0a, 0x65, 0x78, 0x70, 0x69, 0x72, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x22,
	0x14, 0x0a, 0x12, 0x44, 0x69, 0x72, 0x65, 0x63, 0x74, 0x41, 0x64, 0x64, 0x56, 0x65, 0x63, 0x74,
	0x6f, 0x72, 0x52, 0x73, 0x70, 0x22, 0x9e, 0x01, 0x0a, 0x15, 0x44, 0x69, 0x72, 0x65, 0x63, 0x74,
	0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x56, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x52, 0x65, 0x71, 0x12,
	0x1b, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa,
	0x42, 0x04, 0x72, 0x02, 0x10, 0x01, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x22, 0x0a, 0x08,
	0x69, 0x6e, 0x64, 0x65, 0x78, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x04, 0x42, 0x07,
	0xfa, 0x42, 0x04, 0x32, 0x02, 0x28, 0x01, 0x52, 0x07, 0x69, 0x6e, 0x64, 0x65, 0x78, 0x49, 0x64,
	0x12, 0x17, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x04, 0x42, 0x07, 0xfa, 0x42,
	0x04, 0x32, 0x02, 0x28, 0x01, 0x52, 0x02, 0x69, 0x64, 0x12, 0x2b, 0x0a, 0x11, 0x65, 0x6d, 0x62,
	0x65, 0x64, 0x64, 0x69, 0x6e, 0x67, 0x5f, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x04, 0x52, 0x10, 0x65, 0x6d, 0x62, 0x65, 0x64, 0x64, 0x69, 0x6e, 0x67, 0x56,
	0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x22, 0x17, 0x0a, 0x15, 0x44, 0x69, 0x72, 0x65, 0x63, 0x74,
	0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x56, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x52, 0x73, 0x70, 0x22,
	0xcd, 0x02, 0x0a, 0x15, 0x44, 0x69, 0x72, 0x65, 0x63, 0x74, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65,
	0x56, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x52, 0x65, 0x71, 0x12, 0x1b, 0x0a, 0x04, 0x6e, 0x61, 0x6d,
	0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x10, 0x01,
	0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x22, 0x0a, 0x08, 0x69, 0x6e, 0x64, 0x65, 0x78, 0x5f,
	0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x04, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x32, 0x02, 0x28,
	0x01, 0x52, 0x07, 0x69, 0x6e, 0x64, 0x65, 0x78, 0x49, 0x64, 0x12, 0x17, 0x0a, 0x02, 0x69, 0x64,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x04, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x32, 0x02, 0x28, 0x01, 0x52,
	0x02, 0x69, 0x64, 0x12, 0x2d, 0x0a, 0x0c, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x63, 0x6f, 0x6e, 0x74,
	0x65, 0x6e, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0a, 0xfa, 0x42, 0x07, 0x72, 0x05,
	0x10, 0x01, 0x18, 0xd0, 0x0f, 0x52, 0x0b, 0x70, 0x61, 0x67, 0x65, 0x43, 0x6f, 0x6e, 0x74, 0x65,
	0x6e, 0x74, 0x12, 0x19, 0x0a, 0x08, 0x64, 0x6f, 0x63, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x0d, 0x52, 0x07, 0x64, 0x6f, 0x63, 0x54, 0x79, 0x70, 0x65, 0x12, 0x2b, 0x0a,
	0x11, 0x65, 0x6d, 0x62, 0x65, 0x64, 0x64, 0x69, 0x6e, 0x67, 0x5f, 0x76, 0x65, 0x72, 0x73, 0x69,
	0x6f, 0x6e, 0x18, 0x06, 0x20, 0x01, 0x28, 0x04, 0x52, 0x10, 0x65, 0x6d, 0x62, 0x65, 0x64, 0x64,
	0x69, 0x6e, 0x67, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x42, 0x0a, 0x06, 0x6c, 0x61,
	0x62, 0x65, 0x6c, 0x73, 0x18, 0x07, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2a, 0x2e, 0x74, 0x72, 0x70,
	0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x72, 0x65, 0x74, 0x72, 0x69, 0x65,
	0x76, 0x61, 0x6c, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x56, 0x65, 0x63, 0x74, 0x6f,
	0x72, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x52, 0x06, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x73, 0x12, 0x1f,
	0x0a, 0x0b, 0x65, 0x78, 0x70, 0x69, 0x72, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x08, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x0a, 0x65, 0x78, 0x70, 0x69, 0x72, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x22,
	0x17, 0x0a, 0x15, 0x44, 0x69, 0x72, 0x65, 0x63, 0x74, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x56,
	0x65, 0x63, 0x74, 0x6f, 0x72, 0x52, 0x73, 0x70, 0x22, 0x85, 0x05, 0x0a, 0x15, 0x44, 0x69, 0x72,
	0x65, 0x63, 0x74, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x56, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x52,
	0x65, 0x71, 0x12, 0x1b, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x10, 0x01, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12,
	0x23, 0x0a, 0x08, 0x71, 0x75, 0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x10, 0x01, 0x52, 0x08, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x69, 0x6f, 0x6e, 0x12, 0x55, 0x0a, 0x07, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x73, 0x18,
	0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x3b, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50,
	0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x72, 0x65, 0x74, 0x72, 0x69, 0x65, 0x76, 0x61, 0x6c, 0x5f, 0x73,
	0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x44, 0x69, 0x72, 0x65, 0x63, 0x74, 0x53, 0x65, 0x61, 0x72,
	0x63, 0x68, 0x56, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x52, 0x65, 0x71, 0x2e, 0x46, 0x69, 0x6c, 0x74,
	0x65, 0x72, 0x52, 0x07, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x73, 0x12, 0x13, 0x0a, 0x05, 0x74,
	0x6f, 0x70, 0x5f, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x04, 0x74, 0x6f, 0x70, 0x4e,
	0x12, 0x2b, 0x0a, 0x11, 0x65, 0x6d, 0x62, 0x65, 0x64, 0x64, 0x69, 0x6e, 0x67, 0x5f, 0x76, 0x65,
	0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x05, 0x20, 0x01, 0x28, 0x04, 0x52, 0x10, 0x65, 0x6d, 0x62,
	0x65, 0x64, 0x64, 0x69, 0x6e, 0x67, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x53, 0x0a,
	0x06, 0x72, 0x65, 0x72, 0x61, 0x6e, 0x6b, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x3b, 0x2e,
	0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x72, 0x65, 0x74,
	0x72, 0x69, 0x65, 0x76, 0x61, 0x6c, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x44, 0x69,
	0x72, 0x65, 0x63, 0x74, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x56, 0x65, 0x63, 0x74, 0x6f, 0x72,
	0x52, 0x65, 0x71, 0x2e, 0x52, 0x65, 0x72, 0x61, 0x6e, 0x6b, 0x52, 0x06, 0x72, 0x65, 0x72, 0x61,
	0x6e, 0x6b, 0x1a, 0xee, 0x01, 0x0a, 0x06, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x12, 0x19, 0x0a,
	0x08, 0x69, 0x6e, 0x64, 0x65, 0x78, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52,
	0x07, 0x69, 0x6e, 0x64, 0x65, 0x78, 0x49, 0x64, 0x12, 0x1e, 0x0a, 0x0a, 0x63, 0x6f, 0x6e, 0x66,
	0x69, 0x64, 0x65, 0x6e, 0x63, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x02, 0x52, 0x0a, 0x63, 0x6f,
	0x6e, 0x66, 0x69, 0x64, 0x65, 0x6e, 0x63, 0x65, 0x12, 0x13, 0x0a, 0x05, 0x74, 0x6f, 0x70, 0x5f,
	0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x04, 0x74, 0x6f, 0x70, 0x4e, 0x12, 0x19, 0x0a,
	0x08, 0x64, 0x6f, 0x63, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0d, 0x52,
	0x07, 0x64, 0x6f, 0x63, 0x54, 0x79, 0x70, 0x65, 0x12, 0x2a, 0x0a, 0x11, 0x6c, 0x61, 0x62, 0x65,
	0x6c, 0x5f, 0x65, 0x78, 0x70, 0x72, 0x5f, 0x73, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0f, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x45, 0x78, 0x70, 0x72, 0x53, 0x74,
	0x72, 0x69, 0x6e, 0x67, 0x12, 0x4d, 0x0a, 0x0a, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x5f, 0x65, 0x78,
	0x70, 0x72, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2e, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e,
	0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x72, 0x65, 0x74, 0x72, 0x69, 0x65, 0x76, 0x61,
	0x6c, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x56, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x4c,
	0x61, 0x62, 0x65, 0x6c, 0x45, 0x78, 0x70, 0x72, 0x52, 0x09, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x45,
	0x78, 0x70, 0x72, 0x1a, 0x4b, 0x0a, 0x06, 0x52, 0x65, 0x72, 0x61, 0x6e, 0x6b, 0x12, 0x14, 0x0a,
	0x05, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x12, 0x13, 0x0a, 0x05, 0x74, 0x6f, 0x70, 0x5f, 0x6e, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x0d, 0x52, 0x04, 0x74, 0x6f, 0x70, 0x4e, 0x12, 0x16, 0x0a, 0x06, 0x65, 0x6e, 0x61, 0x62,
	0x6c, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x08, 0x52, 0x06, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65,
	0x22, 0xc5, 0x02, 0x0a, 0x15, 0x44, 0x69, 0x72, 0x65, 0x63, 0x74, 0x53, 0x65, 0x61, 0x72, 0x63,
	0x68, 0x56, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x52, 0x73, 0x70, 0x12, 0x4c, 0x0a, 0x04, 0x64, 0x6f,
	0x63, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x38, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e,
	0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x72, 0x65, 0x74, 0x72, 0x69, 0x65, 0x76, 0x61,
	0x6c, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x44, 0x69, 0x72, 0x65, 0x63, 0x74, 0x53,
	0x65, 0x61, 0x72, 0x63, 0x68, 0x56, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x52, 0x73, 0x70, 0x2e, 0x44,
	0x6f, 0x63, 0x52, 0x04, 0x64, 0x6f, 0x63, 0x73, 0x1a, 0xdd, 0x01, 0x0a, 0x03, 0x44, 0x6f, 0x63,
	0x12, 0x19, 0x0a, 0x08, 0x69, 0x6e, 0x64, 0x65, 0x78, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x04, 0x52, 0x07, 0x69, 0x6e, 0x64, 0x65, 0x78, 0x49, 0x64, 0x12, 0x0e, 0x0a, 0x02, 0x69,
	0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x04, 0x52, 0x02, 0x69, 0x64, 0x12, 0x1e, 0x0a, 0x0a, 0x63,
	0x6f, 0x6e, 0x66, 0x69, 0x64, 0x65, 0x6e, 0x63, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x02, 0x52,
	0x0a, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x64, 0x65, 0x6e, 0x63, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x16, 0x0a, 0x06, 0x61, 0x6e, 0x73, 0x77, 0x65,
	0x72, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x61, 0x6e, 0x73, 0x77, 0x65, 0x72, 0x12,
	0x21, 0x0a, 0x0c, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x18,
	0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x70, 0x61, 0x67, 0x65, 0x43, 0x6f, 0x6e, 0x74, 0x65,
	0x6e, 0x74, 0x12, 0x19, 0x0a, 0x08, 0x6f, 0x72, 0x67, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x18, 0x07,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6f, 0x72, 0x67, 0x44, 0x61, 0x74, 0x61, 0x12, 0x19, 0x0a,
	0x08, 0x64, 0x6f, 0x63, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0d, 0x52,
	0x07, 0x64, 0x6f, 0x63, 0x54, 0x79, 0x70, 0x65, 0x22, 0x94, 0x01, 0x0a, 0x14, 0x41, 0x64, 0x64,
	0x42, 0x69, 0x67, 0x44, 0x61, 0x74, 0x61, 0x45, 0x6c, 0x61, 0x73, 0x74, 0x69, 0x63, 0x52, 0x65,
	0x71, 0x12, 0x3a, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x26, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x72,
	0x65, 0x74, 0x72, 0x69, 0x65, 0x76, 0x61, 0x6c, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e,
	0x42, 0x69, 0x67, 0x44, 0x61, 0x74, 0x61, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x12, 0x40, 0x0a,
	0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x2c, 0x2e, 0x74, 0x72,
	0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x72, 0x65, 0x74, 0x72, 0x69,
	0x65, 0x76, 0x61, 0x6c, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x4b, 0x6e, 0x6f, 0x77,
	0x6c, 0x65, 0x64, 0x67, 0x65, 0x54, 0x79, 0x70, 0x65, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x22,
	0xd1, 0x01, 0x0a, 0x07, 0x42, 0x69, 0x67, 0x44, 0x61, 0x74, 0x61, 0x12, 0x22, 0x0a, 0x08, 0x72,
	0x6f, 0x62, 0x6f, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x42, 0x07, 0xfa,
	0x42, 0x04, 0x32, 0x02, 0x28, 0x01, 0x52, 0x07, 0x72, 0x6f, 0x62, 0x6f, 0x74, 0x49, 0x64, 0x12,
	0x1e, 0x0a, 0x06, 0x64, 0x6f, 0x63, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x04, 0x42,
	0x07, 0xfa, 0x42, 0x04, 0x32, 0x02, 0x28, 0x01, 0x52, 0x05, 0x64, 0x6f, 0x63, 0x49, 0x64, 0x12,
	0x27, 0x0a, 0x0b, 0x62, 0x69, 0x67, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x69, 0x64, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x10, 0x01, 0x52, 0x09, 0x62,
	0x69, 0x67, 0x44, 0x61, 0x74, 0x61, 0x49, 0x64, 0x12, 0x1a, 0x0a, 0x08, 0x42, 0x69, 0x67, 0x53,
	0x74, 0x61, 0x72, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x42, 0x69, 0x67, 0x53,
	0x74, 0x61, 0x72, 0x74, 0x12, 0x16, 0x0a, 0x06, 0x42, 0x69, 0x67, 0x45, 0x6e, 0x64, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x42, 0x69, 0x67, 0x45, 0x6e, 0x64, 0x12, 0x25, 0x0a, 0x09,
	0x42, 0x69, 0x67, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x42,
	0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x10, 0x01, 0x52, 0x09, 0x42, 0x69, 0x67, 0x53, 0x74, 0x72,
	0x69, 0x6e, 0x67, 0x22, 0xae, 0x01, 0x0a, 0x24, 0x42, 0x61, 0x74, 0x63, 0x68, 0x47, 0x65, 0x74,
	0x42, 0x69, 0x67, 0x44, 0x61, 0x74, 0x61, 0x45, 0x53, 0x42, 0x79, 0x52, 0x6f, 0x62, 0x6f, 0x74,
	0x42, 0x69, 0x67, 0x44, 0x61, 0x74, 0x61, 0x49, 0x44, 0x52, 0x65, 0x71, 0x12, 0x22, 0x0a, 0x08,
	0x72, 0x6f, 0x62, 0x6f, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x42, 0x07,
	0xfa, 0x42, 0x04, 0x32, 0x02, 0x28, 0x01, 0x52, 0x07, 0x72, 0x6f, 0x62, 0x6f, 0x74, 0x49, 0x64,
	0x12, 0x20, 0x0a, 0x0c, 0x62, 0x69, 0x67, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x69, 0x64, 0x73,
	0x18, 0x02, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0a, 0x62, 0x69, 0x67, 0x44, 0x61, 0x74, 0x61, 0x49,
	0x64, 0x73, 0x12, 0x40, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e,
	0x32, 0x2c, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f,
	0x72, 0x65, 0x74, 0x72, 0x69, 0x65, 0x76, 0x61, 0x6c, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72,
	0x2e, 0x4b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x54, 0x79, 0x70, 0x65, 0x52, 0x04,
	0x74, 0x79, 0x70, 0x65, 0x22, 0x63, 0x0a, 0x25, 0x42, 0x61, 0x74, 0x63, 0x68, 0x47, 0x65, 0x74,
	0x42, 0x69, 0x67, 0x44, 0x61, 0x74, 0x61, 0x45, 0x53, 0x42, 0x79, 0x52, 0x6f, 0x62, 0x6f, 0x74,
	0x42, 0x69, 0x67, 0x44, 0x61, 0x74, 0x61, 0x49, 0x44, 0x52, 0x65, 0x73, 0x70, 0x12, 0x3a, 0x0a,
	0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x26, 0x2e, 0x74, 0x72,
	0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x72, 0x65, 0x74, 0x72, 0x69,
	0x65, 0x76, 0x61, 0x6c, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x42, 0x69, 0x67, 0x44,
	0x61, 0x74, 0x61, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x22, 0x16, 0x0a, 0x14, 0x41, 0x64, 0x64,
	0x42, 0x69, 0x67, 0x44, 0x61, 0x74, 0x61, 0x45, 0x6c, 0x61, 0x73, 0x74, 0x69, 0x63, 0x52, 0x73,
	0x70, 0x22, 0xc0, 0x01, 0x0a, 0x17, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x42, 0x69, 0x67, 0x44,
	0x61, 0x74, 0x61, 0x45, 0x6c, 0x61, 0x73, 0x74, 0x69, 0x63, 0x52, 0x65, 0x71, 0x12, 0x22, 0x0a,
	0x08, 0x72, 0x6f, 0x62, 0x6f, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x42,
	0x07, 0xfa, 0x42, 0x04, 0x32, 0x02, 0x28, 0x01, 0x52, 0x07, 0x72, 0x6f, 0x62, 0x6f, 0x74, 0x49,
	0x64, 0x12, 0x1e, 0x0a, 0x06, 0x64, 0x6f, 0x63, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x04, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x32, 0x02, 0x28, 0x01, 0x52, 0x05, 0x64, 0x6f, 0x63, 0x49,
	0x64, 0x12, 0x40, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32,
	0x2c, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x72,
	0x65, 0x74, 0x72, 0x69, 0x65, 0x76, 0x61, 0x6c, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e,
	0x4b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x54, 0x79, 0x70, 0x65, 0x52, 0x04, 0x74,
	0x79, 0x70, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x68, 0x61, 0x72, 0x64, 0x5f, 0x64, 0x65, 0x6c, 0x65,
	0x74, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0a, 0x68, 0x61, 0x72, 0x64, 0x44, 0x65,
	0x6c, 0x65, 0x74, 0x65, 0x22, 0x19, 0x0a, 0x17, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x42, 0x69,
	0x67, 0x44, 0x61, 0x74, 0x61, 0x45, 0x6c, 0x61, 0x73, 0x74, 0x69, 0x63, 0x52, 0x73, 0x70, 0x22,
	0x5e, 0x0a, 0x18, 0x52, 0x65, 0x63, 0x6f, 0x76, 0x65, 0x72, 0x42, 0x69, 0x67, 0x44, 0x61, 0x74,
	0x61, 0x45, 0x6c, 0x61, 0x73, 0x74, 0x69, 0x63, 0x52, 0x65, 0x71, 0x12, 0x22, 0x0a, 0x08, 0x72,
	0x6f, 0x62, 0x6f, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x42, 0x07, 0xfa,
	0x42, 0x04, 0x32, 0x02, 0x28, 0x01, 0x52, 0x07, 0x72, 0x6f, 0x62, 0x6f, 0x74, 0x49, 0x64, 0x12,
	0x1e, 0x0a, 0x06, 0x64, 0x6f, 0x63, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x04, 0x42,
	0x07, 0xfa, 0x42, 0x04, 0x32, 0x02, 0x28, 0x01, 0x52, 0x05, 0x64, 0x6f, 0x63, 0x49, 0x64, 0x22,
	0x1a, 0x0a, 0x18, 0x52, 0x65, 0x63, 0x6f, 0x76, 0x65, 0x72, 0x42, 0x69, 0x67, 0x44, 0x61, 0x74,
	0x61, 0x45, 0x6c, 0x61, 0x73, 0x74, 0x69, 0x63, 0x52, 0x73, 0x70, 0x22, 0x9a, 0x03, 0x0a, 0x0f,
	0x41, 0x64, 0x64, 0x4b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x52, 0x65, 0x71, 0x12,
	0x22, 0x0a, 0x08, 0x72, 0x6f, 0x62, 0x6f, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x04, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x32, 0x02, 0x28, 0x01, 0x52, 0x07, 0x72, 0x6f, 0x62, 0x6f,
	0x74, 0x49, 0x64, 0x12, 0x22, 0x0a, 0x08, 0x69, 0x6e, 0x64, 0x65, 0x78, 0x5f, 0x69, 0x64, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x04, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x32, 0x02, 0x28, 0x01, 0x52, 0x07,
	0x69, 0x6e, 0x64, 0x65, 0x78, 0x49, 0x64, 0x12, 0x17, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x04, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x32, 0x02, 0x28, 0x01, 0x52, 0x02, 0x69, 0x64,
	0x12, 0x19, 0x0a, 0x08, 0x64, 0x6f, 0x63, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x0d, 0x52, 0x07, 0x64, 0x6f, 0x63, 0x54, 0x79, 0x70, 0x65, 0x12, 0x21, 0x0a, 0x0c, 0x73,
	0x65, 0x67, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0b, 0x73, 0x65, 0x67, 0x6d, 0x65, 0x6e, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x15,
	0x0a, 0x06, 0x64, 0x6f, 0x63, 0x5f, 0x69, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x04, 0x52, 0x05,
	0x64, 0x6f, 0x63, 0x49, 0x64, 0x12, 0x21, 0x0a, 0x0c, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x63, 0x6f,
	0x6e, 0x74, 0x65, 0x6e, 0x74, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x70, 0x61, 0x67,
	0x65, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x12, 0x2b, 0x0a, 0x11, 0x65, 0x6d, 0x62, 0x65,
	0x64, 0x64, 0x69, 0x6e, 0x67, 0x5f, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x08, 0x20,
	0x01, 0x28, 0x04, 0x52, 0x10, 0x65, 0x6d, 0x62, 0x65, 0x64, 0x64, 0x69, 0x6e, 0x67, 0x56, 0x65,
	0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x42, 0x0a, 0x06, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x73, 0x18,
	0x09, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2a, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50,
	0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x72, 0x65, 0x74, 0x72, 0x69, 0x65, 0x76, 0x61, 0x6c, 0x5f, 0x73,
	0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x56, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x4c, 0x61, 0x62, 0x65,
	0x6c, 0x52, 0x06, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x73, 0x12, 0x1f, 0x0a, 0x0b, 0x65, 0x78, 0x70,
	0x69, 0x72, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a,
	0x65, 0x78, 0x70, 0x69, 0x72, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x1c, 0x0a, 0x0a, 0x62, 0x6f,
	0x74, 0x5f, 0x62, 0x69, 0x7a, 0x5f, 0x69, 0x64, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x04, 0x52, 0x08,
	0x62, 0x6f, 0x74, 0x42, 0x69, 0x7a, 0x49, 0x64, 0x22, 0x11, 0x0a, 0x0f, 0x41, 0x64, 0x64, 0x4b,
	0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x52, 0x73, 0x70, 0x22, 0x90, 0x02, 0x0a, 0x14,
	0x42, 0x61, 0x74, 0x63, 0x68, 0x41, 0x64, 0x64, 0x4b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67,
	0x65, 0x52, 0x65, 0x71, 0x12, 0x22, 0x0a, 0x08, 0x72, 0x6f, 0x62, 0x6f, 0x74, 0x5f, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x32, 0x02, 0x28, 0x01, 0x52,
	0x07, 0x72, 0x6f, 0x62, 0x6f, 0x74, 0x49, 0x64, 0x12, 0x22, 0x0a, 0x08, 0x69, 0x6e, 0x64, 0x65,
	0x78, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x04, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x32,
	0x02, 0x28, 0x01, 0x52, 0x07, 0x69, 0x6e, 0x64, 0x65, 0x78, 0x49, 0x64, 0x12, 0x19, 0x0a, 0x08,
	0x64, 0x6f, 0x63, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x07,
	0x64, 0x6f, 0x63, 0x54, 0x79, 0x70, 0x65, 0x12, 0x2b, 0x0a, 0x11, 0x65, 0x6d, 0x62, 0x65, 0x64,
	0x64, 0x69, 0x6e, 0x67, 0x5f, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x04, 0x52, 0x10, 0x65, 0x6d, 0x62, 0x65, 0x64, 0x64, 0x69, 0x6e, 0x67, 0x56, 0x65, 0x72,
	0x73, 0x69, 0x6f, 0x6e, 0x12, 0x4a, 0x0a, 0x09, 0x6b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67,
	0x65, 0x18, 0x05, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2c, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b,
	0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x72, 0x65, 0x74, 0x72, 0x69, 0x65, 0x76, 0x61, 0x6c,
	0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x4b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67,
	0x65, 0x44, 0x61, 0x74, 0x61, 0x52, 0x09, 0x6b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65,
	0x12, 0x1c, 0x0a, 0x0a, 0x62, 0x6f, 0x74, 0x5f, 0x62, 0x69, 0x7a, 0x5f, 0x69, 0x64, 0x18, 0x06,
	0x20, 0x01, 0x28, 0x04, 0x52, 0x08, 0x62, 0x6f, 0x74, 0x42, 0x69, 0x7a, 0x49, 0x64, 0x22, 0x16,
	0x0a, 0x14, 0x42, 0x61, 0x74, 0x63, 0x68, 0x41, 0x64, 0x64, 0x4b, 0x6e, 0x6f, 0x77, 0x6c, 0x65,
	0x64, 0x67, 0x65, 0x52, 0x73, 0x70, 0x22, 0xfe, 0x01, 0x0a, 0x12, 0x44, 0x65, 0x6c, 0x65, 0x74,
	0x65, 0x4b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x52, 0x65, 0x71, 0x12, 0x22, 0x0a,
	0x08, 0x72, 0x6f, 0x62, 0x6f, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x42,
	0x07, 0xfa, 0x42, 0x04, 0x32, 0x02, 0x28, 0x01, 0x52, 0x07, 0x72, 0x6f, 0x62, 0x6f, 0x74, 0x49,
	0x64, 0x12, 0x22, 0x0a, 0x08, 0x69, 0x6e, 0x64, 0x65, 0x78, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x04, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x32, 0x02, 0x28, 0x01, 0x52, 0x07, 0x69, 0x6e,
	0x64, 0x65, 0x78, 0x49, 0x64, 0x12, 0x17, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x04, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x32, 0x02, 0x28, 0x01, 0x52, 0x02, 0x69, 0x64, 0x12, 0x19,
	0x0a, 0x08, 0x64, 0x6f, 0x63, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0d,
	0x52, 0x07, 0x64, 0x6f, 0x63, 0x54, 0x79, 0x70, 0x65, 0x12, 0x21, 0x0a, 0x0c, 0x73, 0x65, 0x67,
	0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0b, 0x73, 0x65, 0x67, 0x6d, 0x65, 0x6e, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x2b, 0x0a, 0x11,
	0x65, 0x6d, 0x62, 0x65, 0x64, 0x64, 0x69, 0x6e, 0x67, 0x5f, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f,
	0x6e, 0x18, 0x06, 0x20, 0x01, 0x28, 0x04, 0x52, 0x10, 0x65, 0x6d, 0x62, 0x65, 0x64, 0x64, 0x69,
	0x6e, 0x67, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x1c, 0x0a, 0x0a, 0x62, 0x6f, 0x74,
	0x5f, 0x62, 0x69, 0x7a, 0x5f, 0x69, 0x64, 0x18, 0x07, 0x20, 0x01, 0x28, 0x04, 0x52, 0x08, 0x62,
	0x6f, 0x74, 0x42, 0x69, 0x7a, 0x49, 0x64, 0x22, 0x14, 0x0a, 0x12, 0x44, 0x65, 0x6c, 0x65, 0x74,
	0x65, 0x4b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x52, 0x73, 0x70, 0x22, 0x8b, 0x02,
	0x0a, 0x17, 0x42, 0x61, 0x74, 0x63, 0x68, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x4b, 0x6e, 0x6f,
	0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x52, 0x65, 0x71, 0x12, 0x22, 0x0a, 0x08, 0x72, 0x6f, 0x62,
	0x6f, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x42, 0x07, 0xfa, 0x42, 0x04,
	0x32, 0x02, 0x28, 0x01, 0x52, 0x07, 0x72, 0x6f, 0x62, 0x6f, 0x74, 0x49, 0x64, 0x12, 0x22, 0x0a,
	0x08, 0x69, 0x6e, 0x64, 0x65, 0x78, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x04, 0x42,
	0x07, 0xfa, 0x42, 0x04, 0x32, 0x02, 0x28, 0x01, 0x52, 0x07, 0x69, 0x6e, 0x64, 0x65, 0x78, 0x49,
	0x64, 0x12, 0x42, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x2e, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x72,
	0x65, 0x74, 0x72, 0x69, 0x65, 0x76, 0x61, 0x6c, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e,
	0x4b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x49, 0x44, 0x54, 0x79, 0x70, 0x65, 0x52,
	0x04, 0x64, 0x61, 0x74, 0x61, 0x12, 0x19, 0x0a, 0x08, 0x64, 0x6f, 0x63, 0x5f, 0x74, 0x79, 0x70,
	0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x07, 0x64, 0x6f, 0x63, 0x54, 0x79, 0x70, 0x65,
	0x12, 0x2b, 0x0a, 0x11, 0x65, 0x6d, 0x62, 0x65, 0x64, 0x64, 0x69, 0x6e, 0x67, 0x5f, 0x76, 0x65,
	0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x05, 0x20, 0x01, 0x28, 0x04, 0x52, 0x10, 0x65, 0x6d, 0x62,
	0x65, 0x64, 0x64, 0x69, 0x6e, 0x67, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x1c, 0x0a,
	0x0a, 0x62, 0x6f, 0x74, 0x5f, 0x62, 0x69, 0x7a, 0x5f, 0x69, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28,
	0x04, 0x52, 0x08, 0x62, 0x6f, 0x74, 0x42, 0x69, 0x7a, 0x49, 0x64, 0x22, 0x19, 0x0a, 0x17, 0x42,
	0x61, 0x74, 0x63, 0x68, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x4b, 0x6e, 0x6f, 0x77, 0x6c, 0x65,
	0x64, 0x67, 0x65, 0x52, 0x73, 0x70, 0x22, 0x80, 0x02, 0x0a, 0x1e, 0x42, 0x61, 0x74, 0x63, 0x68,
	0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x41, 0x6c, 0x6c, 0x4b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64,
	0x67, 0x65, 0x50, 0x72, 0x6f, 0x64, 0x52, 0x65, 0x71, 0x12, 0x22, 0x0a, 0x08, 0x72, 0x6f, 0x62,
	0x6f, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x42, 0x07, 0xfa, 0x42, 0x04,
	0x32, 0x02, 0x28, 0x01, 0x52, 0x07, 0x72, 0x6f, 0x62, 0x6f, 0x74, 0x49, 0x64, 0x12, 0x26, 0x0a,
	0x0a, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x04, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x32, 0x02, 0x28, 0x01, 0x52, 0x09, 0x76, 0x65, 0x72, 0x73,
	0x69, 0x6f, 0x6e, 0x49, 0x64, 0x12, 0x42, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x03, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x2e, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62,
	0x6f, 0x74, 0x5f, 0x72, 0x65, 0x74, 0x72, 0x69, 0x65, 0x76, 0x61, 0x6c, 0x5f, 0x73, 0x65, 0x72,
	0x76, 0x65, 0x72, 0x2e, 0x4b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x49, 0x44, 0x54,
	0x79, 0x70, 0x65, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x12, 0x19, 0x0a, 0x08, 0x64, 0x6f, 0x63,
	0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x07, 0x64, 0x6f, 0x63,
	0x54, 0x79, 0x70, 0x65, 0x12, 0x1c, 0x0a, 0x0a, 0x62, 0x6f, 0x74, 0x5f, 0x62, 0x69, 0x7a, 0x5f,
	0x69, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x04, 0x52, 0x08, 0x62, 0x6f, 0x74, 0x42, 0x69, 0x7a,
	0x49, 0x64, 0x12, 0x15, 0x0a, 0x06, 0x64, 0x6f, 0x63, 0x5f, 0x69, 0x64, 0x18, 0x06, 0x20, 0x01,
	0x28, 0x04, 0x52, 0x05, 0x64, 0x6f, 0x63, 0x49, 0x64, 0x22, 0x20, 0x0a, 0x1e, 0x42, 0x61, 0x74,
	0x63, 0x68, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x41, 0x6c, 0x6c, 0x4b, 0x6e, 0x6f, 0x77, 0x6c,
	0x65, 0x64, 0x67, 0x65, 0x50, 0x72, 0x6f, 0x64, 0x52, 0x73, 0x70, 0x22, 0x9d, 0x03, 0x0a, 0x12,
	0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x4b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x52,
	0x65, 0x71, 0x12, 0x22, 0x0a, 0x08, 0x72, 0x6f, 0x62, 0x6f, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x04, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x32, 0x02, 0x28, 0x01, 0x52, 0x07, 0x72,
	0x6f, 0x62, 0x6f, 0x74, 0x49, 0x64, 0x12, 0x22, 0x0a, 0x08, 0x69, 0x6e, 0x64, 0x65, 0x78, 0x5f,
	0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x04, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x32, 0x02, 0x28,
	0x01, 0x52, 0x07, 0x69, 0x6e, 0x64, 0x65, 0x78, 0x49, 0x64, 0x12, 0x17, 0x0a, 0x02, 0x69, 0x64,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x04, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x32, 0x02, 0x28, 0x01, 0x52,
	0x02, 0x69, 0x64, 0x12, 0x19, 0x0a, 0x08, 0x64, 0x6f, 0x63, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x07, 0x64, 0x6f, 0x63, 0x54, 0x79, 0x70, 0x65, 0x12, 0x21,
	0x0a, 0x0c, 0x73, 0x65, 0x67, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x73, 0x65, 0x67, 0x6d, 0x65, 0x6e, 0x74, 0x54, 0x79, 0x70,
	0x65, 0x12, 0x21, 0x0a, 0x0c, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e,
	0x74, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x70, 0x61, 0x67, 0x65, 0x43, 0x6f, 0x6e,
	0x74, 0x65, 0x6e, 0x74, 0x12, 0x2b, 0x0a, 0x11, 0x65, 0x6d, 0x62, 0x65, 0x64, 0x64, 0x69, 0x6e,
	0x67, 0x5f, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x07, 0x20, 0x01, 0x28, 0x04, 0x52,
	0x10, 0x65, 0x6d, 0x62, 0x65, 0x64, 0x64, 0x69, 0x6e, 0x67, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f,
	0x6e, 0x12, 0x42, 0x0a, 0x06, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x73, 0x18, 0x08, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x2a, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74,
	0x5f, 0x72, 0x65, 0x74, 0x72, 0x69, 0x65, 0x76, 0x61, 0x6c, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65,
	0x72, 0x2e, 0x56, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x52, 0x06, 0x6c,
	0x61, 0x62, 0x65, 0x6c, 0x73, 0x12, 0x1f, 0x0a, 0x0b, 0x65, 0x78, 0x70, 0x69, 0x72, 0x65, 0x5f,
	0x74, 0x69, 0x6d, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x65, 0x78, 0x70, 0x69,
	0x72, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x15, 0x0a, 0x06, 0x64, 0x6f, 0x63, 0x5f, 0x69, 0x64,
	0x18, 0x0a, 0x20, 0x01, 0x28, 0x04, 0x52, 0x05, 0x64, 0x6f, 0x63, 0x49, 0x64, 0x12, 0x1c, 0x0a,
	0x0a, 0x62, 0x6f, 0x74, 0x5f, 0x62, 0x69, 0x7a, 0x5f, 0x69, 0x64, 0x18, 0x0b, 0x20, 0x01, 0x28,
	0x04, 0x52, 0x08, 0x62, 0x6f, 0x74, 0x42, 0x69, 0x7a, 0x49, 0x64, 0x22, 0x14, 0x0a, 0x12, 0x55,
	0x70, 0x64, 0x61, 0x74, 0x65, 0x4b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x52, 0x73,
	0x70, 0x22, 0x6f, 0x0a, 0x04, 0x43, 0x65, 0x6c, 0x6c, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c,
	0x75, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x12,
	0x51, 0x0a, 0x0e, 0x63, 0x65, 0x6c, 0x6c, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x74, 0x79, 0x70,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x2b, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b,
	0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x72, 0x65, 0x74, 0x72, 0x69, 0x65, 0x76, 0x61, 0x6c,
	0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x43, 0x65, 0x6c, 0x6c, 0x44, 0x61, 0x74, 0x61,
	0x54, 0x79, 0x70, 0x65, 0x52, 0x0c, 0x63, 0x65, 0x6c, 0x6c, 0x44, 0x61, 0x74, 0x61, 0x54, 0x79,
	0x70, 0x65, 0x22, 0x40, 0x0a, 0x03, 0x52, 0x6f, 0x77, 0x12, 0x39, 0x0a, 0x05, 0x63, 0x65, 0x6c,
	0x6c, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x23, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e,
	0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x72, 0x65, 0x74, 0x72, 0x69, 0x65, 0x76, 0x61,
	0x6c, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x43, 0x65, 0x6c, 0x6c, 0x52, 0x05, 0x63,
	0x65, 0x6c, 0x6c, 0x73, 0x22, 0x9b, 0x03, 0x0a, 0x0c, 0x54, 0x65, 0x78, 0x74, 0x32, 0x53, 0x51,
	0x4c, 0x4d, 0x65, 0x74, 0x61, 0x12, 0x19, 0x0a, 0x08, 0x74, 0x61, 0x62, 0x6c, 0x65, 0x5f, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x74, 0x61, 0x62, 0x6c, 0x65, 0x49, 0x64,
	0x12, 0x1d, 0x0a, 0x0a, 0x74, 0x61, 0x62, 0x6c, 0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x74, 0x61, 0x62, 0x6c, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12,
	0x19, 0x0a, 0x08, 0x64, 0x6f, 0x63, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x0d, 0x52, 0x07, 0x64, 0x6f, 0x63, 0x54, 0x79, 0x70, 0x65, 0x12, 0x4c, 0x0a, 0x07, 0x68, 0x65,
	0x61, 0x64, 0x65, 0x72, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x32, 0x2e, 0x74, 0x72,
	0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x72, 0x65, 0x74, 0x72, 0x69,
	0x65, 0x76, 0x61, 0x6c, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x54, 0x65, 0x78, 0x74,
	0x32, 0x53, 0x51, 0x4c, 0x4d, 0x65, 0x74, 0x61, 0x2e, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52,
	0x07, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x73, 0x1a, 0xe7, 0x01, 0x0a, 0x06, 0x48, 0x65, 0x61,
	0x64, 0x65, 0x72, 0x12, 0x51, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x3d, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74,
	0x5f, 0x72, 0x65, 0x74, 0x72, 0x69, 0x65, 0x76, 0x61, 0x6c, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65,
	0x72, 0x2e, 0x54, 0x65, 0x78, 0x74, 0x32, 0x53, 0x51, 0x4c, 0x4d, 0x65, 0x74, 0x61, 0x2e, 0x48,
	0x65, 0x61, 0x64, 0x65, 0x72, 0x2e, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x54, 0x79, 0x70, 0x65,
	0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x36, 0x0a, 0x04, 0x72, 0x6f, 0x77, 0x73, 0x18, 0x02,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e,
	0x62, 0x6f, 0x74, 0x5f, 0x72, 0x65, 0x74, 0x72, 0x69, 0x65, 0x76, 0x61, 0x6c, 0x5f, 0x73, 0x65,
	0x72, 0x76, 0x65, 0x72, 0x2e, 0x52, 0x6f, 0x77, 0x52, 0x04, 0x72, 0x6f, 0x77, 0x73, 0x22, 0x52,
	0x0a, 0x0a, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x54, 0x79, 0x70, 0x65, 0x12, 0x17, 0x0a, 0x13,
	0x48, 0x45, 0x41, 0x44, 0x45, 0x52, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x55, 0x4e, 0x4b, 0x4e,
	0x4f, 0x57, 0x4e, 0x10, 0x00, 0x12, 0x16, 0x0a, 0x12, 0x48, 0x45, 0x41, 0x44, 0x45, 0x52, 0x5f,
	0x54, 0x59, 0x50, 0x45, 0x5f, 0x43, 0x4f, 0x4c, 0x55, 0x4d, 0x4e, 0x10, 0x01, 0x12, 0x13, 0x0a,
	0x0f, 0x48, 0x45, 0x41, 0x44, 0x45, 0x52, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x52, 0x4f, 0x57,
	0x10, 0x02, 0x22, 0x83, 0x01, 0x0a, 0x0f, 0x54, 0x65, 0x78, 0x74, 0x32, 0x53, 0x51, 0x4c, 0x52,
	0x6f, 0x77, 0x44, 0x61, 0x74, 0x61, 0x12, 0x17, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x04, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x32, 0x02, 0x28, 0x01, 0x52, 0x02, 0x69, 0x64, 0x12,
	0x21, 0x0a, 0x0c, 0x73, 0x65, 0x67, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x73, 0x65, 0x67, 0x6d, 0x65, 0x6e, 0x74, 0x54, 0x79,
	0x70, 0x65, 0x12, 0x34, 0x0a, 0x03, 0x72, 0x6f, 0x77, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x22, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x72,
	0x65, 0x74, 0x72, 0x69, 0x65, 0x76, 0x61, 0x6c, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e,
	0x52, 0x6f, 0x77, 0x52, 0x03, 0x72, 0x6f, 0x77, 0x22, 0xfd, 0x02, 0x0a, 0x0e, 0x41, 0x64, 0x64,
	0x54, 0x65, 0x78, 0x74, 0x32, 0x53, 0x51, 0x4c, 0x52, 0x65, 0x71, 0x12, 0x22, 0x0a, 0x08, 0x72,
	0x6f, 0x62, 0x6f, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x42, 0x07, 0xfa,
	0x42, 0x04, 0x32, 0x02, 0x28, 0x01, 0x52, 0x07, 0x72, 0x6f, 0x62, 0x6f, 0x74, 0x49, 0x64, 0x12,
	0x1e, 0x0a, 0x06, 0x64, 0x6f, 0x63, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x04, 0x42,
	0x07, 0xfa, 0x42, 0x04, 0x32, 0x02, 0x28, 0x01, 0x52, 0x05, 0x64, 0x6f, 0x63, 0x49, 0x64, 0x12,
	0x3f, 0x0a, 0x04, 0x6d, 0x65, 0x74, 0x61, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2b, 0x2e,
	0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x72, 0x65, 0x74,
	0x72, 0x69, 0x65, 0x76, 0x61, 0x6c, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x54, 0x65,
	0x78, 0x74, 0x32, 0x53, 0x51, 0x4c, 0x4d, 0x65, 0x74, 0x61, 0x52, 0x04, 0x6d, 0x65, 0x74, 0x61,
	0x12, 0x42, 0x0a, 0x04, 0x72, 0x6f, 0x77, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2e,
	0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x72, 0x65,
	0x74, 0x72, 0x69, 0x65, 0x76, 0x61, 0x6c, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x54,
	0x65, 0x78, 0x74, 0x32, 0x53, 0x51, 0x4c, 0x52, 0x6f, 0x77, 0x44, 0x61, 0x74, 0x61, 0x52, 0x04,
	0x72, 0x6f, 0x77, 0x73, 0x12, 0x42, 0x0a, 0x06, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x73, 0x18, 0x05,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x2a, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e,
	0x62, 0x6f, 0x74, 0x5f, 0x72, 0x65, 0x74, 0x72, 0x69, 0x65, 0x76, 0x61, 0x6c, 0x5f, 0x73, 0x65,
	0x72, 0x76, 0x65, 0x72, 0x2e, 0x56, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x4c, 0x61, 0x62, 0x65, 0x6c,
	0x52, 0x06, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x73, 0x12, 0x1f, 0x0a, 0x0b, 0x65, 0x78, 0x70, 0x69,
	0x72, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x65,
	0x78, 0x70, 0x69, 0x72, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x66, 0x69, 0x6c,
	0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x66, 0x69,
	0x6c, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x20, 0x0a, 0x07, 0x63, 0x6f, 0x72, 0x70, 0x5f, 0x69,
	0x64, 0x18, 0x08, 0x20, 0x01, 0x28, 0x04, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x32, 0x02, 0x28, 0x01,
	0x52, 0x06, 0x63, 0x6f, 0x72, 0x70, 0x49, 0x64, 0x22, 0x10, 0x0a, 0x0e, 0x41, 0x64, 0x64, 0x54,
	0x65, 0x78, 0x74, 0x32, 0x53, 0x51, 0x4c, 0x52, 0x73, 0x70, 0x22, 0x7a, 0x0a, 0x11, 0x44, 0x65,
	0x6c, 0x65, 0x74, 0x65, 0x54, 0x65, 0x78, 0x74, 0x32, 0x53, 0x51, 0x4c, 0x52, 0x65, 0x71, 0x12,
	0x22, 0x0a, 0x08, 0x72, 0x6f, 0x62, 0x6f, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x04, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x32, 0x02, 0x28, 0x01, 0x52, 0x07, 0x72, 0x6f, 0x62, 0x6f,
	0x74, 0x49, 0x64, 0x12, 0x1e, 0x0a, 0x06, 0x64, 0x6f, 0x63, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x04, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x32, 0x02, 0x28, 0x01, 0x52, 0x05, 0x64, 0x6f,
	0x63, 0x49, 0x64, 0x12, 0x21, 0x0a, 0x0c, 0x73, 0x65, 0x67, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x74,
	0x79, 0x70, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x73, 0x65, 0x67, 0x6d, 0x65,
	0x6e, 0x74, 0x54, 0x79, 0x70, 0x65, 0x22, 0x13, 0x0a, 0x11, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65,
	0x54, 0x65, 0x78, 0x74, 0x32, 0x53, 0x51, 0x4c, 0x52, 0x73, 0x70, 0x22, 0xea, 0x01, 0x0a, 0x0d,
	0x4b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x44, 0x61, 0x74, 0x61, 0x12, 0x17, 0x0a,
	0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x32, 0x02,
	0x28, 0x01, 0x52, 0x02, 0x69, 0x64, 0x12, 0x21, 0x0a, 0x0c, 0x73, 0x65, 0x67, 0x6d, 0x65, 0x6e,
	0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x73, 0x65,
	0x67, 0x6d, 0x65, 0x6e, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x15, 0x0a, 0x06, 0x64, 0x6f, 0x63,
	0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x04, 0x52, 0x05, 0x64, 0x6f, 0x63, 0x49, 0x64,
	0x12, 0x21, 0x0a, 0x0c, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x70, 0x61, 0x67, 0x65, 0x43, 0x6f, 0x6e, 0x74,
	0x65, 0x6e, 0x74, 0x12, 0x42, 0x0a, 0x06, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x73, 0x18, 0x05, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x2a, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62,
	0x6f, 0x74, 0x5f, 0x72, 0x65, 0x74, 0x72, 0x69, 0x65, 0x76, 0x61, 0x6c, 0x5f, 0x73, 0x65, 0x72,
	0x76, 0x65, 0x72, 0x2e, 0x56, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x52,
	0x06, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x73, 0x12, 0x1f, 0x0a, 0x0b, 0x65, 0x78, 0x70, 0x69, 0x72,
	0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x65, 0x78,
	0x70, 0x69, 0x72, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x22, 0x93, 0x02, 0x0a, 0x17, 0x41, 0x64, 0x64,
	0x52, 0x65, 0x61, 0x6c, 0x54, 0x69, 0x6d, 0x65, 0x4b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67,
	0x65, 0x52, 0x65, 0x71, 0x12, 0x22, 0x0a, 0x08, 0x72, 0x6f, 0x62, 0x6f, 0x74, 0x5f, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x32, 0x02, 0x28, 0x01, 0x52,
	0x07, 0x72, 0x6f, 0x62, 0x6f, 0x74, 0x49, 0x64, 0x12, 0x22, 0x0a, 0x08, 0x69, 0x6e, 0x64, 0x65,
	0x78, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x04, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x32,
	0x02, 0x28, 0x01, 0x52, 0x07, 0x69, 0x6e, 0x64, 0x65, 0x78, 0x49, 0x64, 0x12, 0x19, 0x0a, 0x08,
	0x64, 0x6f, 0x63, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x07,
	0x64, 0x6f, 0x63, 0x54, 0x79, 0x70, 0x65, 0x12, 0x2b, 0x0a, 0x11, 0x65, 0x6d, 0x62, 0x65, 0x64,
	0x64, 0x69, 0x6e, 0x67, 0x5f, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x04, 0x52, 0x10, 0x65, 0x6d, 0x62, 0x65, 0x64, 0x64, 0x69, 0x6e, 0x67, 0x56, 0x65, 0x72,
	0x73, 0x69, 0x6f, 0x6e, 0x12, 0x4a, 0x0a, 0x09, 0x6b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67,
	0x65, 0x18, 0x05, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2c, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b,
	0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x72, 0x65, 0x74, 0x72, 0x69, 0x65, 0x76, 0x61, 0x6c,
	0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x4b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67,
	0x65, 0x44, 0x61, 0x74, 0x61, 0x52, 0x09, 0x6b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65,
	0x12, 0x1c, 0x0a, 0x0a, 0x62, 0x6f, 0x74, 0x5f, 0x62, 0x69, 0x7a, 0x5f, 0x69, 0x64, 0x18, 0x06,
	0x20, 0x01, 0x28, 0x04, 0x52, 0x08, 0x62, 0x6f, 0x74, 0x42, 0x69, 0x7a, 0x49, 0x64, 0x22, 0x19,
	0x0a, 0x17, 0x41, 0x64, 0x64, 0x52, 0x65, 0x61, 0x6c, 0x54, 0x69, 0x6d, 0x65, 0x4b, 0x6e, 0x6f,
	0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x52, 0x73, 0x70, 0x22, 0x4d, 0x0a, 0x0f, 0x4b, 0x6e, 0x6f,
	0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x49, 0x44, 0x54, 0x79, 0x70, 0x65, 0x12, 0x17, 0x0a, 0x02,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x32, 0x02, 0x28,
	0x01, 0x52, 0x02, 0x69, 0x64, 0x12, 0x21, 0x0a, 0x0c, 0x73, 0x65, 0x67, 0x6d, 0x65, 0x6e, 0x74,
	0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x73, 0x65, 0x67,
	0x6d, 0x65, 0x6e, 0x74, 0x54, 0x79, 0x70, 0x65, 0x22, 0x8e, 0x02, 0x0a, 0x1a, 0x44, 0x65, 0x6c,
	0x65, 0x74, 0x65, 0x52, 0x65, 0x61, 0x6c, 0x54, 0x69, 0x6d, 0x65, 0x4b, 0x6e, 0x6f, 0x77, 0x6c,
	0x65, 0x64, 0x67, 0x65, 0x52, 0x65, 0x71, 0x12, 0x22, 0x0a, 0x08, 0x72, 0x6f, 0x62, 0x6f, 0x74,
	0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x32, 0x02,
	0x28, 0x01, 0x52, 0x07, 0x72, 0x6f, 0x62, 0x6f, 0x74, 0x49, 0x64, 0x12, 0x22, 0x0a, 0x08, 0x69,
	0x6e, 0x64, 0x65, 0x78, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x04, 0x42, 0x07, 0xfa,
	0x42, 0x04, 0x32, 0x02, 0x28, 0x01, 0x52, 0x07, 0x69, 0x6e, 0x64, 0x65, 0x78, 0x49, 0x64, 0x12,
	0x2b, 0x0a, 0x11, 0x65, 0x6d, 0x62, 0x65, 0x64, 0x64, 0x69, 0x6e, 0x67, 0x5f, 0x76, 0x65, 0x72,
	0x73, 0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x04, 0x52, 0x10, 0x65, 0x6d, 0x62, 0x65,
	0x64, 0x64, 0x69, 0x6e, 0x67, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x19, 0x0a, 0x08,
	0x64, 0x6f, 0x63, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x07,
	0x64, 0x6f, 0x63, 0x54, 0x79, 0x70, 0x65, 0x12, 0x42, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18,
	0x05, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2e, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50,
	0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x72, 0x65, 0x74, 0x72, 0x69, 0x65, 0x76, 0x61, 0x6c, 0x5f, 0x73,
	0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x4b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x49,
	0x44, 0x54, 0x79, 0x70, 0x65, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x12, 0x1c, 0x0a, 0x0a, 0x62,
	0x6f, 0x74, 0x5f, 0x62, 0x69, 0x7a, 0x5f, 0x69, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x04, 0x52,
	0x08, 0x62, 0x6f, 0x74, 0x42, 0x69, 0x7a, 0x49, 0x64, 0x22, 0x1c, 0x0a, 0x1a, 0x44, 0x65, 0x6c,
	0x65, 0x74, 0x65, 0x52, 0x65, 0x61, 0x6c, 0x54, 0x69, 0x6d, 0x65, 0x4b, 0x6e, 0x6f, 0x77, 0x6c,
	0x65, 0x64, 0x67, 0x65, 0x52, 0x73, 0x70, 0x22, 0xaa, 0x07, 0x0a, 0x14, 0x52, 0x65, 0x74, 0x72,
	0x69, 0x65, 0x76, 0x61, 0x6c, 0x52, 0x65, 0x61, 0x6c, 0x54, 0x69, 0x6d, 0x65, 0x52, 0x65, 0x71,
	0x12, 0x22, 0x0a, 0x08, 0x72, 0x6f, 0x62, 0x6f, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x04, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x32, 0x02, 0x28, 0x01, 0x52, 0x07, 0x72, 0x6f, 0x62,
	0x6f, 0x74, 0x49, 0x64, 0x12, 0x23, 0x0a, 0x08, 0x71, 0x75, 0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x10, 0x01, 0x52,
	0x08, 0x71, 0x75, 0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x54, 0x0a, 0x07, 0x66, 0x69, 0x6c,
	0x74, 0x65, 0x72, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x3a, 0x2e, 0x74, 0x72, 0x70,
	0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x72, 0x65, 0x74, 0x72, 0x69, 0x65,
	0x76, 0x61, 0x6c, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x52, 0x65, 0x74, 0x72, 0x69,
	0x65, 0x76, 0x61, 0x6c, 0x52, 0x65, 0x61, 0x6c, 0x54, 0x69, 0x6d, 0x65, 0x52, 0x65, 0x71, 0x2e,
	0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x52, 0x07, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x73, 0x12,
	0x13, 0x0a, 0x05, 0x74, 0x6f, 0x70, 0x5f, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x04,
	0x74, 0x6f, 0x70, 0x4e, 0x12, 0x2b, 0x0a, 0x11, 0x65, 0x6d, 0x62, 0x65, 0x64, 0x64, 0x69, 0x6e,
	0x67, 0x5f, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x05, 0x20, 0x01, 0x28, 0x04, 0x52,
	0x10, 0x65, 0x6d, 0x62, 0x65, 0x64, 0x64, 0x69, 0x6e, 0x67, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f,
	0x6e, 0x12, 0x52, 0x0a, 0x06, 0x72, 0x65, 0x72, 0x61, 0x6e, 0x6b, 0x18, 0x07, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x3a, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74,
	0x5f, 0x72, 0x65, 0x74, 0x72, 0x69, 0x65, 0x76, 0x61, 0x6c, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65,
	0x72, 0x2e, 0x52, 0x65, 0x74, 0x72, 0x69, 0x65, 0x76, 0x61, 0x6c, 0x52, 0x65, 0x61, 0x6c, 0x54,
	0x69, 0x6d, 0x65, 0x52, 0x65, 0x71, 0x2e, 0x52, 0x65, 0x72, 0x61, 0x6e, 0x6b, 0x52, 0x06, 0x72,
	0x65, 0x72, 0x61, 0x6e, 0x6b, 0x12, 0x1d, 0x0a, 0x0a, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x5f,
	0x6b, 0x65, 0x79, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x66, 0x69, 0x6c, 0x74, 0x65,
	0x72, 0x4b, 0x65, 0x79, 0x12, 0x48, 0x0a, 0x06, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x73, 0x18, 0x09,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x30, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e,
	0x62, 0x6f, 0x74, 0x5f, 0x72, 0x65, 0x74, 0x72, 0x69, 0x65, 0x76, 0x61, 0x6c, 0x5f, 0x73, 0x65,
	0x72, 0x76, 0x65, 0x72, 0x2e, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x56, 0x65, 0x63, 0x74, 0x6f,
	0x72, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x52, 0x06, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x73, 0x12, 0x1d,
	0x0a, 0x0a, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x5f, 0x75, 0x72, 0x6c, 0x73, 0x18, 0x0a, 0x20, 0x03,
	0x28, 0x09, 0x52, 0x09, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x55, 0x72, 0x6c, 0x73, 0x12, 0x1c, 0x0a,
	0x0a, 0x62, 0x6f, 0x74, 0x5f, 0x62, 0x69, 0x7a, 0x5f, 0x69, 0x64, 0x18, 0x0b, 0x20, 0x01, 0x28,
	0x04, 0x52, 0x08, 0x62, 0x6f, 0x74, 0x42, 0x69, 0x7a, 0x49, 0x64, 0x12, 0x59, 0x0a, 0x10, 0x6c,
	0x61, 0x62, 0x65, 0x6c, 0x5f, 0x65, 0x78, 0x70, 0x72, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x18,
	0x0c, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2e, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50,
	0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x72, 0x65, 0x74, 0x72, 0x69, 0x65, 0x76, 0x61, 0x6c, 0x5f, 0x73,
	0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x45, 0x78, 0x70, 0x72, 0x65,
	0x73, 0x73, 0x69, 0x6f, 0x6e, 0x52, 0x0f, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x45, 0x78, 0x70, 0x72,
	0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x23, 0x0a, 0x0d, 0x73, 0x75, 0x62, 0x5f, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x0d, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0c, 0x73,
	0x75, 0x62, 0x51, 0x75, 0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x56, 0x0a, 0x0f, 0x73,
	0x65, 0x61, 0x72, 0x63, 0x68, 0x5f, 0x73, 0x74, 0x72, 0x61, 0x74, 0x65, 0x67, 0x79, 0x18, 0x0e,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x2d, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e,
	0x62, 0x6f, 0x74, 0x5f, 0x72, 0x65, 0x74, 0x72, 0x69, 0x65, 0x76, 0x61, 0x6c, 0x5f, 0x73, 0x65,
	0x72, 0x76, 0x65, 0x72, 0x2e, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x53, 0x74, 0x72, 0x61, 0x74,
	0x65, 0x67, 0x79, 0x52, 0x0e, 0x73, 0x65, 0x61, 0x72, 0x63, 0x68, 0x53, 0x74, 0x72, 0x61, 0x74,
	0x65, 0x67, 0x79, 0x12, 0x1d, 0x0a, 0x0a, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x5f, 0x6e, 0x61, 0x6d,
	0x65, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x4e, 0x61,
	0x6d, 0x65, 0x1a, 0x73, 0x0a, 0x06, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x12, 0x19, 0x0a, 0x08,
	0x69, 0x6e, 0x64, 0x65, 0x78, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x07,
	0x69, 0x6e, 0x64, 0x65, 0x78, 0x49, 0x64, 0x12, 0x1e, 0x0a, 0x0a, 0x63, 0x6f, 0x6e, 0x66, 0x69,
	0x64, 0x65, 0x6e, 0x63, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x02, 0x52, 0x0a, 0x63, 0x6f, 0x6e,
	0x66, 0x69, 0x64, 0x65, 0x6e, 0x63, 0x65, 0x12, 0x13, 0x0a, 0x05, 0x74, 0x6f, 0x70, 0x5f, 0x6e,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x04, 0x74, 0x6f, 0x70, 0x4e, 0x12, 0x19, 0x0a, 0x08,
	0x64, 0x6f, 0x63, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x07,
	0x64, 0x6f, 0x63, 0x54, 0x79, 0x70, 0x65, 0x1a, 0x4b, 0x0a, 0x06, 0x52, 0x65, 0x72, 0x61, 0x6e,
	0x6b, 0x12, 0x14, 0x0a, 0x05, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x05, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x12, 0x13, 0x0a, 0x05, 0x74, 0x6f, 0x70, 0x5f, 0x6e,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x04, 0x74, 0x6f, 0x70, 0x4e, 0x12, 0x16, 0x0a, 0x06,
	0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x08, 0x52, 0x06, 0x65, 0x6e,
	0x61, 0x62, 0x6c, 0x65, 0x22, 0xb3, 0x04, 0x0a, 0x14, 0x52, 0x65, 0x74, 0x72, 0x69, 0x65, 0x76,
	0x61, 0x6c, 0x52, 0x65, 0x61, 0x6c, 0x54, 0x69, 0x6d, 0x65, 0x52, 0x73, 0x70, 0x12, 0x4b, 0x0a,
	0x04, 0x64, 0x6f, 0x63, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x37, 0x2e, 0x74, 0x72,
	0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x72, 0x65, 0x74, 0x72, 0x69,
	0x65, 0x76, 0x61, 0x6c, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x52, 0x65, 0x74, 0x72,
	0x69, 0x65, 0x76, 0x61, 0x6c, 0x52, 0x65, 0x61, 0x6c, 0x54, 0x69, 0x6d, 0x65, 0x52, 0x73, 0x70,
	0x2e, 0x44, 0x6f, 0x63, 0x52, 0x04, 0x64, 0x6f, 0x63, 0x73, 0x1a, 0xcd, 0x03, 0x0a, 0x03, 0x44,
	0x6f, 0x63, 0x12, 0x19, 0x0a, 0x08, 0x69, 0x6e, 0x64, 0x65, 0x78, 0x5f, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x04, 0x52, 0x07, 0x69, 0x6e, 0x64, 0x65, 0x78, 0x49, 0x64, 0x12, 0x0e, 0x0a,
	0x02, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x04, 0x52, 0x02, 0x69, 0x64, 0x12, 0x1e, 0x0a,
	0x0a, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x64, 0x65, 0x6e, 0x63, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x02, 0x52, 0x0a, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x64, 0x65, 0x6e, 0x63, 0x65, 0x12, 0x1a, 0x0a,
	0x08, 0x71, 0x75, 0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x08, 0x71, 0x75, 0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x16, 0x0a, 0x06, 0x61, 0x6e, 0x73,
	0x77, 0x65, 0x72, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x61, 0x6e, 0x73, 0x77, 0x65,
	0x72, 0x12, 0x21, 0x0a, 0x0c, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e,
	0x74, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x70, 0x61, 0x67, 0x65, 0x43, 0x6f, 0x6e,
	0x74, 0x65, 0x6e, 0x74, 0x12, 0x19, 0x0a, 0x08, 0x6f, 0x72, 0x67, 0x5f, 0x64, 0x61, 0x74, 0x61,
	0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6f, 0x72, 0x67, 0x44, 0x61, 0x74, 0x61, 0x12,
	0x19, 0x0a, 0x08, 0x64, 0x6f, 0x63, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28,
	0x0d, 0x52, 0x07, 0x64, 0x6f, 0x63, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1e, 0x0a, 0x0b, 0x69, 0x73,
	0x5f, 0x62, 0x69, 0x67, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x18, 0x09, 0x20, 0x01, 0x28, 0x08, 0x52,
	0x09, 0x69, 0x73, 0x42, 0x69, 0x67, 0x44, 0x61, 0x74, 0x61, 0x12, 0x43, 0x0a, 0x05, 0x65, 0x78,
	0x74, 0x72, 0x61, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2d, 0x2e, 0x74, 0x72, 0x70, 0x63,
	0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x72, 0x65, 0x74, 0x72, 0x69, 0x65, 0x76,
	0x61, 0x6c, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x52, 0x65, 0x74, 0x72, 0x69, 0x65,
	0x76, 0x61, 0x6c, 0x45, 0x78, 0x74, 0x72, 0x61, 0x52, 0x05, 0x65, 0x78, 0x74, 0x72, 0x61, 0x12,
	0x15, 0x0a, 0x06, 0x64, 0x6f, 0x63, 0x5f, 0x69, 0x64, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x04, 0x52,
	0x05, 0x64, 0x6f, 0x63, 0x49, 0x64, 0x12, 0x53, 0x0a, 0x0b, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74,
	0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x32, 0x2e, 0x74, 0x72,
	0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x72, 0x65, 0x74, 0x72, 0x69,
	0x65, 0x76, 0x61, 0x6c, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x52, 0x65, 0x74, 0x72,
	0x69, 0x65, 0x76, 0x61, 0x6c, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x54, 0x79, 0x70, 0x65, 0x52,
	0x0a, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x69,
	0x6d, 0x61, 0x67, 0x65, 0x5f, 0x75, 0x72, 0x6c, 0x73, 0x18, 0x0d, 0x20, 0x03, 0x28, 0x09, 0x52,
	0x09, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x55, 0x72, 0x6c, 0x73, 0x22, 0x6d, 0x0a, 0x19, 0x43, 0x6c,
	0x65, 0x61, 0x72, 0x41, 0x70, 0x70, 0x56, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x52, 0x65, 0x73, 0x6f,
	0x75, 0x72, 0x63, 0x65, 0x52, 0x65, 0x71, 0x12, 0x19, 0x0a, 0x08, 0x72, 0x6f, 0x62, 0x6f, 0x74,
	0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x07, 0x72, 0x6f, 0x62, 0x6f, 0x74,
	0x49, 0x64, 0x12, 0x1c, 0x0a, 0x0a, 0x62, 0x6f, 0x74, 0x5f, 0x62, 0x69, 0x7a, 0x5f, 0x69, 0x64,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x62, 0x6f, 0x74, 0x42, 0x69, 0x7a, 0x49, 0x64,
	0x12, 0x17, 0x0a, 0x07, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x04, 0x52, 0x06, 0x74, 0x61, 0x73, 0x6b, 0x49, 0x64, 0x22, 0x1b, 0x0a, 0x19, 0x43, 0x6c, 0x65,
	0x61, 0x72, 0x41, 0x70, 0x70, 0x56, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x52, 0x65, 0x73, 0x6f, 0x75,
	0x72, 0x63, 0x65, 0x52, 0x73, 0x70, 0x2a, 0x2c, 0x0a, 0x0a, 0x45, 0x53, 0x44, 0x61, 0x74, 0x61,
	0x54, 0x79, 0x70, 0x65, 0x12, 0x0c, 0x0a, 0x08, 0x4f, 0x52, 0x47, 0x5f, 0x44, 0x41, 0x54, 0x41,
	0x10, 0x00, 0x12, 0x10, 0x0a, 0x0c, 0x54, 0x4d, 0x50, 0x5f, 0x4f, 0x52, 0x47, 0x5f, 0x44, 0x41,
	0x54, 0x41, 0x10, 0x01, 0x2a, 0x23, 0x0a, 0x06, 0x44, 0x42, 0x54, 0x79, 0x70, 0x65, 0x12, 0x09,
	0x0a, 0x05, 0x4d, 0x59, 0x53, 0x51, 0x4c, 0x10, 0x00, 0x12, 0x0e, 0x0a, 0x0a, 0x53, 0x51, 0x4c,
	0x5f, 0x53, 0x45, 0x52, 0x56, 0x45, 0x52, 0x10, 0x01, 0x2a, 0x43, 0x0a, 0x16, 0x53, 0x65, 0x61,
	0x72, 0x63, 0x68, 0x53, 0x74, 0x72, 0x61, 0x74, 0x65, 0x67, 0x79, 0x54, 0x79, 0x70, 0x65, 0x45,
	0x6e, 0x75, 0x6d, 0x12, 0x0a, 0x0a, 0x06, 0x4d, 0x69, 0x78, 0x69, 0x6e, 0x67, 0x10, 0x00, 0x12,
	0x0d, 0x0a, 0x09, 0x53, 0x65, 0x6d, 0x61, 0x6e, 0x74, 0x69, 0x63, 0x73, 0x10, 0x01, 0x12, 0x0e,
	0x0a, 0x0a, 0x4e, 0x6f, 0x6e, 0x65, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x10, 0x02, 0x2a, 0x2c,
	0x0a, 0x0d, 0x4b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12,
	0x0d, 0x0a, 0x09, 0x4b, 0x4e, 0x4f, 0x57, 0x4c, 0x45, 0x44, 0x47, 0x45, 0x10, 0x00, 0x12, 0x0c,
	0x0a, 0x08, 0x52, 0x45, 0x41, 0x4c, 0x54, 0x49, 0x4d, 0x45, 0x10, 0x01, 0x2a, 0x61, 0x0a, 0x13,
	0x52, 0x65, 0x74, 0x72, 0x69, 0x65, 0x76, 0x61, 0x6c, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x54,
	0x79, 0x70, 0x65, 0x12, 0x0d, 0x0a, 0x09, 0x52, 0x45, 0x54, 0x52, 0x49, 0x45, 0x56, 0x41, 0x4c,
	0x10, 0x00, 0x12, 0x0c, 0x0a, 0x08, 0x54, 0x45, 0x58, 0x54, 0x32, 0x53, 0x51, 0x4c, 0x10, 0x01,
	0x12, 0x16, 0x0a, 0x12, 0x49, 0x4d, 0x41, 0x47, 0x45, 0x5f, 0x53, 0x45, 0x41, 0x52, 0x43, 0x48,
	0x5f, 0x49, 0x4d, 0x41, 0x47, 0x45, 0x10, 0x02, 0x12, 0x15, 0x0a, 0x11, 0x54, 0x45, 0x58, 0x54,
	0x5f, 0x53, 0x45, 0x41, 0x52, 0x43, 0x48, 0x5f, 0x49, 0x4d, 0x41, 0x47, 0x45, 0x10, 0x03, 0x2a,
	0xa7, 0x01, 0x0a, 0x0c, 0x43, 0x65, 0x6c, 0x6c, 0x44, 0x61, 0x74, 0x61, 0x54, 0x79, 0x70, 0x65,
	0x12, 0x14, 0x0a, 0x10, 0x44, 0x41, 0x54, 0x41, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x53, 0x54,
	0x52, 0x49, 0x4e, 0x47, 0x10, 0x00, 0x12, 0x15, 0x0a, 0x11, 0x44, 0x41, 0x54, 0x41, 0x5f, 0x54,
	0x59, 0x50, 0x45, 0x5f, 0x49, 0x4e, 0x54, 0x45, 0x47, 0x45, 0x52, 0x10, 0x01, 0x12, 0x13, 0x0a,
	0x0f, 0x44, 0x41, 0x54, 0x41, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x46, 0x4c, 0x4f, 0x41, 0x54,
	0x10, 0x02, 0x12, 0x12, 0x0a, 0x0e, 0x44, 0x41, 0x54, 0x41, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f,
	0x44, 0x41, 0x54, 0x45, 0x10, 0x03, 0x12, 0x12, 0x0a, 0x0e, 0x44, 0x41, 0x54, 0x41, 0x5f, 0x54,
	0x59, 0x50, 0x45, 0x5f, 0x54, 0x49, 0x4d, 0x45, 0x10, 0x04, 0x12, 0x16, 0x0a, 0x12, 0x44, 0x41,
	0x54, 0x41, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x44, 0x41, 0x54, 0x45, 0x54, 0x49, 0x4d, 0x45,
	0x10, 0x05, 0x12, 0x15, 0x0a, 0x11, 0x44, 0x41, 0x54, 0x41, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f,
	0x42, 0x4f, 0x4f, 0x4c, 0x45, 0x41, 0x4e, 0x10, 0x06, 0x32, 0xcc, 0x08, 0x0a, 0x09, 0x52, 0x65,
	0x74, 0x72, 0x69, 0x65, 0x76, 0x61, 0x6c, 0x12, 0x5c, 0x0a, 0x06, 0x53, 0x65, 0x61, 0x72, 0x63,
	0x68, 0x12, 0x28, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74,
	0x5f, 0x72, 0x65, 0x74, 0x72, 0x69, 0x65, 0x76, 0x61, 0x6c, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65,
	0x72, 0x2e, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x52, 0x65, 0x71, 0x1a, 0x28, 0x2e, 0x74, 0x72,
	0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x72, 0x65, 0x74, 0x72, 0x69,
	0x65, 0x76, 0x61, 0x6c, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x53, 0x65, 0x61, 0x72,
	0x63, 0x68, 0x52, 0x73, 0x70, 0x12, 0x5f, 0x0a, 0x07, 0x50, 0x75, 0x62, 0x6c, 0x69, 0x73, 0x68,
	0x12, 0x29, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f,
	0x72, 0x65, 0x74, 0x72, 0x69, 0x65, 0x76, 0x61, 0x6c, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72,
	0x2e, 0x50, 0x75, 0x62, 0x6c, 0x69, 0x73, 0x68, 0x52, 0x65, 0x71, 0x1a, 0x29, 0x2e, 0x74, 0x72,
	0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x72, 0x65, 0x74, 0x72, 0x69,
	0x65, 0x76, 0x61, 0x6c, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x50, 0x75, 0x62, 0x6c,
	0x69, 0x73, 0x68, 0x52, 0x73, 0x70, 0x12, 0x68, 0x0a, 0x0a, 0x53, 0x69, 0x6d, 0x69, 0x6c, 0x61,
	0x72, 0x69, 0x74, 0x79, 0x12, 0x2c, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e,
	0x62, 0x6f, 0x74, 0x5f, 0x72, 0x65, 0x74, 0x72, 0x69, 0x65, 0x76, 0x61, 0x6c, 0x5f, 0x73, 0x65,
	0x72, 0x76, 0x65, 0x72, 0x2e, 0x53, 0x69, 0x6d, 0x69, 0x6c, 0x61, 0x72, 0x69, 0x74, 0x79, 0x52,
	0x65, 0x71, 0x1a, 0x2c, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f,
	0x74, 0x5f, 0x72, 0x65, 0x74, 0x72, 0x69, 0x65, 0x76, 0x61, 0x6c, 0x5f, 0x73, 0x65, 0x72, 0x76,
	0x65, 0x72, 0x2e, 0x53, 0x69, 0x6d, 0x69, 0x6c, 0x61, 0x72, 0x69, 0x74, 0x79, 0x52, 0x73, 0x70,
	0x12, 0x6e, 0x0a, 0x0c, 0x49, 0x6e, 0x64, 0x65, 0x78, 0x52, 0x65, 0x62, 0x75, 0x69, 0x6c, 0x64,
	0x12, 0x2e, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f,
	0x72, 0x65, 0x74, 0x72, 0x69, 0x65, 0x76, 0x61, 0x6c, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72,
	0x2e, 0x49, 0x6e, 0x64, 0x65, 0x78, 0x52, 0x65, 0x62, 0x75, 0x69, 0x6c, 0x64, 0x52, 0x65, 0x71,
	0x1a, 0x2e, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f,
	0x72, 0x65, 0x74, 0x72, 0x69, 0x65, 0x76, 0x61, 0x6c, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72,
	0x2e, 0x49, 0x6e, 0x64, 0x65, 0x78, 0x52, 0x65, 0x62, 0x75, 0x69, 0x6c, 0x64, 0x52, 0x73, 0x70,
	0x12, 0x8c, 0x01, 0x0a, 0x16, 0x43, 0x6f, 0x6e, 0x74, 0x69, 0x6e, 0x75, 0x65, 0x54, 0x65, 0x72,
	0x6d, 0x69, 0x6e, 0x61, 0x74, 0x65, 0x64, 0x54, 0x61, 0x73, 0x6b, 0x12, 0x38, 0x2e, 0x74, 0x72,
	0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x72, 0x65, 0x74, 0x72, 0x69,
	0x65, 0x76, 0x61, 0x6c, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x43, 0x6f, 0x6e, 0x74,
	0x69, 0x6e, 0x75, 0x65, 0x54, 0x65, 0x72, 0x6d, 0x69, 0x6e, 0x61, 0x74, 0x65, 0x64, 0x54, 0x61,
	0x73, 0x6b, 0x52, 0x65, 0x71, 0x1a, 0x38, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50,
	0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x72, 0x65, 0x74, 0x72, 0x69, 0x65, 0x76, 0x61, 0x6c, 0x5f, 0x73,
	0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x43, 0x6f, 0x6e, 0x74, 0x69, 0x6e, 0x75, 0x65, 0x54, 0x65,
	0x72, 0x6d, 0x69, 0x6e, 0x61, 0x74, 0x65, 0x64, 0x54, 0x61, 0x73, 0x6b, 0x52, 0x73, 0x70, 0x12,
	0x7a, 0x0a, 0x10, 0x55, 0x70, 0x67, 0x72, 0x61, 0x64, 0x65, 0x45, 0x6d, 0x62, 0x65, 0x64, 0x64,
	0x69, 0x6e, 0x67, 0x12, 0x32, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62,
	0x6f, 0x74, 0x5f, 0x72, 0x65, 0x74, 0x72, 0x69, 0x65, 0x76, 0x61, 0x6c, 0x5f, 0x73, 0x65, 0x72,
	0x76, 0x65, 0x72, 0x2e, 0x55, 0x70, 0x67, 0x72, 0x61, 0x64, 0x65, 0x45, 0x6d, 0x62, 0x65, 0x64,
	0x64, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x1a, 0x32, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b,
	0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x72, 0x65, 0x74, 0x72, 0x69, 0x65, 0x76, 0x61, 0x6c,
	0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x55, 0x70, 0x67, 0x72, 0x61, 0x64, 0x65, 0x45,
	0x6d, 0x62, 0x65, 0x64, 0x64, 0x69, 0x6e, 0x67, 0x52, 0x73, 0x70, 0x12, 0x6e, 0x0a, 0x0c, 0x43,
	0x68, 0x65, 0x63, 0x6b, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x2e, 0x2e, 0x74, 0x72,
	0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x72, 0x65, 0x74, 0x72, 0x69,
	0x65, 0x76, 0x61, 0x6c, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x43, 0x68, 0x65, 0x63,
	0x6b, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x1a, 0x2e, 0x2e, 0x74, 0x72,
	0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x72, 0x65, 0x74, 0x72, 0x69,
	0x65, 0x76, 0x61, 0x6c, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x43, 0x68, 0x65, 0x63,
	0x6b, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x52, 0x73, 0x70, 0x12, 0x9b, 0x01, 0x0a, 0x1b,
	0x42, 0x61, 0x74, 0x63, 0x68, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x41, 0x6c, 0x6c, 0x4b, 0x6e,
	0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x50, 0x72, 0x6f, 0x64, 0x12, 0x3d, 0x2e, 0x74, 0x72,
	0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x72, 0x65, 0x74, 0x72, 0x69,
	0x65, 0x76, 0x61, 0x6c, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x42, 0x61, 0x74, 0x63,
	0x68, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x41, 0x6c, 0x6c, 0x4b, 0x6e, 0x6f, 0x77, 0x6c, 0x65,
	0x64, 0x67, 0x65, 0x50, 0x72, 0x6f, 0x64, 0x52, 0x65, 0x71, 0x1a, 0x3d, 0x2e, 0x74, 0x72, 0x70,
	0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x72, 0x65, 0x74, 0x72, 0x69, 0x65,
	0x76, 0x61, 0x6c, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x42, 0x61, 0x74, 0x63, 0x68,
	0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x41, 0x6c, 0x6c, 0x4b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64,
	0x67, 0x65, 0x50, 0x72, 0x6f, 0x64, 0x52, 0x73, 0x70, 0x12, 0x8c, 0x01, 0x0a, 0x16, 0x43, 0x6c,
	0x65, 0x61, 0x72, 0x41, 0x70, 0x70, 0x56, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x52, 0x65, 0x73, 0x6f,
	0x75, 0x72, 0x63, 0x65, 0x12, 0x38, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e,
	0x62, 0x6f, 0x74, 0x5f, 0x72, 0x65, 0x74, 0x72, 0x69, 0x65, 0x76, 0x61, 0x6c, 0x5f, 0x73, 0x65,
	0x72, 0x76, 0x65, 0x72, 0x2e, 0x43, 0x6c, 0x65, 0x61, 0x72, 0x41, 0x70, 0x70, 0x56, 0x65, 0x63,
	0x74, 0x6f, 0x72, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x52, 0x65, 0x71, 0x1a, 0x38,
	0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x72, 0x65,
	0x74, 0x72, 0x69, 0x65, 0x76, 0x61, 0x6c, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x43,
	0x6c, 0x65, 0x61, 0x72, 0x41, 0x70, 0x70, 0x56, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x52, 0x65, 0x73,
	0x6f, 0x75, 0x72, 0x63, 0x65, 0x52, 0x73, 0x70, 0x32, 0x80, 0x1f, 0x0a, 0x0b, 0x44, 0x69, 0x72,
	0x65, 0x63, 0x74, 0x49, 0x6e, 0x64, 0x65, 0x78, 0x12, 0x6b, 0x0a, 0x0b, 0x43, 0x72, 0x65, 0x61,
	0x74, 0x65, 0x49, 0x6e, 0x64, 0x65, 0x78, 0x12, 0x2d, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b,
	0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x72, 0x65, 0x74, 0x72, 0x69, 0x65, 0x76, 0x61, 0x6c,
	0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x49, 0x6e,
	0x64, 0x65, 0x78, 0x52, 0x65, 0x71, 0x1a, 0x2d, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45,
	0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x72, 0x65, 0x74, 0x72, 0x69, 0x65, 0x76, 0x61, 0x6c, 0x5f,
	0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x49, 0x6e, 0x64,
	0x65, 0x78, 0x52, 0x73, 0x70, 0x12, 0x6b, 0x0a, 0x0b, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x49,
	0x6e, 0x64, 0x65, 0x78, 0x12, 0x2d, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e,
	0x62, 0x6f, 0x74, 0x5f, 0x72, 0x65, 0x74, 0x72, 0x69, 0x65, 0x76, 0x61, 0x6c, 0x5f, 0x73, 0x65,
	0x72, 0x76, 0x65, 0x72, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x49, 0x6e, 0x64, 0x65, 0x78,
	0x52, 0x65, 0x71, 0x1a, 0x2d, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62,
	0x6f, 0x74, 0x5f, 0x72, 0x65, 0x74, 0x72, 0x69, 0x65, 0x76, 0x61, 0x6c, 0x5f, 0x73, 0x65, 0x72,
	0x76, 0x65, 0x72, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x49, 0x6e, 0x64, 0x65, 0x78, 0x52,
	0x73, 0x70, 0x12, 0x65, 0x0a, 0x09, 0x41, 0x64, 0x64, 0x56, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x12,
	0x2b, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x72,
	0x65, 0x74, 0x72, 0x69, 0x65, 0x76, 0x61, 0x6c, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e,
	0x41, 0x64, 0x64, 0x56, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x52, 0x65, 0x71, 0x1a, 0x2b, 0x2e, 0x74,
	0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x72, 0x65, 0x74, 0x72,
	0x69, 0x65, 0x76, 0x61, 0x6c, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x41, 0x64, 0x64,
	0x56, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x52, 0x73, 0x70, 0x12, 0x6e, 0x0a, 0x0c, 0x44, 0x65, 0x6c,
	0x65, 0x74, 0x65, 0x56, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x12, 0x2e, 0x2e, 0x74, 0x72, 0x70, 0x63,
	0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x72, 0x65, 0x74, 0x72, 0x69, 0x65, 0x76,
	0x61, 0x6c, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65,
	0x56, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x52, 0x65, 0x71, 0x1a, 0x2e, 0x2e, 0x74, 0x72, 0x70, 0x63,
	0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x72, 0x65, 0x74, 0x72, 0x69, 0x65, 0x76,
	0x61, 0x6c, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65,
	0x56, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x52, 0x73, 0x70, 0x12, 0x6e, 0x0a, 0x0c, 0x55, 0x70, 0x64,
	0x61, 0x74, 0x65, 0x56, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x12, 0x2e, 0x2e, 0x74, 0x72, 0x70, 0x63,
	0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x72, 0x65, 0x74, 0x72, 0x69, 0x65, 0x76,
	0x61, 0x6c, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65,
	0x56, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x52, 0x65, 0x71, 0x1a, 0x2e, 0x2e, 0x74, 0x72, 0x70, 0x63,
	0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x72, 0x65, 0x74, 0x72, 0x69, 0x65, 0x76,
	0x61, 0x6c, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65,
	0x56, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x52, 0x73, 0x70, 0x12, 0x6e, 0x0a, 0x0c, 0x53, 0x65, 0x61,
	0x72, 0x63, 0x68, 0x56, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x12, 0x2e, 0x2e, 0x74, 0x72, 0x70, 0x63,
	0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x72, 0x65, 0x74, 0x72, 0x69, 0x65, 0x76,
	0x61, 0x6c, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68,
	0x56, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x52, 0x65, 0x71, 0x1a, 0x2e, 0x2e, 0x74, 0x72, 0x70, 0x63,
	0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x72, 0x65, 0x74, 0x72, 0x69, 0x65, 0x76,
	0x61, 0x6c, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68,
	0x56, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x52, 0x73, 0x70, 0x12, 0x7e, 0x0a, 0x14, 0x53, 0x65, 0x61,
	0x72, 0x63, 0x68, 0x4d, 0x75, 0x6c, 0x74, 0x69, 0x4b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67,
	0x65, 0x12, 0x36, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74,
	0x5f, 0x72, 0x65, 0x74, 0x72, 0x69, 0x65, 0x76, 0x61, 0x6c, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65,
	0x72, 0x2e, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x4d, 0x75, 0x6c, 0x74, 0x69, 0x4b, 0x6e, 0x6f,
	0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x52, 0x65, 0x71, 0x1a, 0x2e, 0x2e, 0x74, 0x72, 0x70, 0x63,
	0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x72, 0x65, 0x74, 0x72, 0x69, 0x65, 0x76,
	0x61, 0x6c, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68,
	0x56, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x52, 0x73, 0x70, 0x12, 0x7d, 0x0a, 0x11, 0x44, 0x69, 0x72,
	0x65, 0x63, 0x74, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x49, 0x6e, 0x64, 0x65, 0x78, 0x12, 0x33,
	0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x72, 0x65,
	0x74, 0x72, 0x69, 0x65, 0x76, 0x61, 0x6c, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x44,
	0x69, 0x72, 0x65, 0x63, 0x74, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x49, 0x6e, 0x64, 0x65, 0x78,
	0x52, 0x65, 0x71, 0x1a, 0x33, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62,
	0x6f, 0x74, 0x5f, 0x72, 0x65, 0x74, 0x72, 0x69, 0x65, 0x76, 0x61, 0x6c, 0x5f, 0x73, 0x65, 0x72,
	0x76, 0x65, 0x72, 0x2e, 0x44, 0x69, 0x72, 0x65, 0x63, 0x74, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65,
	0x49, 0x6e, 0x64, 0x65, 0x78, 0x52, 0x73, 0x70, 0x12, 0x7d, 0x0a, 0x11, 0x44, 0x69, 0x72, 0x65,
	0x63, 0x74, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x49, 0x6e, 0x64, 0x65, 0x78, 0x12, 0x33, 0x2e,
	0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x72, 0x65, 0x74,
	0x72, 0x69, 0x65, 0x76, 0x61, 0x6c, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x44, 0x69,
	0x72, 0x65, 0x63, 0x74, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x49, 0x6e, 0x64, 0x65, 0x78, 0x52,
	0x65, 0x71, 0x1a, 0x33, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f,
	0x74, 0x5f, 0x72, 0x65, 0x74, 0x72, 0x69, 0x65, 0x76, 0x61, 0x6c, 0x5f, 0x73, 0x65, 0x72, 0x76,
	0x65, 0x72, 0x2e, 0x44, 0x69, 0x72, 0x65, 0x63, 0x74, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x49,
	0x6e, 0x64, 0x65, 0x78, 0x52, 0x73, 0x70, 0x12, 0x77, 0x0a, 0x0f, 0x44, 0x69, 0x72, 0x65, 0x63,
	0x74, 0x41, 0x64, 0x64, 0x56, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x12, 0x31, 0x2e, 0x74, 0x72, 0x70,
	0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x72, 0x65, 0x74, 0x72, 0x69, 0x65,
	0x76, 0x61, 0x6c, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x44, 0x69, 0x72, 0x65, 0x63,
	0x74, 0x41, 0x64, 0x64, 0x56, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x52, 0x65, 0x71, 0x1a, 0x31, 0x2e,
	0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x72, 0x65, 0x74,
	0x72, 0x69, 0x65, 0x76, 0x61, 0x6c, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x44, 0x69,
	0x72, 0x65, 0x63, 0x74, 0x41, 0x64, 0x64, 0x56, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x52, 0x73, 0x70,
	0x12, 0x80, 0x01, 0x0a, 0x12, 0x44, 0x69, 0x72, 0x65, 0x63, 0x74, 0x44, 0x65, 0x6c, 0x65, 0x74,
	0x65, 0x56, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x12, 0x34, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b,
	0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x72, 0x65, 0x74, 0x72, 0x69, 0x65, 0x76, 0x61, 0x6c,
	0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x44, 0x69, 0x72, 0x65, 0x63, 0x74, 0x44, 0x65,
	0x6c, 0x65, 0x74, 0x65, 0x56, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x52, 0x65, 0x71, 0x1a, 0x34, 0x2e,
	0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x72, 0x65, 0x74,
	0x72, 0x69, 0x65, 0x76, 0x61, 0x6c, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x44, 0x69,
	0x72, 0x65, 0x63, 0x74, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x56, 0x65, 0x63, 0x74, 0x6f, 0x72,
	0x52, 0x73, 0x70, 0x12, 0x80, 0x01, 0x0a, 0x12, 0x44, 0x69, 0x72, 0x65, 0x63, 0x74, 0x55, 0x70,
	0x64, 0x61, 0x74, 0x65, 0x56, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x12, 0x34, 0x2e, 0x74, 0x72, 0x70,
	0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x72, 0x65, 0x74, 0x72, 0x69, 0x65,
	0x76, 0x61, 0x6c, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x44, 0x69, 0x72, 0x65, 0x63,
	0x74, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x56, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x52, 0x65, 0x71,
	0x1a, 0x34, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f,
	0x72, 0x65, 0x74, 0x72, 0x69, 0x65, 0x76, 0x61, 0x6c, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72,
	0x2e, 0x44, 0x69, 0x72, 0x65, 0x63, 0x74, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x56, 0x65, 0x63,
	0x74, 0x6f, 0x72, 0x52, 0x73, 0x70, 0x12, 0x80, 0x01, 0x0a, 0x12, 0x44, 0x69, 0x72, 0x65, 0x63,
	0x74, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x56, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x12, 0x34, 0x2e,
	0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x72, 0x65, 0x74,
	0x72, 0x69, 0x65, 0x76, 0x61, 0x6c, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x44, 0x69,
	0x72, 0x65, 0x63, 0x74, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x56, 0x65, 0x63, 0x74, 0x6f, 0x72,
	0x52, 0x65, 0x71, 0x1a, 0x34, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62,
	0x6f, 0x74, 0x5f, 0x72, 0x65, 0x74, 0x72, 0x69, 0x65, 0x76, 0x61, 0x6c, 0x5f, 0x73, 0x65, 0x72,
	0x76, 0x65, 0x72, 0x2e, 0x44, 0x69, 0x72, 0x65, 0x63, 0x74, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68,
	0x56, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x52, 0x73, 0x70, 0x12, 0x7d, 0x0a, 0x11, 0x41, 0x64, 0x64,
	0x42, 0x69, 0x67, 0x44, 0x61, 0x74, 0x61, 0x45, 0x6c, 0x61, 0x73, 0x74, 0x69, 0x63, 0x12, 0x33,
	0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x72, 0x65,
	0x74, 0x72, 0x69, 0x65, 0x76, 0x61, 0x6c, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x41,
	0x64, 0x64, 0x42, 0x69, 0x67, 0x44, 0x61, 0x74, 0x61, 0x45, 0x6c, 0x61, 0x73, 0x74, 0x69, 0x63,
	0x52, 0x65, 0x71, 0x1a, 0x33, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62,
	0x6f, 0x74, 0x5f, 0x72, 0x65, 0x74, 0x72, 0x69, 0x65, 0x76, 0x61, 0x6c, 0x5f, 0x73, 0x65, 0x72,
	0x76, 0x65, 0x72, 0x2e, 0x41, 0x64, 0x64, 0x42, 0x69, 0x67, 0x44, 0x61, 0x74, 0x61, 0x45, 0x6c,
	0x61, 0x73, 0x74, 0x69, 0x63, 0x52, 0x73, 0x70, 0x12, 0x86, 0x01, 0x0a, 0x14, 0x44, 0x65, 0x6c,
	0x65, 0x74, 0x65, 0x42, 0x69, 0x67, 0x44, 0x61, 0x74, 0x61, 0x45, 0x6c, 0x61, 0x73, 0x74, 0x69,
	0x63, 0x12, 0x36, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74,
	0x5f, 0x72, 0x65, 0x74, 0x72, 0x69, 0x65, 0x76, 0x61, 0x6c, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65,
	0x72, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x42, 0x69, 0x67, 0x44, 0x61, 0x74, 0x61, 0x45,
	0x6c, 0x61, 0x73, 0x74, 0x69, 0x63, 0x52, 0x65, 0x71, 0x1a, 0x36, 0x2e, 0x74, 0x72, 0x70, 0x63,
	0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x72, 0x65, 0x74, 0x72, 0x69, 0x65, 0x76,
	0x61, 0x6c, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65,
	0x42, 0x69, 0x67, 0x44, 0x61, 0x74, 0x61, 0x45, 0x6c, 0x61, 0x73, 0x74, 0x69, 0x63, 0x52, 0x73,
	0x70, 0x12, 0x89, 0x01, 0x0a, 0x15, 0x52, 0x65, 0x63, 0x6f, 0x76, 0x65, 0x72, 0x42, 0x69, 0x67,
	0x44, 0x61, 0x74, 0x61, 0x45, 0x6c, 0x61, 0x73, 0x74, 0x69, 0x63, 0x12, 0x37, 0x2e, 0x74, 0x72,
	0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x72, 0x65, 0x74, 0x72, 0x69,
	0x65, 0x76, 0x61, 0x6c, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x52, 0x65, 0x63, 0x6f,
	0x76, 0x65, 0x72, 0x42, 0x69, 0x67, 0x44, 0x61, 0x74, 0x61, 0x45, 0x6c, 0x61, 0x73, 0x74, 0x69,
	0x63, 0x52, 0x65, 0x71, 0x1a, 0x37, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e,
	0x62, 0x6f, 0x74, 0x5f, 0x72, 0x65, 0x74, 0x72, 0x69, 0x65, 0x76, 0x61, 0x6c, 0x5f, 0x73, 0x65,
	0x72, 0x76, 0x65, 0x72, 0x2e, 0x52, 0x65, 0x63, 0x6f, 0x76, 0x65, 0x72, 0x42, 0x69, 0x67, 0x44,
	0x61, 0x74, 0x61, 0x45, 0x6c, 0x61, 0x73, 0x74, 0x69, 0x63, 0x52, 0x73, 0x70, 0x12, 0xae, 0x01,
	0x0a, 0x21, 0x42, 0x61, 0x74, 0x63, 0x68, 0x47, 0x65, 0x74, 0x42, 0x69, 0x67, 0x44, 0x61, 0x74,
	0x61, 0x45, 0x53, 0x42, 0x79, 0x52, 0x6f, 0x62, 0x6f, 0x74, 0x42, 0x69, 0x67, 0x44, 0x61, 0x74,
	0x61, 0x49, 0x44, 0x12, 0x43, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62,
	0x6f, 0x74, 0x5f, 0x72, 0x65, 0x74, 0x72, 0x69, 0x65, 0x76, 0x61, 0x6c, 0x5f, 0x73, 0x65, 0x72,
	0x76, 0x65, 0x72, 0x2e, 0x42, 0x61, 0x74, 0x63, 0x68, 0x47, 0x65, 0x74, 0x42, 0x69, 0x67, 0x44,
	0x61, 0x74, 0x61, 0x45, 0x53, 0x42, 0x79, 0x52, 0x6f, 0x62, 0x6f, 0x74, 0x42, 0x69, 0x67, 0x44,
	0x61, 0x74, 0x61, 0x49, 0x44, 0x52, 0x65, 0x71, 0x1a, 0x44, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e,
	0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x72, 0x65, 0x74, 0x72, 0x69, 0x65, 0x76, 0x61,
	0x6c, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x42, 0x61, 0x74, 0x63, 0x68, 0x47, 0x65,
	0x74, 0x42, 0x69, 0x67, 0x44, 0x61, 0x74, 0x61, 0x45, 0x53, 0x42, 0x79, 0x52, 0x6f, 0x62, 0x6f,
	0x74, 0x42, 0x69, 0x67, 0x44, 0x61, 0x74, 0x61, 0x49, 0x44, 0x52, 0x65, 0x73, 0x70, 0x12, 0x6e,
	0x0a, 0x0c, 0x41, 0x64, 0x64, 0x4b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x12, 0x2e,
	0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x72, 0x65,
	0x74, 0x72, 0x69, 0x65, 0x76, 0x61, 0x6c, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x41,
	0x64, 0x64, 0x4b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x52, 0x65, 0x71, 0x1a, 0x2e,
	0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x72, 0x65,
	0x74, 0x72, 0x69, 0x65, 0x76, 0x61, 0x6c, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x41,
	0x64, 0x64, 0x4b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x52, 0x73, 0x70, 0x12, 0x7d,
	0x0a, 0x11, 0x42, 0x61, 0x74, 0x63, 0x68, 0x41, 0x64, 0x64, 0x4b, 0x6e, 0x6f, 0x77, 0x6c, 0x65,
	0x64, 0x67, 0x65, 0x12, 0x33, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62,
	0x6f, 0x74, 0x5f, 0x72, 0x65, 0x74, 0x72, 0x69, 0x65, 0x76, 0x61, 0x6c, 0x5f, 0x73, 0x65, 0x72,
	0x76, 0x65, 0x72, 0x2e, 0x42, 0x61, 0x74, 0x63, 0x68, 0x41, 0x64, 0x64, 0x4b, 0x6e, 0x6f, 0x77,
	0x6c, 0x65, 0x64, 0x67, 0x65, 0x52, 0x65, 0x71, 0x1a, 0x33, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e,
	0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x72, 0x65, 0x74, 0x72, 0x69, 0x65, 0x76, 0x61,
	0x6c, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x42, 0x61, 0x74, 0x63, 0x68, 0x41, 0x64,
	0x64, 0x4b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x52, 0x73, 0x70, 0x12, 0x77, 0x0a,
	0x0f, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x4b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65,
	0x12, 0x31, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f,
	0x72, 0x65, 0x74, 0x72, 0x69, 0x65, 0x76, 0x61, 0x6c, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72,
	0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x4b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65,
	0x52, 0x65, 0x71, 0x1a, 0x31, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62,
	0x6f, 0x74, 0x5f, 0x72, 0x65, 0x74, 0x72, 0x69, 0x65, 0x76, 0x61, 0x6c, 0x5f, 0x73, 0x65, 0x72,
	0x76, 0x65, 0x72, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x4b, 0x6e, 0x6f, 0x77, 0x6c, 0x65,
	0x64, 0x67, 0x65, 0x52, 0x73, 0x70, 0x12, 0x86, 0x01, 0x0a, 0x14, 0x42, 0x61, 0x74, 0x63, 0x68,
	0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x4b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x12,
	0x36, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x72,
	0x65, 0x74, 0x72, 0x69, 0x65, 0x76, 0x61, 0x6c, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e,
	0x42, 0x61, 0x74, 0x63, 0x68, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x4b, 0x6e, 0x6f, 0x77, 0x6c,
	0x65, 0x64, 0x67, 0x65, 0x52, 0x65, 0x71, 0x1a, 0x36, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b,
	0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x72, 0x65, 0x74, 0x72, 0x69, 0x65, 0x76, 0x61, 0x6c,
	0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x42, 0x61, 0x74, 0x63, 0x68, 0x44, 0x65, 0x6c,
	0x65, 0x74, 0x65, 0x4b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x52, 0x73, 0x70, 0x12,
	0x77, 0x0a, 0x0f, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x4b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64,
	0x67, 0x65, 0x12, 0x31, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f,
	0x74, 0x5f, 0x72, 0x65, 0x74, 0x72, 0x69, 0x65, 0x76, 0x61, 0x6c, 0x5f, 0x73, 0x65, 0x72, 0x76,
	0x65, 0x72, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x4b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64,
	0x67, 0x65, 0x52, 0x65, 0x71, 0x1a, 0x31, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50,
	0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x72, 0x65, 0x74, 0x72, 0x69, 0x65, 0x76, 0x61, 0x6c, 0x5f, 0x73,
	0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x4b, 0x6e, 0x6f, 0x77,
	0x6c, 0x65, 0x64, 0x67, 0x65, 0x52, 0x73, 0x70, 0x12, 0x6b, 0x0a, 0x0b, 0x41, 0x64, 0x64, 0x54,
	0x65, 0x78, 0x74, 0x32, 0x53, 0x51, 0x4c, 0x12, 0x2d, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b,
	0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x72, 0x65, 0x74, 0x72, 0x69, 0x65, 0x76, 0x61, 0x6c,
	0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x41, 0x64, 0x64, 0x54, 0x65, 0x78, 0x74, 0x32,
	0x53, 0x51, 0x4c, 0x52, 0x65, 0x71, 0x1a, 0x2d, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45,
	0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x72, 0x65, 0x74, 0x72, 0x69, 0x65, 0x76, 0x61, 0x6c, 0x5f,
	0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x41, 0x64, 0x64, 0x54, 0x65, 0x78, 0x74, 0x32, 0x53,
	0x51, 0x4c, 0x52, 0x73, 0x70, 0x12, 0x74, 0x0a, 0x0e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x54,
	0x65, 0x78, 0x74, 0x32, 0x53, 0x51, 0x4c, 0x12, 0x30, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b,
	0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x72, 0x65, 0x74, 0x72, 0x69, 0x65, 0x76, 0x61, 0x6c,
	0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x54, 0x65,
	0x78, 0x74, 0x32, 0x53, 0x51, 0x4c, 0x52, 0x65, 0x71, 0x1a, 0x30, 0x2e, 0x74, 0x72, 0x70, 0x63,
	0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x72, 0x65, 0x74, 0x72, 0x69, 0x65, 0x76,
	0x61, 0x6c, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65,
	0x54, 0x65, 0x78, 0x74, 0x32, 0x53, 0x51, 0x4c, 0x52, 0x73, 0x70, 0x12, 0x86, 0x01, 0x0a, 0x14,
	0x41, 0x64, 0x64, 0x52, 0x65, 0x61, 0x6c, 0x54, 0x69, 0x6d, 0x65, 0x4b, 0x6e, 0x6f, 0x77, 0x6c,
	0x65, 0x64, 0x67, 0x65, 0x12, 0x36, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e,
	0x62, 0x6f, 0x74, 0x5f, 0x72, 0x65, 0x74, 0x72, 0x69, 0x65, 0x76, 0x61, 0x6c, 0x5f, 0x73, 0x65,
	0x72, 0x76, 0x65, 0x72, 0x2e, 0x41, 0x64, 0x64, 0x52, 0x65, 0x61, 0x6c, 0x54, 0x69, 0x6d, 0x65,
	0x4b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x52, 0x65, 0x71, 0x1a, 0x36, 0x2e, 0x74,
	0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x72, 0x65, 0x74, 0x72,
	0x69, 0x65, 0x76, 0x61, 0x6c, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x41, 0x64, 0x64,
	0x52, 0x65, 0x61, 0x6c, 0x54, 0x69, 0x6d, 0x65, 0x4b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67,
	0x65, 0x52, 0x73, 0x70, 0x12, 0x8f, 0x01, 0x0a, 0x17, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x52,
	0x65, 0x61, 0x6c, 0x54, 0x69, 0x6d, 0x65, 0x4b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65,
	0x12, 0x39, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f,
	0x72, 0x65, 0x74, 0x72, 0x69, 0x65, 0x76, 0x61, 0x6c, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72,
	0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x52, 0x65, 0x61, 0x6c, 0x54, 0x69, 0x6d, 0x65, 0x4b,
	0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x52, 0x65, 0x71, 0x1a, 0x39, 0x2e, 0x74, 0x72,
	0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x72, 0x65, 0x74, 0x72, 0x69,
	0x65, 0x76, 0x61, 0x6c, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x44, 0x65, 0x6c, 0x65,
	0x74, 0x65, 0x52, 0x65, 0x61, 0x6c, 0x54, 0x69, 0x6d, 0x65, 0x4b, 0x6e, 0x6f, 0x77, 0x6c, 0x65,
	0x64, 0x67, 0x65, 0x52, 0x73, 0x70, 0x12, 0x7d, 0x0a, 0x11, 0x52, 0x65, 0x74, 0x72, 0x69, 0x65,
	0x76, 0x61, 0x6c, 0x52, 0x65, 0x61, 0x6c, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x33, 0x2e, 0x74, 0x72,
	0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x72, 0x65, 0x74, 0x72, 0x69,
	0x65, 0x76, 0x61, 0x6c, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x52, 0x65, 0x74, 0x72,
	0x69, 0x65, 0x76, 0x61, 0x6c, 0x52, 0x65, 0x61, 0x6c, 0x54, 0x69, 0x6d, 0x65, 0x52, 0x65, 0x71,
	0x1a, 0x33, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f,
	0x72, 0x65, 0x74, 0x72, 0x69, 0x65, 0x76, 0x61, 0x6c, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72,
	0x2e, 0x52, 0x65, 0x74, 0x72, 0x69, 0x65, 0x76, 0x61, 0x6c, 0x52, 0x65, 0x61, 0x6c, 0x54, 0x69,
	0x6d, 0x65, 0x52, 0x73, 0x70, 0x12, 0x71, 0x0a, 0x0d, 0x41, 0x64, 0x64, 0x44, 0x42, 0x54, 0x65,
	0x78, 0x74, 0x32, 0x53, 0x51, 0x4c, 0x12, 0x2f, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45,
	0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x72, 0x65, 0x74, 0x72, 0x69, 0x65, 0x76, 0x61, 0x6c, 0x5f,
	0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x41, 0x64, 0x64, 0x44, 0x42, 0x54, 0x65, 0x78, 0x74,
	0x32, 0x53, 0x51, 0x4c, 0x52, 0x65, 0x71, 0x1a, 0x2f, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b,
	0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x72, 0x65, 0x74, 0x72, 0x69, 0x65, 0x76, 0x61, 0x6c,
	0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x41, 0x64, 0x64, 0x44, 0x42, 0x54, 0x65, 0x78,
	0x74, 0x32, 0x53, 0x51, 0x4c, 0x52, 0x73, 0x70, 0x12, 0x7a, 0x0a, 0x10, 0x44, 0x65, 0x6c, 0x65,
	0x74, 0x65, 0x44, 0x42, 0x54, 0x65, 0x78, 0x74, 0x32, 0x53, 0x51, 0x4c, 0x12, 0x32, 0x2e, 0x74,
	0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x72, 0x65, 0x74, 0x72,
	0x69, 0x65, 0x76, 0x61, 0x6c, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x44, 0x65, 0x6c,
	0x65, 0x74, 0x65, 0x44, 0x42, 0x54, 0x65, 0x78, 0x74, 0x32, 0x53, 0x51, 0x4c, 0x52, 0x65, 0x71,
	0x1a, 0x32, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f,
	0x72, 0x65, 0x74, 0x72, 0x69, 0x65, 0x76, 0x61, 0x6c, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72,
	0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x44, 0x42, 0x54, 0x65, 0x78, 0x74, 0x32, 0x53, 0x51,
	0x4c, 0x52, 0x73, 0x70, 0x12, 0x71, 0x0a, 0x12, 0x41, 0x64, 0x64, 0x41, 0x6e, 0x64, 0x55, 0x70,
	0x64, 0x61, 0x74, 0x65, 0x45, 0x53, 0x44, 0x61, 0x74, 0x61, 0x12, 0x2b, 0x2e, 0x74, 0x72, 0x70,
	0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x72, 0x65, 0x74, 0x72, 0x69, 0x65,
	0x76, 0x61, 0x6c, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x41, 0x64, 0x64, 0x45, 0x53,
	0x44, 0x61, 0x74, 0x61, 0x52, 0x65, 0x71, 0x1a, 0x2e, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b,
	0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x72, 0x65, 0x74, 0x72, 0x69, 0x65, 0x76, 0x61, 0x6c,
	0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x41, 0x64, 0x64, 0x45, 0x53, 0x44, 0x61, 0x74,
	0x61, 0x52, 0x65, 0x71, 0x52, 0x73, 0x70, 0x12, 0x6e, 0x0a, 0x0c, 0x44, 0x65, 0x6c, 0x65, 0x74,
	0x65, 0x45, 0x53, 0x44, 0x61, 0x74, 0x61, 0x12, 0x2e, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b,
	0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x72, 0x65, 0x74, 0x72, 0x69, 0x65, 0x76, 0x61, 0x6c,
	0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x45, 0x53,
	0x44, 0x61, 0x74, 0x61, 0x52, 0x65, 0x71, 0x1a, 0x2e, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b,
	0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x72, 0x65, 0x74, 0x72, 0x69, 0x65, 0x76, 0x61, 0x6c,
	0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x45, 0x53,
	0x44, 0x61, 0x74, 0x61, 0x52, 0x73, 0x70, 0x12, 0x62, 0x0a, 0x08, 0x53, 0x65, 0x61, 0x72, 0x63,
	0x68, 0x45, 0x53, 0x12, 0x2a, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62,
	0x6f, 0x74, 0x5f, 0x72, 0x65, 0x74, 0x72, 0x69, 0x65, 0x76, 0x61, 0x6c, 0x5f, 0x73, 0x65, 0x72,
	0x76, 0x65, 0x72, 0x2e, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x45, 0x53, 0x52, 0x65, 0x71, 0x1a,
	0x2a, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x72,
	0x65, 0x74, 0x72, 0x69, 0x65, 0x76, 0x61, 0x6c, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e,
	0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x45, 0x53, 0x52, 0x73, 0x70, 0x42, 0x4a, 0x5a, 0x48, 0x67,
	0x69, 0x74, 0x2e, 0x77, 0x6f, 0x61, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x64, 0x69, 0x61, 0x6c, 0x6f,
	0x67, 0x75, 0x65, 0x2d, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x2f, 0x6c, 0x6b, 0x65,
	0x5f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x70, 0x62, 0x2d, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x63,
	0x6f, 0x6c, 0x2f, 0x62, 0x6f, 0x74, 0x5f, 0x72, 0x65, 0x74, 0x72, 0x69, 0x65, 0x76, 0x61, 0x6c,
	0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_retrieval_proto_rawDescOnce sync.Once
	file_retrieval_proto_rawDescData = file_retrieval_proto_rawDesc
)

func file_retrieval_proto_rawDescGZIP() []byte {
	file_retrieval_proto_rawDescOnce.Do(func() {
		file_retrieval_proto_rawDescData = protoimpl.X.CompressGZIP(file_retrieval_proto_rawDescData)
	})
	return file_retrieval_proto_rawDescData
}

var file_retrieval_proto_enumTypes = make([]protoimpl.EnumInfo, 11)
var file_retrieval_proto_msgTypes = make([]protoimpl.MessageInfo, 122)
var file_retrieval_proto_goTypes = []interface{}{
	(ESDataType)(0),                                     // 0: trpc.KEP.bot_retrieval_server.ESDataType
	(DBType)(0),                                         // 1: trpc.KEP.bot_retrieval_server.DBType
	(SearchStrategyTypeEnum)(0),                         // 2: trpc.KEP.bot_retrieval_server.SearchStrategyTypeEnum
	(KnowledgeType)(0),                                  // 3: trpc.KEP.bot_retrieval_server.KnowledgeType
	(RetrievalResultType)(0),                            // 4: trpc.KEP.bot_retrieval_server.RetrievalResultType
	(CellDataType)(0),                                   // 5: trpc.KEP.bot_retrieval_server.CellDataType
	(LabelExpression_LogicOpr)(0),                       // 6: trpc.KEP.bot_retrieval_server.LabelExpression.LogicOpr
	(LabelExpression_Condition_DataType)(0),             // 7: trpc.KEP.bot_retrieval_server.LabelExpression.Condition.DataType
	(VectorLabelExpr_Operator)(0),                       // 8: trpc.KEP.bot_retrieval_server.VectorLabelExpr.Operator
	(VectorLabelExpr_Type)(0),                           // 9: trpc.KEP.bot_retrieval_server.VectorLabelExpr.Type
	(Text2SQLMeta_Header_HeaderType)(0),                 // 10: trpc.KEP.bot_retrieval_server.Text2SQLMeta.Header.HeaderType
	(*SearchESReq)(nil),                                 // 11: trpc.KEP.bot_retrieval_server.SearchESReq
	(*SearchESRsp)(nil),                                 // 12: trpc.KEP.bot_retrieval_server.SearchESRsp
	(*DeleteESDataReq)(nil),                             // 13: trpc.KEP.bot_retrieval_server.DeleteESDataReq
	(*ESDataIDType)(nil),                                // 14: trpc.KEP.bot_retrieval_server.ESDataIDType
	(*DeleteESDataRsp)(nil),                             // 15: trpc.KEP.bot_retrieval_server.DeleteESDataRsp
	(*AddESDataReq)(nil),                                // 16: trpc.KEP.bot_retrieval_server.AddESDataReq
	(*ESData)(nil),                                      // 17: trpc.KEP.bot_retrieval_server.ESData
	(*AddESDataReqRsp)(nil),                             // 18: trpc.KEP.bot_retrieval_server.AddESDataReqRsp
	(*AddDBText2SQLReq)(nil),                            // 19: trpc.KEP.bot_retrieval_server.AddDBText2SQLReq
	(*DBRowData)(nil),                                   // 20: trpc.KEP.bot_retrieval_server.DBRowData
	(*DBCell)(nil),                                      // 21: trpc.KEP.bot_retrieval_server.DBCell
	(*AddDBText2SQLRsp)(nil),                            // 22: trpc.KEP.bot_retrieval_server.AddDBText2SQLRsp
	(*DeleteDBText2SQLReq)(nil),                         // 23: trpc.KEP.bot_retrieval_server.DeleteDBText2SQLReq
	(*DeleteDBText2SQLRsp)(nil),                         // 24: trpc.KEP.bot_retrieval_server.DeleteDBText2SQLRsp
	(*SearchStrategy)(nil),                              // 25: trpc.KEP.bot_retrieval_server.SearchStrategy
	(*LabelExpression)(nil),                             // 26: trpc.KEP.bot_retrieval_server.LabelExpression
	(*VectorLabel)(nil),                                 // 27: trpc.KEP.bot_retrieval_server.VectorLabel
	(*SearchVectorLabel)(nil),                           // 28: trpc.KEP.bot_retrieval_server.SearchVectorLabel
	(*VectorLabelExpr)(nil),                             // 29: trpc.KEP.bot_retrieval_server.VectorLabelExpr
	(*RetrievalExtra)(nil),                              // 30: trpc.KEP.bot_retrieval_server.RetrievalExtra
	(*SearchReq)(nil),                                   // 31: trpc.KEP.bot_retrieval_server.SearchReq
	(*SearchRsp)(nil),                                   // 32: trpc.KEP.bot_retrieval_server.SearchRsp
	(*PublishReq)(nil),                                  // 33: trpc.KEP.bot_retrieval_server.PublishReq
	(*PublishRsp)(nil),                                  // 34: trpc.KEP.bot_retrieval_server.PublishRsp
	(*SimilarityReq)(nil),                               // 35: trpc.KEP.bot_retrieval_server.SimilarityReq
	(*SimilarityRsp)(nil),                               // 36: trpc.KEP.bot_retrieval_server.SimilarityRsp
	(*IndexRebuildReq)(nil),                             // 37: trpc.KEP.bot_retrieval_server.IndexRebuildReq
	(*IndexRebuildRsp)(nil),                             // 38: trpc.KEP.bot_retrieval_server.IndexRebuildRsp
	(*ContinueTerminatedTaskReq)(nil),                   // 39: trpc.KEP.bot_retrieval_server.ContinueTerminatedTaskReq
	(*ContinueTerminatedTaskRsp)(nil),                   // 40: trpc.KEP.bot_retrieval_server.ContinueTerminatedTaskRsp
	(*UpgradeEmbeddingReq)(nil),                         // 41: trpc.KEP.bot_retrieval_server.UpgradeEmbeddingReq
	(*UpgradeEmbeddingRsp)(nil),                         // 42: trpc.KEP.bot_retrieval_server.UpgradeEmbeddingRsp
	(*CheckVersionReq)(nil),                             // 43: trpc.KEP.bot_retrieval_server.CheckVersionReq
	(*CheckVersionRsp)(nil),                             // 44: trpc.KEP.bot_retrieval_server.CheckVersionRsp
	(*CreateIndexReq)(nil),                              // 45: trpc.KEP.bot_retrieval_server.CreateIndexReq
	(*CreateIndexRsp)(nil),                              // 46: trpc.KEP.bot_retrieval_server.CreateIndexRsp
	(*DeleteIndexReq)(nil),                              // 47: trpc.KEP.bot_retrieval_server.DeleteIndexReq
	(*DeleteIndexRsp)(nil),                              // 48: trpc.KEP.bot_retrieval_server.DeleteIndexRsp
	(*AddVectorReq)(nil),                                // 49: trpc.KEP.bot_retrieval_server.AddVectorReq
	(*AddVectorRsp)(nil),                                // 50: trpc.KEP.bot_retrieval_server.AddVectorRsp
	(*DeleteVectorReq)(nil),                             // 51: trpc.KEP.bot_retrieval_server.DeleteVectorReq
	(*DeleteVectorRsp)(nil),                             // 52: trpc.KEP.bot_retrieval_server.DeleteVectorRsp
	(*UpdateVectorReq)(nil),                             // 53: trpc.KEP.bot_retrieval_server.UpdateVectorReq
	(*UpdateVectorRsp)(nil),                             // 54: trpc.KEP.bot_retrieval_server.UpdateVectorRsp
	(*SearchData)(nil),                                  // 55: trpc.KEP.bot_retrieval_server.SearchData
	(*SearchFilter)(nil),                                // 56: trpc.KEP.bot_retrieval_server.SearchFilter
	(*Rerank)(nil),                                      // 57: trpc.KEP.bot_retrieval_server.Rerank
	(*SearchMultiKnowledgeReq)(nil),                     // 58: trpc.KEP.bot_retrieval_server.SearchMultiKnowledgeReq
	(*SearchVectorReq)(nil),                             // 59: trpc.KEP.bot_retrieval_server.SearchVectorReq
	(*SearchVectorRsp)(nil),                             // 60: trpc.KEP.bot_retrieval_server.SearchVectorRsp
	(*DirectCreateIndexReq)(nil),                        // 61: trpc.KEP.bot_retrieval_server.DirectCreateIndexReq
	(*DirectCreateIndexRsp)(nil),                        // 62: trpc.KEP.bot_retrieval_server.DirectCreateIndexRsp
	(*DirectDeleteIndexReq)(nil),                        // 63: trpc.KEP.bot_retrieval_server.DirectDeleteIndexReq
	(*DirectDeleteIndexRsp)(nil),                        // 64: trpc.KEP.bot_retrieval_server.DirectDeleteIndexRsp
	(*DirectAddVectorReq)(nil),                          // 65: trpc.KEP.bot_retrieval_server.DirectAddVectorReq
	(*DirectAddVectorRsp)(nil),                          // 66: trpc.KEP.bot_retrieval_server.DirectAddVectorRsp
	(*DirectDeleteVectorReq)(nil),                       // 67: trpc.KEP.bot_retrieval_server.DirectDeleteVectorReq
	(*DirectDeleteVectorRsp)(nil),                       // 68: trpc.KEP.bot_retrieval_server.DirectDeleteVectorRsp
	(*DirectUpdateVectorReq)(nil),                       // 69: trpc.KEP.bot_retrieval_server.DirectUpdateVectorReq
	(*DirectUpdateVectorRsp)(nil),                       // 70: trpc.KEP.bot_retrieval_server.DirectUpdateVectorRsp
	(*DirectSearchVectorReq)(nil),                       // 71: trpc.KEP.bot_retrieval_server.DirectSearchVectorReq
	(*DirectSearchVectorRsp)(nil),                       // 72: trpc.KEP.bot_retrieval_server.DirectSearchVectorRsp
	(*AddBigDataElasticReq)(nil),                        // 73: trpc.KEP.bot_retrieval_server.AddBigDataElasticReq
	(*BigData)(nil),                                     // 74: trpc.KEP.bot_retrieval_server.BigData
	(*BatchGetBigDataESByRobotBigDataIDReq)(nil),        // 75: trpc.KEP.bot_retrieval_server.BatchGetBigDataESByRobotBigDataIDReq
	(*BatchGetBigDataESByRobotBigDataIDResp)(nil),       // 76: trpc.KEP.bot_retrieval_server.BatchGetBigDataESByRobotBigDataIDResp
	(*AddBigDataElasticRsp)(nil),                        // 77: trpc.KEP.bot_retrieval_server.AddBigDataElasticRsp
	(*DeleteBigDataElasticReq)(nil),                     // 78: trpc.KEP.bot_retrieval_server.DeleteBigDataElasticReq
	(*DeleteBigDataElasticRsp)(nil),                     // 79: trpc.KEP.bot_retrieval_server.DeleteBigDataElasticRsp
	(*RecoverBigDataElasticReq)(nil),                    // 80: trpc.KEP.bot_retrieval_server.RecoverBigDataElasticReq
	(*RecoverBigDataElasticRsp)(nil),                    // 81: trpc.KEP.bot_retrieval_server.RecoverBigDataElasticRsp
	(*AddKnowledgeReq)(nil),                             // 82: trpc.KEP.bot_retrieval_server.AddKnowledgeReq
	(*AddKnowledgeRsp)(nil),                             // 83: trpc.KEP.bot_retrieval_server.AddKnowledgeRsp
	(*BatchAddKnowledgeReq)(nil),                        // 84: trpc.KEP.bot_retrieval_server.BatchAddKnowledgeReq
	(*BatchAddKnowledgeRsp)(nil),                        // 85: trpc.KEP.bot_retrieval_server.BatchAddKnowledgeRsp
	(*DeleteKnowledgeReq)(nil),                          // 86: trpc.KEP.bot_retrieval_server.DeleteKnowledgeReq
	(*DeleteKnowledgeRsp)(nil),                          // 87: trpc.KEP.bot_retrieval_server.DeleteKnowledgeRsp
	(*BatchDeleteKnowledgeReq)(nil),                     // 88: trpc.KEP.bot_retrieval_server.BatchDeleteKnowledgeReq
	(*BatchDeleteKnowledgeRsp)(nil),                     // 89: trpc.KEP.bot_retrieval_server.BatchDeleteKnowledgeRsp
	(*BatchDeleteAllKnowledgeProdReq)(nil),              // 90: trpc.KEP.bot_retrieval_server.BatchDeleteAllKnowledgeProdReq
	(*BatchDeleteAllKnowledgeProdRsp)(nil),              // 91: trpc.KEP.bot_retrieval_server.BatchDeleteAllKnowledgeProdRsp
	(*UpdateKnowledgeReq)(nil),                          // 92: trpc.KEP.bot_retrieval_server.UpdateKnowledgeReq
	(*UpdateKnowledgeRsp)(nil),                          // 93: trpc.KEP.bot_retrieval_server.UpdateKnowledgeRsp
	(*Cell)(nil),                                        // 94: trpc.KEP.bot_retrieval_server.Cell
	(*Row)(nil),                                         // 95: trpc.KEP.bot_retrieval_server.Row
	(*Text2SQLMeta)(nil),                                // 96: trpc.KEP.bot_retrieval_server.Text2SQLMeta
	(*Text2SQLRowData)(nil),                             // 97: trpc.KEP.bot_retrieval_server.Text2SQLRowData
	(*AddText2SQLReq)(nil),                              // 98: trpc.KEP.bot_retrieval_server.AddText2SQLReq
	(*AddText2SQLRsp)(nil),                              // 99: trpc.KEP.bot_retrieval_server.AddText2SQLRsp
	(*DeleteText2SQLReq)(nil),                           // 100: trpc.KEP.bot_retrieval_server.DeleteText2SQLReq
	(*DeleteText2SQLRsp)(nil),                           // 101: trpc.KEP.bot_retrieval_server.DeleteText2SQLRsp
	(*KnowledgeData)(nil),                               // 102: trpc.KEP.bot_retrieval_server.KnowledgeData
	(*AddRealTimeKnowledgeReq)(nil),                     // 103: trpc.KEP.bot_retrieval_server.AddRealTimeKnowledgeReq
	(*AddRealTimeKnowledgeRsp)(nil),                     // 104: trpc.KEP.bot_retrieval_server.AddRealTimeKnowledgeRsp
	(*KnowledgeIDType)(nil),                             // 105: trpc.KEP.bot_retrieval_server.KnowledgeIDType
	(*DeleteRealTimeKnowledgeReq)(nil),                  // 106: trpc.KEP.bot_retrieval_server.DeleteRealTimeKnowledgeReq
	(*DeleteRealTimeKnowledgeRsp)(nil),                  // 107: trpc.KEP.bot_retrieval_server.DeleteRealTimeKnowledgeRsp
	(*RetrievalRealTimeReq)(nil),                        // 108: trpc.KEP.bot_retrieval_server.RetrievalRealTimeReq
	(*RetrievalRealTimeRsp)(nil),                        // 109: trpc.KEP.bot_retrieval_server.RetrievalRealTimeRsp
	(*ClearAppVectorResourceReq)(nil),                   // 110: trpc.KEP.bot_retrieval_server.ClearAppVectorResourceReq
	(*ClearAppVectorResourceRsp)(nil),                   // 111: trpc.KEP.bot_retrieval_server.ClearAppVectorResourceRsp
	(*LabelExpression_Condition)(nil),                   // 112: trpc.KEP.bot_retrieval_server.LabelExpression.Condition
	(*SearchReq_Filter)(nil),                            // 113: trpc.KEP.bot_retrieval_server.SearchReq.Filter
	(*SearchReq_Rerank)(nil),                            // 114: trpc.KEP.bot_retrieval_server.SearchReq.Rerank
	(*SearchRsp_Doc)(nil),                               // 115: trpc.KEP.bot_retrieval_server.SearchRsp.Doc
	(*SearchRsp_Doc_SimilarQuestionExtra)(nil),          // 116: trpc.KEP.bot_retrieval_server.SearchRsp.Doc.SimilarQuestionExtra
	(*SearchRsp_Doc_Text2SQLExtra)(nil),                 // 117: trpc.KEP.bot_retrieval_server.SearchRsp.Doc.Text2SQLExtra
	(*SearchRsp_Doc_Text2SQLExtra_TableInfo)(nil),       // 118: trpc.KEP.bot_retrieval_server.SearchRsp.Doc.Text2SQLExtra.TableInfo
	(*SimilarityReq_Doc)(nil),                           // 119: trpc.KEP.bot_retrieval_server.SimilarityReq.Doc
	(*SearchVectorReq_Filter)(nil),                      // 120: trpc.KEP.bot_retrieval_server.SearchVectorReq.Filter
	(*SearchVectorReq_Rerank)(nil),                      // 121: trpc.KEP.bot_retrieval_server.SearchVectorReq.Rerank
	(*SearchVectorRsp_Doc)(nil),                         // 122: trpc.KEP.bot_retrieval_server.SearchVectorRsp.Doc
	(*SearchVectorRsp_Doc_SimilarQuestionExtra)(nil),    // 123: trpc.KEP.bot_retrieval_server.SearchVectorRsp.Doc.SimilarQuestionExtra
	(*SearchVectorRsp_Doc_Text2SQLExtra)(nil),           // 124: trpc.KEP.bot_retrieval_server.SearchVectorRsp.Doc.Text2SQLExtra
	(*SearchVectorRsp_Doc_Text2SQLExtra_TableInfo)(nil), // 125: trpc.KEP.bot_retrieval_server.SearchVectorRsp.Doc.Text2SQLExtra.TableInfo
	(*DirectSearchVectorReq_Filter)(nil),                // 126: trpc.KEP.bot_retrieval_server.DirectSearchVectorReq.Filter
	(*DirectSearchVectorReq_Rerank)(nil),                // 127: trpc.KEP.bot_retrieval_server.DirectSearchVectorReq.Rerank
	(*DirectSearchVectorRsp_Doc)(nil),                   // 128: trpc.KEP.bot_retrieval_server.DirectSearchVectorRsp.Doc
	(*Text2SQLMeta_Header)(nil),                         // 129: trpc.KEP.bot_retrieval_server.Text2SQLMeta.Header
	(*RetrievalRealTimeReq_Filter)(nil),                 // 130: trpc.KEP.bot_retrieval_server.RetrievalRealTimeReq.Filter
	(*RetrievalRealTimeReq_Rerank)(nil),                 // 131: trpc.KEP.bot_retrieval_server.RetrievalRealTimeReq.Rerank
	(*RetrievalRealTimeRsp_Doc)(nil),                    // 132: trpc.KEP.bot_retrieval_server.RetrievalRealTimeRsp.Doc
}
var file_retrieval_proto_depIdxs = []int32{
	17,  // 0: trpc.KEP.bot_retrieval_server.SearchESRsp.data:type_name -> trpc.KEP.bot_retrieval_server.ESData
	14,  // 1: trpc.KEP.bot_retrieval_server.DeleteESDataReq.delete_ids:type_name -> trpc.KEP.bot_retrieval_server.ESDataIDType
	0,   // 2: trpc.KEP.bot_retrieval_server.ESDataIDType.data_type:type_name -> trpc.KEP.bot_retrieval_server.ESDataType
	17,  // 3: trpc.KEP.bot_retrieval_server.AddESDataReq.data:type_name -> trpc.KEP.bot_retrieval_server.ESData
	0,   // 4: trpc.KEP.bot_retrieval_server.ESData.data_type:type_name -> trpc.KEP.bot_retrieval_server.ESDataType
	1,   // 5: trpc.KEP.bot_retrieval_server.AddDBText2SQLReq.db_type:type_name -> trpc.KEP.bot_retrieval_server.DBType
	20,  // 6: trpc.KEP.bot_retrieval_server.AddDBText2SQLReq.rows:type_name -> trpc.KEP.bot_retrieval_server.DBRowData
	21,  // 7: trpc.KEP.bot_retrieval_server.DBRowData.cells:type_name -> trpc.KEP.bot_retrieval_server.DBCell
	2,   // 8: trpc.KEP.bot_retrieval_server.SearchStrategy.strategy_type:type_name -> trpc.KEP.bot_retrieval_server.SearchStrategyTypeEnum
	6,   // 9: trpc.KEP.bot_retrieval_server.LabelExpression.operator:type_name -> trpc.KEP.bot_retrieval_server.LabelExpression.LogicOpr
	26,  // 10: trpc.KEP.bot_retrieval_server.LabelExpression.expressions:type_name -> trpc.KEP.bot_retrieval_server.LabelExpression
	112, // 11: trpc.KEP.bot_retrieval_server.LabelExpression.condition:type_name -> trpc.KEP.bot_retrieval_server.LabelExpression.Condition
	8,   // 12: trpc.KEP.bot_retrieval_server.VectorLabelExpr.op:type_name -> trpc.KEP.bot_retrieval_server.VectorLabelExpr.Operator
	29,  // 13: trpc.KEP.bot_retrieval_server.VectorLabelExpr.expressions:type_name -> trpc.KEP.bot_retrieval_server.VectorLabelExpr
	9,   // 14: trpc.KEP.bot_retrieval_server.VectorLabelExpr.type:type_name -> trpc.KEP.bot_retrieval_server.VectorLabelExpr.Type
	113, // 15: trpc.KEP.bot_retrieval_server.SearchReq.filters:type_name -> trpc.KEP.bot_retrieval_server.SearchReq.Filter
	114, // 16: trpc.KEP.bot_retrieval_server.SearchReq.rerank:type_name -> trpc.KEP.bot_retrieval_server.SearchReq.Rerank
	28,  // 17: trpc.KEP.bot_retrieval_server.SearchReq.labels:type_name -> trpc.KEP.bot_retrieval_server.SearchVectorLabel
	26,  // 18: trpc.KEP.bot_retrieval_server.SearchReq.label_expression:type_name -> trpc.KEP.bot_retrieval_server.LabelExpression
	25,  // 19: trpc.KEP.bot_retrieval_server.SearchReq.search_strategy:type_name -> trpc.KEP.bot_retrieval_server.SearchStrategy
	115, // 20: trpc.KEP.bot_retrieval_server.SearchRsp.docs:type_name -> trpc.KEP.bot_retrieval_server.SearchRsp.Doc
	119, // 21: trpc.KEP.bot_retrieval_server.SimilarityReq.docs:type_name -> trpc.KEP.bot_retrieval_server.SimilarityReq.Doc
	27,  // 22: trpc.KEP.bot_retrieval_server.AddVectorReq.labels:type_name -> trpc.KEP.bot_retrieval_server.VectorLabel
	3,   // 23: trpc.KEP.bot_retrieval_server.AddVectorReq.type:type_name -> trpc.KEP.bot_retrieval_server.KnowledgeType
	27,  // 24: trpc.KEP.bot_retrieval_server.UpdateVectorReq.labels:type_name -> trpc.KEP.bot_retrieval_server.VectorLabel
	56,  // 25: trpc.KEP.bot_retrieval_server.SearchData.filters:type_name -> trpc.KEP.bot_retrieval_server.SearchFilter
	28,  // 26: trpc.KEP.bot_retrieval_server.SearchData.labels:type_name -> trpc.KEP.bot_retrieval_server.SearchVectorLabel
	26,  // 27: trpc.KEP.bot_retrieval_server.SearchData.label_expression:type_name -> trpc.KEP.bot_retrieval_server.LabelExpression
	29,  // 28: trpc.KEP.bot_retrieval_server.SearchFilter.label_expr:type_name -> trpc.KEP.bot_retrieval_server.VectorLabelExpr
	57,  // 29: trpc.KEP.bot_retrieval_server.SearchMultiKnowledgeReq.rerank:type_name -> trpc.KEP.bot_retrieval_server.Rerank
	25,  // 30: trpc.KEP.bot_retrieval_server.SearchMultiKnowledgeReq.search_strategy:type_name -> trpc.KEP.bot_retrieval_server.SearchStrategy
	55,  // 31: trpc.KEP.bot_retrieval_server.SearchMultiKnowledgeReq.search_data:type_name -> trpc.KEP.bot_retrieval_server.SearchData
	120, // 32: trpc.KEP.bot_retrieval_server.SearchVectorReq.filters:type_name -> trpc.KEP.bot_retrieval_server.SearchVectorReq.Filter
	121, // 33: trpc.KEP.bot_retrieval_server.SearchVectorReq.rerank:type_name -> trpc.KEP.bot_retrieval_server.SearchVectorReq.Rerank
	28,  // 34: trpc.KEP.bot_retrieval_server.SearchVectorReq.labels:type_name -> trpc.KEP.bot_retrieval_server.SearchVectorLabel
	26,  // 35: trpc.KEP.bot_retrieval_server.SearchVectorReq.label_expression:type_name -> trpc.KEP.bot_retrieval_server.LabelExpression
	25,  // 36: trpc.KEP.bot_retrieval_server.SearchVectorReq.search_strategy:type_name -> trpc.KEP.bot_retrieval_server.SearchStrategy
	122, // 37: trpc.KEP.bot_retrieval_server.SearchVectorRsp.docs:type_name -> trpc.KEP.bot_retrieval_server.SearchVectorRsp.Doc
	27,  // 38: trpc.KEP.bot_retrieval_server.DirectAddVectorReq.labels:type_name -> trpc.KEP.bot_retrieval_server.VectorLabel
	27,  // 39: trpc.KEP.bot_retrieval_server.DirectUpdateVectorReq.labels:type_name -> trpc.KEP.bot_retrieval_server.VectorLabel
	126, // 40: trpc.KEP.bot_retrieval_server.DirectSearchVectorReq.filters:type_name -> trpc.KEP.bot_retrieval_server.DirectSearchVectorReq.Filter
	127, // 41: trpc.KEP.bot_retrieval_server.DirectSearchVectorReq.rerank:type_name -> trpc.KEP.bot_retrieval_server.DirectSearchVectorReq.Rerank
	128, // 42: trpc.KEP.bot_retrieval_server.DirectSearchVectorRsp.docs:type_name -> trpc.KEP.bot_retrieval_server.DirectSearchVectorRsp.Doc
	74,  // 43: trpc.KEP.bot_retrieval_server.AddBigDataElasticReq.data:type_name -> trpc.KEP.bot_retrieval_server.BigData
	3,   // 44: trpc.KEP.bot_retrieval_server.AddBigDataElasticReq.type:type_name -> trpc.KEP.bot_retrieval_server.KnowledgeType
	3,   // 45: trpc.KEP.bot_retrieval_server.BatchGetBigDataESByRobotBigDataIDReq.type:type_name -> trpc.KEP.bot_retrieval_server.KnowledgeType
	74,  // 46: trpc.KEP.bot_retrieval_server.BatchGetBigDataESByRobotBigDataIDResp.data:type_name -> trpc.KEP.bot_retrieval_server.BigData
	3,   // 47: trpc.KEP.bot_retrieval_server.DeleteBigDataElasticReq.type:type_name -> trpc.KEP.bot_retrieval_server.KnowledgeType
	27,  // 48: trpc.KEP.bot_retrieval_server.AddKnowledgeReq.labels:type_name -> trpc.KEP.bot_retrieval_server.VectorLabel
	102, // 49: trpc.KEP.bot_retrieval_server.BatchAddKnowledgeReq.knowledge:type_name -> trpc.KEP.bot_retrieval_server.KnowledgeData
	105, // 50: trpc.KEP.bot_retrieval_server.BatchDeleteKnowledgeReq.data:type_name -> trpc.KEP.bot_retrieval_server.KnowledgeIDType
	105, // 51: trpc.KEP.bot_retrieval_server.BatchDeleteAllKnowledgeProdReq.data:type_name -> trpc.KEP.bot_retrieval_server.KnowledgeIDType
	27,  // 52: trpc.KEP.bot_retrieval_server.UpdateKnowledgeReq.labels:type_name -> trpc.KEP.bot_retrieval_server.VectorLabel
	5,   // 53: trpc.KEP.bot_retrieval_server.Cell.cell_data_type:type_name -> trpc.KEP.bot_retrieval_server.CellDataType
	94,  // 54: trpc.KEP.bot_retrieval_server.Row.cells:type_name -> trpc.KEP.bot_retrieval_server.Cell
	129, // 55: trpc.KEP.bot_retrieval_server.Text2SQLMeta.headers:type_name -> trpc.KEP.bot_retrieval_server.Text2SQLMeta.Header
	95,  // 56: trpc.KEP.bot_retrieval_server.Text2SQLRowData.row:type_name -> trpc.KEP.bot_retrieval_server.Row
	96,  // 57: trpc.KEP.bot_retrieval_server.AddText2SQLReq.meta:type_name -> trpc.KEP.bot_retrieval_server.Text2SQLMeta
	97,  // 58: trpc.KEP.bot_retrieval_server.AddText2SQLReq.rows:type_name -> trpc.KEP.bot_retrieval_server.Text2SQLRowData
	27,  // 59: trpc.KEP.bot_retrieval_server.AddText2SQLReq.labels:type_name -> trpc.KEP.bot_retrieval_server.VectorLabel
	27,  // 60: trpc.KEP.bot_retrieval_server.KnowledgeData.labels:type_name -> trpc.KEP.bot_retrieval_server.VectorLabel
	102, // 61: trpc.KEP.bot_retrieval_server.AddRealTimeKnowledgeReq.knowledge:type_name -> trpc.KEP.bot_retrieval_server.KnowledgeData
	105, // 62: trpc.KEP.bot_retrieval_server.DeleteRealTimeKnowledgeReq.data:type_name -> trpc.KEP.bot_retrieval_server.KnowledgeIDType
	130, // 63: trpc.KEP.bot_retrieval_server.RetrievalRealTimeReq.filters:type_name -> trpc.KEP.bot_retrieval_server.RetrievalRealTimeReq.Filter
	131, // 64: trpc.KEP.bot_retrieval_server.RetrievalRealTimeReq.rerank:type_name -> trpc.KEP.bot_retrieval_server.RetrievalRealTimeReq.Rerank
	28,  // 65: trpc.KEP.bot_retrieval_server.RetrievalRealTimeReq.labels:type_name -> trpc.KEP.bot_retrieval_server.SearchVectorLabel
	26,  // 66: trpc.KEP.bot_retrieval_server.RetrievalRealTimeReq.label_expression:type_name -> trpc.KEP.bot_retrieval_server.LabelExpression
	25,  // 67: trpc.KEP.bot_retrieval_server.RetrievalRealTimeReq.search_strategy:type_name -> trpc.KEP.bot_retrieval_server.SearchStrategy
	132, // 68: trpc.KEP.bot_retrieval_server.RetrievalRealTimeRsp.docs:type_name -> trpc.KEP.bot_retrieval_server.RetrievalRealTimeRsp.Doc
	7,   // 69: trpc.KEP.bot_retrieval_server.LabelExpression.Condition.type:type_name -> trpc.KEP.bot_retrieval_server.LabelExpression.Condition.DataType
	29,  // 70: trpc.KEP.bot_retrieval_server.SearchReq.Filter.label_expr:type_name -> trpc.KEP.bot_retrieval_server.VectorLabelExpr
	30,  // 71: trpc.KEP.bot_retrieval_server.SearchRsp.Doc.extra:type_name -> trpc.KEP.bot_retrieval_server.RetrievalExtra
	4,   // 72: trpc.KEP.bot_retrieval_server.SearchRsp.Doc.result_type:type_name -> trpc.KEP.bot_retrieval_server.RetrievalResultType
	116, // 73: trpc.KEP.bot_retrieval_server.SearchRsp.Doc.similar_question_extra:type_name -> trpc.KEP.bot_retrieval_server.SearchRsp.Doc.SimilarQuestionExtra
	117, // 74: trpc.KEP.bot_retrieval_server.SearchRsp.Doc.text2sql_extra:type_name -> trpc.KEP.bot_retrieval_server.SearchRsp.Doc.Text2SQLExtra
	118, // 75: trpc.KEP.bot_retrieval_server.SearchRsp.Doc.Text2SQLExtra.table_infos:type_name -> trpc.KEP.bot_retrieval_server.SearchRsp.Doc.Text2SQLExtra.TableInfo
	29,  // 76: trpc.KEP.bot_retrieval_server.SearchVectorReq.Filter.label_expr:type_name -> trpc.KEP.bot_retrieval_server.VectorLabelExpr
	30,  // 77: trpc.KEP.bot_retrieval_server.SearchVectorRsp.Doc.extra:type_name -> trpc.KEP.bot_retrieval_server.RetrievalExtra
	4,   // 78: trpc.KEP.bot_retrieval_server.SearchVectorRsp.Doc.result_type:type_name -> trpc.KEP.bot_retrieval_server.RetrievalResultType
	123, // 79: trpc.KEP.bot_retrieval_server.SearchVectorRsp.Doc.similar_question_extra:type_name -> trpc.KEP.bot_retrieval_server.SearchVectorRsp.Doc.SimilarQuestionExtra
	124, // 80: trpc.KEP.bot_retrieval_server.SearchVectorRsp.Doc.text2sql_extra:type_name -> trpc.KEP.bot_retrieval_server.SearchVectorRsp.Doc.Text2SQLExtra
	125, // 81: trpc.KEP.bot_retrieval_server.SearchVectorRsp.Doc.Text2SQLExtra.table_infos:type_name -> trpc.KEP.bot_retrieval_server.SearchVectorRsp.Doc.Text2SQLExtra.TableInfo
	29,  // 82: trpc.KEP.bot_retrieval_server.DirectSearchVectorReq.Filter.label_expr:type_name -> trpc.KEP.bot_retrieval_server.VectorLabelExpr
	10,  // 83: trpc.KEP.bot_retrieval_server.Text2SQLMeta.Header.type:type_name -> trpc.KEP.bot_retrieval_server.Text2SQLMeta.Header.HeaderType
	95,  // 84: trpc.KEP.bot_retrieval_server.Text2SQLMeta.Header.rows:type_name -> trpc.KEP.bot_retrieval_server.Row
	30,  // 85: trpc.KEP.bot_retrieval_server.RetrievalRealTimeRsp.Doc.extra:type_name -> trpc.KEP.bot_retrieval_server.RetrievalExtra
	4,   // 86: trpc.KEP.bot_retrieval_server.RetrievalRealTimeRsp.Doc.result_type:type_name -> trpc.KEP.bot_retrieval_server.RetrievalResultType
	31,  // 87: trpc.KEP.bot_retrieval_server.Retrieval.Search:input_type -> trpc.KEP.bot_retrieval_server.SearchReq
	33,  // 88: trpc.KEP.bot_retrieval_server.Retrieval.Publish:input_type -> trpc.KEP.bot_retrieval_server.PublishReq
	35,  // 89: trpc.KEP.bot_retrieval_server.Retrieval.Similarity:input_type -> trpc.KEP.bot_retrieval_server.SimilarityReq
	37,  // 90: trpc.KEP.bot_retrieval_server.Retrieval.IndexRebuild:input_type -> trpc.KEP.bot_retrieval_server.IndexRebuildReq
	39,  // 91: trpc.KEP.bot_retrieval_server.Retrieval.ContinueTerminatedTask:input_type -> trpc.KEP.bot_retrieval_server.ContinueTerminatedTaskReq
	41,  // 92: trpc.KEP.bot_retrieval_server.Retrieval.UpgradeEmbedding:input_type -> trpc.KEP.bot_retrieval_server.UpgradeEmbeddingReq
	43,  // 93: trpc.KEP.bot_retrieval_server.Retrieval.CheckVersion:input_type -> trpc.KEP.bot_retrieval_server.CheckVersionReq
	90,  // 94: trpc.KEP.bot_retrieval_server.Retrieval.BatchDeleteAllKnowledgeProd:input_type -> trpc.KEP.bot_retrieval_server.BatchDeleteAllKnowledgeProdReq
	110, // 95: trpc.KEP.bot_retrieval_server.Retrieval.ClearAppVectorResource:input_type -> trpc.KEP.bot_retrieval_server.ClearAppVectorResourceReq
	45,  // 96: trpc.KEP.bot_retrieval_server.DirectIndex.CreateIndex:input_type -> trpc.KEP.bot_retrieval_server.CreateIndexReq
	47,  // 97: trpc.KEP.bot_retrieval_server.DirectIndex.DeleteIndex:input_type -> trpc.KEP.bot_retrieval_server.DeleteIndexReq
	49,  // 98: trpc.KEP.bot_retrieval_server.DirectIndex.AddVector:input_type -> trpc.KEP.bot_retrieval_server.AddVectorReq
	51,  // 99: trpc.KEP.bot_retrieval_server.DirectIndex.DeleteVector:input_type -> trpc.KEP.bot_retrieval_server.DeleteVectorReq
	53,  // 100: trpc.KEP.bot_retrieval_server.DirectIndex.UpdateVector:input_type -> trpc.KEP.bot_retrieval_server.UpdateVectorReq
	59,  // 101: trpc.KEP.bot_retrieval_server.DirectIndex.SearchVector:input_type -> trpc.KEP.bot_retrieval_server.SearchVectorReq
	58,  // 102: trpc.KEP.bot_retrieval_server.DirectIndex.SearchMultiKnowledge:input_type -> trpc.KEP.bot_retrieval_server.SearchMultiKnowledgeReq
	61,  // 103: trpc.KEP.bot_retrieval_server.DirectIndex.DirectCreateIndex:input_type -> trpc.KEP.bot_retrieval_server.DirectCreateIndexReq
	63,  // 104: trpc.KEP.bot_retrieval_server.DirectIndex.DirectDeleteIndex:input_type -> trpc.KEP.bot_retrieval_server.DirectDeleteIndexReq
	65,  // 105: trpc.KEP.bot_retrieval_server.DirectIndex.DirectAddVector:input_type -> trpc.KEP.bot_retrieval_server.DirectAddVectorReq
	67,  // 106: trpc.KEP.bot_retrieval_server.DirectIndex.DirectDeleteVector:input_type -> trpc.KEP.bot_retrieval_server.DirectDeleteVectorReq
	69,  // 107: trpc.KEP.bot_retrieval_server.DirectIndex.DirectUpdateVector:input_type -> trpc.KEP.bot_retrieval_server.DirectUpdateVectorReq
	71,  // 108: trpc.KEP.bot_retrieval_server.DirectIndex.DirectSearchVector:input_type -> trpc.KEP.bot_retrieval_server.DirectSearchVectorReq
	73,  // 109: trpc.KEP.bot_retrieval_server.DirectIndex.AddBigDataElastic:input_type -> trpc.KEP.bot_retrieval_server.AddBigDataElasticReq
	78,  // 110: trpc.KEP.bot_retrieval_server.DirectIndex.DeleteBigDataElastic:input_type -> trpc.KEP.bot_retrieval_server.DeleteBigDataElasticReq
	80,  // 111: trpc.KEP.bot_retrieval_server.DirectIndex.RecoverBigDataElastic:input_type -> trpc.KEP.bot_retrieval_server.RecoverBigDataElasticReq
	75,  // 112: trpc.KEP.bot_retrieval_server.DirectIndex.BatchGetBigDataESByRobotBigDataID:input_type -> trpc.KEP.bot_retrieval_server.BatchGetBigDataESByRobotBigDataIDReq
	82,  // 113: trpc.KEP.bot_retrieval_server.DirectIndex.AddKnowledge:input_type -> trpc.KEP.bot_retrieval_server.AddKnowledgeReq
	84,  // 114: trpc.KEP.bot_retrieval_server.DirectIndex.BatchAddKnowledge:input_type -> trpc.KEP.bot_retrieval_server.BatchAddKnowledgeReq
	86,  // 115: trpc.KEP.bot_retrieval_server.DirectIndex.DeleteKnowledge:input_type -> trpc.KEP.bot_retrieval_server.DeleteKnowledgeReq
	88,  // 116: trpc.KEP.bot_retrieval_server.DirectIndex.BatchDeleteKnowledge:input_type -> trpc.KEP.bot_retrieval_server.BatchDeleteKnowledgeReq
	92,  // 117: trpc.KEP.bot_retrieval_server.DirectIndex.UpdateKnowledge:input_type -> trpc.KEP.bot_retrieval_server.UpdateKnowledgeReq
	98,  // 118: trpc.KEP.bot_retrieval_server.DirectIndex.AddText2SQL:input_type -> trpc.KEP.bot_retrieval_server.AddText2SQLReq
	100, // 119: trpc.KEP.bot_retrieval_server.DirectIndex.DeleteText2SQL:input_type -> trpc.KEP.bot_retrieval_server.DeleteText2SQLReq
	103, // 120: trpc.KEP.bot_retrieval_server.DirectIndex.AddRealTimeKnowledge:input_type -> trpc.KEP.bot_retrieval_server.AddRealTimeKnowledgeReq
	106, // 121: trpc.KEP.bot_retrieval_server.DirectIndex.DeleteRealTimeKnowledge:input_type -> trpc.KEP.bot_retrieval_server.DeleteRealTimeKnowledgeReq
	108, // 122: trpc.KEP.bot_retrieval_server.DirectIndex.RetrievalRealTime:input_type -> trpc.KEP.bot_retrieval_server.RetrievalRealTimeReq
	19,  // 123: trpc.KEP.bot_retrieval_server.DirectIndex.AddDBText2SQL:input_type -> trpc.KEP.bot_retrieval_server.AddDBText2SQLReq
	23,  // 124: trpc.KEP.bot_retrieval_server.DirectIndex.DeleteDBText2SQL:input_type -> trpc.KEP.bot_retrieval_server.DeleteDBText2SQLReq
	16,  // 125: trpc.KEP.bot_retrieval_server.DirectIndex.AddAndUpdateESData:input_type -> trpc.KEP.bot_retrieval_server.AddESDataReq
	13,  // 126: trpc.KEP.bot_retrieval_server.DirectIndex.DeleteESData:input_type -> trpc.KEP.bot_retrieval_server.DeleteESDataReq
	11,  // 127: trpc.KEP.bot_retrieval_server.DirectIndex.SearchES:input_type -> trpc.KEP.bot_retrieval_server.SearchESReq
	32,  // 128: trpc.KEP.bot_retrieval_server.Retrieval.Search:output_type -> trpc.KEP.bot_retrieval_server.SearchRsp
	34,  // 129: trpc.KEP.bot_retrieval_server.Retrieval.Publish:output_type -> trpc.KEP.bot_retrieval_server.PublishRsp
	36,  // 130: trpc.KEP.bot_retrieval_server.Retrieval.Similarity:output_type -> trpc.KEP.bot_retrieval_server.SimilarityRsp
	38,  // 131: trpc.KEP.bot_retrieval_server.Retrieval.IndexRebuild:output_type -> trpc.KEP.bot_retrieval_server.IndexRebuildRsp
	40,  // 132: trpc.KEP.bot_retrieval_server.Retrieval.ContinueTerminatedTask:output_type -> trpc.KEP.bot_retrieval_server.ContinueTerminatedTaskRsp
	42,  // 133: trpc.KEP.bot_retrieval_server.Retrieval.UpgradeEmbedding:output_type -> trpc.KEP.bot_retrieval_server.UpgradeEmbeddingRsp
	44,  // 134: trpc.KEP.bot_retrieval_server.Retrieval.CheckVersion:output_type -> trpc.KEP.bot_retrieval_server.CheckVersionRsp
	91,  // 135: trpc.KEP.bot_retrieval_server.Retrieval.BatchDeleteAllKnowledgeProd:output_type -> trpc.KEP.bot_retrieval_server.BatchDeleteAllKnowledgeProdRsp
	111, // 136: trpc.KEP.bot_retrieval_server.Retrieval.ClearAppVectorResource:output_type -> trpc.KEP.bot_retrieval_server.ClearAppVectorResourceRsp
	46,  // 137: trpc.KEP.bot_retrieval_server.DirectIndex.CreateIndex:output_type -> trpc.KEP.bot_retrieval_server.CreateIndexRsp
	48,  // 138: trpc.KEP.bot_retrieval_server.DirectIndex.DeleteIndex:output_type -> trpc.KEP.bot_retrieval_server.DeleteIndexRsp
	50,  // 139: trpc.KEP.bot_retrieval_server.DirectIndex.AddVector:output_type -> trpc.KEP.bot_retrieval_server.AddVectorRsp
	52,  // 140: trpc.KEP.bot_retrieval_server.DirectIndex.DeleteVector:output_type -> trpc.KEP.bot_retrieval_server.DeleteVectorRsp
	54,  // 141: trpc.KEP.bot_retrieval_server.DirectIndex.UpdateVector:output_type -> trpc.KEP.bot_retrieval_server.UpdateVectorRsp
	60,  // 142: trpc.KEP.bot_retrieval_server.DirectIndex.SearchVector:output_type -> trpc.KEP.bot_retrieval_server.SearchVectorRsp
	60,  // 143: trpc.KEP.bot_retrieval_server.DirectIndex.SearchMultiKnowledge:output_type -> trpc.KEP.bot_retrieval_server.SearchVectorRsp
	62,  // 144: trpc.KEP.bot_retrieval_server.DirectIndex.DirectCreateIndex:output_type -> trpc.KEP.bot_retrieval_server.DirectCreateIndexRsp
	64,  // 145: trpc.KEP.bot_retrieval_server.DirectIndex.DirectDeleteIndex:output_type -> trpc.KEP.bot_retrieval_server.DirectDeleteIndexRsp
	66,  // 146: trpc.KEP.bot_retrieval_server.DirectIndex.DirectAddVector:output_type -> trpc.KEP.bot_retrieval_server.DirectAddVectorRsp
	68,  // 147: trpc.KEP.bot_retrieval_server.DirectIndex.DirectDeleteVector:output_type -> trpc.KEP.bot_retrieval_server.DirectDeleteVectorRsp
	70,  // 148: trpc.KEP.bot_retrieval_server.DirectIndex.DirectUpdateVector:output_type -> trpc.KEP.bot_retrieval_server.DirectUpdateVectorRsp
	72,  // 149: trpc.KEP.bot_retrieval_server.DirectIndex.DirectSearchVector:output_type -> trpc.KEP.bot_retrieval_server.DirectSearchVectorRsp
	77,  // 150: trpc.KEP.bot_retrieval_server.DirectIndex.AddBigDataElastic:output_type -> trpc.KEP.bot_retrieval_server.AddBigDataElasticRsp
	79,  // 151: trpc.KEP.bot_retrieval_server.DirectIndex.DeleteBigDataElastic:output_type -> trpc.KEP.bot_retrieval_server.DeleteBigDataElasticRsp
	81,  // 152: trpc.KEP.bot_retrieval_server.DirectIndex.RecoverBigDataElastic:output_type -> trpc.KEP.bot_retrieval_server.RecoverBigDataElasticRsp
	76,  // 153: trpc.KEP.bot_retrieval_server.DirectIndex.BatchGetBigDataESByRobotBigDataID:output_type -> trpc.KEP.bot_retrieval_server.BatchGetBigDataESByRobotBigDataIDResp
	83,  // 154: trpc.KEP.bot_retrieval_server.DirectIndex.AddKnowledge:output_type -> trpc.KEP.bot_retrieval_server.AddKnowledgeRsp
	85,  // 155: trpc.KEP.bot_retrieval_server.DirectIndex.BatchAddKnowledge:output_type -> trpc.KEP.bot_retrieval_server.BatchAddKnowledgeRsp
	87,  // 156: trpc.KEP.bot_retrieval_server.DirectIndex.DeleteKnowledge:output_type -> trpc.KEP.bot_retrieval_server.DeleteKnowledgeRsp
	89,  // 157: trpc.KEP.bot_retrieval_server.DirectIndex.BatchDeleteKnowledge:output_type -> trpc.KEP.bot_retrieval_server.BatchDeleteKnowledgeRsp
	93,  // 158: trpc.KEP.bot_retrieval_server.DirectIndex.UpdateKnowledge:output_type -> trpc.KEP.bot_retrieval_server.UpdateKnowledgeRsp
	99,  // 159: trpc.KEP.bot_retrieval_server.DirectIndex.AddText2SQL:output_type -> trpc.KEP.bot_retrieval_server.AddText2SQLRsp
	101, // 160: trpc.KEP.bot_retrieval_server.DirectIndex.DeleteText2SQL:output_type -> trpc.KEP.bot_retrieval_server.DeleteText2SQLRsp
	104, // 161: trpc.KEP.bot_retrieval_server.DirectIndex.AddRealTimeKnowledge:output_type -> trpc.KEP.bot_retrieval_server.AddRealTimeKnowledgeRsp
	107, // 162: trpc.KEP.bot_retrieval_server.DirectIndex.DeleteRealTimeKnowledge:output_type -> trpc.KEP.bot_retrieval_server.DeleteRealTimeKnowledgeRsp
	109, // 163: trpc.KEP.bot_retrieval_server.DirectIndex.RetrievalRealTime:output_type -> trpc.KEP.bot_retrieval_server.RetrievalRealTimeRsp
	22,  // 164: trpc.KEP.bot_retrieval_server.DirectIndex.AddDBText2SQL:output_type -> trpc.KEP.bot_retrieval_server.AddDBText2SQLRsp
	24,  // 165: trpc.KEP.bot_retrieval_server.DirectIndex.DeleteDBText2SQL:output_type -> trpc.KEP.bot_retrieval_server.DeleteDBText2SQLRsp
	18,  // 166: trpc.KEP.bot_retrieval_server.DirectIndex.AddAndUpdateESData:output_type -> trpc.KEP.bot_retrieval_server.AddESDataReqRsp
	15,  // 167: trpc.KEP.bot_retrieval_server.DirectIndex.DeleteESData:output_type -> trpc.KEP.bot_retrieval_server.DeleteESDataRsp
	12,  // 168: trpc.KEP.bot_retrieval_server.DirectIndex.SearchES:output_type -> trpc.KEP.bot_retrieval_server.SearchESRsp
	128, // [128:169] is the sub-list for method output_type
	87,  // [87:128] is the sub-list for method input_type
	87,  // [87:87] is the sub-list for extension type_name
	87,  // [87:87] is the sub-list for extension extendee
	0,   // [0:87] is the sub-list for field type_name
}

func init() { file_retrieval_proto_init() }
func file_retrieval_proto_init() {
	if File_retrieval_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_retrieval_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SearchESReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_retrieval_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SearchESRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_retrieval_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeleteESDataReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_retrieval_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ESDataIDType); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_retrieval_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeleteESDataRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_retrieval_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AddESDataReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_retrieval_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ESData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_retrieval_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AddESDataReqRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_retrieval_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AddDBText2SQLReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_retrieval_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DBRowData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_retrieval_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DBCell); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_retrieval_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AddDBText2SQLRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_retrieval_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeleteDBText2SQLReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_retrieval_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeleteDBText2SQLRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_retrieval_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SearchStrategy); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_retrieval_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LabelExpression); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_retrieval_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*VectorLabel); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_retrieval_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SearchVectorLabel); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_retrieval_proto_msgTypes[18].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*VectorLabelExpr); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_retrieval_proto_msgTypes[19].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RetrievalExtra); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_retrieval_proto_msgTypes[20].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SearchReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_retrieval_proto_msgTypes[21].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SearchRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_retrieval_proto_msgTypes[22].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PublishReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_retrieval_proto_msgTypes[23].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PublishRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_retrieval_proto_msgTypes[24].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SimilarityReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_retrieval_proto_msgTypes[25].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SimilarityRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_retrieval_proto_msgTypes[26].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*IndexRebuildReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_retrieval_proto_msgTypes[27].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*IndexRebuildRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_retrieval_proto_msgTypes[28].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ContinueTerminatedTaskReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_retrieval_proto_msgTypes[29].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ContinueTerminatedTaskRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_retrieval_proto_msgTypes[30].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpgradeEmbeddingReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_retrieval_proto_msgTypes[31].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpgradeEmbeddingRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_retrieval_proto_msgTypes[32].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CheckVersionReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_retrieval_proto_msgTypes[33].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CheckVersionRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_retrieval_proto_msgTypes[34].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateIndexReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_retrieval_proto_msgTypes[35].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateIndexRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_retrieval_proto_msgTypes[36].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeleteIndexReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_retrieval_proto_msgTypes[37].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeleteIndexRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_retrieval_proto_msgTypes[38].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AddVectorReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_retrieval_proto_msgTypes[39].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AddVectorRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_retrieval_proto_msgTypes[40].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeleteVectorReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_retrieval_proto_msgTypes[41].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeleteVectorRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_retrieval_proto_msgTypes[42].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateVectorReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_retrieval_proto_msgTypes[43].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateVectorRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_retrieval_proto_msgTypes[44].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SearchData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_retrieval_proto_msgTypes[45].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SearchFilter); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_retrieval_proto_msgTypes[46].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Rerank); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_retrieval_proto_msgTypes[47].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SearchMultiKnowledgeReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_retrieval_proto_msgTypes[48].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SearchVectorReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_retrieval_proto_msgTypes[49].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SearchVectorRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_retrieval_proto_msgTypes[50].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DirectCreateIndexReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_retrieval_proto_msgTypes[51].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DirectCreateIndexRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_retrieval_proto_msgTypes[52].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DirectDeleteIndexReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_retrieval_proto_msgTypes[53].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DirectDeleteIndexRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_retrieval_proto_msgTypes[54].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DirectAddVectorReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_retrieval_proto_msgTypes[55].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DirectAddVectorRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_retrieval_proto_msgTypes[56].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DirectDeleteVectorReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_retrieval_proto_msgTypes[57].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DirectDeleteVectorRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_retrieval_proto_msgTypes[58].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DirectUpdateVectorReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_retrieval_proto_msgTypes[59].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DirectUpdateVectorRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_retrieval_proto_msgTypes[60].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DirectSearchVectorReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_retrieval_proto_msgTypes[61].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DirectSearchVectorRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_retrieval_proto_msgTypes[62].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AddBigDataElasticReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_retrieval_proto_msgTypes[63].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BigData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_retrieval_proto_msgTypes[64].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BatchGetBigDataESByRobotBigDataIDReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_retrieval_proto_msgTypes[65].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BatchGetBigDataESByRobotBigDataIDResp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_retrieval_proto_msgTypes[66].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AddBigDataElasticRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_retrieval_proto_msgTypes[67].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeleteBigDataElasticReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_retrieval_proto_msgTypes[68].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeleteBigDataElasticRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_retrieval_proto_msgTypes[69].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RecoverBigDataElasticReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_retrieval_proto_msgTypes[70].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RecoverBigDataElasticRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_retrieval_proto_msgTypes[71].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AddKnowledgeReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_retrieval_proto_msgTypes[72].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AddKnowledgeRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_retrieval_proto_msgTypes[73].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BatchAddKnowledgeReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_retrieval_proto_msgTypes[74].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BatchAddKnowledgeRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_retrieval_proto_msgTypes[75].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeleteKnowledgeReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_retrieval_proto_msgTypes[76].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeleteKnowledgeRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_retrieval_proto_msgTypes[77].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BatchDeleteKnowledgeReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_retrieval_proto_msgTypes[78].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BatchDeleteKnowledgeRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_retrieval_proto_msgTypes[79].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BatchDeleteAllKnowledgeProdReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_retrieval_proto_msgTypes[80].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BatchDeleteAllKnowledgeProdRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_retrieval_proto_msgTypes[81].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateKnowledgeReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_retrieval_proto_msgTypes[82].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateKnowledgeRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_retrieval_proto_msgTypes[83].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Cell); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_retrieval_proto_msgTypes[84].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Row); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_retrieval_proto_msgTypes[85].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Text2SQLMeta); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_retrieval_proto_msgTypes[86].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Text2SQLRowData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_retrieval_proto_msgTypes[87].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AddText2SQLReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_retrieval_proto_msgTypes[88].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AddText2SQLRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_retrieval_proto_msgTypes[89].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeleteText2SQLReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_retrieval_proto_msgTypes[90].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeleteText2SQLRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_retrieval_proto_msgTypes[91].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*KnowledgeData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_retrieval_proto_msgTypes[92].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AddRealTimeKnowledgeReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_retrieval_proto_msgTypes[93].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AddRealTimeKnowledgeRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_retrieval_proto_msgTypes[94].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*KnowledgeIDType); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_retrieval_proto_msgTypes[95].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeleteRealTimeKnowledgeReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_retrieval_proto_msgTypes[96].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeleteRealTimeKnowledgeRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_retrieval_proto_msgTypes[97].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RetrievalRealTimeReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_retrieval_proto_msgTypes[98].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RetrievalRealTimeRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_retrieval_proto_msgTypes[99].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ClearAppVectorResourceReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_retrieval_proto_msgTypes[100].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ClearAppVectorResourceRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_retrieval_proto_msgTypes[101].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LabelExpression_Condition); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_retrieval_proto_msgTypes[102].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SearchReq_Filter); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_retrieval_proto_msgTypes[103].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SearchReq_Rerank); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_retrieval_proto_msgTypes[104].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SearchRsp_Doc); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_retrieval_proto_msgTypes[105].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SearchRsp_Doc_SimilarQuestionExtra); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_retrieval_proto_msgTypes[106].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SearchRsp_Doc_Text2SQLExtra); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_retrieval_proto_msgTypes[107].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SearchRsp_Doc_Text2SQLExtra_TableInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_retrieval_proto_msgTypes[108].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SimilarityReq_Doc); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_retrieval_proto_msgTypes[109].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SearchVectorReq_Filter); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_retrieval_proto_msgTypes[110].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SearchVectorReq_Rerank); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_retrieval_proto_msgTypes[111].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SearchVectorRsp_Doc); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_retrieval_proto_msgTypes[112].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SearchVectorRsp_Doc_SimilarQuestionExtra); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_retrieval_proto_msgTypes[113].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SearchVectorRsp_Doc_Text2SQLExtra); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_retrieval_proto_msgTypes[114].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SearchVectorRsp_Doc_Text2SQLExtra_TableInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_retrieval_proto_msgTypes[115].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DirectSearchVectorReq_Filter); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_retrieval_proto_msgTypes[116].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DirectSearchVectorReq_Rerank); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_retrieval_proto_msgTypes[117].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DirectSearchVectorRsp_Doc); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_retrieval_proto_msgTypes[118].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Text2SQLMeta_Header); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_retrieval_proto_msgTypes[119].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RetrievalRealTimeReq_Filter); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_retrieval_proto_msgTypes[120].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RetrievalRealTimeReq_Rerank); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_retrieval_proto_msgTypes[121].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RetrievalRealTimeRsp_Doc); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_retrieval_proto_rawDesc,
			NumEnums:      11,
			NumMessages:   122,
			NumExtensions: 0,
			NumServices:   2,
		},
		GoTypes:           file_retrieval_proto_goTypes,
		DependencyIndexes: file_retrieval_proto_depIdxs,
		EnumInfos:         file_retrieval_proto_enumTypes,
		MessageInfos:      file_retrieval_proto_msgTypes,
	}.Build()
	File_retrieval_proto = out.File
	file_retrieval_proto_rawDesc = nil
	file_retrieval_proto_goTypes = nil
	file_retrieval_proto_depIdxs = nil
}
