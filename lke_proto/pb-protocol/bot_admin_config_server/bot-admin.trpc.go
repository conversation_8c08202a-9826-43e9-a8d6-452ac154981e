// Code generated by trpc-go/trpc-go-cmdline v2.8.32. DO NOT EDIT.
// source: bot-admin.proto

package bot_admin_config_server

import (
	"context"
	"errors"
	"fmt"

	_ "git.code.oa.com/trpc-go/trpc-go"
	"git.code.oa.com/trpc-go/trpc-go/client"
	"git.code.oa.com/trpc-go/trpc-go/codec"
	_ "git.code.oa.com/trpc-go/trpc-go/http"
	"git.code.oa.com/trpc-go/trpc-go/server"
)

// START ======================================= Server Service Definition ======================================= START

// AdminService defines service.
type AdminService interface {
	// CorpInfo Deprecated 企业消息
	CorpInfo(ctx context.Context, req *CorpInfoReq) (*CorpInfoRsp, error)
	// DescribeCorp 企业消息
	DescribeCorp(ctx context.Context, req *DescribeCorpReq) (*DescribeCorpRsp, error)
	// ListCorpStaff 员工信息列表
	ListCorpStaff(ctx context.Context, req *ListCorpStaffReq) (*ListCorpStaffRsp, error)
	// DescribeStorageCredential 获取对象存储的临时密钥
	DescribeStorageCredential(ctx context.Context, req *DescribeStorageCredentialReq) (*DescribeStorageCredentialRsp, error)
	// GetCredential Deprecated 获取对象存储的临时密钥
	GetCredential(ctx context.Context, req *GetCredentialReq) (*GetCredentialRsp, error)
	// GetAuditSwitch Deprecated 获取审核开关
	GetAuditSwitch(ctx context.Context, req *GetAuditSwitchReq) (*GetAuditSwitchRsp, error)
	// DescribeAuditSwitch 获取审核开关
	DescribeAuditSwitch(ctx context.Context, req *DescribeAuditSwitchReq) (*DescribeAuditSwitchRsp, error)
	// ListDoc 文档列表
	ListDoc(ctx context.Context, req *ListDocReq) (*ListDocRsp, error)
	// ListDocV1 文档列表
	ListDocV1(ctx context.Context, req *ListDocV1Req) (*ListDocV1Rsp, error)
	// SaveDocV1 保存文档
	SaveDocV1(ctx context.Context, req *SaveDocV1Req) (*SaveDocV1Rsp, error)
	// ModifyDoc 修改文档
	ModifyDoc(ctx context.Context, req *ModifyDocReq) (*ModifyDocRsp, error)
	// ModifyDocAttrRange 批量编辑文档适用范围
	ModifyDocAttrRange(ctx context.Context, req *ModifyDocAttrRangeReq) (*ModifyDocAttrRangeRsp, error)
	// ModifyDocV1 修改文档
	ModifyDocV1(ctx context.Context, req *ModifyDocV1Req) (*ModifyDocV1Rsp, error)
	// DeleteDoc 删除文档
	DeleteDoc(ctx context.Context, req *DeleteDocReq) (*DeleteDocRsp, error)
	// DescribeDoc 获取文档详情
	DescribeDoc(ctx context.Context, req *DescribeDocReq) (*DescribeDocRsp, error)
	// ReferDoc 答案中是否引用
	ReferDoc(ctx context.Context, req *ReferDocReq) (*ReferDocRsp, error)
	// StartCreateQA Deprecated 重新生成QA对
	StartCreateQA(ctx context.Context, req *StartCreateQAReq) (*StartCreateQARsp, error)
	// GetDocPreview 获取文档预览链接
	GetDocPreview(ctx context.Context, req *GetDocPreviewReq) (*GetDocPreviewRsp, error)
	// GenerateQA 生成QA对
	GenerateQA(ctx context.Context, req *GenerateQAReq) (*GenerateQARsp, error)
	// GetSelectDoc Deprecated 获取文档下拉列表
	GetSelectDoc(ctx context.Context, req *GetSelectDocReq) (*GetSelectDocRsp, error)
	// ListSelectDoc 获取文档下拉列表
	ListSelectDoc(ctx context.Context, req *ListSelectDocReq) (*ListSelectDocRsp, error)
	// StopDocParse 中止文档解析
	StopDocParse(ctx context.Context, req *StopDocParseReq) (*StopDocParseRsp, error)
	// RetryDocParse 重试文档解析
	RetryDocParse(ctx context.Context, req *RetryDocParseReq) (*RetryDocParseRsp, error)
	// RetryDocAudit 重试文档审核
	RetryDocAudit(ctx context.Context, req *RetryDocAuditReq) (*RetryDocAuditRsp, error)
	// CreateRobot 新建机器人
	CreateRobot(ctx context.Context, req *CreateRobotReq) (*CreateRobotRsp, error)
	// DeleteRobot 删除机器人
	DeleteRobot(ctx context.Context, req *DeleteRobotReq) (*DeleteRobotRsp, error)
	// ModifyRobot Deprecated 更新机器人属性
	ModifyRobot(ctx context.Context, req *ModifyRobotReq) (*ModifyRobotRsp, error)
	// ModifyRobotBasicConfig 更新机器人基础属性
	ModifyRobotBasicConfig(ctx context.Context, req *ModifyRobotBasicConfigReq) (*ModifyRobotBasicConfigRsp, error)
	// UpdateRobot Deprecated 更新机器人属性
	UpdateRobot(ctx context.Context, req *UpdateRobotReq) (*UpdateRobotRsp, error)
	// RobotInfo Deprecated 获取机器人
	RobotInfo(ctx context.Context, req *RobotInfoReq) (*RobotInfoRsp, error)
	// DescribeRobot 获取机器人
	DescribeRobot(ctx context.Context, req *DescribeRobotReq) (*DescribeRobotRsp, error)
	// ListRobot 获取机器人列表
	ListRobot(ctx context.Context, req *ListRobotReq) (*ListRobotRsp, error)
	// ListRobotV1 获取机器人列表
	ListRobotV1(ctx context.Context, req *ListRobotV1Req) (*ListRobotV1Rsp, error)
	// AccountInfo Deprecated 获取账户信息
	AccountInfo(ctx context.Context, req *AccountInfoReq) (*AccountInfoRsp, error)
	// DescribeAccount 获取账户信息
	DescribeAccount(ctx context.Context, req *DescribeAccountReq) (*DescribeAccountRsp, error)
	// ListRole Deprecated 获取机器人角色配置
	ListRole(ctx context.Context, req *ListRoleReq) (*ListRoleRsp, error)
	// ListRobotRole 获取机器人角色配置
	ListRobotRole(ctx context.Context, req *ListRobotRoleReq) (*ListRobotRoleRsp, error)
	// ListQACate 获取QA分类
	ListQACate(ctx context.Context, req *ListQACateReq) (*ListQACateRsp, error)
	// ListQACateV1 Deprecated 获取QA分类V1
	ListQACateV1(ctx context.Context, req *ListQACateV1Req) (*ListQACateV1Rsp, error)
	// CreateQACate 创建QA分类
	CreateQACate(ctx context.Context, req *CreateQACateReq) (*CreateQACateRsp, error)
	// CreateQACateV1 Deprecated 创建QA分类
	CreateQACateV1(ctx context.Context, req *CreateQACateV1Req) (*CreateQACateV1Rsp, error)
	// UpdateQACate Deprecated QA分类修改
	UpdateQACate(ctx context.Context, req *UpdateQACateReq) (*UpdateQACateRsp, error)
	// ModifyQACate QA分类修改
	ModifyQACate(ctx context.Context, req *ModifyQACateReq) (*ModifyQACateRsp, error)
	// DeleteQACate QA分类删除
	DeleteQACate(ctx context.Context, req *DeleteQACateReq) (*DeleteQACateRsp, error)
	// GetQAList Deprecated 获取QA列表
	GetQAList(ctx context.Context, req *GetQAListReq) (*GetQAListRsp, error)
	// ListQA 获取QA列表
	ListQA(ctx context.Context, req *ListQAReq) (*ListQARsp, error)
	// GetQADetail Deprecated 获取QA详情
	GetQADetail(ctx context.Context, req *GetQADetailReq) (*GetQADetailRsp, error)
	// DescribeQA 获取QA详情
	DescribeQA(ctx context.Context, req *DescribeQAReq) (*DescribeQARsp, error)
	// CreateQA 新建QA
	CreateQA(ctx context.Context, req *CreateQAReq) (*CreateQARsp, error)
	// CreateQAV1 新建QA
	CreateQAV1(ctx context.Context, req *CreateQAV1Req) (*CreateQAV1Rsp, error)
	// UpdateQA Deprecated 编辑QA
	UpdateQA(ctx context.Context, req *UpdateQAReq) (*UpdateQARsp, error)
	// ModifyQA 编辑QA
	ModifyQA(ctx context.Context, req *ModifyQAReq) (*ModifyQARsp, error)
	// DeleteQA 删除QA
	DeleteQA(ctx context.Context, req *DeleteQAReq) (*DeleteQARsp, error)
	// DeleteQAV1 删除QA
	DeleteQAV1(ctx context.Context, req *DeleteQAV1Req) (*DeleteQAV1Rsp, error)
	// VerifyQA 校验QA
	VerifyQA(ctx context.Context, req *VerifyQAReq) (*VerifyQARsp, error)
	// VerifyQAV1 校验QA
	VerifyQAV1(ctx context.Context, req *VerifyQAV1Req) (*VerifyQAV1Rsp, error)
	// GroupQA QA分组
	GroupQA(ctx context.Context, req *GroupQAReq) (*GroupQARsp, error)
	// ModifyQAAttrRange 编辑QA适用范围
	ModifyQAAttrRange(ctx context.Context, req *ModifyQAAttrRangeReq) (*ModifyQAAttrRangeRsp, error)
	// ExportQAList 导出QA列表
	ExportQAList(ctx context.Context, req *ExportQAListReq) (*ExportQAListRsp, error)
	// ExportQAListV1 Deprecated 导出QA列表
	ExportQAListV1(ctx context.Context, req *ExportQAListReqV1) (*ExportQAListRspV1, error)
	// GetReleaseInfo Deprecated 拉取发布按钮状态、最后发布时间
	GetReleaseInfo(ctx context.Context, req *GetReleaseInfoReq) (*GetReleaseInfoRsp, error)
	// DescribeReleaseInfo 拉取发布按钮状态、最后发布时间
	DescribeReleaseInfo(ctx context.Context, req *DescribeReleaseInfoReq) (*DescribeReleaseInfoRsp, error)
	// ListReleaseDocPreview 发布文档预览
	ListReleaseDocPreview(ctx context.Context, req *ListReleaseDocPreviewReq) (*ListReleaseDocPreviewRsp, error)
	// ListReleaseQAPreview 发布问答预览
	ListReleaseQAPreview(ctx context.Context, req *ListReleaseQAPreviewReq) (*ListReleaseQAPreviewRsp, error)
	// ListRejectedQuestionPreview 发布拒答问题预览
	ListRejectedQuestionPreview(ctx context.Context, req *ListRejectedQuestionPreviewReq) (*ListRejectedQuestionPreviewRsp, error)
	// ListReleaseConfigPreview 待发布配置预览
	ListReleaseConfigPreview(ctx context.Context, req *ListReleaseConfigPreviewReq) (*ListReleaseConfigPreviewRsp, error)
	// GetReleaseDocPreview Deprecated 发布文档预览
	GetReleaseDocPreview(ctx context.Context, req *GetReleaseDocPreviewReq) (*GetReleaseDocPreviewRsp, error)
	// GetReleaseQAPreview Deprecated 发布问答预览
	GetReleaseQAPreview(ctx context.Context, req *GetReleaseQAPreviewReq) (*GetReleaseQAPreviewRsp, error)
	// PreviewRejectedQuestion Deprecated 发布拒答问题预览
	PreviewRejectedQuestion(ctx context.Context, req *PreviewRejectedQuestionReq) (*PreviewRejectedQuestionRsp, error)
	// CreateRelease 新增发布任务
	CreateRelease(ctx context.Context, req *CreateReleaseReq) (*CreateReleaseRsp, error)
	// ListRelease 发布记录列表
	ListRelease(ctx context.Context, req *ListReleaseReq) (*ListReleaseRsp, error)
	// GetReleaseRecordList Deprecated 发布记录列表
	GetReleaseRecordList(ctx context.Context, req *GetReleaseRecordListReq) (*GetReleaseRecordListRsp, error)
	// CheckUnconfirmedQa 是否存在未确认问答
	CheckUnconfirmedQa(ctx context.Context, req *CheckUnconfirmedQaReq) (*CheckUnconfirmedQaRsp, error)
	// DescribeRelease 查询发布任务
	DescribeRelease(ctx context.Context, req *DescribeReleaseReq) (*DescribeReleaseRsp, error)
	// RetryRelease 发布暂停之后再次重新发布
	RetryRelease(ctx context.Context, req *RetryReleaseReq) (*RetryReleaseRsp, error)
	// GetQaSimilar Deprecated 拉取相似问答对
	GetQaSimilar(ctx context.Context, req *GetQaSimilarReq) (*GetQaSimilarRsp, error)
	// ListQaSimilar 拉取相似问答对
	ListQaSimilar(ctx context.Context, req *ListQaSimilarReq) (*ListQaSimilarRsp, error)
	// GetQaSimilarDetail Deprecated 拉取相似问答对详情
	GetQaSimilarDetail(ctx context.Context, req *GetQaSimilarDetailReq) (*GetQaSimilarDetailRsp, error)
	// DescribeQaSimilar 拉取相似问答对详情
	DescribeQaSimilar(ctx context.Context, req *DescribeQaSimilarReq) (*DescribeQaSimilarRsp, error)
	// SubmitQaSimilar 提交相似问答选择结果
	SubmitQaSimilar(ctx context.Context, req *SubmitQaSimilarReq) (*SubmitQaSimilarRsp, error)
	// ListNotify 获取最新的消息
	ListNotify(ctx context.Context, req *ListNotifyReq) (*ListNotifyRsp, error)
	// GetNotify Deprecated 获取最新的消息
	GetNotify(ctx context.Context, req *GetNotifyReq) (*GetNotifyRsp, error)
	// GetHistoryNotify Deprecated 获取历史消息
	GetHistoryNotify(ctx context.Context, req *GetHistoryNotifyReq) (*GetHistoryNotifyRsp, error)
	// ListHistoryNotify 获取历史消息
	ListHistoryNotify(ctx context.Context, req *ListHistoryNotifyReq) (*ListHistoryNotifyRsp, error)
	// ReadNotify 通知已读
	ReadNotify(ctx context.Context, req *ReadNotifyReq) (*ReadNotifyRsp, error)
	// CloseNotify 通知关闭
	CloseNotify(ctx context.Context, req *CloseNotifyReq) (*CloseNotifyRsp, error)
	// GetReferDetail Deprecated 获取来源详情
	GetReferDetail(ctx context.Context, req *GetReferDetailReq) (*GetReferDetailRsp, error)
	// DescribeRefer 获取来源详情
	DescribeRefer(ctx context.Context, req *DescribeReferReq) (*DescribeReferRsp, error)
	// MarkRefer 来源打标
	MarkRefer(ctx context.Context, req *MarkReferReq) (*MarkReferRsp, error)
	// UploadSampleFile Deprecated 上传样本集合
	UploadSampleFile(ctx context.Context, req *UploadSampleReq) (*UploadSampleRsp, error)
	// UploadSampleSet 上传样本集合
	UploadSampleSet(ctx context.Context, req *UploadSampleSetReq) (*UploadSampleSetRsp, error)
	// UploadSampleSetWithCheck 带校验上传样本集合
	UploadSampleSetWithCheck(ctx context.Context, req *UploadSampleSetWithCheckReq) (*UploadSampleSetWithCheckRsp, error)
	// QuerySampleSetList Deprecated 查询样本集列表
	QuerySampleSetList(ctx context.Context, req *QuerySampleReq) (*QuerySampleRsp, error)
	// DeleteSampleFiles Deprecated 批量删除样本集
	DeleteSampleFiles(ctx context.Context, req *DeleteSampleReq) (*DeleteSampleRsp, error)
	// ListSampleSet 查询样本集
	ListSampleSet(ctx context.Context, req *ListSampleSetReq) (*ListSampleSetRsp, error)
	// DeleteSampleSet 删除样本集
	DeleteSampleSet(ctx context.Context, req *DeleteSampleSetReq) (*DeleteSampleSetRsp, error)
	// CreateTest Deprecated 创建评测任务
	CreateTest(ctx context.Context, req *CreateTestReq) (*CreateTestRsp, error)
	// CreateEvaluateTest 创建评测任务
	CreateEvaluateTest(ctx context.Context, req *CreateEvaluateTestReq) (*CreateEvaluateTestRsp, error)
	// QueryTestList Deprecated 条件查询任务列表
	QueryTestList(ctx context.Context, req *QueryTestReq) (*QueryTestRsp, error)
	// ListEvaluateTest 查询任务列表
	ListEvaluateTest(ctx context.Context, req *ListEvaluateTestReq) (*ListEvaluateTestRsp, error)
	// DeleteTest Deprecated 任务删除
	DeleteTest(ctx context.Context, req *DeleteTestReq) (*DeleteTestRsp, error)
	// StopTest Deprecated 任务停止
	StopTest(ctx context.Context, req *StopTestReq) (*StopTestRsp, error)
	// RetryTest Deprecated 任务重试
	RetryTest(ctx context.Context, req *RetryTestReq) (*RetryTestRsp, error)
	// DeleteEvaluateTest 任务删除
	DeleteEvaluateTest(ctx context.Context, req *DeleteEvaluateTestReq) (*DeleteEvaluateTestRsp, error)
	// StopEvaluateTest 任务停止
	StopEvaluateTest(ctx context.Context, req *StopEvaluateTestReq) (*StopEvaluateTestRsp, error)
	// RetryEvaluateTest 任务重试
	RetryEvaluateTest(ctx context.Context, req *RetryEvaluateTestReq) (*RetryEvaluateTestRsp, error)
	// GetOneWaitJudging Deprecated 待标注测试记录详情
	GetOneWaitJudging(ctx context.Context, req *GetOneJudgingReq) (*GetOneJudgingRsp, error)
	// DescribeWaitJudgeRecord 待标注测试记录详情
	DescribeWaitJudgeRecord(ctx context.Context, req *DescribeWaitJudgeRecordReq) (*DescribeWaitJudgeRecordRsp, error)
	// GetRecord Deprecated 查询标注记录详情
	GetRecord(ctx context.Context, req *GetRecordReq) (*GetRecordRsp, error)
	// DescribeRecord 查询标注记录详情
	DescribeRecord(ctx context.Context, req *DescribeRecordReq) (*DescribeRecordRsp, error)
	// JudgeRecord 标注会话
	JudgeRecord(ctx context.Context, req *JudgeReq) (*JudgeRsp, error)
	// GetRejectedQuestionList Deprecated 获取拒答问题列表
	GetRejectedQuestionList(ctx context.Context, req *GetRejectedQuestionListReq) (*GetRejectedQuestionListRsp, error)
	// ListRejectedQuestion 获取拒答问题列表
	ListRejectedQuestion(ctx context.Context, req *ListRejectedQuestionReq) (*ListRejectedQuestionRsp, error)
	// CreateRejectedQuestion 创建拒答问题
	CreateRejectedQuestion(ctx context.Context, req *CreateRejectedQuestionReq) (*CreateRejectedQuestionRsp, error)
	// UpdateRejectedQuestion Deprecated 修改拒答问题
	UpdateRejectedQuestion(ctx context.Context, req *UpdateRejectedQuestionReq) (*UpdateRejectedQuestionRsp, error)
	// ModifyRejectedQuestion 修改拒答问题
	ModifyRejectedQuestion(ctx context.Context, req *ModifyRejectedQuestionReq) (*ModifyRejectedQuestionRsp, error)
	// DeleteRejectedQuestion 删除拒答问题
	DeleteRejectedQuestion(ctx context.Context, req *DeleteRejectedQuestionReq) (*DeleteRejectedQuestionRsp, error)
	// ExportRejectedQuestion 导出拒答问题
	ExportRejectedQuestion(ctx context.Context, req *ExportRejectedQuestionReq) (*ExportRejectedQuestionRsp, error)
	// GetUnsatisfiedReply Deprecated 获取不满意回复
	GetUnsatisfiedReply(ctx context.Context, req *GetUnsatisfiedReplyReq) (*GetUnsatisfiedReplyRsp, error)
	// ListUnsatisfiedReply 获取不满意回复
	ListUnsatisfiedReply(ctx context.Context, req *ListUnsatisfiedReplyReq) (*ListUnsatisfiedReplyRsp, error)
	// IgnoreUnsatisfiedReply 忽略不满意回复
	IgnoreUnsatisfiedReply(ctx context.Context, req *IgnoreUnsatisfiedReplyReq) (*IgnoreUnsatisfiedReplyRsp, error)
	// ExportUnsatisfiedReply 导出不满意回复
	ExportUnsatisfiedReply(ctx context.Context, req *ExportUnsatisfiedReplyReq) (*ExportUnsatisfiedReplyRsp, error)
	// GetUnsatisfiedReplyContext Deprecated 获取不满意回复上下文
	GetUnsatisfiedReplyContext(ctx context.Context, req *GetUnsatisfiedReplyContextReq) (*GetUnsatisfiedReplyContextRsp, error)
	// DescribeUnsatisfiedReplyContext 获取不满意回复上下文
	DescribeUnsatisfiedReplyContext(ctx context.Context, req *DescribeUnsatisfiedReplyReq) (*DescribeUnsatisfiedReplyRsp, error)
	// RecordUserFirstGenQA 记录操作首次生成问答标记
	RecordUserFirstGenQA(ctx context.Context, req *RecordUserFirstGenQAReq) (*RecordUserFirstGenQARsp, error)
	// RecordUserAccessUnCheckQATime 记录访问未检验问答时间
	RecordUserAccessUnCheckQATime(ctx context.Context, req *RecordUserAccessUnCheckQATimeReq) (*RecordUserAccessUnCheckQATimeRsp, error)
	// CreateAttributeLabelV1 创建属性标签
	CreateAttributeLabelV1(ctx context.Context, req *CreateAttributeLabelV1Req) (*CreateAttributeLabelV1Rsp, error)
	// CreateAttributeLabel 创建属性标签
	CreateAttributeLabel(ctx context.Context, req *CreateAttributeLabelReq) (*CreateAttributeLabelRsp, error)
	// DeleteAttributeLabel 删除属性标签
	DeleteAttributeLabel(ctx context.Context, req *DeleteAttributeLabelReq) (*DeleteAttributeLabelRsp, error)
	// UpdateAttributeLabel Deprecated 编辑属性标签
	UpdateAttributeLabel(ctx context.Context, req *UpdateAttributeLabelReq) (*UpdateAttributeLabelRsp, error)
	// ModifyAttributeLabel 编辑属性标签
	ModifyAttributeLabel(ctx context.Context, req *ModifyAttributeLabelReq) (*ModifyAttributeLabelRsp, error)
	// GetAttributeLabelList Deprecated 查询属性标签列表
	GetAttributeLabelList(ctx context.Context, req *GetAttributeLabelListReq) (*GetAttributeLabelListRsp, error)
	// ListAttributeLabel 查询属性标签列表
	ListAttributeLabel(ctx context.Context, req *ListAttributeLabelReq) (*ListAttributeLabelRsp, error)
	// GetAttributeLabelDetail Deprecated 查询属性标签详情
	GetAttributeLabelDetail(ctx context.Context, req *GetAttributeLabelDetailReq) (*GetAttributeLabelDetailRsp, error)
	// DescribeAttributeLabel 查询属性标签详情
	DescribeAttributeLabel(ctx context.Context, req *DescribeAttributeLabelReq) (*DescribeAttributeLabelRsp, error)
	// UploadAttributeLabel 导入属性标签
	UploadAttributeLabel(ctx context.Context, req *UploadAttributeLabelReq) (*UploadAttributeLabelRsp, error)
	// ExportAttributeLabel 导出属性标签
	ExportAttributeLabel(ctx context.Context, req *ExportAttributeLabelReq) (*ExportAttributeLabelRsp, error)
	// CheckAttributeLabelRefer 检查属性下标签是否引用
	CheckAttributeLabelRefer(ctx context.Context, req *CheckAttributeLabelReferReq) (*CheckAttributeLabelReferRsp, error)
	// CheckAttributeLabelExist 检查属性下的标签名是否存在请求
	CheckAttributeLabelExist(ctx context.Context, req *CheckAttributeLabelExistReq) (*CheckAttributeLabelExistRsp, error)
	// CreateAppeal 提交申诉请求申请人工审核
	CreateAppeal(ctx context.Context, req *CreateAppealReq) (*CreateAppealRsp, error)
	// CreateApp 应用
	//  创建应用
	CreateApp(ctx context.Context, req *CreateAppReq) (*CreateAppRsp, error)
	// ModifyApp 修改应用
	ModifyApp(ctx context.Context, req *ModifyAppReq) (*ModifyAppRsp, error)
	// ListApp 获取企业下应用列表
	ListApp(ctx context.Context, req *ListAppReq) (*ListAppRsp, error)
	// DescribeApp 获取企业下应用详情
	DescribeApp(ctx context.Context, req *DescribeAppReq) (*DescribeAppRsp, error)
	// DeleteApp 删除应用
	DeleteApp(ctx context.Context, req *DeleteAppReq) (*DeleteAppRsp, error)
	// ListModel 获取模型列表
	ListModel(ctx context.Context, req *ListModelReq) (*ListModelRsp, error)
	// ListAppCategory 应用类型列表
	ListAppCategory(ctx context.Context, req *ListAppCategoryReq) (*ListAppCategoryRsp, error)
	// GetAppSecret 获取应用密钥
	GetAppSecret(ctx context.Context, req *GetAppSecretReq) (*GetAppSecretRsp, error)
	// GetAppKnowledgeCount 获取知识库知识个数
	GetAppKnowledgeCount(ctx context.Context, req *GetAppKnowledgeCountReq) (*GetAppKnowledgeCountRsp, error)
	// DescribeLicense 获取应用License
	DescribeLicense(ctx context.Context, req *DescribeLicenseReq) (*DescribeLicenseRsp, error)
	// GetTaskStatus 获取任务状态
	GetTaskStatus(ctx context.Context, req *GetTaskStatusReq) (*GetTaskStatusRsp, error)
	// DescribeModel 模型详情
	DescribeModel(ctx context.Context, req *DescribeModelReq) (*DescribeModelRsp, error)
	// ListClassifyLabel 标签列表
	ListClassifyLabel(ctx context.Context, req *ListClassifyLabelReq) (*ListClassifyLabelRsp, error)
	// ListSummaryPrompt 摘要Prompt列表
	ListSummaryPrompt(ctx context.Context, req *ListSummaryPromptReq) (*ListSummaryPromptRsp, error)
	// AddAgentFeedback 添加Agent反馈
	AddAgentFeedback(ctx context.Context, req *AddAgentFeedbackReq) (*AddAgentFeedbackRsp, error)
	// DescribeAgentFeedback 获取Agent详情
	DescribeAgentFeedback(ctx context.Context, req *DescribeAgentFeedbackReq) (*DescribeAgentFeedbackRsp, error)
	// DeleteAgentFeedback 删除Agent反馈
	DeleteAgentFeedback(ctx context.Context, req *DeleteAgentFeedbackReq) (*DeleteAgentFeedbackRsp, error)
	// ListAgentFeedback 查询Agent反馈信息列表
	ListAgentFeedback(ctx context.Context, req *ListAgentFeedbackReq) (*ListAgentFeedbackRsp, error)
	// AddFeedback 添加反馈
	AddFeedback(ctx context.Context, req *AddFeedbackReq) (*AddFeedbackRsp, error)
	// ListFeedback 查询反馈信息列表
	ListFeedback(ctx context.Context, req *ListFeedbackReq) (*ListFeedbackRsp, error)
	// DescribeFeedback 查询反馈信息详情
	DescribeFeedback(ctx context.Context, req *DescribeFeedbackReq) (*DescribeFeedbackRsp, error)
	// DeleteFeedback 删除反馈记录详情
	DeleteFeedback(ctx context.Context, req *DeleteFeedbackReq) (*DeleteFeedbackRsp, error)
	// ListExperienceCenterApp ListExperienceApp 体验中心-获取体验应用列表
	ListExperienceCenterApp(ctx context.Context, req *ListExperienceCenterAppReq) (*ListExperienceCenterAppRsp, error)
	// CreateAppByExperienceApp CreateAppByExperienceApp 基于体验应用，创建app
	CreateAppByExperienceApp(ctx context.Context, req *CreateAppByExperienceAppReq) (*CreateAppByExperienceAppRsp, error)
	// GetDisplayDocs 获取体验应用对外显示的文档列表
	GetDisplayDocs(ctx context.Context, req *GetDisplayDocsReq) (*GetDisplayDocsRsp, error)
	// ListPackage 资源包列表
	ListPackage(ctx context.Context, req *ListPackageReq) (*ListPackageRsp, error)
	// ListConcurrency 并发扩展
	ListConcurrency(ctx context.Context, req *ListConcurrencyReq) (*ListConcurrencyRsp, error)
	// ListKnowledgeCapacity 知识库容量扩展
	ListKnowledgeCapacity(ctx context.Context, req *ListKnowledgeCapacityReq) (*ListKnowledgeCapacityRsp, error)
	// DescribeTokenUsage 接口调用token
	DescribeTokenUsage(ctx context.Context, req *DescribeTokenUsageReq) (*DescribeTokenUsageRsp, error)
	// DescribeTokenUsageGraph 接口调用token折线图
	DescribeTokenUsageGraph(ctx context.Context, req *DescribeTokenUsageGraphReq) (*DescribeTokenUsageGraphRsp, error)
	// ListProducts 获取产品列表
	ListProducts(ctx context.Context, req *ListProductReq) (*ListProductRsp, error)
	// ListAccount 获取账户列表
	ListAccount(ctx context.Context, req *ListAccountReq) (*ListAccountRsp, error)
	// ExportBillingInfo 导出计费详情
	ExportBillingInfo(ctx context.Context, req *ExportBillingInfoReq) (*ExportBillingInfoRsp, error)
	// DescribeCallStatsGraph 接口调用折线图
	DescribeCallStatsGraph(ctx context.Context, req *DescribeCallStatsGraphReq) (*DescribeCallStatsGraphRsp, error)
	// GetSearchResourceStatus 获取搜索引擎资源状态
	GetSearchResourceStatus(ctx context.Context, req *GetSearchResourceStatusReq) (*GetSearchResourceStatusRsp, error)
	// ListPostpaidProduct 获取后付费产品列表
	ListPostpaidProduct(ctx context.Context, req *ListPostpaidProductReq) (*ListPostpaidProductRsp, error)
	// ModifyPostpaidSwitch 修改后付费开关状态
	ModifyPostpaidSwitch(ctx context.Context, req *ModifyPostpaidSwitchReq) (*ModifyPostpaidSwitchRsp, error)
	// ListInvalidResource 查询账户未生效资源包
	ListInvalidResource(ctx context.Context, req *ListInvalidResourceReq) (*ListInvalidResourceRsp, error)
	// CreatePostPayResource 创建单个后付费资源
	CreatePostPayResource(ctx context.Context, req *CreatePostPayResourceReq) (*CreatePostPayResourceRsp, error)
	// DescribePostpaidSwitch 查询后付费开关状态
	DescribePostpaidSwitch(ctx context.Context, req *DescribePostpaidSwitchReq) (*DescribePostpaidSwitchRsp, error)
	// DescribeSearchStatsGraph 查询搜索服务调用折线图
	DescribeSearchStatsGraph(ctx context.Context, req *DescribeSearchStatsGraphReq) (*DescribeSearchStatsGraphRsp, error)
	// AddEventReport 统计上报-事件上报
	AddEventReport(ctx context.Context, req *AddEventReportReq) (*AddEventReportRsp, error)
	// BatchEventReport 统计上报-批量事件上报
	BatchEventReport(ctx context.Context, req *BatchEventReportReq) (*BatchEventReportRsp, error)
	// DescribeKnowledgeUsage 知识库容量统计
	DescribeKnowledgeUsage(ctx context.Context, req *DescribeKnowledgeUsageReq) (*DescribeKnowledgeUsageRsp, error)
	// DescribeKnowledgeUsagePieGraph 知识库容量统计饼图
	DescribeKnowledgeUsagePieGraph(ctx context.Context, req *DescribeKnowledgeUsagePieGraphReq) (*DescribeKnowledgeUsagePieGraphRsp, error)
	// ListAppKnowledgeDetail 获取应用知识库调用明细列表
	ListAppKnowledgeDetail(ctx context.Context, req *ListAppKnowledgeDetailReq) (*ListAppKnowledgeDetailRsp, error)
	// ListUsageCallDetail 获取单次调用详情
	ListUsageCallDetail(ctx context.Context, req *ListUsageCallDetailReq) (*ListUsageCallDetailRsp, error)
	// DescribeConcurrencyUsage 并发调用
	DescribeConcurrencyUsage(ctx context.Context, req *DescribeConcurrencyUsageReq) (*DescribeConcurrencyUsageRsp, error)
	// ListConcurrencyDetail 并发调用明细
	ListConcurrencyDetail(ctx context.Context, req *ListConcurrencyDetailReq) (*ListConcurrencyDetailRsp, error)
	// DescribeConcurrencyUsageGraph 并发调用折线图
	DescribeConcurrencyUsageGraph(ctx context.Context, req *DescribeConcurrencyUsageGraphReq) (*DescribeConcurrencyUsageGraphRsp, error)
	// ExportConcurrencyInfo 导出并发详情
	ExportConcurrencyInfo(ctx context.Context, req *ExportConcurrencyInfoReq) (*ExportConcurrencyInfoRsp, error)
	// ExportKnowledgeInfo 导出知识库容量详情
	ExportKnowledgeInfo(ctx context.Context, req *ExportKnowledgeInfoReq) (*ExportKnowledgeInfoRsp, error)
	// ListReleaseLabelPreview 发布标签预览请求
	ListReleaseLabelPreview(ctx context.Context, req *ListReleaseLabelPreviewReq) (*ListReleaseLabelPreviewRsp, error)
	// ListReleaseSynonymsPreview 发布同义词预览请求
	ListReleaseSynonymsPreview(ctx context.Context, req *ListReleaseSynonymsPreviewReq) (*ListReleaseSynonymsPreviewRsp, error)
	// ModifyAppBase 修改应用基础信息
	ModifyAppBase(ctx context.Context, req *ModifyAppBaseReq) (*ModifyAppBaseRsp, error)
	// CreateCustomModel 创建自定义模型
	CreateCustomModel(ctx context.Context, req *CreateCustomModelReq) (*CreateCustomModelRsp, error)
	// ModifyCustomModel 编辑自定义模型
	ModifyCustomModel(ctx context.Context, req *ModifyCustomModelReq) (*ModifyCustomModelRsp, error)
	// DeleteCustomModel 删除自定义模型
	DeleteCustomModel(ctx context.Context, req *DeleteCustomModelReq) (*DeleteCustomModelRsp, error)
	// DescribeCustomModel 查询自定义模型详情
	DescribeCustomModel(ctx context.Context, req *DescribeCustomModelReq) (*DescribeCustomModelRsp, error)
	// DescribeModelApps 获取模型关联的应用
	DescribeModelApps(ctx context.Context, req *DescribeModelAppsReq) (*DescribeModelAppsRsp, error)
	// GetUserGuideViewInfos 查看用户引导是否观看信息
	GetUserGuideViewInfos(ctx context.Context, req *GetUserGuideViewInfosReq) (*GetUserGuideViewInfosRsp, error)
	// SetUserGuideViewInfo 设置用户引导已观看
	SetUserGuideViewInfo(ctx context.Context, req *SetUserGuideViewInfoReq) (*SetUserGuideViewInfoRsp, error)
	// ExportMinuteDosage 导出调用统计并发QPM、TPM分钟用量信息
	ExportMinuteDosage(ctx context.Context, req *ExportMinuteDosageReq) (*ExportMinuteDosageRsp, error)
	// GetImagePreview 图片预览请求,私有读
	GetImagePreview(ctx context.Context, req *GetImagePreviewReq) (*GetImagePreviewRsp, error)
	// ListVoice 查询音色列表
	ListVoice(ctx context.Context, req *ListVoiceReq) (*ListVoiceRsp, error)
	// ListDigitalHuman 查询数智人列表
	ListDigitalHuman(ctx context.Context, req *ListDigitalHumanReq) (*ListDigitalHumanRsp, error)
	// GetPromptTemplateList GetPromptTemplateList 模板中心列表
	GetPromptTemplateList(ctx context.Context, req *GetPromptTemplateListReq) (*GetPromptTemplateListRsp, error)
	// GetMsgLogList 获取消息日志列表
	GetMsgLogList(ctx context.Context, req *GetMsgLogListReq) (*GetMsgLogListRsp, error)
	// ExportMsgLog 导出消息日志
	ExportMsgLog(ctx context.Context, req *ExportMsgLogReq) (*ExportMsgLogRsp, error)
	// ModifyConcurrencyRule 修改并发规则
	ModifyConcurrencyRule(ctx context.Context, req *ModifyConcurrencyRuleReq) (*ModifyConcurrencyRuleRsp, error)
	// DescribeConcurrencyRule 查看并发规则
	DescribeConcurrencyRule(ctx context.Context, req *DescribeConcurrencyRuleReq) (*DescribeConcurrencyRuleRsp, error)
	// CheckWhitelist 检查是否命中白名单
	CheckWhitelist(ctx context.Context, req *CheckWhitelistReq) (*CheckWhitelistRsp, error)
	// GetMsgLogOverview 获取消息日志概览
	GetMsgLogOverview(ctx context.Context, req *GetMsgLogOverviewReq) (*GetMsgLogOverviewRsp, error)
	// GetMsgLogCountTrend 获取消息数趋势
	GetMsgLogCountTrend(ctx context.Context, req *GetMsgLogCountTrendReq) (*GetMsgLogCountTrendRsp, error)
	// GetMsgLogUserCountTrend 获取消息互动用户数趋势
	GetMsgLogUserCountTrend(ctx context.Context, req *GetMsgLogUserCountTrendReq) (*GetMsgLogUserCountTrendRsp, error)
	// GetMsgLogFeedbackCountTrend 获取消息数反馈数趋势
	GetMsgLogFeedbackCountTrend(ctx context.Context, req *GetMsgLogFeedbackCountTrendReq) (*GetMsgLogFeedbackCountTrendRsp, error)
	// ExportMsgLogStatistical 导出消息统计
	ExportMsgLogStatistical(ctx context.Context, req *ExportMsgLogStatisticalReq) (*ExportMsgLogStatisticalRsp, error)
	// CreateApproval 创建审批单
	CreateApproval(ctx context.Context, req *CreateApprovalReq) (*CreateApprovalRsp, error)
	// UpdateApprovalStatus 更新审批单状态
	UpdateApprovalStatus(ctx context.Context, req *UpdateApprovalStatusReq) (*UpdateApprovalStatusRsp, error)
	// GetLastApproval 获取最新一条审批
	GetLastApproval(ctx context.Context, req *GetLastApprovalReq) (*GetLastApprovalRsp, error)
}

func AdminService_CorpInfo_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &CorpInfoReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(AdminService).CorpInfo(ctx, reqbody.(*CorpInfoReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func AdminService_DescribeCorp_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &DescribeCorpReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(AdminService).DescribeCorp(ctx, reqbody.(*DescribeCorpReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func AdminService_ListCorpStaff_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &ListCorpStaffReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(AdminService).ListCorpStaff(ctx, reqbody.(*ListCorpStaffReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func AdminService_DescribeStorageCredential_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &DescribeStorageCredentialReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(AdminService).DescribeStorageCredential(ctx, reqbody.(*DescribeStorageCredentialReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func AdminService_GetCredential_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &GetCredentialReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(AdminService).GetCredential(ctx, reqbody.(*GetCredentialReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func AdminService_GetAuditSwitch_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &GetAuditSwitchReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(AdminService).GetAuditSwitch(ctx, reqbody.(*GetAuditSwitchReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func AdminService_DescribeAuditSwitch_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &DescribeAuditSwitchReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(AdminService).DescribeAuditSwitch(ctx, reqbody.(*DescribeAuditSwitchReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func AdminService_ListDoc_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &ListDocReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(AdminService).ListDoc(ctx, reqbody.(*ListDocReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func AdminService_ListDocV1_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &ListDocV1Req{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(AdminService).ListDocV1(ctx, reqbody.(*ListDocV1Req))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func AdminService_SaveDocV1_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &SaveDocV1Req{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(AdminService).SaveDocV1(ctx, reqbody.(*SaveDocV1Req))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func AdminService_ModifyDoc_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &ModifyDocReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(AdminService).ModifyDoc(ctx, reqbody.(*ModifyDocReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func AdminService_ModifyDocAttrRange_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &ModifyDocAttrRangeReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(AdminService).ModifyDocAttrRange(ctx, reqbody.(*ModifyDocAttrRangeReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func AdminService_ModifyDocV1_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &ModifyDocV1Req{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(AdminService).ModifyDocV1(ctx, reqbody.(*ModifyDocV1Req))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func AdminService_DeleteDoc_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &DeleteDocReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(AdminService).DeleteDoc(ctx, reqbody.(*DeleteDocReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func AdminService_DescribeDoc_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &DescribeDocReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(AdminService).DescribeDoc(ctx, reqbody.(*DescribeDocReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func AdminService_ReferDoc_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &ReferDocReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(AdminService).ReferDoc(ctx, reqbody.(*ReferDocReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func AdminService_StartCreateQA_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &StartCreateQAReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(AdminService).StartCreateQA(ctx, reqbody.(*StartCreateQAReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func AdminService_GetDocPreview_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &GetDocPreviewReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(AdminService).GetDocPreview(ctx, reqbody.(*GetDocPreviewReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func AdminService_GenerateQA_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &GenerateQAReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(AdminService).GenerateQA(ctx, reqbody.(*GenerateQAReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func AdminService_GetSelectDoc_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &GetSelectDocReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(AdminService).GetSelectDoc(ctx, reqbody.(*GetSelectDocReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func AdminService_ListSelectDoc_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &ListSelectDocReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(AdminService).ListSelectDoc(ctx, reqbody.(*ListSelectDocReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func AdminService_StopDocParse_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &StopDocParseReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(AdminService).StopDocParse(ctx, reqbody.(*StopDocParseReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func AdminService_RetryDocParse_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &RetryDocParseReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(AdminService).RetryDocParse(ctx, reqbody.(*RetryDocParseReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func AdminService_RetryDocAudit_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &RetryDocAuditReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(AdminService).RetryDocAudit(ctx, reqbody.(*RetryDocAuditReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func AdminService_CreateRobot_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &CreateRobotReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(AdminService).CreateRobot(ctx, reqbody.(*CreateRobotReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func AdminService_DeleteRobot_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &DeleteRobotReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(AdminService).DeleteRobot(ctx, reqbody.(*DeleteRobotReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func AdminService_ModifyRobot_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &ModifyRobotReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(AdminService).ModifyRobot(ctx, reqbody.(*ModifyRobotReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func AdminService_ModifyRobotBasicConfig_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &ModifyRobotBasicConfigReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(AdminService).ModifyRobotBasicConfig(ctx, reqbody.(*ModifyRobotBasicConfigReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func AdminService_UpdateRobot_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &UpdateRobotReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(AdminService).UpdateRobot(ctx, reqbody.(*UpdateRobotReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func AdminService_RobotInfo_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &RobotInfoReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(AdminService).RobotInfo(ctx, reqbody.(*RobotInfoReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func AdminService_DescribeRobot_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &DescribeRobotReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(AdminService).DescribeRobot(ctx, reqbody.(*DescribeRobotReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func AdminService_ListRobot_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &ListRobotReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(AdminService).ListRobot(ctx, reqbody.(*ListRobotReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func AdminService_ListRobotV1_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &ListRobotV1Req{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(AdminService).ListRobotV1(ctx, reqbody.(*ListRobotV1Req))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func AdminService_AccountInfo_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &AccountInfoReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(AdminService).AccountInfo(ctx, reqbody.(*AccountInfoReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func AdminService_DescribeAccount_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &DescribeAccountReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(AdminService).DescribeAccount(ctx, reqbody.(*DescribeAccountReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func AdminService_ListRole_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &ListRoleReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(AdminService).ListRole(ctx, reqbody.(*ListRoleReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func AdminService_ListRobotRole_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &ListRobotRoleReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(AdminService).ListRobotRole(ctx, reqbody.(*ListRobotRoleReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func AdminService_ListQACate_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &ListQACateReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(AdminService).ListQACate(ctx, reqbody.(*ListQACateReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func AdminService_ListQACateV1_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &ListQACateV1Req{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(AdminService).ListQACateV1(ctx, reqbody.(*ListQACateV1Req))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func AdminService_CreateQACate_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &CreateQACateReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(AdminService).CreateQACate(ctx, reqbody.(*CreateQACateReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func AdminService_CreateQACateV1_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &CreateQACateV1Req{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(AdminService).CreateQACateV1(ctx, reqbody.(*CreateQACateV1Req))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func AdminService_UpdateQACate_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &UpdateQACateReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(AdminService).UpdateQACate(ctx, reqbody.(*UpdateQACateReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func AdminService_ModifyQACate_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &ModifyQACateReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(AdminService).ModifyQACate(ctx, reqbody.(*ModifyQACateReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func AdminService_DeleteQACate_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &DeleteQACateReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(AdminService).DeleteQACate(ctx, reqbody.(*DeleteQACateReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func AdminService_GetQAList_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &GetQAListReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(AdminService).GetQAList(ctx, reqbody.(*GetQAListReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func AdminService_ListQA_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &ListQAReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(AdminService).ListQA(ctx, reqbody.(*ListQAReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func AdminService_GetQADetail_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &GetQADetailReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(AdminService).GetQADetail(ctx, reqbody.(*GetQADetailReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func AdminService_DescribeQA_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &DescribeQAReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(AdminService).DescribeQA(ctx, reqbody.(*DescribeQAReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func AdminService_CreateQA_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &CreateQAReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(AdminService).CreateQA(ctx, reqbody.(*CreateQAReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func AdminService_CreateQAV1_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &CreateQAV1Req{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(AdminService).CreateQAV1(ctx, reqbody.(*CreateQAV1Req))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func AdminService_UpdateQA_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &UpdateQAReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(AdminService).UpdateQA(ctx, reqbody.(*UpdateQAReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func AdminService_ModifyQA_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &ModifyQAReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(AdminService).ModifyQA(ctx, reqbody.(*ModifyQAReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func AdminService_DeleteQA_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &DeleteQAReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(AdminService).DeleteQA(ctx, reqbody.(*DeleteQAReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func AdminService_DeleteQAV1_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &DeleteQAV1Req{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(AdminService).DeleteQAV1(ctx, reqbody.(*DeleteQAV1Req))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func AdminService_VerifyQA_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &VerifyQAReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(AdminService).VerifyQA(ctx, reqbody.(*VerifyQAReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func AdminService_VerifyQAV1_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &VerifyQAV1Req{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(AdminService).VerifyQAV1(ctx, reqbody.(*VerifyQAV1Req))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func AdminService_GroupQA_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &GroupQAReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(AdminService).GroupQA(ctx, reqbody.(*GroupQAReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func AdminService_ModifyQAAttrRange_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &ModifyQAAttrRangeReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(AdminService).ModifyQAAttrRange(ctx, reqbody.(*ModifyQAAttrRangeReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func AdminService_ExportQAList_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &ExportQAListReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(AdminService).ExportQAList(ctx, reqbody.(*ExportQAListReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func AdminService_ExportQAListV1_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &ExportQAListReqV1{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(AdminService).ExportQAListV1(ctx, reqbody.(*ExportQAListReqV1))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func AdminService_GetReleaseInfo_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &GetReleaseInfoReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(AdminService).GetReleaseInfo(ctx, reqbody.(*GetReleaseInfoReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func AdminService_DescribeReleaseInfo_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &DescribeReleaseInfoReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(AdminService).DescribeReleaseInfo(ctx, reqbody.(*DescribeReleaseInfoReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func AdminService_ListReleaseDocPreview_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &ListReleaseDocPreviewReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(AdminService).ListReleaseDocPreview(ctx, reqbody.(*ListReleaseDocPreviewReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func AdminService_ListReleaseQAPreview_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &ListReleaseQAPreviewReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(AdminService).ListReleaseQAPreview(ctx, reqbody.(*ListReleaseQAPreviewReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func AdminService_ListRejectedQuestionPreview_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &ListRejectedQuestionPreviewReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(AdminService).ListRejectedQuestionPreview(ctx, reqbody.(*ListRejectedQuestionPreviewReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func AdminService_ListReleaseConfigPreview_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &ListReleaseConfigPreviewReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(AdminService).ListReleaseConfigPreview(ctx, reqbody.(*ListReleaseConfigPreviewReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func AdminService_GetReleaseDocPreview_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &GetReleaseDocPreviewReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(AdminService).GetReleaseDocPreview(ctx, reqbody.(*GetReleaseDocPreviewReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func AdminService_GetReleaseQAPreview_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &GetReleaseQAPreviewReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(AdminService).GetReleaseQAPreview(ctx, reqbody.(*GetReleaseQAPreviewReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func AdminService_PreviewRejectedQuestion_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &PreviewRejectedQuestionReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(AdminService).PreviewRejectedQuestion(ctx, reqbody.(*PreviewRejectedQuestionReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func AdminService_CreateRelease_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &CreateReleaseReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(AdminService).CreateRelease(ctx, reqbody.(*CreateReleaseReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func AdminService_ListRelease_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &ListReleaseReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(AdminService).ListRelease(ctx, reqbody.(*ListReleaseReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func AdminService_GetReleaseRecordList_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &GetReleaseRecordListReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(AdminService).GetReleaseRecordList(ctx, reqbody.(*GetReleaseRecordListReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func AdminService_CheckUnconfirmedQa_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &CheckUnconfirmedQaReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(AdminService).CheckUnconfirmedQa(ctx, reqbody.(*CheckUnconfirmedQaReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func AdminService_DescribeRelease_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &DescribeReleaseReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(AdminService).DescribeRelease(ctx, reqbody.(*DescribeReleaseReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func AdminService_RetryRelease_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &RetryReleaseReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(AdminService).RetryRelease(ctx, reqbody.(*RetryReleaseReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func AdminService_GetQaSimilar_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &GetQaSimilarReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(AdminService).GetQaSimilar(ctx, reqbody.(*GetQaSimilarReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func AdminService_ListQaSimilar_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &ListQaSimilarReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(AdminService).ListQaSimilar(ctx, reqbody.(*ListQaSimilarReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func AdminService_GetQaSimilarDetail_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &GetQaSimilarDetailReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(AdminService).GetQaSimilarDetail(ctx, reqbody.(*GetQaSimilarDetailReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func AdminService_DescribeQaSimilar_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &DescribeQaSimilarReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(AdminService).DescribeQaSimilar(ctx, reqbody.(*DescribeQaSimilarReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func AdminService_SubmitQaSimilar_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &SubmitQaSimilarReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(AdminService).SubmitQaSimilar(ctx, reqbody.(*SubmitQaSimilarReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func AdminService_ListNotify_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &ListNotifyReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(AdminService).ListNotify(ctx, reqbody.(*ListNotifyReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func AdminService_GetNotify_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &GetNotifyReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(AdminService).GetNotify(ctx, reqbody.(*GetNotifyReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func AdminService_GetHistoryNotify_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &GetHistoryNotifyReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(AdminService).GetHistoryNotify(ctx, reqbody.(*GetHistoryNotifyReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func AdminService_ListHistoryNotify_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &ListHistoryNotifyReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(AdminService).ListHistoryNotify(ctx, reqbody.(*ListHistoryNotifyReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func AdminService_ReadNotify_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &ReadNotifyReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(AdminService).ReadNotify(ctx, reqbody.(*ReadNotifyReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func AdminService_CloseNotify_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &CloseNotifyReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(AdminService).CloseNotify(ctx, reqbody.(*CloseNotifyReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func AdminService_GetReferDetail_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &GetReferDetailReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(AdminService).GetReferDetail(ctx, reqbody.(*GetReferDetailReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func AdminService_DescribeRefer_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &DescribeReferReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(AdminService).DescribeRefer(ctx, reqbody.(*DescribeReferReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func AdminService_MarkRefer_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &MarkReferReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(AdminService).MarkRefer(ctx, reqbody.(*MarkReferReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func AdminService_UploadSampleFile_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &UploadSampleReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(AdminService).UploadSampleFile(ctx, reqbody.(*UploadSampleReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func AdminService_UploadSampleSet_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &UploadSampleSetReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(AdminService).UploadSampleSet(ctx, reqbody.(*UploadSampleSetReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func AdminService_UploadSampleSetWithCheck_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &UploadSampleSetWithCheckReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(AdminService).UploadSampleSetWithCheck(ctx, reqbody.(*UploadSampleSetWithCheckReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func AdminService_QuerySampleSetList_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &QuerySampleReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(AdminService).QuerySampleSetList(ctx, reqbody.(*QuerySampleReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func AdminService_DeleteSampleFiles_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &DeleteSampleReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(AdminService).DeleteSampleFiles(ctx, reqbody.(*DeleteSampleReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func AdminService_ListSampleSet_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &ListSampleSetReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(AdminService).ListSampleSet(ctx, reqbody.(*ListSampleSetReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func AdminService_DeleteSampleSet_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &DeleteSampleSetReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(AdminService).DeleteSampleSet(ctx, reqbody.(*DeleteSampleSetReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func AdminService_CreateTest_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &CreateTestReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(AdminService).CreateTest(ctx, reqbody.(*CreateTestReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func AdminService_CreateEvaluateTest_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &CreateEvaluateTestReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(AdminService).CreateEvaluateTest(ctx, reqbody.(*CreateEvaluateTestReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func AdminService_QueryTestList_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &QueryTestReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(AdminService).QueryTestList(ctx, reqbody.(*QueryTestReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func AdminService_ListEvaluateTest_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &ListEvaluateTestReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(AdminService).ListEvaluateTest(ctx, reqbody.(*ListEvaluateTestReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func AdminService_DeleteTest_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &DeleteTestReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(AdminService).DeleteTest(ctx, reqbody.(*DeleteTestReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func AdminService_StopTest_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &StopTestReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(AdminService).StopTest(ctx, reqbody.(*StopTestReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func AdminService_RetryTest_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &RetryTestReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(AdminService).RetryTest(ctx, reqbody.(*RetryTestReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func AdminService_DeleteEvaluateTest_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &DeleteEvaluateTestReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(AdminService).DeleteEvaluateTest(ctx, reqbody.(*DeleteEvaluateTestReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func AdminService_StopEvaluateTest_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &StopEvaluateTestReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(AdminService).StopEvaluateTest(ctx, reqbody.(*StopEvaluateTestReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func AdminService_RetryEvaluateTest_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &RetryEvaluateTestReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(AdminService).RetryEvaluateTest(ctx, reqbody.(*RetryEvaluateTestReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func AdminService_GetOneWaitJudging_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &GetOneJudgingReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(AdminService).GetOneWaitJudging(ctx, reqbody.(*GetOneJudgingReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func AdminService_DescribeWaitJudgeRecord_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &DescribeWaitJudgeRecordReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(AdminService).DescribeWaitJudgeRecord(ctx, reqbody.(*DescribeWaitJudgeRecordReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func AdminService_GetRecord_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &GetRecordReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(AdminService).GetRecord(ctx, reqbody.(*GetRecordReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func AdminService_DescribeRecord_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &DescribeRecordReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(AdminService).DescribeRecord(ctx, reqbody.(*DescribeRecordReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func AdminService_JudgeRecord_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &JudgeReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(AdminService).JudgeRecord(ctx, reqbody.(*JudgeReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func AdminService_GetRejectedQuestionList_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &GetRejectedQuestionListReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(AdminService).GetRejectedQuestionList(ctx, reqbody.(*GetRejectedQuestionListReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func AdminService_ListRejectedQuestion_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &ListRejectedQuestionReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(AdminService).ListRejectedQuestion(ctx, reqbody.(*ListRejectedQuestionReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func AdminService_CreateRejectedQuestion_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &CreateRejectedQuestionReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(AdminService).CreateRejectedQuestion(ctx, reqbody.(*CreateRejectedQuestionReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func AdminService_UpdateRejectedQuestion_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &UpdateRejectedQuestionReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(AdminService).UpdateRejectedQuestion(ctx, reqbody.(*UpdateRejectedQuestionReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func AdminService_ModifyRejectedQuestion_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &ModifyRejectedQuestionReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(AdminService).ModifyRejectedQuestion(ctx, reqbody.(*ModifyRejectedQuestionReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func AdminService_DeleteRejectedQuestion_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &DeleteRejectedQuestionReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(AdminService).DeleteRejectedQuestion(ctx, reqbody.(*DeleteRejectedQuestionReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func AdminService_ExportRejectedQuestion_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &ExportRejectedQuestionReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(AdminService).ExportRejectedQuestion(ctx, reqbody.(*ExportRejectedQuestionReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func AdminService_GetUnsatisfiedReply_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &GetUnsatisfiedReplyReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(AdminService).GetUnsatisfiedReply(ctx, reqbody.(*GetUnsatisfiedReplyReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func AdminService_ListUnsatisfiedReply_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &ListUnsatisfiedReplyReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(AdminService).ListUnsatisfiedReply(ctx, reqbody.(*ListUnsatisfiedReplyReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func AdminService_IgnoreUnsatisfiedReply_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &IgnoreUnsatisfiedReplyReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(AdminService).IgnoreUnsatisfiedReply(ctx, reqbody.(*IgnoreUnsatisfiedReplyReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func AdminService_ExportUnsatisfiedReply_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &ExportUnsatisfiedReplyReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(AdminService).ExportUnsatisfiedReply(ctx, reqbody.(*ExportUnsatisfiedReplyReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func AdminService_GetUnsatisfiedReplyContext_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &GetUnsatisfiedReplyContextReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(AdminService).GetUnsatisfiedReplyContext(ctx, reqbody.(*GetUnsatisfiedReplyContextReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func AdminService_DescribeUnsatisfiedReplyContext_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &DescribeUnsatisfiedReplyReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(AdminService).DescribeUnsatisfiedReplyContext(ctx, reqbody.(*DescribeUnsatisfiedReplyReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func AdminService_RecordUserFirstGenQA_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &RecordUserFirstGenQAReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(AdminService).RecordUserFirstGenQA(ctx, reqbody.(*RecordUserFirstGenQAReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func AdminService_RecordUserAccessUnCheckQATime_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &RecordUserAccessUnCheckQATimeReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(AdminService).RecordUserAccessUnCheckQATime(ctx, reqbody.(*RecordUserAccessUnCheckQATimeReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func AdminService_CreateAttributeLabelV1_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &CreateAttributeLabelV1Req{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(AdminService).CreateAttributeLabelV1(ctx, reqbody.(*CreateAttributeLabelV1Req))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func AdminService_CreateAttributeLabel_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &CreateAttributeLabelReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(AdminService).CreateAttributeLabel(ctx, reqbody.(*CreateAttributeLabelReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func AdminService_DeleteAttributeLabel_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &DeleteAttributeLabelReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(AdminService).DeleteAttributeLabel(ctx, reqbody.(*DeleteAttributeLabelReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func AdminService_UpdateAttributeLabel_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &UpdateAttributeLabelReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(AdminService).UpdateAttributeLabel(ctx, reqbody.(*UpdateAttributeLabelReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func AdminService_ModifyAttributeLabel_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &ModifyAttributeLabelReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(AdminService).ModifyAttributeLabel(ctx, reqbody.(*ModifyAttributeLabelReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func AdminService_GetAttributeLabelList_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &GetAttributeLabelListReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(AdminService).GetAttributeLabelList(ctx, reqbody.(*GetAttributeLabelListReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func AdminService_ListAttributeLabel_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &ListAttributeLabelReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(AdminService).ListAttributeLabel(ctx, reqbody.(*ListAttributeLabelReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func AdminService_GetAttributeLabelDetail_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &GetAttributeLabelDetailReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(AdminService).GetAttributeLabelDetail(ctx, reqbody.(*GetAttributeLabelDetailReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func AdminService_DescribeAttributeLabel_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &DescribeAttributeLabelReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(AdminService).DescribeAttributeLabel(ctx, reqbody.(*DescribeAttributeLabelReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func AdminService_UploadAttributeLabel_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &UploadAttributeLabelReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(AdminService).UploadAttributeLabel(ctx, reqbody.(*UploadAttributeLabelReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func AdminService_ExportAttributeLabel_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &ExportAttributeLabelReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(AdminService).ExportAttributeLabel(ctx, reqbody.(*ExportAttributeLabelReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func AdminService_CheckAttributeLabelRefer_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &CheckAttributeLabelReferReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(AdminService).CheckAttributeLabelRefer(ctx, reqbody.(*CheckAttributeLabelReferReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func AdminService_CheckAttributeLabelExist_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &CheckAttributeLabelExistReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(AdminService).CheckAttributeLabelExist(ctx, reqbody.(*CheckAttributeLabelExistReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func AdminService_CreateAppeal_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &CreateAppealReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(AdminService).CreateAppeal(ctx, reqbody.(*CreateAppealReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func AdminService_CreateApp_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &CreateAppReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(AdminService).CreateApp(ctx, reqbody.(*CreateAppReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func AdminService_ModifyApp_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &ModifyAppReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(AdminService).ModifyApp(ctx, reqbody.(*ModifyAppReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func AdminService_ListApp_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &ListAppReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(AdminService).ListApp(ctx, reqbody.(*ListAppReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func AdminService_DescribeApp_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &DescribeAppReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(AdminService).DescribeApp(ctx, reqbody.(*DescribeAppReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func AdminService_DeleteApp_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &DeleteAppReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(AdminService).DeleteApp(ctx, reqbody.(*DeleteAppReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func AdminService_ListModel_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &ListModelReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(AdminService).ListModel(ctx, reqbody.(*ListModelReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func AdminService_ListAppCategory_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &ListAppCategoryReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(AdminService).ListAppCategory(ctx, reqbody.(*ListAppCategoryReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func AdminService_GetAppSecret_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &GetAppSecretReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(AdminService).GetAppSecret(ctx, reqbody.(*GetAppSecretReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func AdminService_GetAppKnowledgeCount_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &GetAppKnowledgeCountReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(AdminService).GetAppKnowledgeCount(ctx, reqbody.(*GetAppKnowledgeCountReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func AdminService_DescribeLicense_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &DescribeLicenseReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(AdminService).DescribeLicense(ctx, reqbody.(*DescribeLicenseReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func AdminService_GetTaskStatus_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &GetTaskStatusReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(AdminService).GetTaskStatus(ctx, reqbody.(*GetTaskStatusReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func AdminService_DescribeModel_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &DescribeModelReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(AdminService).DescribeModel(ctx, reqbody.(*DescribeModelReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func AdminService_ListClassifyLabel_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &ListClassifyLabelReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(AdminService).ListClassifyLabel(ctx, reqbody.(*ListClassifyLabelReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func AdminService_ListSummaryPrompt_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &ListSummaryPromptReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(AdminService).ListSummaryPrompt(ctx, reqbody.(*ListSummaryPromptReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func AdminService_AddAgentFeedback_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &AddAgentFeedbackReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(AdminService).AddAgentFeedback(ctx, reqbody.(*AddAgentFeedbackReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func AdminService_DescribeAgentFeedback_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &DescribeAgentFeedbackReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(AdminService).DescribeAgentFeedback(ctx, reqbody.(*DescribeAgentFeedbackReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func AdminService_DeleteAgentFeedback_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &DeleteAgentFeedbackReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(AdminService).DeleteAgentFeedback(ctx, reqbody.(*DeleteAgentFeedbackReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func AdminService_ListAgentFeedback_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &ListAgentFeedbackReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(AdminService).ListAgentFeedback(ctx, reqbody.(*ListAgentFeedbackReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func AdminService_AddFeedback_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &AddFeedbackReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(AdminService).AddFeedback(ctx, reqbody.(*AddFeedbackReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func AdminService_ListFeedback_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &ListFeedbackReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(AdminService).ListFeedback(ctx, reqbody.(*ListFeedbackReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func AdminService_DescribeFeedback_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &DescribeFeedbackReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(AdminService).DescribeFeedback(ctx, reqbody.(*DescribeFeedbackReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func AdminService_DeleteFeedback_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &DeleteFeedbackReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(AdminService).DeleteFeedback(ctx, reqbody.(*DeleteFeedbackReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func AdminService_ListExperienceCenterApp_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &ListExperienceCenterAppReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(AdminService).ListExperienceCenterApp(ctx, reqbody.(*ListExperienceCenterAppReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func AdminService_CreateAppByExperienceApp_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &CreateAppByExperienceAppReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(AdminService).CreateAppByExperienceApp(ctx, reqbody.(*CreateAppByExperienceAppReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func AdminService_GetDisplayDocs_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &GetDisplayDocsReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(AdminService).GetDisplayDocs(ctx, reqbody.(*GetDisplayDocsReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func AdminService_ListPackage_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &ListPackageReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(AdminService).ListPackage(ctx, reqbody.(*ListPackageReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func AdminService_ListConcurrency_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &ListConcurrencyReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(AdminService).ListConcurrency(ctx, reqbody.(*ListConcurrencyReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func AdminService_ListKnowledgeCapacity_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &ListKnowledgeCapacityReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(AdminService).ListKnowledgeCapacity(ctx, reqbody.(*ListKnowledgeCapacityReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func AdminService_DescribeTokenUsage_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &DescribeTokenUsageReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(AdminService).DescribeTokenUsage(ctx, reqbody.(*DescribeTokenUsageReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func AdminService_DescribeTokenUsageGraph_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &DescribeTokenUsageGraphReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(AdminService).DescribeTokenUsageGraph(ctx, reqbody.(*DescribeTokenUsageGraphReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func AdminService_ListProducts_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &ListProductReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(AdminService).ListProducts(ctx, reqbody.(*ListProductReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func AdminService_ListAccount_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &ListAccountReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(AdminService).ListAccount(ctx, reqbody.(*ListAccountReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func AdminService_ExportBillingInfo_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &ExportBillingInfoReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(AdminService).ExportBillingInfo(ctx, reqbody.(*ExportBillingInfoReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func AdminService_DescribeCallStatsGraph_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &DescribeCallStatsGraphReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(AdminService).DescribeCallStatsGraph(ctx, reqbody.(*DescribeCallStatsGraphReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func AdminService_GetSearchResourceStatus_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &GetSearchResourceStatusReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(AdminService).GetSearchResourceStatus(ctx, reqbody.(*GetSearchResourceStatusReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func AdminService_ListPostpaidProduct_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &ListPostpaidProductReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(AdminService).ListPostpaidProduct(ctx, reqbody.(*ListPostpaidProductReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func AdminService_ModifyPostpaidSwitch_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &ModifyPostpaidSwitchReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(AdminService).ModifyPostpaidSwitch(ctx, reqbody.(*ModifyPostpaidSwitchReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func AdminService_ListInvalidResource_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &ListInvalidResourceReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(AdminService).ListInvalidResource(ctx, reqbody.(*ListInvalidResourceReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func AdminService_CreatePostPayResource_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &CreatePostPayResourceReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(AdminService).CreatePostPayResource(ctx, reqbody.(*CreatePostPayResourceReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func AdminService_DescribePostpaidSwitch_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &DescribePostpaidSwitchReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(AdminService).DescribePostpaidSwitch(ctx, reqbody.(*DescribePostpaidSwitchReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func AdminService_DescribeSearchStatsGraph_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &DescribeSearchStatsGraphReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(AdminService).DescribeSearchStatsGraph(ctx, reqbody.(*DescribeSearchStatsGraphReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func AdminService_AddEventReport_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &AddEventReportReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(AdminService).AddEventReport(ctx, reqbody.(*AddEventReportReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func AdminService_BatchEventReport_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &BatchEventReportReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(AdminService).BatchEventReport(ctx, reqbody.(*BatchEventReportReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func AdminService_DescribeKnowledgeUsage_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &DescribeKnowledgeUsageReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(AdminService).DescribeKnowledgeUsage(ctx, reqbody.(*DescribeKnowledgeUsageReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func AdminService_DescribeKnowledgeUsagePieGraph_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &DescribeKnowledgeUsagePieGraphReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(AdminService).DescribeKnowledgeUsagePieGraph(ctx, reqbody.(*DescribeKnowledgeUsagePieGraphReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func AdminService_ListAppKnowledgeDetail_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &ListAppKnowledgeDetailReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(AdminService).ListAppKnowledgeDetail(ctx, reqbody.(*ListAppKnowledgeDetailReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func AdminService_ListUsageCallDetail_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &ListUsageCallDetailReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(AdminService).ListUsageCallDetail(ctx, reqbody.(*ListUsageCallDetailReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func AdminService_DescribeConcurrencyUsage_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &DescribeConcurrencyUsageReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(AdminService).DescribeConcurrencyUsage(ctx, reqbody.(*DescribeConcurrencyUsageReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func AdminService_ListConcurrencyDetail_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &ListConcurrencyDetailReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(AdminService).ListConcurrencyDetail(ctx, reqbody.(*ListConcurrencyDetailReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func AdminService_DescribeConcurrencyUsageGraph_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &DescribeConcurrencyUsageGraphReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(AdminService).DescribeConcurrencyUsageGraph(ctx, reqbody.(*DescribeConcurrencyUsageGraphReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func AdminService_ExportConcurrencyInfo_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &ExportConcurrencyInfoReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(AdminService).ExportConcurrencyInfo(ctx, reqbody.(*ExportConcurrencyInfoReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func AdminService_ExportKnowledgeInfo_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &ExportKnowledgeInfoReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(AdminService).ExportKnowledgeInfo(ctx, reqbody.(*ExportKnowledgeInfoReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func AdminService_ListReleaseLabelPreview_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &ListReleaseLabelPreviewReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(AdminService).ListReleaseLabelPreview(ctx, reqbody.(*ListReleaseLabelPreviewReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func AdminService_ListReleaseSynonymsPreview_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &ListReleaseSynonymsPreviewReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(AdminService).ListReleaseSynonymsPreview(ctx, reqbody.(*ListReleaseSynonymsPreviewReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func AdminService_ModifyAppBase_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &ModifyAppBaseReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(AdminService).ModifyAppBase(ctx, reqbody.(*ModifyAppBaseReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func AdminService_CreateCustomModel_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &CreateCustomModelReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(AdminService).CreateCustomModel(ctx, reqbody.(*CreateCustomModelReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func AdminService_ModifyCustomModel_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &ModifyCustomModelReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(AdminService).ModifyCustomModel(ctx, reqbody.(*ModifyCustomModelReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func AdminService_DeleteCustomModel_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &DeleteCustomModelReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(AdminService).DeleteCustomModel(ctx, reqbody.(*DeleteCustomModelReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func AdminService_DescribeCustomModel_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &DescribeCustomModelReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(AdminService).DescribeCustomModel(ctx, reqbody.(*DescribeCustomModelReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func AdminService_DescribeModelApps_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &DescribeModelAppsReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(AdminService).DescribeModelApps(ctx, reqbody.(*DescribeModelAppsReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func AdminService_GetUserGuideViewInfos_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &GetUserGuideViewInfosReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(AdminService).GetUserGuideViewInfos(ctx, reqbody.(*GetUserGuideViewInfosReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func AdminService_SetUserGuideViewInfo_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &SetUserGuideViewInfoReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(AdminService).SetUserGuideViewInfo(ctx, reqbody.(*SetUserGuideViewInfoReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func AdminService_ExportMinuteDosage_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &ExportMinuteDosageReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(AdminService).ExportMinuteDosage(ctx, reqbody.(*ExportMinuteDosageReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func AdminService_GetImagePreview_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &GetImagePreviewReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(AdminService).GetImagePreview(ctx, reqbody.(*GetImagePreviewReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func AdminService_ListVoice_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &ListVoiceReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(AdminService).ListVoice(ctx, reqbody.(*ListVoiceReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func AdminService_ListDigitalHuman_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &ListDigitalHumanReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(AdminService).ListDigitalHuman(ctx, reqbody.(*ListDigitalHumanReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func AdminService_GetPromptTemplateList_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &GetPromptTemplateListReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(AdminService).GetPromptTemplateList(ctx, reqbody.(*GetPromptTemplateListReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func AdminService_GetMsgLogList_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &GetMsgLogListReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(AdminService).GetMsgLogList(ctx, reqbody.(*GetMsgLogListReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func AdminService_ExportMsgLog_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &ExportMsgLogReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(AdminService).ExportMsgLog(ctx, reqbody.(*ExportMsgLogReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func AdminService_ModifyConcurrencyRule_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &ModifyConcurrencyRuleReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(AdminService).ModifyConcurrencyRule(ctx, reqbody.(*ModifyConcurrencyRuleReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func AdminService_DescribeConcurrencyRule_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &DescribeConcurrencyRuleReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(AdminService).DescribeConcurrencyRule(ctx, reqbody.(*DescribeConcurrencyRuleReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func AdminService_CheckWhitelist_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &CheckWhitelistReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(AdminService).CheckWhitelist(ctx, reqbody.(*CheckWhitelistReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func AdminService_GetMsgLogOverview_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &GetMsgLogOverviewReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(AdminService).GetMsgLogOverview(ctx, reqbody.(*GetMsgLogOverviewReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func AdminService_GetMsgLogCountTrend_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &GetMsgLogCountTrendReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(AdminService).GetMsgLogCountTrend(ctx, reqbody.(*GetMsgLogCountTrendReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func AdminService_GetMsgLogUserCountTrend_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &GetMsgLogUserCountTrendReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(AdminService).GetMsgLogUserCountTrend(ctx, reqbody.(*GetMsgLogUserCountTrendReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func AdminService_GetMsgLogFeedbackCountTrend_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &GetMsgLogFeedbackCountTrendReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(AdminService).GetMsgLogFeedbackCountTrend(ctx, reqbody.(*GetMsgLogFeedbackCountTrendReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func AdminService_ExportMsgLogStatistical_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &ExportMsgLogStatisticalReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(AdminService).ExportMsgLogStatistical(ctx, reqbody.(*ExportMsgLogStatisticalReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func AdminService_CreateApproval_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &CreateApprovalReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(AdminService).CreateApproval(ctx, reqbody.(*CreateApprovalReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func AdminService_UpdateApprovalStatus_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &UpdateApprovalStatusReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(AdminService).UpdateApprovalStatus(ctx, reqbody.(*UpdateApprovalStatusReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func AdminService_GetLastApproval_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &GetLastApprovalReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(AdminService).GetLastApproval(ctx, reqbody.(*GetLastApprovalReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

// AdminServer_ServiceDesc descriptor for server.RegisterService.
var AdminServer_ServiceDesc = server.ServiceDesc{
	ServiceName: "trpc.KEP.bot_admin_config_server.Admin",
	HandlerType: ((*AdminService)(nil)),
	Methods: []server.Method{
		{
			Name: "/trpc.KEP.bot_admin_config_server.Admin/CorpInfo",
			Func: AdminService_CorpInfo_Handler,
		},
		{
			Name: "/trpc.KEP.bot_admin_config_server.Admin/DescribeCorp",
			Func: AdminService_DescribeCorp_Handler,
		},
		{
			Name: "/trpc.KEP.bot_admin_config_server.Admin/ListCorpStaff",
			Func: AdminService_ListCorpStaff_Handler,
		},
		{
			Name: "/trpc.KEP.bot_admin_config_server.Admin/DescribeStorageCredential",
			Func: AdminService_DescribeStorageCredential_Handler,
		},
		{
			Name: "/trpc.KEP.bot_admin_config_server.Admin/GetCredential",
			Func: AdminService_GetCredential_Handler,
		},
		{
			Name: "/trpc.KEP.bot_admin_config_server.Admin/GetAuditSwitch",
			Func: AdminService_GetAuditSwitch_Handler,
		},
		{
			Name: "/trpc.KEP.bot_admin_config_server.Admin/DescribeAuditSwitch",
			Func: AdminService_DescribeAuditSwitch_Handler,
		},
		{
			Name: "/trpc.KEP.bot_admin_config_server.Admin/ListDoc",
			Func: AdminService_ListDoc_Handler,
		},
		{
			Name: "/trpc.KEP.bot_admin_config_server.Admin/ListDocV1",
			Func: AdminService_ListDocV1_Handler,
		},
		{
			Name: "/trpc.KEP.bot_admin_config_server.Admin/SaveDocV1",
			Func: AdminService_SaveDocV1_Handler,
		},
		{
			Name: "/trpc.KEP.bot_admin_config_server.Admin/ModifyDoc",
			Func: AdminService_ModifyDoc_Handler,
		},
		{
			Name: "/trpc.KEP.bot_admin_config_server.Admin/ModifyDocAttrRange",
			Func: AdminService_ModifyDocAttrRange_Handler,
		},
		{
			Name: "/trpc.KEP.bot_admin_config_server.Admin/ModifyDocV1",
			Func: AdminService_ModifyDocV1_Handler,
		},
		{
			Name: "/trpc.KEP.bot_admin_config_server.Admin/DeleteDoc",
			Func: AdminService_DeleteDoc_Handler,
		},
		{
			Name: "/trpc.KEP.bot_admin_config_server.Admin/DescribeDoc",
			Func: AdminService_DescribeDoc_Handler,
		},
		{
			Name: "/trpc.KEP.bot_admin_config_server.Admin/ReferDoc",
			Func: AdminService_ReferDoc_Handler,
		},
		{
			Name: "/trpc.KEP.bot_admin_config_server.Admin/StartCreateQA",
			Func: AdminService_StartCreateQA_Handler,
		},
		{
			Name: "/trpc.KEP.bot_admin_config_server.Admin/GetDocPreview",
			Func: AdminService_GetDocPreview_Handler,
		},
		{
			Name: "/trpc.KEP.bot_admin_config_server.Admin/GenerateQA",
			Func: AdminService_GenerateQA_Handler,
		},
		{
			Name: "/trpc.KEP.bot_admin_config_server.Admin/GetSelectDoc",
			Func: AdminService_GetSelectDoc_Handler,
		},
		{
			Name: "/trpc.KEP.bot_admin_config_server.Admin/ListSelectDoc",
			Func: AdminService_ListSelectDoc_Handler,
		},
		{
			Name: "/trpc.KEP.bot_admin_config_server.Admin/StopDocParse",
			Func: AdminService_StopDocParse_Handler,
		},
		{
			Name: "/trpc.KEP.bot_admin_config_server.Admin/RetryDocParse",
			Func: AdminService_RetryDocParse_Handler,
		},
		{
			Name: "/trpc.KEP.bot_admin_config_server.Admin/RetryDocAudit",
			Func: AdminService_RetryDocAudit_Handler,
		},
		{
			Name: "/trpc.KEP.bot_admin_config_server.Admin/CreateRobot",
			Func: AdminService_CreateRobot_Handler,
		},
		{
			Name: "/trpc.KEP.bot_admin_config_server.Admin/DeleteRobot",
			Func: AdminService_DeleteRobot_Handler,
		},
		{
			Name: "/trpc.KEP.bot_admin_config_server.Admin/ModifyRobot",
			Func: AdminService_ModifyRobot_Handler,
		},
		{
			Name: "/trpc.KEP.bot_admin_config_server.Admin/ModifyRobotBasicConfig",
			Func: AdminService_ModifyRobotBasicConfig_Handler,
		},
		{
			Name: "/trpc.KEP.bot_admin_config_server.Admin/UpdateRobot",
			Func: AdminService_UpdateRobot_Handler,
		},
		{
			Name: "/trpc.KEP.bot_admin_config_server.Admin/RobotInfo",
			Func: AdminService_RobotInfo_Handler,
		},
		{
			Name: "/trpc.KEP.bot_admin_config_server.Admin/DescribeRobot",
			Func: AdminService_DescribeRobot_Handler,
		},
		{
			Name: "/trpc.KEP.bot_admin_config_server.Admin/ListRobot",
			Func: AdminService_ListRobot_Handler,
		},
		{
			Name: "/trpc.KEP.bot_admin_config_server.Admin/ListRobotV1",
			Func: AdminService_ListRobotV1_Handler,
		},
		{
			Name: "/trpc.KEP.bot_admin_config_server.Admin/AccountInfo",
			Func: AdminService_AccountInfo_Handler,
		},
		{
			Name: "/trpc.KEP.bot_admin_config_server.Admin/DescribeAccount",
			Func: AdminService_DescribeAccount_Handler,
		},
		{
			Name: "/trpc.KEP.bot_admin_config_server.Admin/ListRole",
			Func: AdminService_ListRole_Handler,
		},
		{
			Name: "/trpc.KEP.bot_admin_config_server.Admin/ListRobotRole",
			Func: AdminService_ListRobotRole_Handler,
		},
		{
			Name: "/trpc.KEP.bot_admin_config_server.Admin/ListQACate",
			Func: AdminService_ListQACate_Handler,
		},
		{
			Name: "/trpc.KEP.bot_admin_config_server.Admin/ListQACateV1",
			Func: AdminService_ListQACateV1_Handler,
		},
		{
			Name: "/trpc.KEP.bot_admin_config_server.Admin/CreateQACate",
			Func: AdminService_CreateQACate_Handler,
		},
		{
			Name: "/trpc.KEP.bot_admin_config_server.Admin/CreateQACateV1",
			Func: AdminService_CreateQACateV1_Handler,
		},
		{
			Name: "/trpc.KEP.bot_admin_config_server.Admin/UpdateQACate",
			Func: AdminService_UpdateQACate_Handler,
		},
		{
			Name: "/trpc.KEP.bot_admin_config_server.Admin/ModifyQACate",
			Func: AdminService_ModifyQACate_Handler,
		},
		{
			Name: "/trpc.KEP.bot_admin_config_server.Admin/DeleteQACate",
			Func: AdminService_DeleteQACate_Handler,
		},
		{
			Name: "/trpc.KEP.bot_admin_config_server.Admin/GetQAList",
			Func: AdminService_GetQAList_Handler,
		},
		{
			Name: "/trpc.KEP.bot_admin_config_server.Admin/ListQA",
			Func: AdminService_ListQA_Handler,
		},
		{
			Name: "/trpc.KEP.bot_admin_config_server.Admin/GetQADetail",
			Func: AdminService_GetQADetail_Handler,
		},
		{
			Name: "/trpc.KEP.bot_admin_config_server.Admin/DescribeQA",
			Func: AdminService_DescribeQA_Handler,
		},
		{
			Name: "/trpc.KEP.bot_admin_config_server.Admin/CreateQA",
			Func: AdminService_CreateQA_Handler,
		},
		{
			Name: "/trpc.KEP.bot_admin_config_server.Admin/CreateQAV1",
			Func: AdminService_CreateQAV1_Handler,
		},
		{
			Name: "/trpc.KEP.bot_admin_config_server.Admin/UpdateQA",
			Func: AdminService_UpdateQA_Handler,
		},
		{
			Name: "/trpc.KEP.bot_admin_config_server.Admin/ModifyQA",
			Func: AdminService_ModifyQA_Handler,
		},
		{
			Name: "/trpc.KEP.bot_admin_config_server.Admin/DeleteQA",
			Func: AdminService_DeleteQA_Handler,
		},
		{
			Name: "/trpc.KEP.bot_admin_config_server.Admin/DeleteQAV1",
			Func: AdminService_DeleteQAV1_Handler,
		},
		{
			Name: "/trpc.KEP.bot_admin_config_server.Admin/VerifyQA",
			Func: AdminService_VerifyQA_Handler,
		},
		{
			Name: "/trpc.KEP.bot_admin_config_server.Admin/VerifyQAV1",
			Func: AdminService_VerifyQAV1_Handler,
		},
		{
			Name: "/trpc.KEP.bot_admin_config_server.Admin/GroupQA",
			Func: AdminService_GroupQA_Handler,
		},
		{
			Name: "/trpc.KEP.bot_admin_config_server.Admin/ModifyQAAttrRange",
			Func: AdminService_ModifyQAAttrRange_Handler,
		},
		{
			Name: "/trpc.KEP.bot_admin_config_server.Admin/ExportQAList",
			Func: AdminService_ExportQAList_Handler,
		},
		{
			Name: "/trpc.KEP.bot_admin_config_server.Admin/ExportQAListV1",
			Func: AdminService_ExportQAListV1_Handler,
		},
		{
			Name: "/trpc.KEP.bot_admin_config_server.Admin/GetReleaseInfo",
			Func: AdminService_GetReleaseInfo_Handler,
		},
		{
			Name: "/trpc.KEP.bot_admin_config_server.Admin/DescribeReleaseInfo",
			Func: AdminService_DescribeReleaseInfo_Handler,
		},
		{
			Name: "/trpc.KEP.bot_admin_config_server.Admin/ListReleaseDocPreview",
			Func: AdminService_ListReleaseDocPreview_Handler,
		},
		{
			Name: "/trpc.KEP.bot_admin_config_server.Admin/ListReleaseQAPreview",
			Func: AdminService_ListReleaseQAPreview_Handler,
		},
		{
			Name: "/trpc.KEP.bot_admin_config_server.Admin/ListRejectedQuestionPreview",
			Func: AdminService_ListRejectedQuestionPreview_Handler,
		},
		{
			Name: "/trpc.KEP.bot_admin_config_server.Admin/ListReleaseConfigPreview",
			Func: AdminService_ListReleaseConfigPreview_Handler,
		},
		{
			Name: "/trpc.KEP.bot_admin_config_server.Admin/GetReleaseDocPreview",
			Func: AdminService_GetReleaseDocPreview_Handler,
		},
		{
			Name: "/trpc.KEP.bot_admin_config_server.Admin/GetReleaseQAPreview",
			Func: AdminService_GetReleaseQAPreview_Handler,
		},
		{
			Name: "/trpc.KEP.bot_admin_config_server.Admin/PreviewRejectedQuestion",
			Func: AdminService_PreviewRejectedQuestion_Handler,
		},
		{
			Name: "/trpc.KEP.bot_admin_config_server.Admin/CreateRelease",
			Func: AdminService_CreateRelease_Handler,
		},
		{
			Name: "/trpc.KEP.bot_admin_config_server.Admin/ListRelease",
			Func: AdminService_ListRelease_Handler,
		},
		{
			Name: "/trpc.KEP.bot_admin_config_server.Admin/GetReleaseRecordList",
			Func: AdminService_GetReleaseRecordList_Handler,
		},
		{
			Name: "/trpc.KEP.bot_admin_config_server.Admin/CheckUnconfirmedQa",
			Func: AdminService_CheckUnconfirmedQa_Handler,
		},
		{
			Name: "/trpc.KEP.bot_admin_config_server.Admin/DescribeRelease",
			Func: AdminService_DescribeRelease_Handler,
		},
		{
			Name: "/trpc.KEP.bot_admin_config_server.Admin/RetryRelease",
			Func: AdminService_RetryRelease_Handler,
		},
		{
			Name: "/trpc.KEP.bot_admin_config_server.Admin/GetQaSimilar",
			Func: AdminService_GetQaSimilar_Handler,
		},
		{
			Name: "/trpc.KEP.bot_admin_config_server.Admin/ListQaSimilar",
			Func: AdminService_ListQaSimilar_Handler,
		},
		{
			Name: "/trpc.KEP.bot_admin_config_server.Admin/GetQaSimilarDetail",
			Func: AdminService_GetQaSimilarDetail_Handler,
		},
		{
			Name: "/trpc.KEP.bot_admin_config_server.Admin/DescribeQaSimilar",
			Func: AdminService_DescribeQaSimilar_Handler,
		},
		{
			Name: "/trpc.KEP.bot_admin_config_server.Admin/SubmitQaSimilar",
			Func: AdminService_SubmitQaSimilar_Handler,
		},
		{
			Name: "/trpc.KEP.bot_admin_config_server.Admin/ListNotify",
			Func: AdminService_ListNotify_Handler,
		},
		{
			Name: "/trpc.KEP.bot_admin_config_server.Admin/GetNotify",
			Func: AdminService_GetNotify_Handler,
		},
		{
			Name: "/trpc.KEP.bot_admin_config_server.Admin/GetHistoryNotify",
			Func: AdminService_GetHistoryNotify_Handler,
		},
		{
			Name: "/trpc.KEP.bot_admin_config_server.Admin/ListHistoryNotify",
			Func: AdminService_ListHistoryNotify_Handler,
		},
		{
			Name: "/trpc.KEP.bot_admin_config_server.Admin/ReadNotify",
			Func: AdminService_ReadNotify_Handler,
		},
		{
			Name: "/trpc.KEP.bot_admin_config_server.Admin/CloseNotify",
			Func: AdminService_CloseNotify_Handler,
		},
		{
			Name: "/trpc.KEP.bot_admin_config_server.Admin/GetReferDetail",
			Func: AdminService_GetReferDetail_Handler,
		},
		{
			Name: "/trpc.KEP.bot_admin_config_server.Admin/DescribeRefer",
			Func: AdminService_DescribeRefer_Handler,
		},
		{
			Name: "/trpc.KEP.bot_admin_config_server.Admin/MarkRefer",
			Func: AdminService_MarkRefer_Handler,
		},
		{
			Name: "/trpc.KEP.bot_admin_config_server.Admin/UploadSampleFile",
			Func: AdminService_UploadSampleFile_Handler,
		},
		{
			Name: "/trpc.KEP.bot_admin_config_server.Admin/UploadSampleSet",
			Func: AdminService_UploadSampleSet_Handler,
		},
		{
			Name: "/trpc.KEP.bot_admin_config_server.Admin/UploadSampleSetWithCheck",
			Func: AdminService_UploadSampleSetWithCheck_Handler,
		},
		{
			Name: "/trpc.KEP.bot_admin_config_server.Admin/QuerySampleSetList",
			Func: AdminService_QuerySampleSetList_Handler,
		},
		{
			Name: "/trpc.KEP.bot_admin_config_server.Admin/DeleteSampleFiles",
			Func: AdminService_DeleteSampleFiles_Handler,
		},
		{
			Name: "/trpc.KEP.bot_admin_config_server.Admin/ListSampleSet",
			Func: AdminService_ListSampleSet_Handler,
		},
		{
			Name: "/trpc.KEP.bot_admin_config_server.Admin/DeleteSampleSet",
			Func: AdminService_DeleteSampleSet_Handler,
		},
		{
			Name: "/trpc.KEP.bot_admin_config_server.Admin/CreateTest",
			Func: AdminService_CreateTest_Handler,
		},
		{
			Name: "/trpc.KEP.bot_admin_config_server.Admin/CreateEvaluateTest",
			Func: AdminService_CreateEvaluateTest_Handler,
		},
		{
			Name: "/trpc.KEP.bot_admin_config_server.Admin/QueryTestList",
			Func: AdminService_QueryTestList_Handler,
		},
		{
			Name: "/trpc.KEP.bot_admin_config_server.Admin/ListEvaluateTest",
			Func: AdminService_ListEvaluateTest_Handler,
		},
		{
			Name: "/trpc.KEP.bot_admin_config_server.Admin/DeleteTest",
			Func: AdminService_DeleteTest_Handler,
		},
		{
			Name: "/trpc.KEP.bot_admin_config_server.Admin/StopTest",
			Func: AdminService_StopTest_Handler,
		},
		{
			Name: "/trpc.KEP.bot_admin_config_server.Admin/RetryTest",
			Func: AdminService_RetryTest_Handler,
		},
		{
			Name: "/trpc.KEP.bot_admin_config_server.Admin/DeleteEvaluateTest",
			Func: AdminService_DeleteEvaluateTest_Handler,
		},
		{
			Name: "/trpc.KEP.bot_admin_config_server.Admin/StopEvaluateTest",
			Func: AdminService_StopEvaluateTest_Handler,
		},
		{
			Name: "/trpc.KEP.bot_admin_config_server.Admin/RetryEvaluateTest",
			Func: AdminService_RetryEvaluateTest_Handler,
		},
		{
			Name: "/trpc.KEP.bot_admin_config_server.Admin/GetOneWaitJudging",
			Func: AdminService_GetOneWaitJudging_Handler,
		},
		{
			Name: "/trpc.KEP.bot_admin_config_server.Admin/DescribeWaitJudgeRecord",
			Func: AdminService_DescribeWaitJudgeRecord_Handler,
		},
		{
			Name: "/trpc.KEP.bot_admin_config_server.Admin/GetRecord",
			Func: AdminService_GetRecord_Handler,
		},
		{
			Name: "/trpc.KEP.bot_admin_config_server.Admin/DescribeRecord",
			Func: AdminService_DescribeRecord_Handler,
		},
		{
			Name: "/trpc.KEP.bot_admin_config_server.Admin/JudgeRecord",
			Func: AdminService_JudgeRecord_Handler,
		},
		{
			Name: "/trpc.KEP.bot_admin_config_server.Admin/GetRejectedQuestionList",
			Func: AdminService_GetRejectedQuestionList_Handler,
		},
		{
			Name: "/trpc.KEP.bot_admin_config_server.Admin/ListRejectedQuestion",
			Func: AdminService_ListRejectedQuestion_Handler,
		},
		{
			Name: "/trpc.KEP.bot_admin_config_server.Admin/CreateRejectedQuestion",
			Func: AdminService_CreateRejectedQuestion_Handler,
		},
		{
			Name: "/trpc.KEP.bot_admin_config_server.Admin/UpdateRejectedQuestion",
			Func: AdminService_UpdateRejectedQuestion_Handler,
		},
		{
			Name: "/trpc.KEP.bot_admin_config_server.Admin/ModifyRejectedQuestion",
			Func: AdminService_ModifyRejectedQuestion_Handler,
		},
		{
			Name: "/trpc.KEP.bot_admin_config_server.Admin/DeleteRejectedQuestion",
			Func: AdminService_DeleteRejectedQuestion_Handler,
		},
		{
			Name: "/trpc.KEP.bot_admin_config_server.Admin/ExportRejectedQuestion",
			Func: AdminService_ExportRejectedQuestion_Handler,
		},
		{
			Name: "/trpc.KEP.bot_admin_config_server.Admin/GetUnsatisfiedReply",
			Func: AdminService_GetUnsatisfiedReply_Handler,
		},
		{
			Name: "/trpc.KEP.bot_admin_config_server.Admin/ListUnsatisfiedReply",
			Func: AdminService_ListUnsatisfiedReply_Handler,
		},
		{
			Name: "/trpc.KEP.bot_admin_config_server.Admin/IgnoreUnsatisfiedReply",
			Func: AdminService_IgnoreUnsatisfiedReply_Handler,
		},
		{
			Name: "/trpc.KEP.bot_admin_config_server.Admin/ExportUnsatisfiedReply",
			Func: AdminService_ExportUnsatisfiedReply_Handler,
		},
		{
			Name: "/trpc.KEP.bot_admin_config_server.Admin/GetUnsatisfiedReplyContext",
			Func: AdminService_GetUnsatisfiedReplyContext_Handler,
		},
		{
			Name: "/trpc.KEP.bot_admin_config_server.Admin/DescribeUnsatisfiedReplyContext",
			Func: AdminService_DescribeUnsatisfiedReplyContext_Handler,
		},
		{
			Name: "/trpc.KEP.bot_admin_config_server.Admin/RecordUserFirstGenQA",
			Func: AdminService_RecordUserFirstGenQA_Handler,
		},
		{
			Name: "/trpc.KEP.bot_admin_config_server.Admin/RecordUserAccessUnCheckQATime",
			Func: AdminService_RecordUserAccessUnCheckQATime_Handler,
		},
		{
			Name: "/trpc.KEP.bot_admin_config_server.Admin/CreateAttributeLabelV1",
			Func: AdminService_CreateAttributeLabelV1_Handler,
		},
		{
			Name: "/trpc.KEP.bot_admin_config_server.Admin/CreateAttributeLabel",
			Func: AdminService_CreateAttributeLabel_Handler,
		},
		{
			Name: "/trpc.KEP.bot_admin_config_server.Admin/DeleteAttributeLabel",
			Func: AdminService_DeleteAttributeLabel_Handler,
		},
		{
			Name: "/trpc.KEP.bot_admin_config_server.Admin/UpdateAttributeLabel",
			Func: AdminService_UpdateAttributeLabel_Handler,
		},
		{
			Name: "/trpc.KEP.bot_admin_config_server.Admin/ModifyAttributeLabel",
			Func: AdminService_ModifyAttributeLabel_Handler,
		},
		{
			Name: "/trpc.KEP.bot_admin_config_server.Admin/GetAttributeLabelList",
			Func: AdminService_GetAttributeLabelList_Handler,
		},
		{
			Name: "/trpc.KEP.bot_admin_config_server.Admin/ListAttributeLabel",
			Func: AdminService_ListAttributeLabel_Handler,
		},
		{
			Name: "/trpc.KEP.bot_admin_config_server.Admin/GetAttributeLabelDetail",
			Func: AdminService_GetAttributeLabelDetail_Handler,
		},
		{
			Name: "/trpc.KEP.bot_admin_config_server.Admin/DescribeAttributeLabel",
			Func: AdminService_DescribeAttributeLabel_Handler,
		},
		{
			Name: "/trpc.KEP.bot_admin_config_server.Admin/UploadAttributeLabel",
			Func: AdminService_UploadAttributeLabel_Handler,
		},
		{
			Name: "/trpc.KEP.bot_admin_config_server.Admin/ExportAttributeLabel",
			Func: AdminService_ExportAttributeLabel_Handler,
		},
		{
			Name: "/trpc.KEP.bot_admin_config_server.Admin/CheckAttributeLabelRefer",
			Func: AdminService_CheckAttributeLabelRefer_Handler,
		},
		{
			Name: "/trpc.KEP.bot_admin_config_server.Admin/CheckAttributeLabelExist",
			Func: AdminService_CheckAttributeLabelExist_Handler,
		},
		{
			Name: "/trpc.KEP.bot_admin_config_server.Admin/CreateAppeal",
			Func: AdminService_CreateAppeal_Handler,
		},
		{
			Name: "/trpc.KEP.bot_admin_config_server.Admin/CreateApp",
			Func: AdminService_CreateApp_Handler,
		},
		{
			Name: "/trpc.KEP.bot_admin_config_server.Admin/ModifyApp",
			Func: AdminService_ModifyApp_Handler,
		},
		{
			Name: "/trpc.KEP.bot_admin_config_server.Admin/ListApp",
			Func: AdminService_ListApp_Handler,
		},
		{
			Name: "/trpc.KEP.bot_admin_config_server.Admin/DescribeApp",
			Func: AdminService_DescribeApp_Handler,
		},
		{
			Name: "/trpc.KEP.bot_admin_config_server.Admin/DeleteApp",
			Func: AdminService_DeleteApp_Handler,
		},
		{
			Name: "/trpc.KEP.bot_admin_config_server.Admin/ListModel",
			Func: AdminService_ListModel_Handler,
		},
		{
			Name: "/trpc.KEP.bot_admin_config_server.Admin/ListAppCategory",
			Func: AdminService_ListAppCategory_Handler,
		},
		{
			Name: "/trpc.KEP.bot_admin_config_server.Admin/GetAppSecret",
			Func: AdminService_GetAppSecret_Handler,
		},
		{
			Name: "/trpc.KEP.bot_admin_config_server.Admin/GetAppKnowledgeCount",
			Func: AdminService_GetAppKnowledgeCount_Handler,
		},
		{
			Name: "/trpc.KEP.bot_admin_config_server.Admin/DescribeLicense",
			Func: AdminService_DescribeLicense_Handler,
		},
		{
			Name: "/trpc.KEP.bot_admin_config_server.Admin/GetTaskStatus",
			Func: AdminService_GetTaskStatus_Handler,
		},
		{
			Name: "/trpc.KEP.bot_admin_config_server.Admin/DescribeModel",
			Func: AdminService_DescribeModel_Handler,
		},
		{
			Name: "/trpc.KEP.bot_admin_config_server.Admin/ListClassifyLabel",
			Func: AdminService_ListClassifyLabel_Handler,
		},
		{
			Name: "/trpc.KEP.bot_admin_config_server.Admin/ListSummaryPrompt",
			Func: AdminService_ListSummaryPrompt_Handler,
		},
		{
			Name: "/trpc.KEP.bot_admin_config_server.Admin/AddAgentFeedback",
			Func: AdminService_AddAgentFeedback_Handler,
		},
		{
			Name: "/trpc.KEP.bot_admin_config_server.Admin/DescribeAgentFeedback",
			Func: AdminService_DescribeAgentFeedback_Handler,
		},
		{
			Name: "/trpc.KEP.bot_admin_config_server.Admin/DeleteAgentFeedback",
			Func: AdminService_DeleteAgentFeedback_Handler,
		},
		{
			Name: "/trpc.KEP.bot_admin_config_server.Admin/ListAgentFeedback",
			Func: AdminService_ListAgentFeedback_Handler,
		},
		{
			Name: "/trpc.KEP.bot_admin_config_server.Admin/AddFeedback",
			Func: AdminService_AddFeedback_Handler,
		},
		{
			Name: "/trpc.KEP.bot_admin_config_server.Admin/ListFeedback",
			Func: AdminService_ListFeedback_Handler,
		},
		{
			Name: "/trpc.KEP.bot_admin_config_server.Admin/DescribeFeedback",
			Func: AdminService_DescribeFeedback_Handler,
		},
		{
			Name: "/trpc.KEP.bot_admin_config_server.Admin/DeleteFeedback",
			Func: AdminService_DeleteFeedback_Handler,
		},
		{
			Name: "/trpc.KEP.bot_admin_config_server.Admin/ListExperienceCenterApp",
			Func: AdminService_ListExperienceCenterApp_Handler,
		},
		{
			Name: "/trpc.KEP.bot_admin_config_server.Admin/CreateAppByExperienceApp",
			Func: AdminService_CreateAppByExperienceApp_Handler,
		},
		{
			Name: "/trpc.KEP.bot_admin_config_server.Admin/GetDisplayDocs",
			Func: AdminService_GetDisplayDocs_Handler,
		},
		{
			Name: "/trpc.KEP.bot_admin_config_server.Admin/ListPackage",
			Func: AdminService_ListPackage_Handler,
		},
		{
			Name: "/trpc.KEP.bot_admin_config_server.Admin/ListConcurrency",
			Func: AdminService_ListConcurrency_Handler,
		},
		{
			Name: "/trpc.KEP.bot_admin_config_server.Admin/ListKnowledgeCapacity",
			Func: AdminService_ListKnowledgeCapacity_Handler,
		},
		{
			Name: "/trpc.KEP.bot_admin_config_server.Admin/DescribeTokenUsage",
			Func: AdminService_DescribeTokenUsage_Handler,
		},
		{
			Name: "/trpc.KEP.bot_admin_config_server.Admin/DescribeTokenUsageGraph",
			Func: AdminService_DescribeTokenUsageGraph_Handler,
		},
		{
			Name: "/trpc.KEP.bot_admin_config_server.Admin/ListProducts",
			Func: AdminService_ListProducts_Handler,
		},
		{
			Name: "/trpc.KEP.bot_admin_config_server.Admin/ListAccount",
			Func: AdminService_ListAccount_Handler,
		},
		{
			Name: "/trpc.KEP.bot_admin_config_server.Admin/ExportBillingInfo",
			Func: AdminService_ExportBillingInfo_Handler,
		},
		{
			Name: "/trpc.KEP.bot_admin_config_server.Admin/DescribeCallStatsGraph",
			Func: AdminService_DescribeCallStatsGraph_Handler,
		},
		{
			Name: "/trpc.KEP.bot_admin_config_server.Admin/GetSearchResourceStatus",
			Func: AdminService_GetSearchResourceStatus_Handler,
		},
		{
			Name: "/trpc.KEP.bot_admin_config_server.Admin/ListPostpaidProduct",
			Func: AdminService_ListPostpaidProduct_Handler,
		},
		{
			Name: "/trpc.KEP.bot_admin_config_server.Admin/ModifyPostpaidSwitch",
			Func: AdminService_ModifyPostpaidSwitch_Handler,
		},
		{
			Name: "/trpc.KEP.bot_admin_config_server.Admin/ListInvalidResource",
			Func: AdminService_ListInvalidResource_Handler,
		},
		{
			Name: "/trpc.KEP.bot_admin_config_server.Admin/CreatePostPayResource",
			Func: AdminService_CreatePostPayResource_Handler,
		},
		{
			Name: "/trpc.KEP.bot_admin_config_server.Admin/DescribePostpaidSwitch",
			Func: AdminService_DescribePostpaidSwitch_Handler,
		},
		{
			Name: "/trpc.KEP.bot_admin_config_server.Admin/DescribeSearchStatsGraph",
			Func: AdminService_DescribeSearchStatsGraph_Handler,
		},
		{
			Name: "/trpc.KEP.bot_admin_config_server.Admin/AddEventReport",
			Func: AdminService_AddEventReport_Handler,
		},
		{
			Name: "/trpc.KEP.bot_admin_config_server.Admin/BatchEventReport",
			Func: AdminService_BatchEventReport_Handler,
		},
		{
			Name: "/trpc.KEP.bot_admin_config_server.Admin/DescribeKnowledgeUsage",
			Func: AdminService_DescribeKnowledgeUsage_Handler,
		},
		{
			Name: "/trpc.KEP.bot_admin_config_server.Admin/DescribeKnowledgeUsagePieGraph",
			Func: AdminService_DescribeKnowledgeUsagePieGraph_Handler,
		},
		{
			Name: "/trpc.KEP.bot_admin_config_server.Admin/ListAppKnowledgeDetail",
			Func: AdminService_ListAppKnowledgeDetail_Handler,
		},
		{
			Name: "/trpc.KEP.bot_admin_config_server.Admin/ListUsageCallDetail",
			Func: AdminService_ListUsageCallDetail_Handler,
		},
		{
			Name: "/trpc.KEP.bot_admin_config_server.Admin/DescribeConcurrencyUsage",
			Func: AdminService_DescribeConcurrencyUsage_Handler,
		},
		{
			Name: "/trpc.KEP.bot_admin_config_server.Admin/ListConcurrencyDetail",
			Func: AdminService_ListConcurrencyDetail_Handler,
		},
		{
			Name: "/trpc.KEP.bot_admin_config_server.Admin/DescribeConcurrencyUsageGraph",
			Func: AdminService_DescribeConcurrencyUsageGraph_Handler,
		},
		{
			Name: "/trpc.KEP.bot_admin_config_server.Admin/ExportConcurrencyInfo",
			Func: AdminService_ExportConcurrencyInfo_Handler,
		},
		{
			Name: "/trpc.KEP.bot_admin_config_server.Admin/ExportKnowledgeInfo",
			Func: AdminService_ExportKnowledgeInfo_Handler,
		},
		{
			Name: "/trpc.KEP.bot_admin_config_server.Admin/ListReleaseLabelPreview",
			Func: AdminService_ListReleaseLabelPreview_Handler,
		},
		{
			Name: "/trpc.KEP.bot_admin_config_server.Admin/ListReleaseSynonymsPreview",
			Func: AdminService_ListReleaseSynonymsPreview_Handler,
		},
		{
			Name: "/trpc.KEP.bot_admin_config_server.Admin/ModifyAppBase",
			Func: AdminService_ModifyAppBase_Handler,
		},
		{
			Name: "/trpc.KEP.bot_admin_config_server.Admin/CreateCustomModel",
			Func: AdminService_CreateCustomModel_Handler,
		},
		{
			Name: "/trpc.KEP.bot_admin_config_server.Admin/ModifyCustomModel",
			Func: AdminService_ModifyCustomModel_Handler,
		},
		{
			Name: "/trpc.KEP.bot_admin_config_server.Admin/DeleteCustomModel",
			Func: AdminService_DeleteCustomModel_Handler,
		},
		{
			Name: "/trpc.KEP.bot_admin_config_server.Admin/DescribeCustomModel",
			Func: AdminService_DescribeCustomModel_Handler,
		},
		{
			Name: "/trpc.KEP.bot_admin_config_server.Admin/DescribeModelApps",
			Func: AdminService_DescribeModelApps_Handler,
		},
		{
			Name: "/trpc.KEP.bot_admin_config_server.Admin/GetUserGuideViewInfos",
			Func: AdminService_GetUserGuideViewInfos_Handler,
		},
		{
			Name: "/trpc.KEP.bot_admin_config_server.Admin/SetUserGuideViewInfo",
			Func: AdminService_SetUserGuideViewInfo_Handler,
		},
		{
			Name: "/trpc.KEP.bot_admin_config_server.Admin/ExportMinuteDosage",
			Func: AdminService_ExportMinuteDosage_Handler,
		},
		{
			Name: "/trpc.KEP.bot_admin_config_server.Admin/GetImagePreview",
			Func: AdminService_GetImagePreview_Handler,
		},
		{
			Name: "/trpc.KEP.bot_admin_config_server.Admin/ListVoice",
			Func: AdminService_ListVoice_Handler,
		},
		{
			Name: "/trpc.KEP.bot_admin_config_server.Admin/ListDigitalHuman",
			Func: AdminService_ListDigitalHuman_Handler,
		},
		{
			Name: "/trpc.KEP.bot_admin_config_server.Admin/GetPromptTemplateList",
			Func: AdminService_GetPromptTemplateList_Handler,
		},
		{
			Name: "/trpc.KEP.bot_admin_config_server.Admin/GetMsgLogList",
			Func: AdminService_GetMsgLogList_Handler,
		},
		{
			Name: "/trpc.KEP.bot_admin_config_server.Admin/ExportMsgLog",
			Func: AdminService_ExportMsgLog_Handler,
		},
		{
			Name: "/trpc.KEP.bot_admin_config_server.Admin/ModifyConcurrencyRule",
			Func: AdminService_ModifyConcurrencyRule_Handler,
		},
		{
			Name: "/trpc.KEP.bot_admin_config_server.Admin/DescribeConcurrencyRule",
			Func: AdminService_DescribeConcurrencyRule_Handler,
		},
		{
			Name: "/trpc.KEP.bot_admin_config_server.Admin/CheckWhitelist",
			Func: AdminService_CheckWhitelist_Handler,
		},
		{
			Name: "/trpc.KEP.bot_admin_config_server.Admin/GetMsgLogOverview",
			Func: AdminService_GetMsgLogOverview_Handler,
		},
		{
			Name: "/trpc.KEP.bot_admin_config_server.Admin/GetMsgLogCountTrend",
			Func: AdminService_GetMsgLogCountTrend_Handler,
		},
		{
			Name: "/trpc.KEP.bot_admin_config_server.Admin/GetMsgLogUserCountTrend",
			Func: AdminService_GetMsgLogUserCountTrend_Handler,
		},
		{
			Name: "/trpc.KEP.bot_admin_config_server.Admin/GetMsgLogFeedbackCountTrend",
			Func: AdminService_GetMsgLogFeedbackCountTrend_Handler,
		},
		{
			Name: "/trpc.KEP.bot_admin_config_server.Admin/ExportMsgLogStatistical",
			Func: AdminService_ExportMsgLogStatistical_Handler,
		},
		{
			Name: "/trpc.KEP.bot_admin_config_server.Admin/CreateApproval",
			Func: AdminService_CreateApproval_Handler,
		},
		{
			Name: "/trpc.KEP.bot_admin_config_server.Admin/UpdateApprovalStatus",
			Func: AdminService_UpdateApprovalStatus_Handler,
		},
		{
			Name: "/trpc.KEP.bot_admin_config_server.Admin/GetLastApproval",
			Func: AdminService_GetLastApproval_Handler,
		},
	},
}

// RegisterAdminService registers service.
func RegisterAdminService(s server.Service, svr AdminService) {
	if err := s.Register(&AdminServer_ServiceDesc, svr); err != nil {
		panic(fmt.Sprintf("Admin register error:%v", err))
	}
}

// START --------------------------------- Default Unimplemented Server Service --------------------------------- START

type UnimplementedAdmin struct{}

// CorpInfo Deprecated 企业消息
func (s *UnimplementedAdmin) CorpInfo(ctx context.Context, req *CorpInfoReq) (*CorpInfoRsp, error) {
	return nil, errors.New("rpc CorpInfo of service Admin is not implemented")
}

// DescribeCorp 企业消息
func (s *UnimplementedAdmin) DescribeCorp(ctx context.Context, req *DescribeCorpReq) (*DescribeCorpRsp, error) {
	return nil, errors.New("rpc DescribeCorp of service Admin is not implemented")
}

// ListCorpStaff 员工信息列表
func (s *UnimplementedAdmin) ListCorpStaff(ctx context.Context, req *ListCorpStaffReq) (*ListCorpStaffRsp, error) {
	return nil, errors.New("rpc ListCorpStaff of service Admin is not implemented")
}

// DescribeStorageCredential 获取对象存储的临时密钥
func (s *UnimplementedAdmin) DescribeStorageCredential(ctx context.Context, req *DescribeStorageCredentialReq) (*DescribeStorageCredentialRsp, error) {
	return nil, errors.New("rpc DescribeStorageCredential of service Admin is not implemented")
}

// GetCredential Deprecated 获取对象存储的临时密钥
func (s *UnimplementedAdmin) GetCredential(ctx context.Context, req *GetCredentialReq) (*GetCredentialRsp, error) {
	return nil, errors.New("rpc GetCredential of service Admin is not implemented")
}

// GetAuditSwitch Deprecated 获取审核开关
func (s *UnimplementedAdmin) GetAuditSwitch(ctx context.Context, req *GetAuditSwitchReq) (*GetAuditSwitchRsp, error) {
	return nil, errors.New("rpc GetAuditSwitch of service Admin is not implemented")
}

// DescribeAuditSwitch 获取审核开关
func (s *UnimplementedAdmin) DescribeAuditSwitch(ctx context.Context, req *DescribeAuditSwitchReq) (*DescribeAuditSwitchRsp, error) {
	return nil, errors.New("rpc DescribeAuditSwitch of service Admin is not implemented")
}

// ListDoc 文档列表
func (s *UnimplementedAdmin) ListDoc(ctx context.Context, req *ListDocReq) (*ListDocRsp, error) {
	return nil, errors.New("rpc ListDoc of service Admin is not implemented")
}

// ListDocV1 文档列表
func (s *UnimplementedAdmin) ListDocV1(ctx context.Context, req *ListDocV1Req) (*ListDocV1Rsp, error) {
	return nil, errors.New("rpc ListDocV1 of service Admin is not implemented")
}

// SaveDocV1 保存文档
func (s *UnimplementedAdmin) SaveDocV1(ctx context.Context, req *SaveDocV1Req) (*SaveDocV1Rsp, error) {
	return nil, errors.New("rpc SaveDocV1 of service Admin is not implemented")
}

// ModifyDoc 修改文档
func (s *UnimplementedAdmin) ModifyDoc(ctx context.Context, req *ModifyDocReq) (*ModifyDocRsp, error) {
	return nil, errors.New("rpc ModifyDoc of service Admin is not implemented")
}

// ModifyDocAttrRange 批量编辑文档适用范围
func (s *UnimplementedAdmin) ModifyDocAttrRange(ctx context.Context, req *ModifyDocAttrRangeReq) (*ModifyDocAttrRangeRsp, error) {
	return nil, errors.New("rpc ModifyDocAttrRange of service Admin is not implemented")
}

// ModifyDocV1 修改文档
func (s *UnimplementedAdmin) ModifyDocV1(ctx context.Context, req *ModifyDocV1Req) (*ModifyDocV1Rsp, error) {
	return nil, errors.New("rpc ModifyDocV1 of service Admin is not implemented")
}

// DeleteDoc 删除文档
func (s *UnimplementedAdmin) DeleteDoc(ctx context.Context, req *DeleteDocReq) (*DeleteDocRsp, error) {
	return nil, errors.New("rpc DeleteDoc of service Admin is not implemented")
}

// DescribeDoc 获取文档详情
func (s *UnimplementedAdmin) DescribeDoc(ctx context.Context, req *DescribeDocReq) (*DescribeDocRsp, error) {
	return nil, errors.New("rpc DescribeDoc of service Admin is not implemented")
}

// ReferDoc 答案中是否引用
func (s *UnimplementedAdmin) ReferDoc(ctx context.Context, req *ReferDocReq) (*ReferDocRsp, error) {
	return nil, errors.New("rpc ReferDoc of service Admin is not implemented")
}

// StartCreateQA Deprecated 重新生成QA对
func (s *UnimplementedAdmin) StartCreateQA(ctx context.Context, req *StartCreateQAReq) (*StartCreateQARsp, error) {
	return nil, errors.New("rpc StartCreateQA of service Admin is not implemented")
}

// GetDocPreview 获取文档预览链接
func (s *UnimplementedAdmin) GetDocPreview(ctx context.Context, req *GetDocPreviewReq) (*GetDocPreviewRsp, error) {
	return nil, errors.New("rpc GetDocPreview of service Admin is not implemented")
}

// GenerateQA 生成QA对
func (s *UnimplementedAdmin) GenerateQA(ctx context.Context, req *GenerateQAReq) (*GenerateQARsp, error) {
	return nil, errors.New("rpc GenerateQA of service Admin is not implemented")
}

// GetSelectDoc Deprecated 获取文档下拉列表
func (s *UnimplementedAdmin) GetSelectDoc(ctx context.Context, req *GetSelectDocReq) (*GetSelectDocRsp, error) {
	return nil, errors.New("rpc GetSelectDoc of service Admin is not implemented")
}

// ListSelectDoc 获取文档下拉列表
func (s *UnimplementedAdmin) ListSelectDoc(ctx context.Context, req *ListSelectDocReq) (*ListSelectDocRsp, error) {
	return nil, errors.New("rpc ListSelectDoc of service Admin is not implemented")
}

// StopDocParse 中止文档解析
func (s *UnimplementedAdmin) StopDocParse(ctx context.Context, req *StopDocParseReq) (*StopDocParseRsp, error) {
	return nil, errors.New("rpc StopDocParse of service Admin is not implemented")
}

// RetryDocParse 重试文档解析
func (s *UnimplementedAdmin) RetryDocParse(ctx context.Context, req *RetryDocParseReq) (*RetryDocParseRsp, error) {
	return nil, errors.New("rpc RetryDocParse of service Admin is not implemented")
}

// RetryDocAudit 重试文档审核
func (s *UnimplementedAdmin) RetryDocAudit(ctx context.Context, req *RetryDocAuditReq) (*RetryDocAuditRsp, error) {
	return nil, errors.New("rpc RetryDocAudit of service Admin is not implemented")
}

// CreateRobot 新建机器人
func (s *UnimplementedAdmin) CreateRobot(ctx context.Context, req *CreateRobotReq) (*CreateRobotRsp, error) {
	return nil, errors.New("rpc CreateRobot of service Admin is not implemented")
}

// DeleteRobot 删除机器人
func (s *UnimplementedAdmin) DeleteRobot(ctx context.Context, req *DeleteRobotReq) (*DeleteRobotRsp, error) {
	return nil, errors.New("rpc DeleteRobot of service Admin is not implemented")
}

// ModifyRobot Deprecated 更新机器人属性
func (s *UnimplementedAdmin) ModifyRobot(ctx context.Context, req *ModifyRobotReq) (*ModifyRobotRsp, error) {
	return nil, errors.New("rpc ModifyRobot of service Admin is not implemented")
}

// ModifyRobotBasicConfig 更新机器人基础属性
func (s *UnimplementedAdmin) ModifyRobotBasicConfig(ctx context.Context, req *ModifyRobotBasicConfigReq) (*ModifyRobotBasicConfigRsp, error) {
	return nil, errors.New("rpc ModifyRobotBasicConfig of service Admin is not implemented")
}

// UpdateRobot Deprecated 更新机器人属性
func (s *UnimplementedAdmin) UpdateRobot(ctx context.Context, req *UpdateRobotReq) (*UpdateRobotRsp, error) {
	return nil, errors.New("rpc UpdateRobot of service Admin is not implemented")
}

// RobotInfo Deprecated 获取机器人
func (s *UnimplementedAdmin) RobotInfo(ctx context.Context, req *RobotInfoReq) (*RobotInfoRsp, error) {
	return nil, errors.New("rpc RobotInfo of service Admin is not implemented")
}

// DescribeRobot 获取机器人
func (s *UnimplementedAdmin) DescribeRobot(ctx context.Context, req *DescribeRobotReq) (*DescribeRobotRsp, error) {
	return nil, errors.New("rpc DescribeRobot of service Admin is not implemented")
}

// ListRobot 获取机器人列表
func (s *UnimplementedAdmin) ListRobot(ctx context.Context, req *ListRobotReq) (*ListRobotRsp, error) {
	return nil, errors.New("rpc ListRobot of service Admin is not implemented")
}

// ListRobotV1 获取机器人列表
func (s *UnimplementedAdmin) ListRobotV1(ctx context.Context, req *ListRobotV1Req) (*ListRobotV1Rsp, error) {
	return nil, errors.New("rpc ListRobotV1 of service Admin is not implemented")
}

// AccountInfo Deprecated 获取账户信息
func (s *UnimplementedAdmin) AccountInfo(ctx context.Context, req *AccountInfoReq) (*AccountInfoRsp, error) {
	return nil, errors.New("rpc AccountInfo of service Admin is not implemented")
}

// DescribeAccount 获取账户信息
func (s *UnimplementedAdmin) DescribeAccount(ctx context.Context, req *DescribeAccountReq) (*DescribeAccountRsp, error) {
	return nil, errors.New("rpc DescribeAccount of service Admin is not implemented")
}

// ListRole Deprecated 获取机器人角色配置
func (s *UnimplementedAdmin) ListRole(ctx context.Context, req *ListRoleReq) (*ListRoleRsp, error) {
	return nil, errors.New("rpc ListRole of service Admin is not implemented")
}

// ListRobotRole 获取机器人角色配置
func (s *UnimplementedAdmin) ListRobotRole(ctx context.Context, req *ListRobotRoleReq) (*ListRobotRoleRsp, error) {
	return nil, errors.New("rpc ListRobotRole of service Admin is not implemented")
}

// ListQACate 获取QA分类
func (s *UnimplementedAdmin) ListQACate(ctx context.Context, req *ListQACateReq) (*ListQACateRsp, error) {
	return nil, errors.New("rpc ListQACate of service Admin is not implemented")
}

// ListQACateV1 Deprecated 获取QA分类V1
func (s *UnimplementedAdmin) ListQACateV1(ctx context.Context, req *ListQACateV1Req) (*ListQACateV1Rsp, error) {
	return nil, errors.New("rpc ListQACateV1 of service Admin is not implemented")
}

// CreateQACate 创建QA分类
func (s *UnimplementedAdmin) CreateQACate(ctx context.Context, req *CreateQACateReq) (*CreateQACateRsp, error) {
	return nil, errors.New("rpc CreateQACate of service Admin is not implemented")
}

// CreateQACateV1 Deprecated 创建QA分类
func (s *UnimplementedAdmin) CreateQACateV1(ctx context.Context, req *CreateQACateV1Req) (*CreateQACateV1Rsp, error) {
	return nil, errors.New("rpc CreateQACateV1 of service Admin is not implemented")
}

// UpdateQACate Deprecated QA分类修改
func (s *UnimplementedAdmin) UpdateQACate(ctx context.Context, req *UpdateQACateReq) (*UpdateQACateRsp, error) {
	return nil, errors.New("rpc UpdateQACate of service Admin is not implemented")
}

// ModifyQACate QA分类修改
func (s *UnimplementedAdmin) ModifyQACate(ctx context.Context, req *ModifyQACateReq) (*ModifyQACateRsp, error) {
	return nil, errors.New("rpc ModifyQACate of service Admin is not implemented")
}

// DeleteQACate QA分类删除
func (s *UnimplementedAdmin) DeleteQACate(ctx context.Context, req *DeleteQACateReq) (*DeleteQACateRsp, error) {
	return nil, errors.New("rpc DeleteQACate of service Admin is not implemented")
}

// GetQAList Deprecated 获取QA列表
func (s *UnimplementedAdmin) GetQAList(ctx context.Context, req *GetQAListReq) (*GetQAListRsp, error) {
	return nil, errors.New("rpc GetQAList of service Admin is not implemented")
}

// ListQA 获取QA列表
func (s *UnimplementedAdmin) ListQA(ctx context.Context, req *ListQAReq) (*ListQARsp, error) {
	return nil, errors.New("rpc ListQA of service Admin is not implemented")
}

// GetQADetail Deprecated 获取QA详情
func (s *UnimplementedAdmin) GetQADetail(ctx context.Context, req *GetQADetailReq) (*GetQADetailRsp, error) {
	return nil, errors.New("rpc GetQADetail of service Admin is not implemented")
}

// DescribeQA 获取QA详情
func (s *UnimplementedAdmin) DescribeQA(ctx context.Context, req *DescribeQAReq) (*DescribeQARsp, error) {
	return nil, errors.New("rpc DescribeQA of service Admin is not implemented")
}

// CreateQA 新建QA
func (s *UnimplementedAdmin) CreateQA(ctx context.Context, req *CreateQAReq) (*CreateQARsp, error) {
	return nil, errors.New("rpc CreateQA of service Admin is not implemented")
}

// CreateQAV1 新建QA
func (s *UnimplementedAdmin) CreateQAV1(ctx context.Context, req *CreateQAV1Req) (*CreateQAV1Rsp, error) {
	return nil, errors.New("rpc CreateQAV1 of service Admin is not implemented")
}

// UpdateQA Deprecated 编辑QA
func (s *UnimplementedAdmin) UpdateQA(ctx context.Context, req *UpdateQAReq) (*UpdateQARsp, error) {
	return nil, errors.New("rpc UpdateQA of service Admin is not implemented")
}

// ModifyQA 编辑QA
func (s *UnimplementedAdmin) ModifyQA(ctx context.Context, req *ModifyQAReq) (*ModifyQARsp, error) {
	return nil, errors.New("rpc ModifyQA of service Admin is not implemented")
}

// DeleteQA 删除QA
func (s *UnimplementedAdmin) DeleteQA(ctx context.Context, req *DeleteQAReq) (*DeleteQARsp, error) {
	return nil, errors.New("rpc DeleteQA of service Admin is not implemented")
}

// DeleteQAV1 删除QA
func (s *UnimplementedAdmin) DeleteQAV1(ctx context.Context, req *DeleteQAV1Req) (*DeleteQAV1Rsp, error) {
	return nil, errors.New("rpc DeleteQAV1 of service Admin is not implemented")
}

// VerifyQA 校验QA
func (s *UnimplementedAdmin) VerifyQA(ctx context.Context, req *VerifyQAReq) (*VerifyQARsp, error) {
	return nil, errors.New("rpc VerifyQA of service Admin is not implemented")
}

// VerifyQAV1 校验QA
func (s *UnimplementedAdmin) VerifyQAV1(ctx context.Context, req *VerifyQAV1Req) (*VerifyQAV1Rsp, error) {
	return nil, errors.New("rpc VerifyQAV1 of service Admin is not implemented")
}

// GroupQA QA分组
func (s *UnimplementedAdmin) GroupQA(ctx context.Context, req *GroupQAReq) (*GroupQARsp, error) {
	return nil, errors.New("rpc GroupQA of service Admin is not implemented")
}

// ModifyQAAttrRange 编辑QA适用范围
func (s *UnimplementedAdmin) ModifyQAAttrRange(ctx context.Context, req *ModifyQAAttrRangeReq) (*ModifyQAAttrRangeRsp, error) {
	return nil, errors.New("rpc ModifyQAAttrRange of service Admin is not implemented")
}

// ExportQAList 导出QA列表
func (s *UnimplementedAdmin) ExportQAList(ctx context.Context, req *ExportQAListReq) (*ExportQAListRsp, error) {
	return nil, errors.New("rpc ExportQAList of service Admin is not implemented")
}

// ExportQAListV1 Deprecated 导出QA列表
func (s *UnimplementedAdmin) ExportQAListV1(ctx context.Context, req *ExportQAListReqV1) (*ExportQAListRspV1, error) {
	return nil, errors.New("rpc ExportQAListV1 of service Admin is not implemented")
}

// GetReleaseInfo Deprecated 拉取发布按钮状态、最后发布时间
func (s *UnimplementedAdmin) GetReleaseInfo(ctx context.Context, req *GetReleaseInfoReq) (*GetReleaseInfoRsp, error) {
	return nil, errors.New("rpc GetReleaseInfo of service Admin is not implemented")
}

// DescribeReleaseInfo 拉取发布按钮状态、最后发布时间
func (s *UnimplementedAdmin) DescribeReleaseInfo(ctx context.Context, req *DescribeReleaseInfoReq) (*DescribeReleaseInfoRsp, error) {
	return nil, errors.New("rpc DescribeReleaseInfo of service Admin is not implemented")
}

// ListReleaseDocPreview 发布文档预览
func (s *UnimplementedAdmin) ListReleaseDocPreview(ctx context.Context, req *ListReleaseDocPreviewReq) (*ListReleaseDocPreviewRsp, error) {
	return nil, errors.New("rpc ListReleaseDocPreview of service Admin is not implemented")
}

// ListReleaseQAPreview 发布问答预览
func (s *UnimplementedAdmin) ListReleaseQAPreview(ctx context.Context, req *ListReleaseQAPreviewReq) (*ListReleaseQAPreviewRsp, error) {
	return nil, errors.New("rpc ListReleaseQAPreview of service Admin is not implemented")
}

// ListRejectedQuestionPreview 发布拒答问题预览
func (s *UnimplementedAdmin) ListRejectedQuestionPreview(ctx context.Context, req *ListRejectedQuestionPreviewReq) (*ListRejectedQuestionPreviewRsp, error) {
	return nil, errors.New("rpc ListRejectedQuestionPreview of service Admin is not implemented")
}

// ListReleaseConfigPreview 待发布配置预览
func (s *UnimplementedAdmin) ListReleaseConfigPreview(ctx context.Context, req *ListReleaseConfigPreviewReq) (*ListReleaseConfigPreviewRsp, error) {
	return nil, errors.New("rpc ListReleaseConfigPreview of service Admin is not implemented")
}

// GetReleaseDocPreview Deprecated 发布文档预览
func (s *UnimplementedAdmin) GetReleaseDocPreview(ctx context.Context, req *GetReleaseDocPreviewReq) (*GetReleaseDocPreviewRsp, error) {
	return nil, errors.New("rpc GetReleaseDocPreview of service Admin is not implemented")
}

// GetReleaseQAPreview Deprecated 发布问答预览
func (s *UnimplementedAdmin) GetReleaseQAPreview(ctx context.Context, req *GetReleaseQAPreviewReq) (*GetReleaseQAPreviewRsp, error) {
	return nil, errors.New("rpc GetReleaseQAPreview of service Admin is not implemented")
}

// PreviewRejectedQuestion Deprecated 发布拒答问题预览
func (s *UnimplementedAdmin) PreviewRejectedQuestion(ctx context.Context, req *PreviewRejectedQuestionReq) (*PreviewRejectedQuestionRsp, error) {
	return nil, errors.New("rpc PreviewRejectedQuestion of service Admin is not implemented")
}

// CreateRelease 新增发布任务
func (s *UnimplementedAdmin) CreateRelease(ctx context.Context, req *CreateReleaseReq) (*CreateReleaseRsp, error) {
	return nil, errors.New("rpc CreateRelease of service Admin is not implemented")
}

// ListRelease 发布记录列表
func (s *UnimplementedAdmin) ListRelease(ctx context.Context, req *ListReleaseReq) (*ListReleaseRsp, error) {
	return nil, errors.New("rpc ListRelease of service Admin is not implemented")
}

// GetReleaseRecordList Deprecated 发布记录列表
func (s *UnimplementedAdmin) GetReleaseRecordList(ctx context.Context, req *GetReleaseRecordListReq) (*GetReleaseRecordListRsp, error) {
	return nil, errors.New("rpc GetReleaseRecordList of service Admin is not implemented")
}

// CheckUnconfirmedQa 是否存在未确认问答
func (s *UnimplementedAdmin) CheckUnconfirmedQa(ctx context.Context, req *CheckUnconfirmedQaReq) (*CheckUnconfirmedQaRsp, error) {
	return nil, errors.New("rpc CheckUnconfirmedQa of service Admin is not implemented")
}

// DescribeRelease 查询发布任务
func (s *UnimplementedAdmin) DescribeRelease(ctx context.Context, req *DescribeReleaseReq) (*DescribeReleaseRsp, error) {
	return nil, errors.New("rpc DescribeRelease of service Admin is not implemented")
}

// RetryRelease 发布暂停之后再次重新发布
func (s *UnimplementedAdmin) RetryRelease(ctx context.Context, req *RetryReleaseReq) (*RetryReleaseRsp, error) {
	return nil, errors.New("rpc RetryRelease of service Admin is not implemented")
}

// GetQaSimilar Deprecated 拉取相似问答对
func (s *UnimplementedAdmin) GetQaSimilar(ctx context.Context, req *GetQaSimilarReq) (*GetQaSimilarRsp, error) {
	return nil, errors.New("rpc GetQaSimilar of service Admin is not implemented")
}

// ListQaSimilar 拉取相似问答对
func (s *UnimplementedAdmin) ListQaSimilar(ctx context.Context, req *ListQaSimilarReq) (*ListQaSimilarRsp, error) {
	return nil, errors.New("rpc ListQaSimilar of service Admin is not implemented")
}

// GetQaSimilarDetail Deprecated 拉取相似问答对详情
func (s *UnimplementedAdmin) GetQaSimilarDetail(ctx context.Context, req *GetQaSimilarDetailReq) (*GetQaSimilarDetailRsp, error) {
	return nil, errors.New("rpc GetQaSimilarDetail of service Admin is not implemented")
}

// DescribeQaSimilar 拉取相似问答对详情
func (s *UnimplementedAdmin) DescribeQaSimilar(ctx context.Context, req *DescribeQaSimilarReq) (*DescribeQaSimilarRsp, error) {
	return nil, errors.New("rpc DescribeQaSimilar of service Admin is not implemented")
}

// SubmitQaSimilar 提交相似问答选择结果
func (s *UnimplementedAdmin) SubmitQaSimilar(ctx context.Context, req *SubmitQaSimilarReq) (*SubmitQaSimilarRsp, error) {
	return nil, errors.New("rpc SubmitQaSimilar of service Admin is not implemented")
}

// ListNotify 获取最新的消息
func (s *UnimplementedAdmin) ListNotify(ctx context.Context, req *ListNotifyReq) (*ListNotifyRsp, error) {
	return nil, errors.New("rpc ListNotify of service Admin is not implemented")
}

// GetNotify Deprecated 获取最新的消息
func (s *UnimplementedAdmin) GetNotify(ctx context.Context, req *GetNotifyReq) (*GetNotifyRsp, error) {
	return nil, errors.New("rpc GetNotify of service Admin is not implemented")
}

// GetHistoryNotify Deprecated 获取历史消息
func (s *UnimplementedAdmin) GetHistoryNotify(ctx context.Context, req *GetHistoryNotifyReq) (*GetHistoryNotifyRsp, error) {
	return nil, errors.New("rpc GetHistoryNotify of service Admin is not implemented")
}

// ListHistoryNotify 获取历史消息
func (s *UnimplementedAdmin) ListHistoryNotify(ctx context.Context, req *ListHistoryNotifyReq) (*ListHistoryNotifyRsp, error) {
	return nil, errors.New("rpc ListHistoryNotify of service Admin is not implemented")
}

// ReadNotify 通知已读
func (s *UnimplementedAdmin) ReadNotify(ctx context.Context, req *ReadNotifyReq) (*ReadNotifyRsp, error) {
	return nil, errors.New("rpc ReadNotify of service Admin is not implemented")
}

// CloseNotify 通知关闭
func (s *UnimplementedAdmin) CloseNotify(ctx context.Context, req *CloseNotifyReq) (*CloseNotifyRsp, error) {
	return nil, errors.New("rpc CloseNotify of service Admin is not implemented")
}

// GetReferDetail Deprecated 获取来源详情
func (s *UnimplementedAdmin) GetReferDetail(ctx context.Context, req *GetReferDetailReq) (*GetReferDetailRsp, error) {
	return nil, errors.New("rpc GetReferDetail of service Admin is not implemented")
}

// DescribeRefer 获取来源详情
func (s *UnimplementedAdmin) DescribeRefer(ctx context.Context, req *DescribeReferReq) (*DescribeReferRsp, error) {
	return nil, errors.New("rpc DescribeRefer of service Admin is not implemented")
}

// MarkRefer 来源打标
func (s *UnimplementedAdmin) MarkRefer(ctx context.Context, req *MarkReferReq) (*MarkReferRsp, error) {
	return nil, errors.New("rpc MarkRefer of service Admin is not implemented")
}

// UploadSampleFile Deprecated 上传样本集合
func (s *UnimplementedAdmin) UploadSampleFile(ctx context.Context, req *UploadSampleReq) (*UploadSampleRsp, error) {
	return nil, errors.New("rpc UploadSampleFile of service Admin is not implemented")
}

// UploadSampleSet 上传样本集合
func (s *UnimplementedAdmin) UploadSampleSet(ctx context.Context, req *UploadSampleSetReq) (*UploadSampleSetRsp, error) {
	return nil, errors.New("rpc UploadSampleSet of service Admin is not implemented")
}

// UploadSampleSetWithCheck 带校验上传样本集合
func (s *UnimplementedAdmin) UploadSampleSetWithCheck(ctx context.Context, req *UploadSampleSetWithCheckReq) (*UploadSampleSetWithCheckRsp, error) {
	return nil, errors.New("rpc UploadSampleSetWithCheck of service Admin is not implemented")
}

// QuerySampleSetList Deprecated 查询样本集列表
func (s *UnimplementedAdmin) QuerySampleSetList(ctx context.Context, req *QuerySampleReq) (*QuerySampleRsp, error) {
	return nil, errors.New("rpc QuerySampleSetList of service Admin is not implemented")
}

// DeleteSampleFiles Deprecated 批量删除样本集
func (s *UnimplementedAdmin) DeleteSampleFiles(ctx context.Context, req *DeleteSampleReq) (*DeleteSampleRsp, error) {
	return nil, errors.New("rpc DeleteSampleFiles of service Admin is not implemented")
}

// ListSampleSet 查询样本集
func (s *UnimplementedAdmin) ListSampleSet(ctx context.Context, req *ListSampleSetReq) (*ListSampleSetRsp, error) {
	return nil, errors.New("rpc ListSampleSet of service Admin is not implemented")
}

// DeleteSampleSet 删除样本集
func (s *UnimplementedAdmin) DeleteSampleSet(ctx context.Context, req *DeleteSampleSetReq) (*DeleteSampleSetRsp, error) {
	return nil, errors.New("rpc DeleteSampleSet of service Admin is not implemented")
}

// CreateTest Deprecated 创建评测任务
func (s *UnimplementedAdmin) CreateTest(ctx context.Context, req *CreateTestReq) (*CreateTestRsp, error) {
	return nil, errors.New("rpc CreateTest of service Admin is not implemented")
}

// CreateEvaluateTest 创建评测任务
func (s *UnimplementedAdmin) CreateEvaluateTest(ctx context.Context, req *CreateEvaluateTestReq) (*CreateEvaluateTestRsp, error) {
	return nil, errors.New("rpc CreateEvaluateTest of service Admin is not implemented")
}

// QueryTestList Deprecated 条件查询任务列表
func (s *UnimplementedAdmin) QueryTestList(ctx context.Context, req *QueryTestReq) (*QueryTestRsp, error) {
	return nil, errors.New("rpc QueryTestList of service Admin is not implemented")
}

// ListEvaluateTest 查询任务列表
func (s *UnimplementedAdmin) ListEvaluateTest(ctx context.Context, req *ListEvaluateTestReq) (*ListEvaluateTestRsp, error) {
	return nil, errors.New("rpc ListEvaluateTest of service Admin is not implemented")
}

// DeleteTest Deprecated 任务删除
func (s *UnimplementedAdmin) DeleteTest(ctx context.Context, req *DeleteTestReq) (*DeleteTestRsp, error) {
	return nil, errors.New("rpc DeleteTest of service Admin is not implemented")
}

// StopTest Deprecated 任务停止
func (s *UnimplementedAdmin) StopTest(ctx context.Context, req *StopTestReq) (*StopTestRsp, error) {
	return nil, errors.New("rpc StopTest of service Admin is not implemented")
}

// RetryTest Deprecated 任务重试
func (s *UnimplementedAdmin) RetryTest(ctx context.Context, req *RetryTestReq) (*RetryTestRsp, error) {
	return nil, errors.New("rpc RetryTest of service Admin is not implemented")
}

// DeleteEvaluateTest 任务删除
func (s *UnimplementedAdmin) DeleteEvaluateTest(ctx context.Context, req *DeleteEvaluateTestReq) (*DeleteEvaluateTestRsp, error) {
	return nil, errors.New("rpc DeleteEvaluateTest of service Admin is not implemented")
}

// StopEvaluateTest 任务停止
func (s *UnimplementedAdmin) StopEvaluateTest(ctx context.Context, req *StopEvaluateTestReq) (*StopEvaluateTestRsp, error) {
	return nil, errors.New("rpc StopEvaluateTest of service Admin is not implemented")
}

// RetryEvaluateTest 任务重试
func (s *UnimplementedAdmin) RetryEvaluateTest(ctx context.Context, req *RetryEvaluateTestReq) (*RetryEvaluateTestRsp, error) {
	return nil, errors.New("rpc RetryEvaluateTest of service Admin is not implemented")
}

// GetOneWaitJudging Deprecated 待标注测试记录详情
func (s *UnimplementedAdmin) GetOneWaitJudging(ctx context.Context, req *GetOneJudgingReq) (*GetOneJudgingRsp, error) {
	return nil, errors.New("rpc GetOneWaitJudging of service Admin is not implemented")
}

// DescribeWaitJudgeRecord 待标注测试记录详情
func (s *UnimplementedAdmin) DescribeWaitJudgeRecord(ctx context.Context, req *DescribeWaitJudgeRecordReq) (*DescribeWaitJudgeRecordRsp, error) {
	return nil, errors.New("rpc DescribeWaitJudgeRecord of service Admin is not implemented")
}

// GetRecord Deprecated 查询标注记录详情
func (s *UnimplementedAdmin) GetRecord(ctx context.Context, req *GetRecordReq) (*GetRecordRsp, error) {
	return nil, errors.New("rpc GetRecord of service Admin is not implemented")
}

// DescribeRecord 查询标注记录详情
func (s *UnimplementedAdmin) DescribeRecord(ctx context.Context, req *DescribeRecordReq) (*DescribeRecordRsp, error) {
	return nil, errors.New("rpc DescribeRecord of service Admin is not implemented")
}

// JudgeRecord 标注会话
func (s *UnimplementedAdmin) JudgeRecord(ctx context.Context, req *JudgeReq) (*JudgeRsp, error) {
	return nil, errors.New("rpc JudgeRecord of service Admin is not implemented")
}

// GetRejectedQuestionList Deprecated 获取拒答问题列表
func (s *UnimplementedAdmin) GetRejectedQuestionList(ctx context.Context, req *GetRejectedQuestionListReq) (*GetRejectedQuestionListRsp, error) {
	return nil, errors.New("rpc GetRejectedQuestionList of service Admin is not implemented")
}

// ListRejectedQuestion 获取拒答问题列表
func (s *UnimplementedAdmin) ListRejectedQuestion(ctx context.Context, req *ListRejectedQuestionReq) (*ListRejectedQuestionRsp, error) {
	return nil, errors.New("rpc ListRejectedQuestion of service Admin is not implemented")
}

// CreateRejectedQuestion 创建拒答问题
func (s *UnimplementedAdmin) CreateRejectedQuestion(ctx context.Context, req *CreateRejectedQuestionReq) (*CreateRejectedQuestionRsp, error) {
	return nil, errors.New("rpc CreateRejectedQuestion of service Admin is not implemented")
}

// UpdateRejectedQuestion Deprecated 修改拒答问题
func (s *UnimplementedAdmin) UpdateRejectedQuestion(ctx context.Context, req *UpdateRejectedQuestionReq) (*UpdateRejectedQuestionRsp, error) {
	return nil, errors.New("rpc UpdateRejectedQuestion of service Admin is not implemented")
}

// ModifyRejectedQuestion 修改拒答问题
func (s *UnimplementedAdmin) ModifyRejectedQuestion(ctx context.Context, req *ModifyRejectedQuestionReq) (*ModifyRejectedQuestionRsp, error) {
	return nil, errors.New("rpc ModifyRejectedQuestion of service Admin is not implemented")
}

// DeleteRejectedQuestion 删除拒答问题
func (s *UnimplementedAdmin) DeleteRejectedQuestion(ctx context.Context, req *DeleteRejectedQuestionReq) (*DeleteRejectedQuestionRsp, error) {
	return nil, errors.New("rpc DeleteRejectedQuestion of service Admin is not implemented")
}

// ExportRejectedQuestion 导出拒答问题
func (s *UnimplementedAdmin) ExportRejectedQuestion(ctx context.Context, req *ExportRejectedQuestionReq) (*ExportRejectedQuestionRsp, error) {
	return nil, errors.New("rpc ExportRejectedQuestion of service Admin is not implemented")
}

// GetUnsatisfiedReply Deprecated 获取不满意回复
func (s *UnimplementedAdmin) GetUnsatisfiedReply(ctx context.Context, req *GetUnsatisfiedReplyReq) (*GetUnsatisfiedReplyRsp, error) {
	return nil, errors.New("rpc GetUnsatisfiedReply of service Admin is not implemented")
}

// ListUnsatisfiedReply 获取不满意回复
func (s *UnimplementedAdmin) ListUnsatisfiedReply(ctx context.Context, req *ListUnsatisfiedReplyReq) (*ListUnsatisfiedReplyRsp, error) {
	return nil, errors.New("rpc ListUnsatisfiedReply of service Admin is not implemented")
}

// IgnoreUnsatisfiedReply 忽略不满意回复
func (s *UnimplementedAdmin) IgnoreUnsatisfiedReply(ctx context.Context, req *IgnoreUnsatisfiedReplyReq) (*IgnoreUnsatisfiedReplyRsp, error) {
	return nil, errors.New("rpc IgnoreUnsatisfiedReply of service Admin is not implemented")
}

// ExportUnsatisfiedReply 导出不满意回复
func (s *UnimplementedAdmin) ExportUnsatisfiedReply(ctx context.Context, req *ExportUnsatisfiedReplyReq) (*ExportUnsatisfiedReplyRsp, error) {
	return nil, errors.New("rpc ExportUnsatisfiedReply of service Admin is not implemented")
}

// GetUnsatisfiedReplyContext Deprecated 获取不满意回复上下文
func (s *UnimplementedAdmin) GetUnsatisfiedReplyContext(ctx context.Context, req *GetUnsatisfiedReplyContextReq) (*GetUnsatisfiedReplyContextRsp, error) {
	return nil, errors.New("rpc GetUnsatisfiedReplyContext of service Admin is not implemented")
}

// DescribeUnsatisfiedReplyContext 获取不满意回复上下文
func (s *UnimplementedAdmin) DescribeUnsatisfiedReplyContext(ctx context.Context, req *DescribeUnsatisfiedReplyReq) (*DescribeUnsatisfiedReplyRsp, error) {
	return nil, errors.New("rpc DescribeUnsatisfiedReplyContext of service Admin is not implemented")
}

// RecordUserFirstGenQA 记录操作首次生成问答标记
func (s *UnimplementedAdmin) RecordUserFirstGenQA(ctx context.Context, req *RecordUserFirstGenQAReq) (*RecordUserFirstGenQARsp, error) {
	return nil, errors.New("rpc RecordUserFirstGenQA of service Admin is not implemented")
}

// RecordUserAccessUnCheckQATime 记录访问未检验问答时间
func (s *UnimplementedAdmin) RecordUserAccessUnCheckQATime(ctx context.Context, req *RecordUserAccessUnCheckQATimeReq) (*RecordUserAccessUnCheckQATimeRsp, error) {
	return nil, errors.New("rpc RecordUserAccessUnCheckQATime of service Admin is not implemented")
}

// CreateAttributeLabelV1 创建属性标签
func (s *UnimplementedAdmin) CreateAttributeLabelV1(ctx context.Context, req *CreateAttributeLabelV1Req) (*CreateAttributeLabelV1Rsp, error) {
	return nil, errors.New("rpc CreateAttributeLabelV1 of service Admin is not implemented")
}

// CreateAttributeLabel 创建属性标签
func (s *UnimplementedAdmin) CreateAttributeLabel(ctx context.Context, req *CreateAttributeLabelReq) (*CreateAttributeLabelRsp, error) {
	return nil, errors.New("rpc CreateAttributeLabel of service Admin is not implemented")
}

// DeleteAttributeLabel 删除属性标签
func (s *UnimplementedAdmin) DeleteAttributeLabel(ctx context.Context, req *DeleteAttributeLabelReq) (*DeleteAttributeLabelRsp, error) {
	return nil, errors.New("rpc DeleteAttributeLabel of service Admin is not implemented")
}

// UpdateAttributeLabel Deprecated 编辑属性标签
func (s *UnimplementedAdmin) UpdateAttributeLabel(ctx context.Context, req *UpdateAttributeLabelReq) (*UpdateAttributeLabelRsp, error) {
	return nil, errors.New("rpc UpdateAttributeLabel of service Admin is not implemented")
}

// ModifyAttributeLabel 编辑属性标签
func (s *UnimplementedAdmin) ModifyAttributeLabel(ctx context.Context, req *ModifyAttributeLabelReq) (*ModifyAttributeLabelRsp, error) {
	return nil, errors.New("rpc ModifyAttributeLabel of service Admin is not implemented")
}

// GetAttributeLabelList Deprecated 查询属性标签列表
func (s *UnimplementedAdmin) GetAttributeLabelList(ctx context.Context, req *GetAttributeLabelListReq) (*GetAttributeLabelListRsp, error) {
	return nil, errors.New("rpc GetAttributeLabelList of service Admin is not implemented")
}

// ListAttributeLabel 查询属性标签列表
func (s *UnimplementedAdmin) ListAttributeLabel(ctx context.Context, req *ListAttributeLabelReq) (*ListAttributeLabelRsp, error) {
	return nil, errors.New("rpc ListAttributeLabel of service Admin is not implemented")
}

// GetAttributeLabelDetail Deprecated 查询属性标签详情
func (s *UnimplementedAdmin) GetAttributeLabelDetail(ctx context.Context, req *GetAttributeLabelDetailReq) (*GetAttributeLabelDetailRsp, error) {
	return nil, errors.New("rpc GetAttributeLabelDetail of service Admin is not implemented")
}

// DescribeAttributeLabel 查询属性标签详情
func (s *UnimplementedAdmin) DescribeAttributeLabel(ctx context.Context, req *DescribeAttributeLabelReq) (*DescribeAttributeLabelRsp, error) {
	return nil, errors.New("rpc DescribeAttributeLabel of service Admin is not implemented")
}

// UploadAttributeLabel 导入属性标签
func (s *UnimplementedAdmin) UploadAttributeLabel(ctx context.Context, req *UploadAttributeLabelReq) (*UploadAttributeLabelRsp, error) {
	return nil, errors.New("rpc UploadAttributeLabel of service Admin is not implemented")
}

// ExportAttributeLabel 导出属性标签
func (s *UnimplementedAdmin) ExportAttributeLabel(ctx context.Context, req *ExportAttributeLabelReq) (*ExportAttributeLabelRsp, error) {
	return nil, errors.New("rpc ExportAttributeLabel of service Admin is not implemented")
}

// CheckAttributeLabelRefer 检查属性下标签是否引用
func (s *UnimplementedAdmin) CheckAttributeLabelRefer(ctx context.Context, req *CheckAttributeLabelReferReq) (*CheckAttributeLabelReferRsp, error) {
	return nil, errors.New("rpc CheckAttributeLabelRefer of service Admin is not implemented")
}

// CheckAttributeLabelExist 检查属性下的标签名是否存在请求
func (s *UnimplementedAdmin) CheckAttributeLabelExist(ctx context.Context, req *CheckAttributeLabelExistReq) (*CheckAttributeLabelExistRsp, error) {
	return nil, errors.New("rpc CheckAttributeLabelExist of service Admin is not implemented")
}

// CreateAppeal 提交申诉请求申请人工审核
func (s *UnimplementedAdmin) CreateAppeal(ctx context.Context, req *CreateAppealReq) (*CreateAppealRsp, error) {
	return nil, errors.New("rpc CreateAppeal of service Admin is not implemented")
}

// CreateApp 应用
//
//	创建应用
func (s *UnimplementedAdmin) CreateApp(ctx context.Context, req *CreateAppReq) (*CreateAppRsp, error) {
	return nil, errors.New("rpc CreateApp of service Admin is not implemented")
}

// ModifyApp 修改应用
func (s *UnimplementedAdmin) ModifyApp(ctx context.Context, req *ModifyAppReq) (*ModifyAppRsp, error) {
	return nil, errors.New("rpc ModifyApp of service Admin is not implemented")
}

// ListApp 获取企业下应用列表
func (s *UnimplementedAdmin) ListApp(ctx context.Context, req *ListAppReq) (*ListAppRsp, error) {
	return nil, errors.New("rpc ListApp of service Admin is not implemented")
}

// DescribeApp 获取企业下应用详情
func (s *UnimplementedAdmin) DescribeApp(ctx context.Context, req *DescribeAppReq) (*DescribeAppRsp, error) {
	return nil, errors.New("rpc DescribeApp of service Admin is not implemented")
}

// DeleteApp 删除应用
func (s *UnimplementedAdmin) DeleteApp(ctx context.Context, req *DeleteAppReq) (*DeleteAppRsp, error) {
	return nil, errors.New("rpc DeleteApp of service Admin is not implemented")
}

// ListModel 获取模型列表
func (s *UnimplementedAdmin) ListModel(ctx context.Context, req *ListModelReq) (*ListModelRsp, error) {
	return nil, errors.New("rpc ListModel of service Admin is not implemented")
}

// ListAppCategory 应用类型列表
func (s *UnimplementedAdmin) ListAppCategory(ctx context.Context, req *ListAppCategoryReq) (*ListAppCategoryRsp, error) {
	return nil, errors.New("rpc ListAppCategory of service Admin is not implemented")
}

// GetAppSecret 获取应用密钥
func (s *UnimplementedAdmin) GetAppSecret(ctx context.Context, req *GetAppSecretReq) (*GetAppSecretRsp, error) {
	return nil, errors.New("rpc GetAppSecret of service Admin is not implemented")
}

// GetAppKnowledgeCount 获取知识库知识个数
func (s *UnimplementedAdmin) GetAppKnowledgeCount(ctx context.Context, req *GetAppKnowledgeCountReq) (*GetAppKnowledgeCountRsp, error) {
	return nil, errors.New("rpc GetAppKnowledgeCount of service Admin is not implemented")
}

// DescribeLicense 获取应用License
func (s *UnimplementedAdmin) DescribeLicense(ctx context.Context, req *DescribeLicenseReq) (*DescribeLicenseRsp, error) {
	return nil, errors.New("rpc DescribeLicense of service Admin is not implemented")
}

// GetTaskStatus 获取任务状态
func (s *UnimplementedAdmin) GetTaskStatus(ctx context.Context, req *GetTaskStatusReq) (*GetTaskStatusRsp, error) {
	return nil, errors.New("rpc GetTaskStatus of service Admin is not implemented")
}

// DescribeModel 模型详情
func (s *UnimplementedAdmin) DescribeModel(ctx context.Context, req *DescribeModelReq) (*DescribeModelRsp, error) {
	return nil, errors.New("rpc DescribeModel of service Admin is not implemented")
}

// ListClassifyLabel 标签列表
func (s *UnimplementedAdmin) ListClassifyLabel(ctx context.Context, req *ListClassifyLabelReq) (*ListClassifyLabelRsp, error) {
	return nil, errors.New("rpc ListClassifyLabel of service Admin is not implemented")
}

// ListSummaryPrompt 摘要Prompt列表
func (s *UnimplementedAdmin) ListSummaryPrompt(ctx context.Context, req *ListSummaryPromptReq) (*ListSummaryPromptRsp, error) {
	return nil, errors.New("rpc ListSummaryPrompt of service Admin is not implemented")
}

// AddAgentFeedback 添加Agent反馈
func (s *UnimplementedAdmin) AddAgentFeedback(ctx context.Context, req *AddAgentFeedbackReq) (*AddAgentFeedbackRsp, error) {
	return nil, errors.New("rpc AddAgentFeedback of service Admin is not implemented")
}

// DescribeAgentFeedback 获取Agent详情
func (s *UnimplementedAdmin) DescribeAgentFeedback(ctx context.Context, req *DescribeAgentFeedbackReq) (*DescribeAgentFeedbackRsp, error) {
	return nil, errors.New("rpc DescribeAgentFeedback of service Admin is not implemented")
}

// DeleteAgentFeedback 删除Agent反馈
func (s *UnimplementedAdmin) DeleteAgentFeedback(ctx context.Context, req *DeleteAgentFeedbackReq) (*DeleteAgentFeedbackRsp, error) {
	return nil, errors.New("rpc DeleteAgentFeedback of service Admin is not implemented")
}

// ListAgentFeedback 查询Agent反馈信息列表
func (s *UnimplementedAdmin) ListAgentFeedback(ctx context.Context, req *ListAgentFeedbackReq) (*ListAgentFeedbackRsp, error) {
	return nil, errors.New("rpc ListAgentFeedback of service Admin is not implemented")
}

// AddFeedback 添加反馈
func (s *UnimplementedAdmin) AddFeedback(ctx context.Context, req *AddFeedbackReq) (*AddFeedbackRsp, error) {
	return nil, errors.New("rpc AddFeedback of service Admin is not implemented")
}

// ListFeedback 查询反馈信息列表
func (s *UnimplementedAdmin) ListFeedback(ctx context.Context, req *ListFeedbackReq) (*ListFeedbackRsp, error) {
	return nil, errors.New("rpc ListFeedback of service Admin is not implemented")
}

// DescribeFeedback 查询反馈信息详情
func (s *UnimplementedAdmin) DescribeFeedback(ctx context.Context, req *DescribeFeedbackReq) (*DescribeFeedbackRsp, error) {
	return nil, errors.New("rpc DescribeFeedback of service Admin is not implemented")
}

// DeleteFeedback 删除反馈记录详情
func (s *UnimplementedAdmin) DeleteFeedback(ctx context.Context, req *DeleteFeedbackReq) (*DeleteFeedbackRsp, error) {
	return nil, errors.New("rpc DeleteFeedback of service Admin is not implemented")
}

// ListExperienceCenterApp ListExperienceApp 体验中心-获取体验应用列表
func (s *UnimplementedAdmin) ListExperienceCenterApp(ctx context.Context, req *ListExperienceCenterAppReq) (*ListExperienceCenterAppRsp, error) {
	return nil, errors.New("rpc ListExperienceCenterApp of service Admin is not implemented")
}

// CreateAppByExperienceApp CreateAppByExperienceApp 基于体验应用，创建app
func (s *UnimplementedAdmin) CreateAppByExperienceApp(ctx context.Context, req *CreateAppByExperienceAppReq) (*CreateAppByExperienceAppRsp, error) {
	return nil, errors.New("rpc CreateAppByExperienceApp of service Admin is not implemented")
}

// GetDisplayDocs 获取体验应用对外显示的文档列表
func (s *UnimplementedAdmin) GetDisplayDocs(ctx context.Context, req *GetDisplayDocsReq) (*GetDisplayDocsRsp, error) {
	return nil, errors.New("rpc GetDisplayDocs of service Admin is not implemented")
}

// ListPackage 资源包列表
func (s *UnimplementedAdmin) ListPackage(ctx context.Context, req *ListPackageReq) (*ListPackageRsp, error) {
	return nil, errors.New("rpc ListPackage of service Admin is not implemented")
}

// ListConcurrency 并发扩展
func (s *UnimplementedAdmin) ListConcurrency(ctx context.Context, req *ListConcurrencyReq) (*ListConcurrencyRsp, error) {
	return nil, errors.New("rpc ListConcurrency of service Admin is not implemented")
}

// ListKnowledgeCapacity 知识库容量扩展
func (s *UnimplementedAdmin) ListKnowledgeCapacity(ctx context.Context, req *ListKnowledgeCapacityReq) (*ListKnowledgeCapacityRsp, error) {
	return nil, errors.New("rpc ListKnowledgeCapacity of service Admin is not implemented")
}

// DescribeTokenUsage 接口调用token
func (s *UnimplementedAdmin) DescribeTokenUsage(ctx context.Context, req *DescribeTokenUsageReq) (*DescribeTokenUsageRsp, error) {
	return nil, errors.New("rpc DescribeTokenUsage of service Admin is not implemented")
}

// DescribeTokenUsageGraph 接口调用token折线图
func (s *UnimplementedAdmin) DescribeTokenUsageGraph(ctx context.Context, req *DescribeTokenUsageGraphReq) (*DescribeTokenUsageGraphRsp, error) {
	return nil, errors.New("rpc DescribeTokenUsageGraph of service Admin is not implemented")
}

// ListProducts 获取产品列表
func (s *UnimplementedAdmin) ListProducts(ctx context.Context, req *ListProductReq) (*ListProductRsp, error) {
	return nil, errors.New("rpc ListProducts of service Admin is not implemented")
}

// ListAccount 获取账户列表
func (s *UnimplementedAdmin) ListAccount(ctx context.Context, req *ListAccountReq) (*ListAccountRsp, error) {
	return nil, errors.New("rpc ListAccount of service Admin is not implemented")
}

// ExportBillingInfo 导出计费详情
func (s *UnimplementedAdmin) ExportBillingInfo(ctx context.Context, req *ExportBillingInfoReq) (*ExportBillingInfoRsp, error) {
	return nil, errors.New("rpc ExportBillingInfo of service Admin is not implemented")
}

// DescribeCallStatsGraph 接口调用折线图
func (s *UnimplementedAdmin) DescribeCallStatsGraph(ctx context.Context, req *DescribeCallStatsGraphReq) (*DescribeCallStatsGraphRsp, error) {
	return nil, errors.New("rpc DescribeCallStatsGraph of service Admin is not implemented")
}

// GetSearchResourceStatus 获取搜索引擎资源状态
func (s *UnimplementedAdmin) GetSearchResourceStatus(ctx context.Context, req *GetSearchResourceStatusReq) (*GetSearchResourceStatusRsp, error) {
	return nil, errors.New("rpc GetSearchResourceStatus of service Admin is not implemented")
}

// ListPostpaidProduct 获取后付费产品列表
func (s *UnimplementedAdmin) ListPostpaidProduct(ctx context.Context, req *ListPostpaidProductReq) (*ListPostpaidProductRsp, error) {
	return nil, errors.New("rpc ListPostpaidProduct of service Admin is not implemented")
}

// ModifyPostpaidSwitch 修改后付费开关状态
func (s *UnimplementedAdmin) ModifyPostpaidSwitch(ctx context.Context, req *ModifyPostpaidSwitchReq) (*ModifyPostpaidSwitchRsp, error) {
	return nil, errors.New("rpc ModifyPostpaidSwitch of service Admin is not implemented")
}

// ListInvalidResource 查询账户未生效资源包
func (s *UnimplementedAdmin) ListInvalidResource(ctx context.Context, req *ListInvalidResourceReq) (*ListInvalidResourceRsp, error) {
	return nil, errors.New("rpc ListInvalidResource of service Admin is not implemented")
}

// CreatePostPayResource 创建单个后付费资源
func (s *UnimplementedAdmin) CreatePostPayResource(ctx context.Context, req *CreatePostPayResourceReq) (*CreatePostPayResourceRsp, error) {
	return nil, errors.New("rpc CreatePostPayResource of service Admin is not implemented")
}

// DescribePostpaidSwitch 查询后付费开关状态
func (s *UnimplementedAdmin) DescribePostpaidSwitch(ctx context.Context, req *DescribePostpaidSwitchReq) (*DescribePostpaidSwitchRsp, error) {
	return nil, errors.New("rpc DescribePostpaidSwitch of service Admin is not implemented")
}

// DescribeSearchStatsGraph 查询搜索服务调用折线图
func (s *UnimplementedAdmin) DescribeSearchStatsGraph(ctx context.Context, req *DescribeSearchStatsGraphReq) (*DescribeSearchStatsGraphRsp, error) {
	return nil, errors.New("rpc DescribeSearchStatsGraph of service Admin is not implemented")
}

// AddEventReport 统计上报-事件上报
func (s *UnimplementedAdmin) AddEventReport(ctx context.Context, req *AddEventReportReq) (*AddEventReportRsp, error) {
	return nil, errors.New("rpc AddEventReport of service Admin is not implemented")
}

// BatchEventReport 统计上报-批量事件上报
func (s *UnimplementedAdmin) BatchEventReport(ctx context.Context, req *BatchEventReportReq) (*BatchEventReportRsp, error) {
	return nil, errors.New("rpc BatchEventReport of service Admin is not implemented")
}

// DescribeKnowledgeUsage 知识库容量统计
func (s *UnimplementedAdmin) DescribeKnowledgeUsage(ctx context.Context, req *DescribeKnowledgeUsageReq) (*DescribeKnowledgeUsageRsp, error) {
	return nil, errors.New("rpc DescribeKnowledgeUsage of service Admin is not implemented")
}

// DescribeKnowledgeUsagePieGraph 知识库容量统计饼图
func (s *UnimplementedAdmin) DescribeKnowledgeUsagePieGraph(ctx context.Context, req *DescribeKnowledgeUsagePieGraphReq) (*DescribeKnowledgeUsagePieGraphRsp, error) {
	return nil, errors.New("rpc DescribeKnowledgeUsagePieGraph of service Admin is not implemented")
}

// ListAppKnowledgeDetail 获取应用知识库调用明细列表
func (s *UnimplementedAdmin) ListAppKnowledgeDetail(ctx context.Context, req *ListAppKnowledgeDetailReq) (*ListAppKnowledgeDetailRsp, error) {
	return nil, errors.New("rpc ListAppKnowledgeDetail of service Admin is not implemented")
}

// ListUsageCallDetail 获取单次调用详情
func (s *UnimplementedAdmin) ListUsageCallDetail(ctx context.Context, req *ListUsageCallDetailReq) (*ListUsageCallDetailRsp, error) {
	return nil, errors.New("rpc ListUsageCallDetail of service Admin is not implemented")
}

// DescribeConcurrencyUsage 并发调用
func (s *UnimplementedAdmin) DescribeConcurrencyUsage(ctx context.Context, req *DescribeConcurrencyUsageReq) (*DescribeConcurrencyUsageRsp, error) {
	return nil, errors.New("rpc DescribeConcurrencyUsage of service Admin is not implemented")
}

// ListConcurrencyDetail 并发调用明细
func (s *UnimplementedAdmin) ListConcurrencyDetail(ctx context.Context, req *ListConcurrencyDetailReq) (*ListConcurrencyDetailRsp, error) {
	return nil, errors.New("rpc ListConcurrencyDetail of service Admin is not implemented")
}

// DescribeConcurrencyUsageGraph 并发调用折线图
func (s *UnimplementedAdmin) DescribeConcurrencyUsageGraph(ctx context.Context, req *DescribeConcurrencyUsageGraphReq) (*DescribeConcurrencyUsageGraphRsp, error) {
	return nil, errors.New("rpc DescribeConcurrencyUsageGraph of service Admin is not implemented")
}

// ExportConcurrencyInfo 导出并发详情
func (s *UnimplementedAdmin) ExportConcurrencyInfo(ctx context.Context, req *ExportConcurrencyInfoReq) (*ExportConcurrencyInfoRsp, error) {
	return nil, errors.New("rpc ExportConcurrencyInfo of service Admin is not implemented")
}

// ExportKnowledgeInfo 导出知识库容量详情
func (s *UnimplementedAdmin) ExportKnowledgeInfo(ctx context.Context, req *ExportKnowledgeInfoReq) (*ExportKnowledgeInfoRsp, error) {
	return nil, errors.New("rpc ExportKnowledgeInfo of service Admin is not implemented")
}

// ListReleaseLabelPreview 发布标签预览请求
func (s *UnimplementedAdmin) ListReleaseLabelPreview(ctx context.Context, req *ListReleaseLabelPreviewReq) (*ListReleaseLabelPreviewRsp, error) {
	return nil, errors.New("rpc ListReleaseLabelPreview of service Admin is not implemented")
}

// ListReleaseSynonymsPreview 发布同义词预览请求
func (s *UnimplementedAdmin) ListReleaseSynonymsPreview(ctx context.Context, req *ListReleaseSynonymsPreviewReq) (*ListReleaseSynonymsPreviewRsp, error) {
	return nil, errors.New("rpc ListReleaseSynonymsPreview of service Admin is not implemented")
}

// ModifyAppBase 修改应用基础信息
func (s *UnimplementedAdmin) ModifyAppBase(ctx context.Context, req *ModifyAppBaseReq) (*ModifyAppBaseRsp, error) {
	return nil, errors.New("rpc ModifyAppBase of service Admin is not implemented")
}

// CreateCustomModel 创建自定义模型
func (s *UnimplementedAdmin) CreateCustomModel(ctx context.Context, req *CreateCustomModelReq) (*CreateCustomModelRsp, error) {
	return nil, errors.New("rpc CreateCustomModel of service Admin is not implemented")
}

// ModifyCustomModel 编辑自定义模型
func (s *UnimplementedAdmin) ModifyCustomModel(ctx context.Context, req *ModifyCustomModelReq) (*ModifyCustomModelRsp, error) {
	return nil, errors.New("rpc ModifyCustomModel of service Admin is not implemented")
}

// DeleteCustomModel 删除自定义模型
func (s *UnimplementedAdmin) DeleteCustomModel(ctx context.Context, req *DeleteCustomModelReq) (*DeleteCustomModelRsp, error) {
	return nil, errors.New("rpc DeleteCustomModel of service Admin is not implemented")
}

// DescribeCustomModel 查询自定义模型详情
func (s *UnimplementedAdmin) DescribeCustomModel(ctx context.Context, req *DescribeCustomModelReq) (*DescribeCustomModelRsp, error) {
	return nil, errors.New("rpc DescribeCustomModel of service Admin is not implemented")
}

// DescribeModelApps 获取模型关联的应用
func (s *UnimplementedAdmin) DescribeModelApps(ctx context.Context, req *DescribeModelAppsReq) (*DescribeModelAppsRsp, error) {
	return nil, errors.New("rpc DescribeModelApps of service Admin is not implemented")
}

// GetUserGuideViewInfos 查看用户引导是否观看信息
func (s *UnimplementedAdmin) GetUserGuideViewInfos(ctx context.Context, req *GetUserGuideViewInfosReq) (*GetUserGuideViewInfosRsp, error) {
	return nil, errors.New("rpc GetUserGuideViewInfos of service Admin is not implemented")
}

// SetUserGuideViewInfo 设置用户引导已观看
func (s *UnimplementedAdmin) SetUserGuideViewInfo(ctx context.Context, req *SetUserGuideViewInfoReq) (*SetUserGuideViewInfoRsp, error) {
	return nil, errors.New("rpc SetUserGuideViewInfo of service Admin is not implemented")
}

// ExportMinuteDosage 导出调用统计并发QPM、TPM分钟用量信息
func (s *UnimplementedAdmin) ExportMinuteDosage(ctx context.Context, req *ExportMinuteDosageReq) (*ExportMinuteDosageRsp, error) {
	return nil, errors.New("rpc ExportMinuteDosage of service Admin is not implemented")
}

// GetImagePreview 图片预览请求,私有读
func (s *UnimplementedAdmin) GetImagePreview(ctx context.Context, req *GetImagePreviewReq) (*GetImagePreviewRsp, error) {
	return nil, errors.New("rpc GetImagePreview of service Admin is not implemented")
}

// ListVoice 查询音色列表
func (s *UnimplementedAdmin) ListVoice(ctx context.Context, req *ListVoiceReq) (*ListVoiceRsp, error) {
	return nil, errors.New("rpc ListVoice of service Admin is not implemented")
}

// ListDigitalHuman 查询数智人列表
func (s *UnimplementedAdmin) ListDigitalHuman(ctx context.Context, req *ListDigitalHumanReq) (*ListDigitalHumanRsp, error) {
	return nil, errors.New("rpc ListDigitalHuman of service Admin is not implemented")
}

// GetPromptTemplateList GetPromptTemplateList 模板中心列表
func (s *UnimplementedAdmin) GetPromptTemplateList(ctx context.Context, req *GetPromptTemplateListReq) (*GetPromptTemplateListRsp, error) {
	return nil, errors.New("rpc GetPromptTemplateList of service Admin is not implemented")
}

// GetMsgLogList 获取消息日志列表
func (s *UnimplementedAdmin) GetMsgLogList(ctx context.Context, req *GetMsgLogListReq) (*GetMsgLogListRsp, error) {
	return nil, errors.New("rpc GetMsgLogList of service Admin is not implemented")
}

// ExportMsgLog 导出消息日志
func (s *UnimplementedAdmin) ExportMsgLog(ctx context.Context, req *ExportMsgLogReq) (*ExportMsgLogRsp, error) {
	return nil, errors.New("rpc ExportMsgLog of service Admin is not implemented")
}

// ModifyConcurrencyRule 修改并发规则
func (s *UnimplementedAdmin) ModifyConcurrencyRule(ctx context.Context, req *ModifyConcurrencyRuleReq) (*ModifyConcurrencyRuleRsp, error) {
	return nil, errors.New("rpc ModifyConcurrencyRule of service Admin is not implemented")
}

// DescribeConcurrencyRule 查看并发规则
func (s *UnimplementedAdmin) DescribeConcurrencyRule(ctx context.Context, req *DescribeConcurrencyRuleReq) (*DescribeConcurrencyRuleRsp, error) {
	return nil, errors.New("rpc DescribeConcurrencyRule of service Admin is not implemented")
}

// CheckWhitelist 检查是否命中白名单
func (s *UnimplementedAdmin) CheckWhitelist(ctx context.Context, req *CheckWhitelistReq) (*CheckWhitelistRsp, error) {
	return nil, errors.New("rpc CheckWhitelist of service Admin is not implemented")
}

// GetMsgLogOverview 获取消息日志概览
func (s *UnimplementedAdmin) GetMsgLogOverview(ctx context.Context, req *GetMsgLogOverviewReq) (*GetMsgLogOverviewRsp, error) {
	return nil, errors.New("rpc GetMsgLogOverview of service Admin is not implemented")
}

// GetMsgLogCountTrend 获取消息数趋势
func (s *UnimplementedAdmin) GetMsgLogCountTrend(ctx context.Context, req *GetMsgLogCountTrendReq) (*GetMsgLogCountTrendRsp, error) {
	return nil, errors.New("rpc GetMsgLogCountTrend of service Admin is not implemented")
}

// GetMsgLogUserCountTrend 获取消息互动用户数趋势
func (s *UnimplementedAdmin) GetMsgLogUserCountTrend(ctx context.Context, req *GetMsgLogUserCountTrendReq) (*GetMsgLogUserCountTrendRsp, error) {
	return nil, errors.New("rpc GetMsgLogUserCountTrend of service Admin is not implemented")
}

// GetMsgLogFeedbackCountTrend 获取消息数反馈数趋势
func (s *UnimplementedAdmin) GetMsgLogFeedbackCountTrend(ctx context.Context, req *GetMsgLogFeedbackCountTrendReq) (*GetMsgLogFeedbackCountTrendRsp, error) {
	return nil, errors.New("rpc GetMsgLogFeedbackCountTrend of service Admin is not implemented")
}

// ExportMsgLogStatistical 导出消息统计
func (s *UnimplementedAdmin) ExportMsgLogStatistical(ctx context.Context, req *ExportMsgLogStatisticalReq) (*ExportMsgLogStatisticalRsp, error) {
	return nil, errors.New("rpc ExportMsgLogStatistical of service Admin is not implemented")
}

// CreateApproval 创建审批单
func (s *UnimplementedAdmin) CreateApproval(ctx context.Context, req *CreateApprovalReq) (*CreateApprovalRsp, error) {
	return nil, errors.New("rpc CreateApproval of service Admin is not implemented")
}

// UpdateApprovalStatus 更新审批单状态
func (s *UnimplementedAdmin) UpdateApprovalStatus(ctx context.Context, req *UpdateApprovalStatusReq) (*UpdateApprovalStatusRsp, error) {
	return nil, errors.New("rpc UpdateApprovalStatus of service Admin is not implemented")
}

// GetLastApproval 获取最新一条审批
func (s *UnimplementedAdmin) GetLastApproval(ctx context.Context, req *GetLastApprovalReq) (*GetLastApprovalRsp, error) {
	return nil, errors.New("rpc GetLastApproval of service Admin is not implemented")
}

// END --------------------------------- Default Unimplemented Server Service --------------------------------- END

// END ======================================= Server Service Definition ======================================= END

// START ======================================= Client Service Definition ======================================= START

// AdminClientProxy defines service client proxy
type AdminClientProxy interface {
	// CorpInfo Deprecated 企业消息
	CorpInfo(ctx context.Context, req *CorpInfoReq, opts ...client.Option) (rsp *CorpInfoRsp, err error)

	// DescribeCorp 企业消息
	DescribeCorp(ctx context.Context, req *DescribeCorpReq, opts ...client.Option) (rsp *DescribeCorpRsp, err error)

	// ListCorpStaff 员工信息列表
	ListCorpStaff(ctx context.Context, req *ListCorpStaffReq, opts ...client.Option) (rsp *ListCorpStaffRsp, err error)

	// DescribeStorageCredential 获取对象存储的临时密钥
	DescribeStorageCredential(ctx context.Context, req *DescribeStorageCredentialReq, opts ...client.Option) (rsp *DescribeStorageCredentialRsp, err error)

	// GetCredential Deprecated 获取对象存储的临时密钥
	GetCredential(ctx context.Context, req *GetCredentialReq, opts ...client.Option) (rsp *GetCredentialRsp, err error)

	// GetAuditSwitch Deprecated 获取审核开关
	GetAuditSwitch(ctx context.Context, req *GetAuditSwitchReq, opts ...client.Option) (rsp *GetAuditSwitchRsp, err error)

	// DescribeAuditSwitch 获取审核开关
	DescribeAuditSwitch(ctx context.Context, req *DescribeAuditSwitchReq, opts ...client.Option) (rsp *DescribeAuditSwitchRsp, err error)

	// ListDoc 文档列表
	ListDoc(ctx context.Context, req *ListDocReq, opts ...client.Option) (rsp *ListDocRsp, err error)

	// ListDocV1 文档列表
	ListDocV1(ctx context.Context, req *ListDocV1Req, opts ...client.Option) (rsp *ListDocV1Rsp, err error)

	// SaveDocV1 保存文档
	SaveDocV1(ctx context.Context, req *SaveDocV1Req, opts ...client.Option) (rsp *SaveDocV1Rsp, err error)

	// ModifyDoc 修改文档
	ModifyDoc(ctx context.Context, req *ModifyDocReq, opts ...client.Option) (rsp *ModifyDocRsp, err error)

	// ModifyDocAttrRange 批量编辑文档适用范围
	ModifyDocAttrRange(ctx context.Context, req *ModifyDocAttrRangeReq, opts ...client.Option) (rsp *ModifyDocAttrRangeRsp, err error)

	// ModifyDocV1 修改文档
	ModifyDocV1(ctx context.Context, req *ModifyDocV1Req, opts ...client.Option) (rsp *ModifyDocV1Rsp, err error)

	// DeleteDoc 删除文档
	DeleteDoc(ctx context.Context, req *DeleteDocReq, opts ...client.Option) (rsp *DeleteDocRsp, err error)

	// DescribeDoc 获取文档详情
	DescribeDoc(ctx context.Context, req *DescribeDocReq, opts ...client.Option) (rsp *DescribeDocRsp, err error)

	// ReferDoc 答案中是否引用
	ReferDoc(ctx context.Context, req *ReferDocReq, opts ...client.Option) (rsp *ReferDocRsp, err error)

	// StartCreateQA Deprecated 重新生成QA对
	StartCreateQA(ctx context.Context, req *StartCreateQAReq, opts ...client.Option) (rsp *StartCreateQARsp, err error)

	// GetDocPreview 获取文档预览链接
	GetDocPreview(ctx context.Context, req *GetDocPreviewReq, opts ...client.Option) (rsp *GetDocPreviewRsp, err error)

	// GenerateQA 生成QA对
	GenerateQA(ctx context.Context, req *GenerateQAReq, opts ...client.Option) (rsp *GenerateQARsp, err error)

	// GetSelectDoc Deprecated 获取文档下拉列表
	GetSelectDoc(ctx context.Context, req *GetSelectDocReq, opts ...client.Option) (rsp *GetSelectDocRsp, err error)

	// ListSelectDoc 获取文档下拉列表
	ListSelectDoc(ctx context.Context, req *ListSelectDocReq, opts ...client.Option) (rsp *ListSelectDocRsp, err error)

	// StopDocParse 中止文档解析
	StopDocParse(ctx context.Context, req *StopDocParseReq, opts ...client.Option) (rsp *StopDocParseRsp, err error)

	// RetryDocParse 重试文档解析
	RetryDocParse(ctx context.Context, req *RetryDocParseReq, opts ...client.Option) (rsp *RetryDocParseRsp, err error)

	// RetryDocAudit 重试文档审核
	RetryDocAudit(ctx context.Context, req *RetryDocAuditReq, opts ...client.Option) (rsp *RetryDocAuditRsp, err error)

	// CreateRobot 新建机器人
	CreateRobot(ctx context.Context, req *CreateRobotReq, opts ...client.Option) (rsp *CreateRobotRsp, err error)

	// DeleteRobot 删除机器人
	DeleteRobot(ctx context.Context, req *DeleteRobotReq, opts ...client.Option) (rsp *DeleteRobotRsp, err error)

	// ModifyRobot Deprecated 更新机器人属性
	ModifyRobot(ctx context.Context, req *ModifyRobotReq, opts ...client.Option) (rsp *ModifyRobotRsp, err error)

	// ModifyRobotBasicConfig 更新机器人基础属性
	ModifyRobotBasicConfig(ctx context.Context, req *ModifyRobotBasicConfigReq, opts ...client.Option) (rsp *ModifyRobotBasicConfigRsp, err error)

	// UpdateRobot Deprecated 更新机器人属性
	UpdateRobot(ctx context.Context, req *UpdateRobotReq, opts ...client.Option) (rsp *UpdateRobotRsp, err error)

	// RobotInfo Deprecated 获取机器人
	RobotInfo(ctx context.Context, req *RobotInfoReq, opts ...client.Option) (rsp *RobotInfoRsp, err error)

	// DescribeRobot 获取机器人
	DescribeRobot(ctx context.Context, req *DescribeRobotReq, opts ...client.Option) (rsp *DescribeRobotRsp, err error)

	// ListRobot 获取机器人列表
	ListRobot(ctx context.Context, req *ListRobotReq, opts ...client.Option) (rsp *ListRobotRsp, err error)

	// ListRobotV1 获取机器人列表
	ListRobotV1(ctx context.Context, req *ListRobotV1Req, opts ...client.Option) (rsp *ListRobotV1Rsp, err error)

	// AccountInfo Deprecated 获取账户信息
	AccountInfo(ctx context.Context, req *AccountInfoReq, opts ...client.Option) (rsp *AccountInfoRsp, err error)

	// DescribeAccount 获取账户信息
	DescribeAccount(ctx context.Context, req *DescribeAccountReq, opts ...client.Option) (rsp *DescribeAccountRsp, err error)

	// ListRole Deprecated 获取机器人角色配置
	ListRole(ctx context.Context, req *ListRoleReq, opts ...client.Option) (rsp *ListRoleRsp, err error)

	// ListRobotRole 获取机器人角色配置
	ListRobotRole(ctx context.Context, req *ListRobotRoleReq, opts ...client.Option) (rsp *ListRobotRoleRsp, err error)

	// ListQACate 获取QA分类
	ListQACate(ctx context.Context, req *ListQACateReq, opts ...client.Option) (rsp *ListQACateRsp, err error)

	// ListQACateV1 Deprecated 获取QA分类V1
	ListQACateV1(ctx context.Context, req *ListQACateV1Req, opts ...client.Option) (rsp *ListQACateV1Rsp, err error)

	// CreateQACate 创建QA分类
	CreateQACate(ctx context.Context, req *CreateQACateReq, opts ...client.Option) (rsp *CreateQACateRsp, err error)

	// CreateQACateV1 Deprecated 创建QA分类
	CreateQACateV1(ctx context.Context, req *CreateQACateV1Req, opts ...client.Option) (rsp *CreateQACateV1Rsp, err error)

	// UpdateQACate Deprecated QA分类修改
	UpdateQACate(ctx context.Context, req *UpdateQACateReq, opts ...client.Option) (rsp *UpdateQACateRsp, err error)

	// ModifyQACate QA分类修改
	ModifyQACate(ctx context.Context, req *ModifyQACateReq, opts ...client.Option) (rsp *ModifyQACateRsp, err error)

	// DeleteQACate QA分类删除
	DeleteQACate(ctx context.Context, req *DeleteQACateReq, opts ...client.Option) (rsp *DeleteQACateRsp, err error)

	// GetQAList Deprecated 获取QA列表
	GetQAList(ctx context.Context, req *GetQAListReq, opts ...client.Option) (rsp *GetQAListRsp, err error)

	// ListQA 获取QA列表
	ListQA(ctx context.Context, req *ListQAReq, opts ...client.Option) (rsp *ListQARsp, err error)

	// GetQADetail Deprecated 获取QA详情
	GetQADetail(ctx context.Context, req *GetQADetailReq, opts ...client.Option) (rsp *GetQADetailRsp, err error)

	// DescribeQA 获取QA详情
	DescribeQA(ctx context.Context, req *DescribeQAReq, opts ...client.Option) (rsp *DescribeQARsp, err error)

	// CreateQA 新建QA
	CreateQA(ctx context.Context, req *CreateQAReq, opts ...client.Option) (rsp *CreateQARsp, err error)

	// CreateQAV1 新建QA
	CreateQAV1(ctx context.Context, req *CreateQAV1Req, opts ...client.Option) (rsp *CreateQAV1Rsp, err error)

	// UpdateQA Deprecated 编辑QA
	UpdateQA(ctx context.Context, req *UpdateQAReq, opts ...client.Option) (rsp *UpdateQARsp, err error)

	// ModifyQA 编辑QA
	ModifyQA(ctx context.Context, req *ModifyQAReq, opts ...client.Option) (rsp *ModifyQARsp, err error)

	// DeleteQA 删除QA
	DeleteQA(ctx context.Context, req *DeleteQAReq, opts ...client.Option) (rsp *DeleteQARsp, err error)

	// DeleteQAV1 删除QA
	DeleteQAV1(ctx context.Context, req *DeleteQAV1Req, opts ...client.Option) (rsp *DeleteQAV1Rsp, err error)

	// VerifyQA 校验QA
	VerifyQA(ctx context.Context, req *VerifyQAReq, opts ...client.Option) (rsp *VerifyQARsp, err error)

	// VerifyQAV1 校验QA
	VerifyQAV1(ctx context.Context, req *VerifyQAV1Req, opts ...client.Option) (rsp *VerifyQAV1Rsp, err error)

	// GroupQA QA分组
	GroupQA(ctx context.Context, req *GroupQAReq, opts ...client.Option) (rsp *GroupQARsp, err error)

	// ModifyQAAttrRange 编辑QA适用范围
	ModifyQAAttrRange(ctx context.Context, req *ModifyQAAttrRangeReq, opts ...client.Option) (rsp *ModifyQAAttrRangeRsp, err error)

	// ExportQAList 导出QA列表
	ExportQAList(ctx context.Context, req *ExportQAListReq, opts ...client.Option) (rsp *ExportQAListRsp, err error)

	// ExportQAListV1 Deprecated 导出QA列表
	ExportQAListV1(ctx context.Context, req *ExportQAListReqV1, opts ...client.Option) (rsp *ExportQAListRspV1, err error)

	// GetReleaseInfo Deprecated 拉取发布按钮状态、最后发布时间
	GetReleaseInfo(ctx context.Context, req *GetReleaseInfoReq, opts ...client.Option) (rsp *GetReleaseInfoRsp, err error)

	// DescribeReleaseInfo 拉取发布按钮状态、最后发布时间
	DescribeReleaseInfo(ctx context.Context, req *DescribeReleaseInfoReq, opts ...client.Option) (rsp *DescribeReleaseInfoRsp, err error)

	// ListReleaseDocPreview 发布文档预览
	ListReleaseDocPreview(ctx context.Context, req *ListReleaseDocPreviewReq, opts ...client.Option) (rsp *ListReleaseDocPreviewRsp, err error)

	// ListReleaseQAPreview 发布问答预览
	ListReleaseQAPreview(ctx context.Context, req *ListReleaseQAPreviewReq, opts ...client.Option) (rsp *ListReleaseQAPreviewRsp, err error)

	// ListRejectedQuestionPreview 发布拒答问题预览
	ListRejectedQuestionPreview(ctx context.Context, req *ListRejectedQuestionPreviewReq, opts ...client.Option) (rsp *ListRejectedQuestionPreviewRsp, err error)

	// ListReleaseConfigPreview 待发布配置预览
	ListReleaseConfigPreview(ctx context.Context, req *ListReleaseConfigPreviewReq, opts ...client.Option) (rsp *ListReleaseConfigPreviewRsp, err error)

	// GetReleaseDocPreview Deprecated 发布文档预览
	GetReleaseDocPreview(ctx context.Context, req *GetReleaseDocPreviewReq, opts ...client.Option) (rsp *GetReleaseDocPreviewRsp, err error)

	// GetReleaseQAPreview Deprecated 发布问答预览
	GetReleaseQAPreview(ctx context.Context, req *GetReleaseQAPreviewReq, opts ...client.Option) (rsp *GetReleaseQAPreviewRsp, err error)

	// PreviewRejectedQuestion Deprecated 发布拒答问题预览
	PreviewRejectedQuestion(ctx context.Context, req *PreviewRejectedQuestionReq, opts ...client.Option) (rsp *PreviewRejectedQuestionRsp, err error)

	// CreateRelease 新增发布任务
	CreateRelease(ctx context.Context, req *CreateReleaseReq, opts ...client.Option) (rsp *CreateReleaseRsp, err error)

	// ListRelease 发布记录列表
	ListRelease(ctx context.Context, req *ListReleaseReq, opts ...client.Option) (rsp *ListReleaseRsp, err error)

	// GetReleaseRecordList Deprecated 发布记录列表
	GetReleaseRecordList(ctx context.Context, req *GetReleaseRecordListReq, opts ...client.Option) (rsp *GetReleaseRecordListRsp, err error)

	// CheckUnconfirmedQa 是否存在未确认问答
	CheckUnconfirmedQa(ctx context.Context, req *CheckUnconfirmedQaReq, opts ...client.Option) (rsp *CheckUnconfirmedQaRsp, err error)

	// DescribeRelease 查询发布任务
	DescribeRelease(ctx context.Context, req *DescribeReleaseReq, opts ...client.Option) (rsp *DescribeReleaseRsp, err error)

	// RetryRelease 发布暂停之后再次重新发布
	RetryRelease(ctx context.Context, req *RetryReleaseReq, opts ...client.Option) (rsp *RetryReleaseRsp, err error)

	// GetQaSimilar Deprecated 拉取相似问答对
	GetQaSimilar(ctx context.Context, req *GetQaSimilarReq, opts ...client.Option) (rsp *GetQaSimilarRsp, err error)

	// ListQaSimilar 拉取相似问答对
	ListQaSimilar(ctx context.Context, req *ListQaSimilarReq, opts ...client.Option) (rsp *ListQaSimilarRsp, err error)

	// GetQaSimilarDetail Deprecated 拉取相似问答对详情
	GetQaSimilarDetail(ctx context.Context, req *GetQaSimilarDetailReq, opts ...client.Option) (rsp *GetQaSimilarDetailRsp, err error)

	// DescribeQaSimilar 拉取相似问答对详情
	DescribeQaSimilar(ctx context.Context, req *DescribeQaSimilarReq, opts ...client.Option) (rsp *DescribeQaSimilarRsp, err error)

	// SubmitQaSimilar 提交相似问答选择结果
	SubmitQaSimilar(ctx context.Context, req *SubmitQaSimilarReq, opts ...client.Option) (rsp *SubmitQaSimilarRsp, err error)

	// ListNotify 获取最新的消息
	ListNotify(ctx context.Context, req *ListNotifyReq, opts ...client.Option) (rsp *ListNotifyRsp, err error)

	// GetNotify Deprecated 获取最新的消息
	GetNotify(ctx context.Context, req *GetNotifyReq, opts ...client.Option) (rsp *GetNotifyRsp, err error)

	// GetHistoryNotify Deprecated 获取历史消息
	GetHistoryNotify(ctx context.Context, req *GetHistoryNotifyReq, opts ...client.Option) (rsp *GetHistoryNotifyRsp, err error)

	// ListHistoryNotify 获取历史消息
	ListHistoryNotify(ctx context.Context, req *ListHistoryNotifyReq, opts ...client.Option) (rsp *ListHistoryNotifyRsp, err error)

	// ReadNotify 通知已读
	ReadNotify(ctx context.Context, req *ReadNotifyReq, opts ...client.Option) (rsp *ReadNotifyRsp, err error)

	// CloseNotify 通知关闭
	CloseNotify(ctx context.Context, req *CloseNotifyReq, opts ...client.Option) (rsp *CloseNotifyRsp, err error)

	// GetReferDetail Deprecated 获取来源详情
	GetReferDetail(ctx context.Context, req *GetReferDetailReq, opts ...client.Option) (rsp *GetReferDetailRsp, err error)

	// DescribeRefer 获取来源详情
	DescribeRefer(ctx context.Context, req *DescribeReferReq, opts ...client.Option) (rsp *DescribeReferRsp, err error)

	// MarkRefer 来源打标
	MarkRefer(ctx context.Context, req *MarkReferReq, opts ...client.Option) (rsp *MarkReferRsp, err error)

	// UploadSampleFile Deprecated 上传样本集合
	UploadSampleFile(ctx context.Context, req *UploadSampleReq, opts ...client.Option) (rsp *UploadSampleRsp, err error)

	// UploadSampleSet 上传样本集合
	UploadSampleSet(ctx context.Context, req *UploadSampleSetReq, opts ...client.Option) (rsp *UploadSampleSetRsp, err error)

	// UploadSampleSetWithCheck 带校验上传样本集合
	UploadSampleSetWithCheck(ctx context.Context, req *UploadSampleSetWithCheckReq, opts ...client.Option) (rsp *UploadSampleSetWithCheckRsp, err error)

	// QuerySampleSetList Deprecated 查询样本集列表
	QuerySampleSetList(ctx context.Context, req *QuerySampleReq, opts ...client.Option) (rsp *QuerySampleRsp, err error)

	// DeleteSampleFiles Deprecated 批量删除样本集
	DeleteSampleFiles(ctx context.Context, req *DeleteSampleReq, opts ...client.Option) (rsp *DeleteSampleRsp, err error)

	// ListSampleSet 查询样本集
	ListSampleSet(ctx context.Context, req *ListSampleSetReq, opts ...client.Option) (rsp *ListSampleSetRsp, err error)

	// DeleteSampleSet 删除样本集
	DeleteSampleSet(ctx context.Context, req *DeleteSampleSetReq, opts ...client.Option) (rsp *DeleteSampleSetRsp, err error)

	// CreateTest Deprecated 创建评测任务
	CreateTest(ctx context.Context, req *CreateTestReq, opts ...client.Option) (rsp *CreateTestRsp, err error)

	// CreateEvaluateTest 创建评测任务
	CreateEvaluateTest(ctx context.Context, req *CreateEvaluateTestReq, opts ...client.Option) (rsp *CreateEvaluateTestRsp, err error)

	// QueryTestList Deprecated 条件查询任务列表
	QueryTestList(ctx context.Context, req *QueryTestReq, opts ...client.Option) (rsp *QueryTestRsp, err error)

	// ListEvaluateTest 查询任务列表
	ListEvaluateTest(ctx context.Context, req *ListEvaluateTestReq, opts ...client.Option) (rsp *ListEvaluateTestRsp, err error)

	// DeleteTest Deprecated 任务删除
	DeleteTest(ctx context.Context, req *DeleteTestReq, opts ...client.Option) (rsp *DeleteTestRsp, err error)

	// StopTest Deprecated 任务停止
	StopTest(ctx context.Context, req *StopTestReq, opts ...client.Option) (rsp *StopTestRsp, err error)

	// RetryTest Deprecated 任务重试
	RetryTest(ctx context.Context, req *RetryTestReq, opts ...client.Option) (rsp *RetryTestRsp, err error)

	// DeleteEvaluateTest 任务删除
	DeleteEvaluateTest(ctx context.Context, req *DeleteEvaluateTestReq, opts ...client.Option) (rsp *DeleteEvaluateTestRsp, err error)

	// StopEvaluateTest 任务停止
	StopEvaluateTest(ctx context.Context, req *StopEvaluateTestReq, opts ...client.Option) (rsp *StopEvaluateTestRsp, err error)

	// RetryEvaluateTest 任务重试
	RetryEvaluateTest(ctx context.Context, req *RetryEvaluateTestReq, opts ...client.Option) (rsp *RetryEvaluateTestRsp, err error)

	// GetOneWaitJudging Deprecated 待标注测试记录详情
	GetOneWaitJudging(ctx context.Context, req *GetOneJudgingReq, opts ...client.Option) (rsp *GetOneJudgingRsp, err error)

	// DescribeWaitJudgeRecord 待标注测试记录详情
	DescribeWaitJudgeRecord(ctx context.Context, req *DescribeWaitJudgeRecordReq, opts ...client.Option) (rsp *DescribeWaitJudgeRecordRsp, err error)

	// GetRecord Deprecated 查询标注记录详情
	GetRecord(ctx context.Context, req *GetRecordReq, opts ...client.Option) (rsp *GetRecordRsp, err error)

	// DescribeRecord 查询标注记录详情
	DescribeRecord(ctx context.Context, req *DescribeRecordReq, opts ...client.Option) (rsp *DescribeRecordRsp, err error)

	// JudgeRecord 标注会话
	JudgeRecord(ctx context.Context, req *JudgeReq, opts ...client.Option) (rsp *JudgeRsp, err error)

	// GetRejectedQuestionList Deprecated 获取拒答问题列表
	GetRejectedQuestionList(ctx context.Context, req *GetRejectedQuestionListReq, opts ...client.Option) (rsp *GetRejectedQuestionListRsp, err error)

	// ListRejectedQuestion 获取拒答问题列表
	ListRejectedQuestion(ctx context.Context, req *ListRejectedQuestionReq, opts ...client.Option) (rsp *ListRejectedQuestionRsp, err error)

	// CreateRejectedQuestion 创建拒答问题
	CreateRejectedQuestion(ctx context.Context, req *CreateRejectedQuestionReq, opts ...client.Option) (rsp *CreateRejectedQuestionRsp, err error)

	// UpdateRejectedQuestion Deprecated 修改拒答问题
	UpdateRejectedQuestion(ctx context.Context, req *UpdateRejectedQuestionReq, opts ...client.Option) (rsp *UpdateRejectedQuestionRsp, err error)

	// ModifyRejectedQuestion 修改拒答问题
	ModifyRejectedQuestion(ctx context.Context, req *ModifyRejectedQuestionReq, opts ...client.Option) (rsp *ModifyRejectedQuestionRsp, err error)

	// DeleteRejectedQuestion 删除拒答问题
	DeleteRejectedQuestion(ctx context.Context, req *DeleteRejectedQuestionReq, opts ...client.Option) (rsp *DeleteRejectedQuestionRsp, err error)

	// ExportRejectedQuestion 导出拒答问题
	ExportRejectedQuestion(ctx context.Context, req *ExportRejectedQuestionReq, opts ...client.Option) (rsp *ExportRejectedQuestionRsp, err error)

	// GetUnsatisfiedReply Deprecated 获取不满意回复
	GetUnsatisfiedReply(ctx context.Context, req *GetUnsatisfiedReplyReq, opts ...client.Option) (rsp *GetUnsatisfiedReplyRsp, err error)

	// ListUnsatisfiedReply 获取不满意回复
	ListUnsatisfiedReply(ctx context.Context, req *ListUnsatisfiedReplyReq, opts ...client.Option) (rsp *ListUnsatisfiedReplyRsp, err error)

	// IgnoreUnsatisfiedReply 忽略不满意回复
	IgnoreUnsatisfiedReply(ctx context.Context, req *IgnoreUnsatisfiedReplyReq, opts ...client.Option) (rsp *IgnoreUnsatisfiedReplyRsp, err error)

	// ExportUnsatisfiedReply 导出不满意回复
	ExportUnsatisfiedReply(ctx context.Context, req *ExportUnsatisfiedReplyReq, opts ...client.Option) (rsp *ExportUnsatisfiedReplyRsp, err error)

	// GetUnsatisfiedReplyContext Deprecated 获取不满意回复上下文
	GetUnsatisfiedReplyContext(ctx context.Context, req *GetUnsatisfiedReplyContextReq, opts ...client.Option) (rsp *GetUnsatisfiedReplyContextRsp, err error)

	// DescribeUnsatisfiedReplyContext 获取不满意回复上下文
	DescribeUnsatisfiedReplyContext(ctx context.Context, req *DescribeUnsatisfiedReplyReq, opts ...client.Option) (rsp *DescribeUnsatisfiedReplyRsp, err error)

	// RecordUserFirstGenQA 记录操作首次生成问答标记
	RecordUserFirstGenQA(ctx context.Context, req *RecordUserFirstGenQAReq, opts ...client.Option) (rsp *RecordUserFirstGenQARsp, err error)

	// RecordUserAccessUnCheckQATime 记录访问未检验问答时间
	RecordUserAccessUnCheckQATime(ctx context.Context, req *RecordUserAccessUnCheckQATimeReq, opts ...client.Option) (rsp *RecordUserAccessUnCheckQATimeRsp, err error)

	// CreateAttributeLabelV1 创建属性标签
	CreateAttributeLabelV1(ctx context.Context, req *CreateAttributeLabelV1Req, opts ...client.Option) (rsp *CreateAttributeLabelV1Rsp, err error)

	// CreateAttributeLabel 创建属性标签
	CreateAttributeLabel(ctx context.Context, req *CreateAttributeLabelReq, opts ...client.Option) (rsp *CreateAttributeLabelRsp, err error)

	// DeleteAttributeLabel 删除属性标签
	DeleteAttributeLabel(ctx context.Context, req *DeleteAttributeLabelReq, opts ...client.Option) (rsp *DeleteAttributeLabelRsp, err error)

	// UpdateAttributeLabel Deprecated 编辑属性标签
	UpdateAttributeLabel(ctx context.Context, req *UpdateAttributeLabelReq, opts ...client.Option) (rsp *UpdateAttributeLabelRsp, err error)

	// ModifyAttributeLabel 编辑属性标签
	ModifyAttributeLabel(ctx context.Context, req *ModifyAttributeLabelReq, opts ...client.Option) (rsp *ModifyAttributeLabelRsp, err error)

	// GetAttributeLabelList Deprecated 查询属性标签列表
	GetAttributeLabelList(ctx context.Context, req *GetAttributeLabelListReq, opts ...client.Option) (rsp *GetAttributeLabelListRsp, err error)

	// ListAttributeLabel 查询属性标签列表
	ListAttributeLabel(ctx context.Context, req *ListAttributeLabelReq, opts ...client.Option) (rsp *ListAttributeLabelRsp, err error)

	// GetAttributeLabelDetail Deprecated 查询属性标签详情
	GetAttributeLabelDetail(ctx context.Context, req *GetAttributeLabelDetailReq, opts ...client.Option) (rsp *GetAttributeLabelDetailRsp, err error)

	// DescribeAttributeLabel 查询属性标签详情
	DescribeAttributeLabel(ctx context.Context, req *DescribeAttributeLabelReq, opts ...client.Option) (rsp *DescribeAttributeLabelRsp, err error)

	// UploadAttributeLabel 导入属性标签
	UploadAttributeLabel(ctx context.Context, req *UploadAttributeLabelReq, opts ...client.Option) (rsp *UploadAttributeLabelRsp, err error)

	// ExportAttributeLabel 导出属性标签
	ExportAttributeLabel(ctx context.Context, req *ExportAttributeLabelReq, opts ...client.Option) (rsp *ExportAttributeLabelRsp, err error)

	// CheckAttributeLabelRefer 检查属性下标签是否引用
	CheckAttributeLabelRefer(ctx context.Context, req *CheckAttributeLabelReferReq, opts ...client.Option) (rsp *CheckAttributeLabelReferRsp, err error)

	// CheckAttributeLabelExist 检查属性下的标签名是否存在请求
	CheckAttributeLabelExist(ctx context.Context, req *CheckAttributeLabelExistReq, opts ...client.Option) (rsp *CheckAttributeLabelExistRsp, err error)

	// CreateAppeal 提交申诉请求申请人工审核
	CreateAppeal(ctx context.Context, req *CreateAppealReq, opts ...client.Option) (rsp *CreateAppealRsp, err error)

	// CreateApp 应用
	//  创建应用
	CreateApp(ctx context.Context, req *CreateAppReq, opts ...client.Option) (rsp *CreateAppRsp, err error)

	// ModifyApp 修改应用
	ModifyApp(ctx context.Context, req *ModifyAppReq, opts ...client.Option) (rsp *ModifyAppRsp, err error)

	// ListApp 获取企业下应用列表
	ListApp(ctx context.Context, req *ListAppReq, opts ...client.Option) (rsp *ListAppRsp, err error)

	// DescribeApp 获取企业下应用详情
	DescribeApp(ctx context.Context, req *DescribeAppReq, opts ...client.Option) (rsp *DescribeAppRsp, err error)

	// DeleteApp 删除应用
	DeleteApp(ctx context.Context, req *DeleteAppReq, opts ...client.Option) (rsp *DeleteAppRsp, err error)

	// ListModel 获取模型列表
	ListModel(ctx context.Context, req *ListModelReq, opts ...client.Option) (rsp *ListModelRsp, err error)

	// ListAppCategory 应用类型列表
	ListAppCategory(ctx context.Context, req *ListAppCategoryReq, opts ...client.Option) (rsp *ListAppCategoryRsp, err error)

	// GetAppSecret 获取应用密钥
	GetAppSecret(ctx context.Context, req *GetAppSecretReq, opts ...client.Option) (rsp *GetAppSecretRsp, err error)

	// GetAppKnowledgeCount 获取知识库知识个数
	GetAppKnowledgeCount(ctx context.Context, req *GetAppKnowledgeCountReq, opts ...client.Option) (rsp *GetAppKnowledgeCountRsp, err error)

	// DescribeLicense 获取应用License
	DescribeLicense(ctx context.Context, req *DescribeLicenseReq, opts ...client.Option) (rsp *DescribeLicenseRsp, err error)

	// GetTaskStatus 获取任务状态
	GetTaskStatus(ctx context.Context, req *GetTaskStatusReq, opts ...client.Option) (rsp *GetTaskStatusRsp, err error)

	// DescribeModel 模型详情
	DescribeModel(ctx context.Context, req *DescribeModelReq, opts ...client.Option) (rsp *DescribeModelRsp, err error)

	// ListClassifyLabel 标签列表
	ListClassifyLabel(ctx context.Context, req *ListClassifyLabelReq, opts ...client.Option) (rsp *ListClassifyLabelRsp, err error)

	// ListSummaryPrompt 摘要Prompt列表
	ListSummaryPrompt(ctx context.Context, req *ListSummaryPromptReq, opts ...client.Option) (rsp *ListSummaryPromptRsp, err error)

	// AddAgentFeedback 添加Agent反馈
	AddAgentFeedback(ctx context.Context, req *AddAgentFeedbackReq, opts ...client.Option) (rsp *AddAgentFeedbackRsp, err error)

	// DescribeAgentFeedback 获取Agent详情
	DescribeAgentFeedback(ctx context.Context, req *DescribeAgentFeedbackReq, opts ...client.Option) (rsp *DescribeAgentFeedbackRsp, err error)

	// DeleteAgentFeedback 删除Agent反馈
	DeleteAgentFeedback(ctx context.Context, req *DeleteAgentFeedbackReq, opts ...client.Option) (rsp *DeleteAgentFeedbackRsp, err error)

	// ListAgentFeedback 查询Agent反馈信息列表
	ListAgentFeedback(ctx context.Context, req *ListAgentFeedbackReq, opts ...client.Option) (rsp *ListAgentFeedbackRsp, err error)

	// AddFeedback 添加反馈
	AddFeedback(ctx context.Context, req *AddFeedbackReq, opts ...client.Option) (rsp *AddFeedbackRsp, err error)

	// ListFeedback 查询反馈信息列表
	ListFeedback(ctx context.Context, req *ListFeedbackReq, opts ...client.Option) (rsp *ListFeedbackRsp, err error)

	// DescribeFeedback 查询反馈信息详情
	DescribeFeedback(ctx context.Context, req *DescribeFeedbackReq, opts ...client.Option) (rsp *DescribeFeedbackRsp, err error)

	// DeleteFeedback 删除反馈记录详情
	DeleteFeedback(ctx context.Context, req *DeleteFeedbackReq, opts ...client.Option) (rsp *DeleteFeedbackRsp, err error)

	// ListExperienceCenterApp ListExperienceApp 体验中心-获取体验应用列表
	ListExperienceCenterApp(ctx context.Context, req *ListExperienceCenterAppReq, opts ...client.Option) (rsp *ListExperienceCenterAppRsp, err error)

	// CreateAppByExperienceApp CreateAppByExperienceApp 基于体验应用，创建app
	CreateAppByExperienceApp(ctx context.Context, req *CreateAppByExperienceAppReq, opts ...client.Option) (rsp *CreateAppByExperienceAppRsp, err error)

	// GetDisplayDocs 获取体验应用对外显示的文档列表
	GetDisplayDocs(ctx context.Context, req *GetDisplayDocsReq, opts ...client.Option) (rsp *GetDisplayDocsRsp, err error)

	// ListPackage 资源包列表
	ListPackage(ctx context.Context, req *ListPackageReq, opts ...client.Option) (rsp *ListPackageRsp, err error)

	// ListConcurrency 并发扩展
	ListConcurrency(ctx context.Context, req *ListConcurrencyReq, opts ...client.Option) (rsp *ListConcurrencyRsp, err error)

	// ListKnowledgeCapacity 知识库容量扩展
	ListKnowledgeCapacity(ctx context.Context, req *ListKnowledgeCapacityReq, opts ...client.Option) (rsp *ListKnowledgeCapacityRsp, err error)

	// DescribeTokenUsage 接口调用token
	DescribeTokenUsage(ctx context.Context, req *DescribeTokenUsageReq, opts ...client.Option) (rsp *DescribeTokenUsageRsp, err error)

	// DescribeTokenUsageGraph 接口调用token折线图
	DescribeTokenUsageGraph(ctx context.Context, req *DescribeTokenUsageGraphReq, opts ...client.Option) (rsp *DescribeTokenUsageGraphRsp, err error)

	// ListProducts 获取产品列表
	ListProducts(ctx context.Context, req *ListProductReq, opts ...client.Option) (rsp *ListProductRsp, err error)

	// ListAccount 获取账户列表
	ListAccount(ctx context.Context, req *ListAccountReq, opts ...client.Option) (rsp *ListAccountRsp, err error)

	// ExportBillingInfo 导出计费详情
	ExportBillingInfo(ctx context.Context, req *ExportBillingInfoReq, opts ...client.Option) (rsp *ExportBillingInfoRsp, err error)

	// DescribeCallStatsGraph 接口调用折线图
	DescribeCallStatsGraph(ctx context.Context, req *DescribeCallStatsGraphReq, opts ...client.Option) (rsp *DescribeCallStatsGraphRsp, err error)

	// GetSearchResourceStatus 获取搜索引擎资源状态
	GetSearchResourceStatus(ctx context.Context, req *GetSearchResourceStatusReq, opts ...client.Option) (rsp *GetSearchResourceStatusRsp, err error)

	// ListPostpaidProduct 获取后付费产品列表
	ListPostpaidProduct(ctx context.Context, req *ListPostpaidProductReq, opts ...client.Option) (rsp *ListPostpaidProductRsp, err error)

	// ModifyPostpaidSwitch 修改后付费开关状态
	ModifyPostpaidSwitch(ctx context.Context, req *ModifyPostpaidSwitchReq, opts ...client.Option) (rsp *ModifyPostpaidSwitchRsp, err error)

	// ListInvalidResource 查询账户未生效资源包
	ListInvalidResource(ctx context.Context, req *ListInvalidResourceReq, opts ...client.Option) (rsp *ListInvalidResourceRsp, err error)

	// CreatePostPayResource 创建单个后付费资源
	CreatePostPayResource(ctx context.Context, req *CreatePostPayResourceReq, opts ...client.Option) (rsp *CreatePostPayResourceRsp, err error)

	// DescribePostpaidSwitch 查询后付费开关状态
	DescribePostpaidSwitch(ctx context.Context, req *DescribePostpaidSwitchReq, opts ...client.Option) (rsp *DescribePostpaidSwitchRsp, err error)

	// DescribeSearchStatsGraph 查询搜索服务调用折线图
	DescribeSearchStatsGraph(ctx context.Context, req *DescribeSearchStatsGraphReq, opts ...client.Option) (rsp *DescribeSearchStatsGraphRsp, err error)

	// AddEventReport 统计上报-事件上报
	AddEventReport(ctx context.Context, req *AddEventReportReq, opts ...client.Option) (rsp *AddEventReportRsp, err error)

	// BatchEventReport 统计上报-批量事件上报
	BatchEventReport(ctx context.Context, req *BatchEventReportReq, opts ...client.Option) (rsp *BatchEventReportRsp, err error)

	// DescribeKnowledgeUsage 知识库容量统计
	DescribeKnowledgeUsage(ctx context.Context, req *DescribeKnowledgeUsageReq, opts ...client.Option) (rsp *DescribeKnowledgeUsageRsp, err error)

	// DescribeKnowledgeUsagePieGraph 知识库容量统计饼图
	DescribeKnowledgeUsagePieGraph(ctx context.Context, req *DescribeKnowledgeUsagePieGraphReq, opts ...client.Option) (rsp *DescribeKnowledgeUsagePieGraphRsp, err error)

	// ListAppKnowledgeDetail 获取应用知识库调用明细列表
	ListAppKnowledgeDetail(ctx context.Context, req *ListAppKnowledgeDetailReq, opts ...client.Option) (rsp *ListAppKnowledgeDetailRsp, err error)

	// ListUsageCallDetail 获取单次调用详情
	ListUsageCallDetail(ctx context.Context, req *ListUsageCallDetailReq, opts ...client.Option) (rsp *ListUsageCallDetailRsp, err error)

	// DescribeConcurrencyUsage 并发调用
	DescribeConcurrencyUsage(ctx context.Context, req *DescribeConcurrencyUsageReq, opts ...client.Option) (rsp *DescribeConcurrencyUsageRsp, err error)

	// ListConcurrencyDetail 并发调用明细
	ListConcurrencyDetail(ctx context.Context, req *ListConcurrencyDetailReq, opts ...client.Option) (rsp *ListConcurrencyDetailRsp, err error)

	// DescribeConcurrencyUsageGraph 并发调用折线图
	DescribeConcurrencyUsageGraph(ctx context.Context, req *DescribeConcurrencyUsageGraphReq, opts ...client.Option) (rsp *DescribeConcurrencyUsageGraphRsp, err error)

	// ExportConcurrencyInfo 导出并发详情
	ExportConcurrencyInfo(ctx context.Context, req *ExportConcurrencyInfoReq, opts ...client.Option) (rsp *ExportConcurrencyInfoRsp, err error)

	// ExportKnowledgeInfo 导出知识库容量详情
	ExportKnowledgeInfo(ctx context.Context, req *ExportKnowledgeInfoReq, opts ...client.Option) (rsp *ExportKnowledgeInfoRsp, err error)

	// ListReleaseLabelPreview 发布标签预览请求
	ListReleaseLabelPreview(ctx context.Context, req *ListReleaseLabelPreviewReq, opts ...client.Option) (rsp *ListReleaseLabelPreviewRsp, err error)

	// ListReleaseSynonymsPreview 发布同义词预览请求
	ListReleaseSynonymsPreview(ctx context.Context, req *ListReleaseSynonymsPreviewReq, opts ...client.Option) (rsp *ListReleaseSynonymsPreviewRsp, err error)

	// ModifyAppBase 修改应用基础信息
	ModifyAppBase(ctx context.Context, req *ModifyAppBaseReq, opts ...client.Option) (rsp *ModifyAppBaseRsp, err error)

	// CreateCustomModel 创建自定义模型
	CreateCustomModel(ctx context.Context, req *CreateCustomModelReq, opts ...client.Option) (rsp *CreateCustomModelRsp, err error)

	// ModifyCustomModel 编辑自定义模型
	ModifyCustomModel(ctx context.Context, req *ModifyCustomModelReq, opts ...client.Option) (rsp *ModifyCustomModelRsp, err error)

	// DeleteCustomModel 删除自定义模型
	DeleteCustomModel(ctx context.Context, req *DeleteCustomModelReq, opts ...client.Option) (rsp *DeleteCustomModelRsp, err error)

	// DescribeCustomModel 查询自定义模型详情
	DescribeCustomModel(ctx context.Context, req *DescribeCustomModelReq, opts ...client.Option) (rsp *DescribeCustomModelRsp, err error)

	// DescribeModelApps 获取模型关联的应用
	DescribeModelApps(ctx context.Context, req *DescribeModelAppsReq, opts ...client.Option) (rsp *DescribeModelAppsRsp, err error)

	// GetUserGuideViewInfos 查看用户引导是否观看信息
	GetUserGuideViewInfos(ctx context.Context, req *GetUserGuideViewInfosReq, opts ...client.Option) (rsp *GetUserGuideViewInfosRsp, err error)

	// SetUserGuideViewInfo 设置用户引导已观看
	SetUserGuideViewInfo(ctx context.Context, req *SetUserGuideViewInfoReq, opts ...client.Option) (rsp *SetUserGuideViewInfoRsp, err error)

	// ExportMinuteDosage 导出调用统计并发QPM、TPM分钟用量信息
	ExportMinuteDosage(ctx context.Context, req *ExportMinuteDosageReq, opts ...client.Option) (rsp *ExportMinuteDosageRsp, err error)

	// GetImagePreview 图片预览请求,私有读
	GetImagePreview(ctx context.Context, req *GetImagePreviewReq, opts ...client.Option) (rsp *GetImagePreviewRsp, err error)

	// ListVoice 查询音色列表
	ListVoice(ctx context.Context, req *ListVoiceReq, opts ...client.Option) (rsp *ListVoiceRsp, err error)

	// ListDigitalHuman 查询数智人列表
	ListDigitalHuman(ctx context.Context, req *ListDigitalHumanReq, opts ...client.Option) (rsp *ListDigitalHumanRsp, err error)

	// GetPromptTemplateList GetPromptTemplateList 模板中心列表
	GetPromptTemplateList(ctx context.Context, req *GetPromptTemplateListReq, opts ...client.Option) (rsp *GetPromptTemplateListRsp, err error)

	// GetMsgLogList 获取消息日志列表
	GetMsgLogList(ctx context.Context, req *GetMsgLogListReq, opts ...client.Option) (rsp *GetMsgLogListRsp, err error)

	// ExportMsgLog 导出消息日志
	ExportMsgLog(ctx context.Context, req *ExportMsgLogReq, opts ...client.Option) (rsp *ExportMsgLogRsp, err error)

	// ModifyConcurrencyRule 修改并发规则
	ModifyConcurrencyRule(ctx context.Context, req *ModifyConcurrencyRuleReq, opts ...client.Option) (rsp *ModifyConcurrencyRuleRsp, err error)

	// DescribeConcurrencyRule 查看并发规则
	DescribeConcurrencyRule(ctx context.Context, req *DescribeConcurrencyRuleReq, opts ...client.Option) (rsp *DescribeConcurrencyRuleRsp, err error)

	// CheckWhitelist 检查是否命中白名单
	CheckWhitelist(ctx context.Context, req *CheckWhitelistReq, opts ...client.Option) (rsp *CheckWhitelistRsp, err error)

	// GetMsgLogOverview 获取消息日志概览
	GetMsgLogOverview(ctx context.Context, req *GetMsgLogOverviewReq, opts ...client.Option) (rsp *GetMsgLogOverviewRsp, err error)

	// GetMsgLogCountTrend 获取消息数趋势
	GetMsgLogCountTrend(ctx context.Context, req *GetMsgLogCountTrendReq, opts ...client.Option) (rsp *GetMsgLogCountTrendRsp, err error)

	// GetMsgLogUserCountTrend 获取消息互动用户数趋势
	GetMsgLogUserCountTrend(ctx context.Context, req *GetMsgLogUserCountTrendReq, opts ...client.Option) (rsp *GetMsgLogUserCountTrendRsp, err error)

	// GetMsgLogFeedbackCountTrend 获取消息数反馈数趋势
	GetMsgLogFeedbackCountTrend(ctx context.Context, req *GetMsgLogFeedbackCountTrendReq, opts ...client.Option) (rsp *GetMsgLogFeedbackCountTrendRsp, err error)

	// ExportMsgLogStatistical 导出消息统计
	ExportMsgLogStatistical(ctx context.Context, req *ExportMsgLogStatisticalReq, opts ...client.Option) (rsp *ExportMsgLogStatisticalRsp, err error)

	// CreateApproval 创建审批单
	CreateApproval(ctx context.Context, req *CreateApprovalReq, opts ...client.Option) (rsp *CreateApprovalRsp, err error)

	// UpdateApprovalStatus 更新审批单状态
	UpdateApprovalStatus(ctx context.Context, req *UpdateApprovalStatusReq, opts ...client.Option) (rsp *UpdateApprovalStatusRsp, err error)

	// GetLastApproval 获取最新一条审批
	GetLastApproval(ctx context.Context, req *GetLastApprovalReq, opts ...client.Option) (rsp *GetLastApprovalRsp, err error)
}

type AdminClientProxyImpl struct {
	client client.Client
	opts   []client.Option
}

var NewAdminClientProxy = func(opts ...client.Option) AdminClientProxy {
	return &AdminClientProxyImpl{client: client.DefaultClient, opts: opts}
}

func (c *AdminClientProxyImpl) CorpInfo(ctx context.Context, req *CorpInfoReq, opts ...client.Option) (*CorpInfoRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_admin_config_server.Admin/CorpInfo")
	msg.WithCalleeServiceName(AdminServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_admin_config_server")
	msg.WithCalleeService("Admin")
	msg.WithCalleeMethod("CorpInfo")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &CorpInfoRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *AdminClientProxyImpl) DescribeCorp(ctx context.Context, req *DescribeCorpReq, opts ...client.Option) (*DescribeCorpRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_admin_config_server.Admin/DescribeCorp")
	msg.WithCalleeServiceName(AdminServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_admin_config_server")
	msg.WithCalleeService("Admin")
	msg.WithCalleeMethod("DescribeCorp")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &DescribeCorpRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *AdminClientProxyImpl) ListCorpStaff(ctx context.Context, req *ListCorpStaffReq, opts ...client.Option) (*ListCorpStaffRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_admin_config_server.Admin/ListCorpStaff")
	msg.WithCalleeServiceName(AdminServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_admin_config_server")
	msg.WithCalleeService("Admin")
	msg.WithCalleeMethod("ListCorpStaff")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &ListCorpStaffRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *AdminClientProxyImpl) DescribeStorageCredential(ctx context.Context, req *DescribeStorageCredentialReq, opts ...client.Option) (*DescribeStorageCredentialRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_admin_config_server.Admin/DescribeStorageCredential")
	msg.WithCalleeServiceName(AdminServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_admin_config_server")
	msg.WithCalleeService("Admin")
	msg.WithCalleeMethod("DescribeStorageCredential")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &DescribeStorageCredentialRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *AdminClientProxyImpl) GetCredential(ctx context.Context, req *GetCredentialReq, opts ...client.Option) (*GetCredentialRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_admin_config_server.Admin/GetCredential")
	msg.WithCalleeServiceName(AdminServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_admin_config_server")
	msg.WithCalleeService("Admin")
	msg.WithCalleeMethod("GetCredential")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &GetCredentialRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *AdminClientProxyImpl) GetAuditSwitch(ctx context.Context, req *GetAuditSwitchReq, opts ...client.Option) (*GetAuditSwitchRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_admin_config_server.Admin/GetAuditSwitch")
	msg.WithCalleeServiceName(AdminServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_admin_config_server")
	msg.WithCalleeService("Admin")
	msg.WithCalleeMethod("GetAuditSwitch")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &GetAuditSwitchRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *AdminClientProxyImpl) DescribeAuditSwitch(ctx context.Context, req *DescribeAuditSwitchReq, opts ...client.Option) (*DescribeAuditSwitchRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_admin_config_server.Admin/DescribeAuditSwitch")
	msg.WithCalleeServiceName(AdminServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_admin_config_server")
	msg.WithCalleeService("Admin")
	msg.WithCalleeMethod("DescribeAuditSwitch")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &DescribeAuditSwitchRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *AdminClientProxyImpl) ListDoc(ctx context.Context, req *ListDocReq, opts ...client.Option) (*ListDocRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_admin_config_server.Admin/ListDoc")
	msg.WithCalleeServiceName(AdminServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_admin_config_server")
	msg.WithCalleeService("Admin")
	msg.WithCalleeMethod("ListDoc")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &ListDocRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *AdminClientProxyImpl) ListDocV1(ctx context.Context, req *ListDocV1Req, opts ...client.Option) (*ListDocV1Rsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_admin_config_server.Admin/ListDocV1")
	msg.WithCalleeServiceName(AdminServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_admin_config_server")
	msg.WithCalleeService("Admin")
	msg.WithCalleeMethod("ListDocV1")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &ListDocV1Rsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *AdminClientProxyImpl) SaveDocV1(ctx context.Context, req *SaveDocV1Req, opts ...client.Option) (*SaveDocV1Rsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_admin_config_server.Admin/SaveDocV1")
	msg.WithCalleeServiceName(AdminServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_admin_config_server")
	msg.WithCalleeService("Admin")
	msg.WithCalleeMethod("SaveDocV1")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &SaveDocV1Rsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *AdminClientProxyImpl) ModifyDoc(ctx context.Context, req *ModifyDocReq, opts ...client.Option) (*ModifyDocRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_admin_config_server.Admin/ModifyDoc")
	msg.WithCalleeServiceName(AdminServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_admin_config_server")
	msg.WithCalleeService("Admin")
	msg.WithCalleeMethod("ModifyDoc")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &ModifyDocRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *AdminClientProxyImpl) ModifyDocAttrRange(ctx context.Context, req *ModifyDocAttrRangeReq, opts ...client.Option) (*ModifyDocAttrRangeRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_admin_config_server.Admin/ModifyDocAttrRange")
	msg.WithCalleeServiceName(AdminServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_admin_config_server")
	msg.WithCalleeService("Admin")
	msg.WithCalleeMethod("ModifyDocAttrRange")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &ModifyDocAttrRangeRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *AdminClientProxyImpl) ModifyDocV1(ctx context.Context, req *ModifyDocV1Req, opts ...client.Option) (*ModifyDocV1Rsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_admin_config_server.Admin/ModifyDocV1")
	msg.WithCalleeServiceName(AdminServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_admin_config_server")
	msg.WithCalleeService("Admin")
	msg.WithCalleeMethod("ModifyDocV1")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &ModifyDocV1Rsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *AdminClientProxyImpl) DeleteDoc(ctx context.Context, req *DeleteDocReq, opts ...client.Option) (*DeleteDocRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_admin_config_server.Admin/DeleteDoc")
	msg.WithCalleeServiceName(AdminServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_admin_config_server")
	msg.WithCalleeService("Admin")
	msg.WithCalleeMethod("DeleteDoc")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &DeleteDocRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *AdminClientProxyImpl) DescribeDoc(ctx context.Context, req *DescribeDocReq, opts ...client.Option) (*DescribeDocRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_admin_config_server.Admin/DescribeDoc")
	msg.WithCalleeServiceName(AdminServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_admin_config_server")
	msg.WithCalleeService("Admin")
	msg.WithCalleeMethod("DescribeDoc")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &DescribeDocRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *AdminClientProxyImpl) ReferDoc(ctx context.Context, req *ReferDocReq, opts ...client.Option) (*ReferDocRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_admin_config_server.Admin/ReferDoc")
	msg.WithCalleeServiceName(AdminServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_admin_config_server")
	msg.WithCalleeService("Admin")
	msg.WithCalleeMethod("ReferDoc")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &ReferDocRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *AdminClientProxyImpl) StartCreateQA(ctx context.Context, req *StartCreateQAReq, opts ...client.Option) (*StartCreateQARsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_admin_config_server.Admin/StartCreateQA")
	msg.WithCalleeServiceName(AdminServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_admin_config_server")
	msg.WithCalleeService("Admin")
	msg.WithCalleeMethod("StartCreateQA")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &StartCreateQARsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *AdminClientProxyImpl) GetDocPreview(ctx context.Context, req *GetDocPreviewReq, opts ...client.Option) (*GetDocPreviewRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_admin_config_server.Admin/GetDocPreview")
	msg.WithCalleeServiceName(AdminServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_admin_config_server")
	msg.WithCalleeService("Admin")
	msg.WithCalleeMethod("GetDocPreview")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &GetDocPreviewRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *AdminClientProxyImpl) GenerateQA(ctx context.Context, req *GenerateQAReq, opts ...client.Option) (*GenerateQARsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_admin_config_server.Admin/GenerateQA")
	msg.WithCalleeServiceName(AdminServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_admin_config_server")
	msg.WithCalleeService("Admin")
	msg.WithCalleeMethod("GenerateQA")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &GenerateQARsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *AdminClientProxyImpl) GetSelectDoc(ctx context.Context, req *GetSelectDocReq, opts ...client.Option) (*GetSelectDocRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_admin_config_server.Admin/GetSelectDoc")
	msg.WithCalleeServiceName(AdminServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_admin_config_server")
	msg.WithCalleeService("Admin")
	msg.WithCalleeMethod("GetSelectDoc")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &GetSelectDocRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *AdminClientProxyImpl) ListSelectDoc(ctx context.Context, req *ListSelectDocReq, opts ...client.Option) (*ListSelectDocRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_admin_config_server.Admin/ListSelectDoc")
	msg.WithCalleeServiceName(AdminServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_admin_config_server")
	msg.WithCalleeService("Admin")
	msg.WithCalleeMethod("ListSelectDoc")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &ListSelectDocRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *AdminClientProxyImpl) StopDocParse(ctx context.Context, req *StopDocParseReq, opts ...client.Option) (*StopDocParseRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_admin_config_server.Admin/StopDocParse")
	msg.WithCalleeServiceName(AdminServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_admin_config_server")
	msg.WithCalleeService("Admin")
	msg.WithCalleeMethod("StopDocParse")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &StopDocParseRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *AdminClientProxyImpl) RetryDocParse(ctx context.Context, req *RetryDocParseReq, opts ...client.Option) (*RetryDocParseRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_admin_config_server.Admin/RetryDocParse")
	msg.WithCalleeServiceName(AdminServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_admin_config_server")
	msg.WithCalleeService("Admin")
	msg.WithCalleeMethod("RetryDocParse")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &RetryDocParseRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *AdminClientProxyImpl) RetryDocAudit(ctx context.Context, req *RetryDocAuditReq, opts ...client.Option) (*RetryDocAuditRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_admin_config_server.Admin/RetryDocAudit")
	msg.WithCalleeServiceName(AdminServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_admin_config_server")
	msg.WithCalleeService("Admin")
	msg.WithCalleeMethod("RetryDocAudit")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &RetryDocAuditRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *AdminClientProxyImpl) CreateRobot(ctx context.Context, req *CreateRobotReq, opts ...client.Option) (*CreateRobotRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_admin_config_server.Admin/CreateRobot")
	msg.WithCalleeServiceName(AdminServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_admin_config_server")
	msg.WithCalleeService("Admin")
	msg.WithCalleeMethod("CreateRobot")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &CreateRobotRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *AdminClientProxyImpl) DeleteRobot(ctx context.Context, req *DeleteRobotReq, opts ...client.Option) (*DeleteRobotRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_admin_config_server.Admin/DeleteRobot")
	msg.WithCalleeServiceName(AdminServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_admin_config_server")
	msg.WithCalleeService("Admin")
	msg.WithCalleeMethod("DeleteRobot")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &DeleteRobotRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *AdminClientProxyImpl) ModifyRobot(ctx context.Context, req *ModifyRobotReq, opts ...client.Option) (*ModifyRobotRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_admin_config_server.Admin/ModifyRobot")
	msg.WithCalleeServiceName(AdminServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_admin_config_server")
	msg.WithCalleeService("Admin")
	msg.WithCalleeMethod("ModifyRobot")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &ModifyRobotRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *AdminClientProxyImpl) ModifyRobotBasicConfig(ctx context.Context, req *ModifyRobotBasicConfigReq, opts ...client.Option) (*ModifyRobotBasicConfigRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_admin_config_server.Admin/ModifyRobotBasicConfig")
	msg.WithCalleeServiceName(AdminServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_admin_config_server")
	msg.WithCalleeService("Admin")
	msg.WithCalleeMethod("ModifyRobotBasicConfig")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &ModifyRobotBasicConfigRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *AdminClientProxyImpl) UpdateRobot(ctx context.Context, req *UpdateRobotReq, opts ...client.Option) (*UpdateRobotRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_admin_config_server.Admin/UpdateRobot")
	msg.WithCalleeServiceName(AdminServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_admin_config_server")
	msg.WithCalleeService("Admin")
	msg.WithCalleeMethod("UpdateRobot")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &UpdateRobotRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *AdminClientProxyImpl) RobotInfo(ctx context.Context, req *RobotInfoReq, opts ...client.Option) (*RobotInfoRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_admin_config_server.Admin/RobotInfo")
	msg.WithCalleeServiceName(AdminServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_admin_config_server")
	msg.WithCalleeService("Admin")
	msg.WithCalleeMethod("RobotInfo")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &RobotInfoRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *AdminClientProxyImpl) DescribeRobot(ctx context.Context, req *DescribeRobotReq, opts ...client.Option) (*DescribeRobotRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_admin_config_server.Admin/DescribeRobot")
	msg.WithCalleeServiceName(AdminServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_admin_config_server")
	msg.WithCalleeService("Admin")
	msg.WithCalleeMethod("DescribeRobot")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &DescribeRobotRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *AdminClientProxyImpl) ListRobot(ctx context.Context, req *ListRobotReq, opts ...client.Option) (*ListRobotRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_admin_config_server.Admin/ListRobot")
	msg.WithCalleeServiceName(AdminServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_admin_config_server")
	msg.WithCalleeService("Admin")
	msg.WithCalleeMethod("ListRobot")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &ListRobotRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *AdminClientProxyImpl) ListRobotV1(ctx context.Context, req *ListRobotV1Req, opts ...client.Option) (*ListRobotV1Rsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_admin_config_server.Admin/ListRobotV1")
	msg.WithCalleeServiceName(AdminServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_admin_config_server")
	msg.WithCalleeService("Admin")
	msg.WithCalleeMethod("ListRobotV1")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &ListRobotV1Rsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *AdminClientProxyImpl) AccountInfo(ctx context.Context, req *AccountInfoReq, opts ...client.Option) (*AccountInfoRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_admin_config_server.Admin/AccountInfo")
	msg.WithCalleeServiceName(AdminServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_admin_config_server")
	msg.WithCalleeService("Admin")
	msg.WithCalleeMethod("AccountInfo")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &AccountInfoRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *AdminClientProxyImpl) DescribeAccount(ctx context.Context, req *DescribeAccountReq, opts ...client.Option) (*DescribeAccountRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_admin_config_server.Admin/DescribeAccount")
	msg.WithCalleeServiceName(AdminServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_admin_config_server")
	msg.WithCalleeService("Admin")
	msg.WithCalleeMethod("DescribeAccount")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &DescribeAccountRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *AdminClientProxyImpl) ListRole(ctx context.Context, req *ListRoleReq, opts ...client.Option) (*ListRoleRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_admin_config_server.Admin/ListRole")
	msg.WithCalleeServiceName(AdminServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_admin_config_server")
	msg.WithCalleeService("Admin")
	msg.WithCalleeMethod("ListRole")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &ListRoleRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *AdminClientProxyImpl) ListRobotRole(ctx context.Context, req *ListRobotRoleReq, opts ...client.Option) (*ListRobotRoleRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_admin_config_server.Admin/ListRobotRole")
	msg.WithCalleeServiceName(AdminServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_admin_config_server")
	msg.WithCalleeService("Admin")
	msg.WithCalleeMethod("ListRobotRole")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &ListRobotRoleRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *AdminClientProxyImpl) ListQACate(ctx context.Context, req *ListQACateReq, opts ...client.Option) (*ListQACateRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_admin_config_server.Admin/ListQACate")
	msg.WithCalleeServiceName(AdminServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_admin_config_server")
	msg.WithCalleeService("Admin")
	msg.WithCalleeMethod("ListQACate")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &ListQACateRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *AdminClientProxyImpl) ListQACateV1(ctx context.Context, req *ListQACateV1Req, opts ...client.Option) (*ListQACateV1Rsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_admin_config_server.Admin/ListQACateV1")
	msg.WithCalleeServiceName(AdminServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_admin_config_server")
	msg.WithCalleeService("Admin")
	msg.WithCalleeMethod("ListQACateV1")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &ListQACateV1Rsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *AdminClientProxyImpl) CreateQACate(ctx context.Context, req *CreateQACateReq, opts ...client.Option) (*CreateQACateRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_admin_config_server.Admin/CreateQACate")
	msg.WithCalleeServiceName(AdminServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_admin_config_server")
	msg.WithCalleeService("Admin")
	msg.WithCalleeMethod("CreateQACate")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &CreateQACateRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *AdminClientProxyImpl) CreateQACateV1(ctx context.Context, req *CreateQACateV1Req, opts ...client.Option) (*CreateQACateV1Rsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_admin_config_server.Admin/CreateQACateV1")
	msg.WithCalleeServiceName(AdminServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_admin_config_server")
	msg.WithCalleeService("Admin")
	msg.WithCalleeMethod("CreateQACateV1")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &CreateQACateV1Rsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *AdminClientProxyImpl) UpdateQACate(ctx context.Context, req *UpdateQACateReq, opts ...client.Option) (*UpdateQACateRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_admin_config_server.Admin/UpdateQACate")
	msg.WithCalleeServiceName(AdminServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_admin_config_server")
	msg.WithCalleeService("Admin")
	msg.WithCalleeMethod("UpdateQACate")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &UpdateQACateRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *AdminClientProxyImpl) ModifyQACate(ctx context.Context, req *ModifyQACateReq, opts ...client.Option) (*ModifyQACateRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_admin_config_server.Admin/ModifyQACate")
	msg.WithCalleeServiceName(AdminServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_admin_config_server")
	msg.WithCalleeService("Admin")
	msg.WithCalleeMethod("ModifyQACate")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &ModifyQACateRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *AdminClientProxyImpl) DeleteQACate(ctx context.Context, req *DeleteQACateReq, opts ...client.Option) (*DeleteQACateRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_admin_config_server.Admin/DeleteQACate")
	msg.WithCalleeServiceName(AdminServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_admin_config_server")
	msg.WithCalleeService("Admin")
	msg.WithCalleeMethod("DeleteQACate")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &DeleteQACateRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *AdminClientProxyImpl) GetQAList(ctx context.Context, req *GetQAListReq, opts ...client.Option) (*GetQAListRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_admin_config_server.Admin/GetQAList")
	msg.WithCalleeServiceName(AdminServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_admin_config_server")
	msg.WithCalleeService("Admin")
	msg.WithCalleeMethod("GetQAList")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &GetQAListRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *AdminClientProxyImpl) ListQA(ctx context.Context, req *ListQAReq, opts ...client.Option) (*ListQARsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_admin_config_server.Admin/ListQA")
	msg.WithCalleeServiceName(AdminServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_admin_config_server")
	msg.WithCalleeService("Admin")
	msg.WithCalleeMethod("ListQA")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &ListQARsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *AdminClientProxyImpl) GetQADetail(ctx context.Context, req *GetQADetailReq, opts ...client.Option) (*GetQADetailRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_admin_config_server.Admin/GetQADetail")
	msg.WithCalleeServiceName(AdminServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_admin_config_server")
	msg.WithCalleeService("Admin")
	msg.WithCalleeMethod("GetQADetail")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &GetQADetailRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *AdminClientProxyImpl) DescribeQA(ctx context.Context, req *DescribeQAReq, opts ...client.Option) (*DescribeQARsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_admin_config_server.Admin/DescribeQA")
	msg.WithCalleeServiceName(AdminServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_admin_config_server")
	msg.WithCalleeService("Admin")
	msg.WithCalleeMethod("DescribeQA")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &DescribeQARsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *AdminClientProxyImpl) CreateQA(ctx context.Context, req *CreateQAReq, opts ...client.Option) (*CreateQARsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_admin_config_server.Admin/CreateQA")
	msg.WithCalleeServiceName(AdminServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_admin_config_server")
	msg.WithCalleeService("Admin")
	msg.WithCalleeMethod("CreateQA")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &CreateQARsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *AdminClientProxyImpl) CreateQAV1(ctx context.Context, req *CreateQAV1Req, opts ...client.Option) (*CreateQAV1Rsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_admin_config_server.Admin/CreateQAV1")
	msg.WithCalleeServiceName(AdminServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_admin_config_server")
	msg.WithCalleeService("Admin")
	msg.WithCalleeMethod("CreateQAV1")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &CreateQAV1Rsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *AdminClientProxyImpl) UpdateQA(ctx context.Context, req *UpdateQAReq, opts ...client.Option) (*UpdateQARsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_admin_config_server.Admin/UpdateQA")
	msg.WithCalleeServiceName(AdminServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_admin_config_server")
	msg.WithCalleeService("Admin")
	msg.WithCalleeMethod("UpdateQA")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &UpdateQARsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *AdminClientProxyImpl) ModifyQA(ctx context.Context, req *ModifyQAReq, opts ...client.Option) (*ModifyQARsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_admin_config_server.Admin/ModifyQA")
	msg.WithCalleeServiceName(AdminServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_admin_config_server")
	msg.WithCalleeService("Admin")
	msg.WithCalleeMethod("ModifyQA")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &ModifyQARsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *AdminClientProxyImpl) DeleteQA(ctx context.Context, req *DeleteQAReq, opts ...client.Option) (*DeleteQARsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_admin_config_server.Admin/DeleteQA")
	msg.WithCalleeServiceName(AdminServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_admin_config_server")
	msg.WithCalleeService("Admin")
	msg.WithCalleeMethod("DeleteQA")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &DeleteQARsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *AdminClientProxyImpl) DeleteQAV1(ctx context.Context, req *DeleteQAV1Req, opts ...client.Option) (*DeleteQAV1Rsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_admin_config_server.Admin/DeleteQAV1")
	msg.WithCalleeServiceName(AdminServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_admin_config_server")
	msg.WithCalleeService("Admin")
	msg.WithCalleeMethod("DeleteQAV1")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &DeleteQAV1Rsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *AdminClientProxyImpl) VerifyQA(ctx context.Context, req *VerifyQAReq, opts ...client.Option) (*VerifyQARsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_admin_config_server.Admin/VerifyQA")
	msg.WithCalleeServiceName(AdminServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_admin_config_server")
	msg.WithCalleeService("Admin")
	msg.WithCalleeMethod("VerifyQA")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &VerifyQARsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *AdminClientProxyImpl) VerifyQAV1(ctx context.Context, req *VerifyQAV1Req, opts ...client.Option) (*VerifyQAV1Rsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_admin_config_server.Admin/VerifyQAV1")
	msg.WithCalleeServiceName(AdminServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_admin_config_server")
	msg.WithCalleeService("Admin")
	msg.WithCalleeMethod("VerifyQAV1")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &VerifyQAV1Rsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *AdminClientProxyImpl) GroupQA(ctx context.Context, req *GroupQAReq, opts ...client.Option) (*GroupQARsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_admin_config_server.Admin/GroupQA")
	msg.WithCalleeServiceName(AdminServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_admin_config_server")
	msg.WithCalleeService("Admin")
	msg.WithCalleeMethod("GroupQA")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &GroupQARsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *AdminClientProxyImpl) ModifyQAAttrRange(ctx context.Context, req *ModifyQAAttrRangeReq, opts ...client.Option) (*ModifyQAAttrRangeRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_admin_config_server.Admin/ModifyQAAttrRange")
	msg.WithCalleeServiceName(AdminServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_admin_config_server")
	msg.WithCalleeService("Admin")
	msg.WithCalleeMethod("ModifyQAAttrRange")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &ModifyQAAttrRangeRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *AdminClientProxyImpl) ExportQAList(ctx context.Context, req *ExportQAListReq, opts ...client.Option) (*ExportQAListRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_admin_config_server.Admin/ExportQAList")
	msg.WithCalleeServiceName(AdminServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_admin_config_server")
	msg.WithCalleeService("Admin")
	msg.WithCalleeMethod("ExportQAList")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &ExportQAListRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *AdminClientProxyImpl) ExportQAListV1(ctx context.Context, req *ExportQAListReqV1, opts ...client.Option) (*ExportQAListRspV1, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_admin_config_server.Admin/ExportQAListV1")
	msg.WithCalleeServiceName(AdminServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_admin_config_server")
	msg.WithCalleeService("Admin")
	msg.WithCalleeMethod("ExportQAListV1")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &ExportQAListRspV1{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *AdminClientProxyImpl) GetReleaseInfo(ctx context.Context, req *GetReleaseInfoReq, opts ...client.Option) (*GetReleaseInfoRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_admin_config_server.Admin/GetReleaseInfo")
	msg.WithCalleeServiceName(AdminServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_admin_config_server")
	msg.WithCalleeService("Admin")
	msg.WithCalleeMethod("GetReleaseInfo")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &GetReleaseInfoRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *AdminClientProxyImpl) DescribeReleaseInfo(ctx context.Context, req *DescribeReleaseInfoReq, opts ...client.Option) (*DescribeReleaseInfoRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_admin_config_server.Admin/DescribeReleaseInfo")
	msg.WithCalleeServiceName(AdminServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_admin_config_server")
	msg.WithCalleeService("Admin")
	msg.WithCalleeMethod("DescribeReleaseInfo")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &DescribeReleaseInfoRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *AdminClientProxyImpl) ListReleaseDocPreview(ctx context.Context, req *ListReleaseDocPreviewReq, opts ...client.Option) (*ListReleaseDocPreviewRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_admin_config_server.Admin/ListReleaseDocPreview")
	msg.WithCalleeServiceName(AdminServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_admin_config_server")
	msg.WithCalleeService("Admin")
	msg.WithCalleeMethod("ListReleaseDocPreview")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &ListReleaseDocPreviewRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *AdminClientProxyImpl) ListReleaseQAPreview(ctx context.Context, req *ListReleaseQAPreviewReq, opts ...client.Option) (*ListReleaseQAPreviewRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_admin_config_server.Admin/ListReleaseQAPreview")
	msg.WithCalleeServiceName(AdminServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_admin_config_server")
	msg.WithCalleeService("Admin")
	msg.WithCalleeMethod("ListReleaseQAPreview")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &ListReleaseQAPreviewRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *AdminClientProxyImpl) ListRejectedQuestionPreview(ctx context.Context, req *ListRejectedQuestionPreviewReq, opts ...client.Option) (*ListRejectedQuestionPreviewRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_admin_config_server.Admin/ListRejectedQuestionPreview")
	msg.WithCalleeServiceName(AdminServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_admin_config_server")
	msg.WithCalleeService("Admin")
	msg.WithCalleeMethod("ListRejectedQuestionPreview")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &ListRejectedQuestionPreviewRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *AdminClientProxyImpl) ListReleaseConfigPreview(ctx context.Context, req *ListReleaseConfigPreviewReq, opts ...client.Option) (*ListReleaseConfigPreviewRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_admin_config_server.Admin/ListReleaseConfigPreview")
	msg.WithCalleeServiceName(AdminServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_admin_config_server")
	msg.WithCalleeService("Admin")
	msg.WithCalleeMethod("ListReleaseConfigPreview")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &ListReleaseConfigPreviewRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *AdminClientProxyImpl) GetReleaseDocPreview(ctx context.Context, req *GetReleaseDocPreviewReq, opts ...client.Option) (*GetReleaseDocPreviewRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_admin_config_server.Admin/GetReleaseDocPreview")
	msg.WithCalleeServiceName(AdminServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_admin_config_server")
	msg.WithCalleeService("Admin")
	msg.WithCalleeMethod("GetReleaseDocPreview")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &GetReleaseDocPreviewRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *AdminClientProxyImpl) GetReleaseQAPreview(ctx context.Context, req *GetReleaseQAPreviewReq, opts ...client.Option) (*GetReleaseQAPreviewRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_admin_config_server.Admin/GetReleaseQAPreview")
	msg.WithCalleeServiceName(AdminServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_admin_config_server")
	msg.WithCalleeService("Admin")
	msg.WithCalleeMethod("GetReleaseQAPreview")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &GetReleaseQAPreviewRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *AdminClientProxyImpl) PreviewRejectedQuestion(ctx context.Context, req *PreviewRejectedQuestionReq, opts ...client.Option) (*PreviewRejectedQuestionRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_admin_config_server.Admin/PreviewRejectedQuestion")
	msg.WithCalleeServiceName(AdminServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_admin_config_server")
	msg.WithCalleeService("Admin")
	msg.WithCalleeMethod("PreviewRejectedQuestion")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &PreviewRejectedQuestionRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *AdminClientProxyImpl) CreateRelease(ctx context.Context, req *CreateReleaseReq, opts ...client.Option) (*CreateReleaseRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_admin_config_server.Admin/CreateRelease")
	msg.WithCalleeServiceName(AdminServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_admin_config_server")
	msg.WithCalleeService("Admin")
	msg.WithCalleeMethod("CreateRelease")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &CreateReleaseRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *AdminClientProxyImpl) ListRelease(ctx context.Context, req *ListReleaseReq, opts ...client.Option) (*ListReleaseRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_admin_config_server.Admin/ListRelease")
	msg.WithCalleeServiceName(AdminServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_admin_config_server")
	msg.WithCalleeService("Admin")
	msg.WithCalleeMethod("ListRelease")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &ListReleaseRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *AdminClientProxyImpl) GetReleaseRecordList(ctx context.Context, req *GetReleaseRecordListReq, opts ...client.Option) (*GetReleaseRecordListRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_admin_config_server.Admin/GetReleaseRecordList")
	msg.WithCalleeServiceName(AdminServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_admin_config_server")
	msg.WithCalleeService("Admin")
	msg.WithCalleeMethod("GetReleaseRecordList")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &GetReleaseRecordListRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *AdminClientProxyImpl) CheckUnconfirmedQa(ctx context.Context, req *CheckUnconfirmedQaReq, opts ...client.Option) (*CheckUnconfirmedQaRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_admin_config_server.Admin/CheckUnconfirmedQa")
	msg.WithCalleeServiceName(AdminServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_admin_config_server")
	msg.WithCalleeService("Admin")
	msg.WithCalleeMethod("CheckUnconfirmedQa")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &CheckUnconfirmedQaRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *AdminClientProxyImpl) DescribeRelease(ctx context.Context, req *DescribeReleaseReq, opts ...client.Option) (*DescribeReleaseRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_admin_config_server.Admin/DescribeRelease")
	msg.WithCalleeServiceName(AdminServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_admin_config_server")
	msg.WithCalleeService("Admin")
	msg.WithCalleeMethod("DescribeRelease")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &DescribeReleaseRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *AdminClientProxyImpl) RetryRelease(ctx context.Context, req *RetryReleaseReq, opts ...client.Option) (*RetryReleaseRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_admin_config_server.Admin/RetryRelease")
	msg.WithCalleeServiceName(AdminServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_admin_config_server")
	msg.WithCalleeService("Admin")
	msg.WithCalleeMethod("RetryRelease")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &RetryReleaseRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *AdminClientProxyImpl) GetQaSimilar(ctx context.Context, req *GetQaSimilarReq, opts ...client.Option) (*GetQaSimilarRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_admin_config_server.Admin/GetQaSimilar")
	msg.WithCalleeServiceName(AdminServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_admin_config_server")
	msg.WithCalleeService("Admin")
	msg.WithCalleeMethod("GetQaSimilar")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &GetQaSimilarRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *AdminClientProxyImpl) ListQaSimilar(ctx context.Context, req *ListQaSimilarReq, opts ...client.Option) (*ListQaSimilarRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_admin_config_server.Admin/ListQaSimilar")
	msg.WithCalleeServiceName(AdminServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_admin_config_server")
	msg.WithCalleeService("Admin")
	msg.WithCalleeMethod("ListQaSimilar")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &ListQaSimilarRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *AdminClientProxyImpl) GetQaSimilarDetail(ctx context.Context, req *GetQaSimilarDetailReq, opts ...client.Option) (*GetQaSimilarDetailRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_admin_config_server.Admin/GetQaSimilarDetail")
	msg.WithCalleeServiceName(AdminServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_admin_config_server")
	msg.WithCalleeService("Admin")
	msg.WithCalleeMethod("GetQaSimilarDetail")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &GetQaSimilarDetailRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *AdminClientProxyImpl) DescribeQaSimilar(ctx context.Context, req *DescribeQaSimilarReq, opts ...client.Option) (*DescribeQaSimilarRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_admin_config_server.Admin/DescribeQaSimilar")
	msg.WithCalleeServiceName(AdminServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_admin_config_server")
	msg.WithCalleeService("Admin")
	msg.WithCalleeMethod("DescribeQaSimilar")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &DescribeQaSimilarRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *AdminClientProxyImpl) SubmitQaSimilar(ctx context.Context, req *SubmitQaSimilarReq, opts ...client.Option) (*SubmitQaSimilarRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_admin_config_server.Admin/SubmitQaSimilar")
	msg.WithCalleeServiceName(AdminServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_admin_config_server")
	msg.WithCalleeService("Admin")
	msg.WithCalleeMethod("SubmitQaSimilar")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &SubmitQaSimilarRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *AdminClientProxyImpl) ListNotify(ctx context.Context, req *ListNotifyReq, opts ...client.Option) (*ListNotifyRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_admin_config_server.Admin/ListNotify")
	msg.WithCalleeServiceName(AdminServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_admin_config_server")
	msg.WithCalleeService("Admin")
	msg.WithCalleeMethod("ListNotify")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &ListNotifyRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *AdminClientProxyImpl) GetNotify(ctx context.Context, req *GetNotifyReq, opts ...client.Option) (*GetNotifyRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_admin_config_server.Admin/GetNotify")
	msg.WithCalleeServiceName(AdminServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_admin_config_server")
	msg.WithCalleeService("Admin")
	msg.WithCalleeMethod("GetNotify")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &GetNotifyRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *AdminClientProxyImpl) GetHistoryNotify(ctx context.Context, req *GetHistoryNotifyReq, opts ...client.Option) (*GetHistoryNotifyRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_admin_config_server.Admin/GetHistoryNotify")
	msg.WithCalleeServiceName(AdminServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_admin_config_server")
	msg.WithCalleeService("Admin")
	msg.WithCalleeMethod("GetHistoryNotify")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &GetHistoryNotifyRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *AdminClientProxyImpl) ListHistoryNotify(ctx context.Context, req *ListHistoryNotifyReq, opts ...client.Option) (*ListHistoryNotifyRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_admin_config_server.Admin/ListHistoryNotify")
	msg.WithCalleeServiceName(AdminServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_admin_config_server")
	msg.WithCalleeService("Admin")
	msg.WithCalleeMethod("ListHistoryNotify")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &ListHistoryNotifyRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *AdminClientProxyImpl) ReadNotify(ctx context.Context, req *ReadNotifyReq, opts ...client.Option) (*ReadNotifyRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_admin_config_server.Admin/ReadNotify")
	msg.WithCalleeServiceName(AdminServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_admin_config_server")
	msg.WithCalleeService("Admin")
	msg.WithCalleeMethod("ReadNotify")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &ReadNotifyRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *AdminClientProxyImpl) CloseNotify(ctx context.Context, req *CloseNotifyReq, opts ...client.Option) (*CloseNotifyRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_admin_config_server.Admin/CloseNotify")
	msg.WithCalleeServiceName(AdminServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_admin_config_server")
	msg.WithCalleeService("Admin")
	msg.WithCalleeMethod("CloseNotify")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &CloseNotifyRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *AdminClientProxyImpl) GetReferDetail(ctx context.Context, req *GetReferDetailReq, opts ...client.Option) (*GetReferDetailRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_admin_config_server.Admin/GetReferDetail")
	msg.WithCalleeServiceName(AdminServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_admin_config_server")
	msg.WithCalleeService("Admin")
	msg.WithCalleeMethod("GetReferDetail")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &GetReferDetailRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *AdminClientProxyImpl) DescribeRefer(ctx context.Context, req *DescribeReferReq, opts ...client.Option) (*DescribeReferRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_admin_config_server.Admin/DescribeRefer")
	msg.WithCalleeServiceName(AdminServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_admin_config_server")
	msg.WithCalleeService("Admin")
	msg.WithCalleeMethod("DescribeRefer")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &DescribeReferRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *AdminClientProxyImpl) MarkRefer(ctx context.Context, req *MarkReferReq, opts ...client.Option) (*MarkReferRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_admin_config_server.Admin/MarkRefer")
	msg.WithCalleeServiceName(AdminServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_admin_config_server")
	msg.WithCalleeService("Admin")
	msg.WithCalleeMethod("MarkRefer")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &MarkReferRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *AdminClientProxyImpl) UploadSampleFile(ctx context.Context, req *UploadSampleReq, opts ...client.Option) (*UploadSampleRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_admin_config_server.Admin/UploadSampleFile")
	msg.WithCalleeServiceName(AdminServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_admin_config_server")
	msg.WithCalleeService("Admin")
	msg.WithCalleeMethod("UploadSampleFile")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &UploadSampleRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *AdminClientProxyImpl) UploadSampleSet(ctx context.Context, req *UploadSampleSetReq, opts ...client.Option) (*UploadSampleSetRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_admin_config_server.Admin/UploadSampleSet")
	msg.WithCalleeServiceName(AdminServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_admin_config_server")
	msg.WithCalleeService("Admin")
	msg.WithCalleeMethod("UploadSampleSet")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &UploadSampleSetRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *AdminClientProxyImpl) UploadSampleSetWithCheck(ctx context.Context, req *UploadSampleSetWithCheckReq, opts ...client.Option) (*UploadSampleSetWithCheckRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_admin_config_server.Admin/UploadSampleSetWithCheck")
	msg.WithCalleeServiceName(AdminServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_admin_config_server")
	msg.WithCalleeService("Admin")
	msg.WithCalleeMethod("UploadSampleSetWithCheck")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &UploadSampleSetWithCheckRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *AdminClientProxyImpl) QuerySampleSetList(ctx context.Context, req *QuerySampleReq, opts ...client.Option) (*QuerySampleRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_admin_config_server.Admin/QuerySampleSetList")
	msg.WithCalleeServiceName(AdminServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_admin_config_server")
	msg.WithCalleeService("Admin")
	msg.WithCalleeMethod("QuerySampleSetList")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &QuerySampleRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *AdminClientProxyImpl) DeleteSampleFiles(ctx context.Context, req *DeleteSampleReq, opts ...client.Option) (*DeleteSampleRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_admin_config_server.Admin/DeleteSampleFiles")
	msg.WithCalleeServiceName(AdminServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_admin_config_server")
	msg.WithCalleeService("Admin")
	msg.WithCalleeMethod("DeleteSampleFiles")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &DeleteSampleRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *AdminClientProxyImpl) ListSampleSet(ctx context.Context, req *ListSampleSetReq, opts ...client.Option) (*ListSampleSetRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_admin_config_server.Admin/ListSampleSet")
	msg.WithCalleeServiceName(AdminServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_admin_config_server")
	msg.WithCalleeService("Admin")
	msg.WithCalleeMethod("ListSampleSet")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &ListSampleSetRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *AdminClientProxyImpl) DeleteSampleSet(ctx context.Context, req *DeleteSampleSetReq, opts ...client.Option) (*DeleteSampleSetRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_admin_config_server.Admin/DeleteSampleSet")
	msg.WithCalleeServiceName(AdminServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_admin_config_server")
	msg.WithCalleeService("Admin")
	msg.WithCalleeMethod("DeleteSampleSet")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &DeleteSampleSetRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *AdminClientProxyImpl) CreateTest(ctx context.Context, req *CreateTestReq, opts ...client.Option) (*CreateTestRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_admin_config_server.Admin/CreateTest")
	msg.WithCalleeServiceName(AdminServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_admin_config_server")
	msg.WithCalleeService("Admin")
	msg.WithCalleeMethod("CreateTest")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &CreateTestRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *AdminClientProxyImpl) CreateEvaluateTest(ctx context.Context, req *CreateEvaluateTestReq, opts ...client.Option) (*CreateEvaluateTestRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_admin_config_server.Admin/CreateEvaluateTest")
	msg.WithCalleeServiceName(AdminServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_admin_config_server")
	msg.WithCalleeService("Admin")
	msg.WithCalleeMethod("CreateEvaluateTest")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &CreateEvaluateTestRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *AdminClientProxyImpl) QueryTestList(ctx context.Context, req *QueryTestReq, opts ...client.Option) (*QueryTestRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_admin_config_server.Admin/QueryTestList")
	msg.WithCalleeServiceName(AdminServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_admin_config_server")
	msg.WithCalleeService("Admin")
	msg.WithCalleeMethod("QueryTestList")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &QueryTestRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *AdminClientProxyImpl) ListEvaluateTest(ctx context.Context, req *ListEvaluateTestReq, opts ...client.Option) (*ListEvaluateTestRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_admin_config_server.Admin/ListEvaluateTest")
	msg.WithCalleeServiceName(AdminServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_admin_config_server")
	msg.WithCalleeService("Admin")
	msg.WithCalleeMethod("ListEvaluateTest")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &ListEvaluateTestRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *AdminClientProxyImpl) DeleteTest(ctx context.Context, req *DeleteTestReq, opts ...client.Option) (*DeleteTestRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_admin_config_server.Admin/DeleteTest")
	msg.WithCalleeServiceName(AdminServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_admin_config_server")
	msg.WithCalleeService("Admin")
	msg.WithCalleeMethod("DeleteTest")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &DeleteTestRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *AdminClientProxyImpl) StopTest(ctx context.Context, req *StopTestReq, opts ...client.Option) (*StopTestRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_admin_config_server.Admin/StopTest")
	msg.WithCalleeServiceName(AdminServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_admin_config_server")
	msg.WithCalleeService("Admin")
	msg.WithCalleeMethod("StopTest")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &StopTestRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *AdminClientProxyImpl) RetryTest(ctx context.Context, req *RetryTestReq, opts ...client.Option) (*RetryTestRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_admin_config_server.Admin/RetryTest")
	msg.WithCalleeServiceName(AdminServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_admin_config_server")
	msg.WithCalleeService("Admin")
	msg.WithCalleeMethod("RetryTest")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &RetryTestRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *AdminClientProxyImpl) DeleteEvaluateTest(ctx context.Context, req *DeleteEvaluateTestReq, opts ...client.Option) (*DeleteEvaluateTestRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_admin_config_server.Admin/DeleteEvaluateTest")
	msg.WithCalleeServiceName(AdminServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_admin_config_server")
	msg.WithCalleeService("Admin")
	msg.WithCalleeMethod("DeleteEvaluateTest")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &DeleteEvaluateTestRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *AdminClientProxyImpl) StopEvaluateTest(ctx context.Context, req *StopEvaluateTestReq, opts ...client.Option) (*StopEvaluateTestRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_admin_config_server.Admin/StopEvaluateTest")
	msg.WithCalleeServiceName(AdminServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_admin_config_server")
	msg.WithCalleeService("Admin")
	msg.WithCalleeMethod("StopEvaluateTest")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &StopEvaluateTestRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *AdminClientProxyImpl) RetryEvaluateTest(ctx context.Context, req *RetryEvaluateTestReq, opts ...client.Option) (*RetryEvaluateTestRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_admin_config_server.Admin/RetryEvaluateTest")
	msg.WithCalleeServiceName(AdminServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_admin_config_server")
	msg.WithCalleeService("Admin")
	msg.WithCalleeMethod("RetryEvaluateTest")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &RetryEvaluateTestRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *AdminClientProxyImpl) GetOneWaitJudging(ctx context.Context, req *GetOneJudgingReq, opts ...client.Option) (*GetOneJudgingRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_admin_config_server.Admin/GetOneWaitJudging")
	msg.WithCalleeServiceName(AdminServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_admin_config_server")
	msg.WithCalleeService("Admin")
	msg.WithCalleeMethod("GetOneWaitJudging")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &GetOneJudgingRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *AdminClientProxyImpl) DescribeWaitJudgeRecord(ctx context.Context, req *DescribeWaitJudgeRecordReq, opts ...client.Option) (*DescribeWaitJudgeRecordRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_admin_config_server.Admin/DescribeWaitJudgeRecord")
	msg.WithCalleeServiceName(AdminServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_admin_config_server")
	msg.WithCalleeService("Admin")
	msg.WithCalleeMethod("DescribeWaitJudgeRecord")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &DescribeWaitJudgeRecordRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *AdminClientProxyImpl) GetRecord(ctx context.Context, req *GetRecordReq, opts ...client.Option) (*GetRecordRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_admin_config_server.Admin/GetRecord")
	msg.WithCalleeServiceName(AdminServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_admin_config_server")
	msg.WithCalleeService("Admin")
	msg.WithCalleeMethod("GetRecord")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &GetRecordRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *AdminClientProxyImpl) DescribeRecord(ctx context.Context, req *DescribeRecordReq, opts ...client.Option) (*DescribeRecordRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_admin_config_server.Admin/DescribeRecord")
	msg.WithCalleeServiceName(AdminServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_admin_config_server")
	msg.WithCalleeService("Admin")
	msg.WithCalleeMethod("DescribeRecord")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &DescribeRecordRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *AdminClientProxyImpl) JudgeRecord(ctx context.Context, req *JudgeReq, opts ...client.Option) (*JudgeRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_admin_config_server.Admin/JudgeRecord")
	msg.WithCalleeServiceName(AdminServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_admin_config_server")
	msg.WithCalleeService("Admin")
	msg.WithCalleeMethod("JudgeRecord")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &JudgeRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *AdminClientProxyImpl) GetRejectedQuestionList(ctx context.Context, req *GetRejectedQuestionListReq, opts ...client.Option) (*GetRejectedQuestionListRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_admin_config_server.Admin/GetRejectedQuestionList")
	msg.WithCalleeServiceName(AdminServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_admin_config_server")
	msg.WithCalleeService("Admin")
	msg.WithCalleeMethod("GetRejectedQuestionList")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &GetRejectedQuestionListRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *AdminClientProxyImpl) ListRejectedQuestion(ctx context.Context, req *ListRejectedQuestionReq, opts ...client.Option) (*ListRejectedQuestionRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_admin_config_server.Admin/ListRejectedQuestion")
	msg.WithCalleeServiceName(AdminServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_admin_config_server")
	msg.WithCalleeService("Admin")
	msg.WithCalleeMethod("ListRejectedQuestion")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &ListRejectedQuestionRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *AdminClientProxyImpl) CreateRejectedQuestion(ctx context.Context, req *CreateRejectedQuestionReq, opts ...client.Option) (*CreateRejectedQuestionRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_admin_config_server.Admin/CreateRejectedQuestion")
	msg.WithCalleeServiceName(AdminServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_admin_config_server")
	msg.WithCalleeService("Admin")
	msg.WithCalleeMethod("CreateRejectedQuestion")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &CreateRejectedQuestionRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *AdminClientProxyImpl) UpdateRejectedQuestion(ctx context.Context, req *UpdateRejectedQuestionReq, opts ...client.Option) (*UpdateRejectedQuestionRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_admin_config_server.Admin/UpdateRejectedQuestion")
	msg.WithCalleeServiceName(AdminServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_admin_config_server")
	msg.WithCalleeService("Admin")
	msg.WithCalleeMethod("UpdateRejectedQuestion")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &UpdateRejectedQuestionRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *AdminClientProxyImpl) ModifyRejectedQuestion(ctx context.Context, req *ModifyRejectedQuestionReq, opts ...client.Option) (*ModifyRejectedQuestionRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_admin_config_server.Admin/ModifyRejectedQuestion")
	msg.WithCalleeServiceName(AdminServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_admin_config_server")
	msg.WithCalleeService("Admin")
	msg.WithCalleeMethod("ModifyRejectedQuestion")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &ModifyRejectedQuestionRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *AdminClientProxyImpl) DeleteRejectedQuestion(ctx context.Context, req *DeleteRejectedQuestionReq, opts ...client.Option) (*DeleteRejectedQuestionRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_admin_config_server.Admin/DeleteRejectedQuestion")
	msg.WithCalleeServiceName(AdminServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_admin_config_server")
	msg.WithCalleeService("Admin")
	msg.WithCalleeMethod("DeleteRejectedQuestion")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &DeleteRejectedQuestionRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *AdminClientProxyImpl) ExportRejectedQuestion(ctx context.Context, req *ExportRejectedQuestionReq, opts ...client.Option) (*ExportRejectedQuestionRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_admin_config_server.Admin/ExportRejectedQuestion")
	msg.WithCalleeServiceName(AdminServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_admin_config_server")
	msg.WithCalleeService("Admin")
	msg.WithCalleeMethod("ExportRejectedQuestion")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &ExportRejectedQuestionRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *AdminClientProxyImpl) GetUnsatisfiedReply(ctx context.Context, req *GetUnsatisfiedReplyReq, opts ...client.Option) (*GetUnsatisfiedReplyRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_admin_config_server.Admin/GetUnsatisfiedReply")
	msg.WithCalleeServiceName(AdminServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_admin_config_server")
	msg.WithCalleeService("Admin")
	msg.WithCalleeMethod("GetUnsatisfiedReply")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &GetUnsatisfiedReplyRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *AdminClientProxyImpl) ListUnsatisfiedReply(ctx context.Context, req *ListUnsatisfiedReplyReq, opts ...client.Option) (*ListUnsatisfiedReplyRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_admin_config_server.Admin/ListUnsatisfiedReply")
	msg.WithCalleeServiceName(AdminServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_admin_config_server")
	msg.WithCalleeService("Admin")
	msg.WithCalleeMethod("ListUnsatisfiedReply")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &ListUnsatisfiedReplyRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *AdminClientProxyImpl) IgnoreUnsatisfiedReply(ctx context.Context, req *IgnoreUnsatisfiedReplyReq, opts ...client.Option) (*IgnoreUnsatisfiedReplyRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_admin_config_server.Admin/IgnoreUnsatisfiedReply")
	msg.WithCalleeServiceName(AdminServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_admin_config_server")
	msg.WithCalleeService("Admin")
	msg.WithCalleeMethod("IgnoreUnsatisfiedReply")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &IgnoreUnsatisfiedReplyRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *AdminClientProxyImpl) ExportUnsatisfiedReply(ctx context.Context, req *ExportUnsatisfiedReplyReq, opts ...client.Option) (*ExportUnsatisfiedReplyRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_admin_config_server.Admin/ExportUnsatisfiedReply")
	msg.WithCalleeServiceName(AdminServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_admin_config_server")
	msg.WithCalleeService("Admin")
	msg.WithCalleeMethod("ExportUnsatisfiedReply")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &ExportUnsatisfiedReplyRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *AdminClientProxyImpl) GetUnsatisfiedReplyContext(ctx context.Context, req *GetUnsatisfiedReplyContextReq, opts ...client.Option) (*GetUnsatisfiedReplyContextRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_admin_config_server.Admin/GetUnsatisfiedReplyContext")
	msg.WithCalleeServiceName(AdminServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_admin_config_server")
	msg.WithCalleeService("Admin")
	msg.WithCalleeMethod("GetUnsatisfiedReplyContext")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &GetUnsatisfiedReplyContextRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *AdminClientProxyImpl) DescribeUnsatisfiedReplyContext(ctx context.Context, req *DescribeUnsatisfiedReplyReq, opts ...client.Option) (*DescribeUnsatisfiedReplyRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_admin_config_server.Admin/DescribeUnsatisfiedReplyContext")
	msg.WithCalleeServiceName(AdminServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_admin_config_server")
	msg.WithCalleeService("Admin")
	msg.WithCalleeMethod("DescribeUnsatisfiedReplyContext")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &DescribeUnsatisfiedReplyRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *AdminClientProxyImpl) RecordUserFirstGenQA(ctx context.Context, req *RecordUserFirstGenQAReq, opts ...client.Option) (*RecordUserFirstGenQARsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_admin_config_server.Admin/RecordUserFirstGenQA")
	msg.WithCalleeServiceName(AdminServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_admin_config_server")
	msg.WithCalleeService("Admin")
	msg.WithCalleeMethod("RecordUserFirstGenQA")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &RecordUserFirstGenQARsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *AdminClientProxyImpl) RecordUserAccessUnCheckQATime(ctx context.Context, req *RecordUserAccessUnCheckQATimeReq, opts ...client.Option) (*RecordUserAccessUnCheckQATimeRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_admin_config_server.Admin/RecordUserAccessUnCheckQATime")
	msg.WithCalleeServiceName(AdminServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_admin_config_server")
	msg.WithCalleeService("Admin")
	msg.WithCalleeMethod("RecordUserAccessUnCheckQATime")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &RecordUserAccessUnCheckQATimeRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *AdminClientProxyImpl) CreateAttributeLabelV1(ctx context.Context, req *CreateAttributeLabelV1Req, opts ...client.Option) (*CreateAttributeLabelV1Rsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_admin_config_server.Admin/CreateAttributeLabelV1")
	msg.WithCalleeServiceName(AdminServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_admin_config_server")
	msg.WithCalleeService("Admin")
	msg.WithCalleeMethod("CreateAttributeLabelV1")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &CreateAttributeLabelV1Rsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *AdminClientProxyImpl) CreateAttributeLabel(ctx context.Context, req *CreateAttributeLabelReq, opts ...client.Option) (*CreateAttributeLabelRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_admin_config_server.Admin/CreateAttributeLabel")
	msg.WithCalleeServiceName(AdminServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_admin_config_server")
	msg.WithCalleeService("Admin")
	msg.WithCalleeMethod("CreateAttributeLabel")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &CreateAttributeLabelRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *AdminClientProxyImpl) DeleteAttributeLabel(ctx context.Context, req *DeleteAttributeLabelReq, opts ...client.Option) (*DeleteAttributeLabelRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_admin_config_server.Admin/DeleteAttributeLabel")
	msg.WithCalleeServiceName(AdminServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_admin_config_server")
	msg.WithCalleeService("Admin")
	msg.WithCalleeMethod("DeleteAttributeLabel")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &DeleteAttributeLabelRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *AdminClientProxyImpl) UpdateAttributeLabel(ctx context.Context, req *UpdateAttributeLabelReq, opts ...client.Option) (*UpdateAttributeLabelRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_admin_config_server.Admin/UpdateAttributeLabel")
	msg.WithCalleeServiceName(AdminServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_admin_config_server")
	msg.WithCalleeService("Admin")
	msg.WithCalleeMethod("UpdateAttributeLabel")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &UpdateAttributeLabelRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *AdminClientProxyImpl) ModifyAttributeLabel(ctx context.Context, req *ModifyAttributeLabelReq, opts ...client.Option) (*ModifyAttributeLabelRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_admin_config_server.Admin/ModifyAttributeLabel")
	msg.WithCalleeServiceName(AdminServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_admin_config_server")
	msg.WithCalleeService("Admin")
	msg.WithCalleeMethod("ModifyAttributeLabel")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &ModifyAttributeLabelRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *AdminClientProxyImpl) GetAttributeLabelList(ctx context.Context, req *GetAttributeLabelListReq, opts ...client.Option) (*GetAttributeLabelListRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_admin_config_server.Admin/GetAttributeLabelList")
	msg.WithCalleeServiceName(AdminServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_admin_config_server")
	msg.WithCalleeService("Admin")
	msg.WithCalleeMethod("GetAttributeLabelList")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &GetAttributeLabelListRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *AdminClientProxyImpl) ListAttributeLabel(ctx context.Context, req *ListAttributeLabelReq, opts ...client.Option) (*ListAttributeLabelRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_admin_config_server.Admin/ListAttributeLabel")
	msg.WithCalleeServiceName(AdminServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_admin_config_server")
	msg.WithCalleeService("Admin")
	msg.WithCalleeMethod("ListAttributeLabel")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &ListAttributeLabelRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *AdminClientProxyImpl) GetAttributeLabelDetail(ctx context.Context, req *GetAttributeLabelDetailReq, opts ...client.Option) (*GetAttributeLabelDetailRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_admin_config_server.Admin/GetAttributeLabelDetail")
	msg.WithCalleeServiceName(AdminServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_admin_config_server")
	msg.WithCalleeService("Admin")
	msg.WithCalleeMethod("GetAttributeLabelDetail")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &GetAttributeLabelDetailRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *AdminClientProxyImpl) DescribeAttributeLabel(ctx context.Context, req *DescribeAttributeLabelReq, opts ...client.Option) (*DescribeAttributeLabelRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_admin_config_server.Admin/DescribeAttributeLabel")
	msg.WithCalleeServiceName(AdminServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_admin_config_server")
	msg.WithCalleeService("Admin")
	msg.WithCalleeMethod("DescribeAttributeLabel")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &DescribeAttributeLabelRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *AdminClientProxyImpl) UploadAttributeLabel(ctx context.Context, req *UploadAttributeLabelReq, opts ...client.Option) (*UploadAttributeLabelRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_admin_config_server.Admin/UploadAttributeLabel")
	msg.WithCalleeServiceName(AdminServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_admin_config_server")
	msg.WithCalleeService("Admin")
	msg.WithCalleeMethod("UploadAttributeLabel")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &UploadAttributeLabelRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *AdminClientProxyImpl) ExportAttributeLabel(ctx context.Context, req *ExportAttributeLabelReq, opts ...client.Option) (*ExportAttributeLabelRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_admin_config_server.Admin/ExportAttributeLabel")
	msg.WithCalleeServiceName(AdminServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_admin_config_server")
	msg.WithCalleeService("Admin")
	msg.WithCalleeMethod("ExportAttributeLabel")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &ExportAttributeLabelRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *AdminClientProxyImpl) CheckAttributeLabelRefer(ctx context.Context, req *CheckAttributeLabelReferReq, opts ...client.Option) (*CheckAttributeLabelReferRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_admin_config_server.Admin/CheckAttributeLabelRefer")
	msg.WithCalleeServiceName(AdminServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_admin_config_server")
	msg.WithCalleeService("Admin")
	msg.WithCalleeMethod("CheckAttributeLabelRefer")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &CheckAttributeLabelReferRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *AdminClientProxyImpl) CheckAttributeLabelExist(ctx context.Context, req *CheckAttributeLabelExistReq, opts ...client.Option) (*CheckAttributeLabelExistRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_admin_config_server.Admin/CheckAttributeLabelExist")
	msg.WithCalleeServiceName(AdminServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_admin_config_server")
	msg.WithCalleeService("Admin")
	msg.WithCalleeMethod("CheckAttributeLabelExist")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &CheckAttributeLabelExistRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *AdminClientProxyImpl) CreateAppeal(ctx context.Context, req *CreateAppealReq, opts ...client.Option) (*CreateAppealRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_admin_config_server.Admin/CreateAppeal")
	msg.WithCalleeServiceName(AdminServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_admin_config_server")
	msg.WithCalleeService("Admin")
	msg.WithCalleeMethod("CreateAppeal")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &CreateAppealRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *AdminClientProxyImpl) CreateApp(ctx context.Context, req *CreateAppReq, opts ...client.Option) (*CreateAppRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_admin_config_server.Admin/CreateApp")
	msg.WithCalleeServiceName(AdminServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_admin_config_server")
	msg.WithCalleeService("Admin")
	msg.WithCalleeMethod("CreateApp")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &CreateAppRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *AdminClientProxyImpl) ModifyApp(ctx context.Context, req *ModifyAppReq, opts ...client.Option) (*ModifyAppRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_admin_config_server.Admin/ModifyApp")
	msg.WithCalleeServiceName(AdminServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_admin_config_server")
	msg.WithCalleeService("Admin")
	msg.WithCalleeMethod("ModifyApp")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &ModifyAppRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *AdminClientProxyImpl) ListApp(ctx context.Context, req *ListAppReq, opts ...client.Option) (*ListAppRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_admin_config_server.Admin/ListApp")
	msg.WithCalleeServiceName(AdminServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_admin_config_server")
	msg.WithCalleeService("Admin")
	msg.WithCalleeMethod("ListApp")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &ListAppRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *AdminClientProxyImpl) DescribeApp(ctx context.Context, req *DescribeAppReq, opts ...client.Option) (*DescribeAppRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_admin_config_server.Admin/DescribeApp")
	msg.WithCalleeServiceName(AdminServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_admin_config_server")
	msg.WithCalleeService("Admin")
	msg.WithCalleeMethod("DescribeApp")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &DescribeAppRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *AdminClientProxyImpl) DeleteApp(ctx context.Context, req *DeleteAppReq, opts ...client.Option) (*DeleteAppRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_admin_config_server.Admin/DeleteApp")
	msg.WithCalleeServiceName(AdminServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_admin_config_server")
	msg.WithCalleeService("Admin")
	msg.WithCalleeMethod("DeleteApp")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &DeleteAppRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *AdminClientProxyImpl) ListModel(ctx context.Context, req *ListModelReq, opts ...client.Option) (*ListModelRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_admin_config_server.Admin/ListModel")
	msg.WithCalleeServiceName(AdminServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_admin_config_server")
	msg.WithCalleeService("Admin")
	msg.WithCalleeMethod("ListModel")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &ListModelRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *AdminClientProxyImpl) ListAppCategory(ctx context.Context, req *ListAppCategoryReq, opts ...client.Option) (*ListAppCategoryRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_admin_config_server.Admin/ListAppCategory")
	msg.WithCalleeServiceName(AdminServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_admin_config_server")
	msg.WithCalleeService("Admin")
	msg.WithCalleeMethod("ListAppCategory")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &ListAppCategoryRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *AdminClientProxyImpl) GetAppSecret(ctx context.Context, req *GetAppSecretReq, opts ...client.Option) (*GetAppSecretRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_admin_config_server.Admin/GetAppSecret")
	msg.WithCalleeServiceName(AdminServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_admin_config_server")
	msg.WithCalleeService("Admin")
	msg.WithCalleeMethod("GetAppSecret")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &GetAppSecretRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *AdminClientProxyImpl) GetAppKnowledgeCount(ctx context.Context, req *GetAppKnowledgeCountReq, opts ...client.Option) (*GetAppKnowledgeCountRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_admin_config_server.Admin/GetAppKnowledgeCount")
	msg.WithCalleeServiceName(AdminServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_admin_config_server")
	msg.WithCalleeService("Admin")
	msg.WithCalleeMethod("GetAppKnowledgeCount")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &GetAppKnowledgeCountRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *AdminClientProxyImpl) DescribeLicense(ctx context.Context, req *DescribeLicenseReq, opts ...client.Option) (*DescribeLicenseRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_admin_config_server.Admin/DescribeLicense")
	msg.WithCalleeServiceName(AdminServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_admin_config_server")
	msg.WithCalleeService("Admin")
	msg.WithCalleeMethod("DescribeLicense")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &DescribeLicenseRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *AdminClientProxyImpl) GetTaskStatus(ctx context.Context, req *GetTaskStatusReq, opts ...client.Option) (*GetTaskStatusRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_admin_config_server.Admin/GetTaskStatus")
	msg.WithCalleeServiceName(AdminServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_admin_config_server")
	msg.WithCalleeService("Admin")
	msg.WithCalleeMethod("GetTaskStatus")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &GetTaskStatusRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *AdminClientProxyImpl) DescribeModel(ctx context.Context, req *DescribeModelReq, opts ...client.Option) (*DescribeModelRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_admin_config_server.Admin/DescribeModel")
	msg.WithCalleeServiceName(AdminServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_admin_config_server")
	msg.WithCalleeService("Admin")
	msg.WithCalleeMethod("DescribeModel")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &DescribeModelRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *AdminClientProxyImpl) ListClassifyLabel(ctx context.Context, req *ListClassifyLabelReq, opts ...client.Option) (*ListClassifyLabelRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_admin_config_server.Admin/ListClassifyLabel")
	msg.WithCalleeServiceName(AdminServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_admin_config_server")
	msg.WithCalleeService("Admin")
	msg.WithCalleeMethod("ListClassifyLabel")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &ListClassifyLabelRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *AdminClientProxyImpl) ListSummaryPrompt(ctx context.Context, req *ListSummaryPromptReq, opts ...client.Option) (*ListSummaryPromptRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_admin_config_server.Admin/ListSummaryPrompt")
	msg.WithCalleeServiceName(AdminServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_admin_config_server")
	msg.WithCalleeService("Admin")
	msg.WithCalleeMethod("ListSummaryPrompt")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &ListSummaryPromptRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *AdminClientProxyImpl) AddAgentFeedback(ctx context.Context, req *AddAgentFeedbackReq, opts ...client.Option) (*AddAgentFeedbackRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_admin_config_server.Admin/AddAgentFeedback")
	msg.WithCalleeServiceName(AdminServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_admin_config_server")
	msg.WithCalleeService("Admin")
	msg.WithCalleeMethod("AddAgentFeedback")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &AddAgentFeedbackRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *AdminClientProxyImpl) DescribeAgentFeedback(ctx context.Context, req *DescribeAgentFeedbackReq, opts ...client.Option) (*DescribeAgentFeedbackRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_admin_config_server.Admin/DescribeAgentFeedback")
	msg.WithCalleeServiceName(AdminServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_admin_config_server")
	msg.WithCalleeService("Admin")
	msg.WithCalleeMethod("DescribeAgentFeedback")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &DescribeAgentFeedbackRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *AdminClientProxyImpl) DeleteAgentFeedback(ctx context.Context, req *DeleteAgentFeedbackReq, opts ...client.Option) (*DeleteAgentFeedbackRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_admin_config_server.Admin/DeleteAgentFeedback")
	msg.WithCalleeServiceName(AdminServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_admin_config_server")
	msg.WithCalleeService("Admin")
	msg.WithCalleeMethod("DeleteAgentFeedback")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &DeleteAgentFeedbackRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *AdminClientProxyImpl) ListAgentFeedback(ctx context.Context, req *ListAgentFeedbackReq, opts ...client.Option) (*ListAgentFeedbackRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_admin_config_server.Admin/ListAgentFeedback")
	msg.WithCalleeServiceName(AdminServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_admin_config_server")
	msg.WithCalleeService("Admin")
	msg.WithCalleeMethod("ListAgentFeedback")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &ListAgentFeedbackRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *AdminClientProxyImpl) AddFeedback(ctx context.Context, req *AddFeedbackReq, opts ...client.Option) (*AddFeedbackRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_admin_config_server.Admin/AddFeedback")
	msg.WithCalleeServiceName(AdminServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_admin_config_server")
	msg.WithCalleeService("Admin")
	msg.WithCalleeMethod("AddFeedback")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &AddFeedbackRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *AdminClientProxyImpl) ListFeedback(ctx context.Context, req *ListFeedbackReq, opts ...client.Option) (*ListFeedbackRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_admin_config_server.Admin/ListFeedback")
	msg.WithCalleeServiceName(AdminServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_admin_config_server")
	msg.WithCalleeService("Admin")
	msg.WithCalleeMethod("ListFeedback")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &ListFeedbackRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *AdminClientProxyImpl) DescribeFeedback(ctx context.Context, req *DescribeFeedbackReq, opts ...client.Option) (*DescribeFeedbackRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_admin_config_server.Admin/DescribeFeedback")
	msg.WithCalleeServiceName(AdminServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_admin_config_server")
	msg.WithCalleeService("Admin")
	msg.WithCalleeMethod("DescribeFeedback")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &DescribeFeedbackRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *AdminClientProxyImpl) DeleteFeedback(ctx context.Context, req *DeleteFeedbackReq, opts ...client.Option) (*DeleteFeedbackRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_admin_config_server.Admin/DeleteFeedback")
	msg.WithCalleeServiceName(AdminServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_admin_config_server")
	msg.WithCalleeService("Admin")
	msg.WithCalleeMethod("DeleteFeedback")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &DeleteFeedbackRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *AdminClientProxyImpl) ListExperienceCenterApp(ctx context.Context, req *ListExperienceCenterAppReq, opts ...client.Option) (*ListExperienceCenterAppRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_admin_config_server.Admin/ListExperienceCenterApp")
	msg.WithCalleeServiceName(AdminServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_admin_config_server")
	msg.WithCalleeService("Admin")
	msg.WithCalleeMethod("ListExperienceCenterApp")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &ListExperienceCenterAppRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *AdminClientProxyImpl) CreateAppByExperienceApp(ctx context.Context, req *CreateAppByExperienceAppReq, opts ...client.Option) (*CreateAppByExperienceAppRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_admin_config_server.Admin/CreateAppByExperienceApp")
	msg.WithCalleeServiceName(AdminServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_admin_config_server")
	msg.WithCalleeService("Admin")
	msg.WithCalleeMethod("CreateAppByExperienceApp")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &CreateAppByExperienceAppRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *AdminClientProxyImpl) GetDisplayDocs(ctx context.Context, req *GetDisplayDocsReq, opts ...client.Option) (*GetDisplayDocsRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_admin_config_server.Admin/GetDisplayDocs")
	msg.WithCalleeServiceName(AdminServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_admin_config_server")
	msg.WithCalleeService("Admin")
	msg.WithCalleeMethod("GetDisplayDocs")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &GetDisplayDocsRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *AdminClientProxyImpl) ListPackage(ctx context.Context, req *ListPackageReq, opts ...client.Option) (*ListPackageRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_admin_config_server.Admin/ListPackage")
	msg.WithCalleeServiceName(AdminServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_admin_config_server")
	msg.WithCalleeService("Admin")
	msg.WithCalleeMethod("ListPackage")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &ListPackageRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *AdminClientProxyImpl) ListConcurrency(ctx context.Context, req *ListConcurrencyReq, opts ...client.Option) (*ListConcurrencyRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_admin_config_server.Admin/ListConcurrency")
	msg.WithCalleeServiceName(AdminServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_admin_config_server")
	msg.WithCalleeService("Admin")
	msg.WithCalleeMethod("ListConcurrency")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &ListConcurrencyRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *AdminClientProxyImpl) ListKnowledgeCapacity(ctx context.Context, req *ListKnowledgeCapacityReq, opts ...client.Option) (*ListKnowledgeCapacityRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_admin_config_server.Admin/ListKnowledgeCapacity")
	msg.WithCalleeServiceName(AdminServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_admin_config_server")
	msg.WithCalleeService("Admin")
	msg.WithCalleeMethod("ListKnowledgeCapacity")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &ListKnowledgeCapacityRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *AdminClientProxyImpl) DescribeTokenUsage(ctx context.Context, req *DescribeTokenUsageReq, opts ...client.Option) (*DescribeTokenUsageRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_admin_config_server.Admin/DescribeTokenUsage")
	msg.WithCalleeServiceName(AdminServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_admin_config_server")
	msg.WithCalleeService("Admin")
	msg.WithCalleeMethod("DescribeTokenUsage")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &DescribeTokenUsageRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *AdminClientProxyImpl) DescribeTokenUsageGraph(ctx context.Context, req *DescribeTokenUsageGraphReq, opts ...client.Option) (*DescribeTokenUsageGraphRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_admin_config_server.Admin/DescribeTokenUsageGraph")
	msg.WithCalleeServiceName(AdminServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_admin_config_server")
	msg.WithCalleeService("Admin")
	msg.WithCalleeMethod("DescribeTokenUsageGraph")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &DescribeTokenUsageGraphRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *AdminClientProxyImpl) ListProducts(ctx context.Context, req *ListProductReq, opts ...client.Option) (*ListProductRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_admin_config_server.Admin/ListProducts")
	msg.WithCalleeServiceName(AdminServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_admin_config_server")
	msg.WithCalleeService("Admin")
	msg.WithCalleeMethod("ListProducts")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &ListProductRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *AdminClientProxyImpl) ListAccount(ctx context.Context, req *ListAccountReq, opts ...client.Option) (*ListAccountRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_admin_config_server.Admin/ListAccount")
	msg.WithCalleeServiceName(AdminServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_admin_config_server")
	msg.WithCalleeService("Admin")
	msg.WithCalleeMethod("ListAccount")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &ListAccountRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *AdminClientProxyImpl) ExportBillingInfo(ctx context.Context, req *ExportBillingInfoReq, opts ...client.Option) (*ExportBillingInfoRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_admin_config_server.Admin/ExportBillingInfo")
	msg.WithCalleeServiceName(AdminServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_admin_config_server")
	msg.WithCalleeService("Admin")
	msg.WithCalleeMethod("ExportBillingInfo")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &ExportBillingInfoRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *AdminClientProxyImpl) DescribeCallStatsGraph(ctx context.Context, req *DescribeCallStatsGraphReq, opts ...client.Option) (*DescribeCallStatsGraphRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_admin_config_server.Admin/DescribeCallStatsGraph")
	msg.WithCalleeServiceName(AdminServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_admin_config_server")
	msg.WithCalleeService("Admin")
	msg.WithCalleeMethod("DescribeCallStatsGraph")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &DescribeCallStatsGraphRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *AdminClientProxyImpl) GetSearchResourceStatus(ctx context.Context, req *GetSearchResourceStatusReq, opts ...client.Option) (*GetSearchResourceStatusRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_admin_config_server.Admin/GetSearchResourceStatus")
	msg.WithCalleeServiceName(AdminServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_admin_config_server")
	msg.WithCalleeService("Admin")
	msg.WithCalleeMethod("GetSearchResourceStatus")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &GetSearchResourceStatusRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *AdminClientProxyImpl) ListPostpaidProduct(ctx context.Context, req *ListPostpaidProductReq, opts ...client.Option) (*ListPostpaidProductRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_admin_config_server.Admin/ListPostpaidProduct")
	msg.WithCalleeServiceName(AdminServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_admin_config_server")
	msg.WithCalleeService("Admin")
	msg.WithCalleeMethod("ListPostpaidProduct")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &ListPostpaidProductRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *AdminClientProxyImpl) ModifyPostpaidSwitch(ctx context.Context, req *ModifyPostpaidSwitchReq, opts ...client.Option) (*ModifyPostpaidSwitchRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_admin_config_server.Admin/ModifyPostpaidSwitch")
	msg.WithCalleeServiceName(AdminServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_admin_config_server")
	msg.WithCalleeService("Admin")
	msg.WithCalleeMethod("ModifyPostpaidSwitch")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &ModifyPostpaidSwitchRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *AdminClientProxyImpl) ListInvalidResource(ctx context.Context, req *ListInvalidResourceReq, opts ...client.Option) (*ListInvalidResourceRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_admin_config_server.Admin/ListInvalidResource")
	msg.WithCalleeServiceName(AdminServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_admin_config_server")
	msg.WithCalleeService("Admin")
	msg.WithCalleeMethod("ListInvalidResource")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &ListInvalidResourceRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *AdminClientProxyImpl) CreatePostPayResource(ctx context.Context, req *CreatePostPayResourceReq, opts ...client.Option) (*CreatePostPayResourceRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_admin_config_server.Admin/CreatePostPayResource")
	msg.WithCalleeServiceName(AdminServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_admin_config_server")
	msg.WithCalleeService("Admin")
	msg.WithCalleeMethod("CreatePostPayResource")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &CreatePostPayResourceRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *AdminClientProxyImpl) DescribePostpaidSwitch(ctx context.Context, req *DescribePostpaidSwitchReq, opts ...client.Option) (*DescribePostpaidSwitchRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_admin_config_server.Admin/DescribePostpaidSwitch")
	msg.WithCalleeServiceName(AdminServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_admin_config_server")
	msg.WithCalleeService("Admin")
	msg.WithCalleeMethod("DescribePostpaidSwitch")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &DescribePostpaidSwitchRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *AdminClientProxyImpl) DescribeSearchStatsGraph(ctx context.Context, req *DescribeSearchStatsGraphReq, opts ...client.Option) (*DescribeSearchStatsGraphRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_admin_config_server.Admin/DescribeSearchStatsGraph")
	msg.WithCalleeServiceName(AdminServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_admin_config_server")
	msg.WithCalleeService("Admin")
	msg.WithCalleeMethod("DescribeSearchStatsGraph")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &DescribeSearchStatsGraphRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *AdminClientProxyImpl) AddEventReport(ctx context.Context, req *AddEventReportReq, opts ...client.Option) (*AddEventReportRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_admin_config_server.Admin/AddEventReport")
	msg.WithCalleeServiceName(AdminServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_admin_config_server")
	msg.WithCalleeService("Admin")
	msg.WithCalleeMethod("AddEventReport")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &AddEventReportRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *AdminClientProxyImpl) BatchEventReport(ctx context.Context, req *BatchEventReportReq, opts ...client.Option) (*BatchEventReportRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_admin_config_server.Admin/BatchEventReport")
	msg.WithCalleeServiceName(AdminServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_admin_config_server")
	msg.WithCalleeService("Admin")
	msg.WithCalleeMethod("BatchEventReport")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &BatchEventReportRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *AdminClientProxyImpl) DescribeKnowledgeUsage(ctx context.Context, req *DescribeKnowledgeUsageReq, opts ...client.Option) (*DescribeKnowledgeUsageRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_admin_config_server.Admin/DescribeKnowledgeUsage")
	msg.WithCalleeServiceName(AdminServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_admin_config_server")
	msg.WithCalleeService("Admin")
	msg.WithCalleeMethod("DescribeKnowledgeUsage")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &DescribeKnowledgeUsageRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *AdminClientProxyImpl) DescribeKnowledgeUsagePieGraph(ctx context.Context, req *DescribeKnowledgeUsagePieGraphReq, opts ...client.Option) (*DescribeKnowledgeUsagePieGraphRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_admin_config_server.Admin/DescribeKnowledgeUsagePieGraph")
	msg.WithCalleeServiceName(AdminServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_admin_config_server")
	msg.WithCalleeService("Admin")
	msg.WithCalleeMethod("DescribeKnowledgeUsagePieGraph")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &DescribeKnowledgeUsagePieGraphRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *AdminClientProxyImpl) ListAppKnowledgeDetail(ctx context.Context, req *ListAppKnowledgeDetailReq, opts ...client.Option) (*ListAppKnowledgeDetailRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_admin_config_server.Admin/ListAppKnowledgeDetail")
	msg.WithCalleeServiceName(AdminServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_admin_config_server")
	msg.WithCalleeService("Admin")
	msg.WithCalleeMethod("ListAppKnowledgeDetail")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &ListAppKnowledgeDetailRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *AdminClientProxyImpl) ListUsageCallDetail(ctx context.Context, req *ListUsageCallDetailReq, opts ...client.Option) (*ListUsageCallDetailRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_admin_config_server.Admin/ListUsageCallDetail")
	msg.WithCalleeServiceName(AdminServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_admin_config_server")
	msg.WithCalleeService("Admin")
	msg.WithCalleeMethod("ListUsageCallDetail")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &ListUsageCallDetailRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *AdminClientProxyImpl) DescribeConcurrencyUsage(ctx context.Context, req *DescribeConcurrencyUsageReq, opts ...client.Option) (*DescribeConcurrencyUsageRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_admin_config_server.Admin/DescribeConcurrencyUsage")
	msg.WithCalleeServiceName(AdminServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_admin_config_server")
	msg.WithCalleeService("Admin")
	msg.WithCalleeMethod("DescribeConcurrencyUsage")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &DescribeConcurrencyUsageRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *AdminClientProxyImpl) ListConcurrencyDetail(ctx context.Context, req *ListConcurrencyDetailReq, opts ...client.Option) (*ListConcurrencyDetailRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_admin_config_server.Admin/ListConcurrencyDetail")
	msg.WithCalleeServiceName(AdminServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_admin_config_server")
	msg.WithCalleeService("Admin")
	msg.WithCalleeMethod("ListConcurrencyDetail")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &ListConcurrencyDetailRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *AdminClientProxyImpl) DescribeConcurrencyUsageGraph(ctx context.Context, req *DescribeConcurrencyUsageGraphReq, opts ...client.Option) (*DescribeConcurrencyUsageGraphRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_admin_config_server.Admin/DescribeConcurrencyUsageGraph")
	msg.WithCalleeServiceName(AdminServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_admin_config_server")
	msg.WithCalleeService("Admin")
	msg.WithCalleeMethod("DescribeConcurrencyUsageGraph")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &DescribeConcurrencyUsageGraphRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *AdminClientProxyImpl) ExportConcurrencyInfo(ctx context.Context, req *ExportConcurrencyInfoReq, opts ...client.Option) (*ExportConcurrencyInfoRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_admin_config_server.Admin/ExportConcurrencyInfo")
	msg.WithCalleeServiceName(AdminServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_admin_config_server")
	msg.WithCalleeService("Admin")
	msg.WithCalleeMethod("ExportConcurrencyInfo")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &ExportConcurrencyInfoRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *AdminClientProxyImpl) ExportKnowledgeInfo(ctx context.Context, req *ExportKnowledgeInfoReq, opts ...client.Option) (*ExportKnowledgeInfoRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_admin_config_server.Admin/ExportKnowledgeInfo")
	msg.WithCalleeServiceName(AdminServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_admin_config_server")
	msg.WithCalleeService("Admin")
	msg.WithCalleeMethod("ExportKnowledgeInfo")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &ExportKnowledgeInfoRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *AdminClientProxyImpl) ListReleaseLabelPreview(ctx context.Context, req *ListReleaseLabelPreviewReq, opts ...client.Option) (*ListReleaseLabelPreviewRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_admin_config_server.Admin/ListReleaseLabelPreview")
	msg.WithCalleeServiceName(AdminServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_admin_config_server")
	msg.WithCalleeService("Admin")
	msg.WithCalleeMethod("ListReleaseLabelPreview")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &ListReleaseLabelPreviewRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *AdminClientProxyImpl) ListReleaseSynonymsPreview(ctx context.Context, req *ListReleaseSynonymsPreviewReq, opts ...client.Option) (*ListReleaseSynonymsPreviewRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_admin_config_server.Admin/ListReleaseSynonymsPreview")
	msg.WithCalleeServiceName(AdminServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_admin_config_server")
	msg.WithCalleeService("Admin")
	msg.WithCalleeMethod("ListReleaseSynonymsPreview")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &ListReleaseSynonymsPreviewRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *AdminClientProxyImpl) ModifyAppBase(ctx context.Context, req *ModifyAppBaseReq, opts ...client.Option) (*ModifyAppBaseRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_admin_config_server.Admin/ModifyAppBase")
	msg.WithCalleeServiceName(AdminServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_admin_config_server")
	msg.WithCalleeService("Admin")
	msg.WithCalleeMethod("ModifyAppBase")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &ModifyAppBaseRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *AdminClientProxyImpl) CreateCustomModel(ctx context.Context, req *CreateCustomModelReq, opts ...client.Option) (*CreateCustomModelRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_admin_config_server.Admin/CreateCustomModel")
	msg.WithCalleeServiceName(AdminServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_admin_config_server")
	msg.WithCalleeService("Admin")
	msg.WithCalleeMethod("CreateCustomModel")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &CreateCustomModelRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *AdminClientProxyImpl) ModifyCustomModel(ctx context.Context, req *ModifyCustomModelReq, opts ...client.Option) (*ModifyCustomModelRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_admin_config_server.Admin/ModifyCustomModel")
	msg.WithCalleeServiceName(AdminServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_admin_config_server")
	msg.WithCalleeService("Admin")
	msg.WithCalleeMethod("ModifyCustomModel")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &ModifyCustomModelRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *AdminClientProxyImpl) DeleteCustomModel(ctx context.Context, req *DeleteCustomModelReq, opts ...client.Option) (*DeleteCustomModelRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_admin_config_server.Admin/DeleteCustomModel")
	msg.WithCalleeServiceName(AdminServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_admin_config_server")
	msg.WithCalleeService("Admin")
	msg.WithCalleeMethod("DeleteCustomModel")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &DeleteCustomModelRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *AdminClientProxyImpl) DescribeCustomModel(ctx context.Context, req *DescribeCustomModelReq, opts ...client.Option) (*DescribeCustomModelRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_admin_config_server.Admin/DescribeCustomModel")
	msg.WithCalleeServiceName(AdminServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_admin_config_server")
	msg.WithCalleeService("Admin")
	msg.WithCalleeMethod("DescribeCustomModel")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &DescribeCustomModelRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *AdminClientProxyImpl) DescribeModelApps(ctx context.Context, req *DescribeModelAppsReq, opts ...client.Option) (*DescribeModelAppsRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_admin_config_server.Admin/DescribeModelApps")
	msg.WithCalleeServiceName(AdminServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_admin_config_server")
	msg.WithCalleeService("Admin")
	msg.WithCalleeMethod("DescribeModelApps")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &DescribeModelAppsRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *AdminClientProxyImpl) GetUserGuideViewInfos(ctx context.Context, req *GetUserGuideViewInfosReq, opts ...client.Option) (*GetUserGuideViewInfosRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_admin_config_server.Admin/GetUserGuideViewInfos")
	msg.WithCalleeServiceName(AdminServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_admin_config_server")
	msg.WithCalleeService("Admin")
	msg.WithCalleeMethod("GetUserGuideViewInfos")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &GetUserGuideViewInfosRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *AdminClientProxyImpl) SetUserGuideViewInfo(ctx context.Context, req *SetUserGuideViewInfoReq, opts ...client.Option) (*SetUserGuideViewInfoRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_admin_config_server.Admin/SetUserGuideViewInfo")
	msg.WithCalleeServiceName(AdminServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_admin_config_server")
	msg.WithCalleeService("Admin")
	msg.WithCalleeMethod("SetUserGuideViewInfo")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &SetUserGuideViewInfoRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *AdminClientProxyImpl) ExportMinuteDosage(ctx context.Context, req *ExportMinuteDosageReq, opts ...client.Option) (*ExportMinuteDosageRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_admin_config_server.Admin/ExportMinuteDosage")
	msg.WithCalleeServiceName(AdminServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_admin_config_server")
	msg.WithCalleeService("Admin")
	msg.WithCalleeMethod("ExportMinuteDosage")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &ExportMinuteDosageRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *AdminClientProxyImpl) GetImagePreview(ctx context.Context, req *GetImagePreviewReq, opts ...client.Option) (*GetImagePreviewRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_admin_config_server.Admin/GetImagePreview")
	msg.WithCalleeServiceName(AdminServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_admin_config_server")
	msg.WithCalleeService("Admin")
	msg.WithCalleeMethod("GetImagePreview")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &GetImagePreviewRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *AdminClientProxyImpl) ListVoice(ctx context.Context, req *ListVoiceReq, opts ...client.Option) (*ListVoiceRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_admin_config_server.Admin/ListVoice")
	msg.WithCalleeServiceName(AdminServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_admin_config_server")
	msg.WithCalleeService("Admin")
	msg.WithCalleeMethod("ListVoice")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &ListVoiceRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *AdminClientProxyImpl) ListDigitalHuman(ctx context.Context, req *ListDigitalHumanReq, opts ...client.Option) (*ListDigitalHumanRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_admin_config_server.Admin/ListDigitalHuman")
	msg.WithCalleeServiceName(AdminServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_admin_config_server")
	msg.WithCalleeService("Admin")
	msg.WithCalleeMethod("ListDigitalHuman")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &ListDigitalHumanRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *AdminClientProxyImpl) GetPromptTemplateList(ctx context.Context, req *GetPromptTemplateListReq, opts ...client.Option) (*GetPromptTemplateListRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_admin_config_server.Admin/GetPromptTemplateList")
	msg.WithCalleeServiceName(AdminServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_admin_config_server")
	msg.WithCalleeService("Admin")
	msg.WithCalleeMethod("GetPromptTemplateList")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &GetPromptTemplateListRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *AdminClientProxyImpl) GetMsgLogList(ctx context.Context, req *GetMsgLogListReq, opts ...client.Option) (*GetMsgLogListRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_admin_config_server.Admin/GetMsgLogList")
	msg.WithCalleeServiceName(AdminServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_admin_config_server")
	msg.WithCalleeService("Admin")
	msg.WithCalleeMethod("GetMsgLogList")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &GetMsgLogListRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *AdminClientProxyImpl) ExportMsgLog(ctx context.Context, req *ExportMsgLogReq, opts ...client.Option) (*ExportMsgLogRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_admin_config_server.Admin/ExportMsgLog")
	msg.WithCalleeServiceName(AdminServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_admin_config_server")
	msg.WithCalleeService("Admin")
	msg.WithCalleeMethod("ExportMsgLog")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &ExportMsgLogRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *AdminClientProxyImpl) ModifyConcurrencyRule(ctx context.Context, req *ModifyConcurrencyRuleReq, opts ...client.Option) (*ModifyConcurrencyRuleRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_admin_config_server.Admin/ModifyConcurrencyRule")
	msg.WithCalleeServiceName(AdminServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_admin_config_server")
	msg.WithCalleeService("Admin")
	msg.WithCalleeMethod("ModifyConcurrencyRule")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &ModifyConcurrencyRuleRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *AdminClientProxyImpl) DescribeConcurrencyRule(ctx context.Context, req *DescribeConcurrencyRuleReq, opts ...client.Option) (*DescribeConcurrencyRuleRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_admin_config_server.Admin/DescribeConcurrencyRule")
	msg.WithCalleeServiceName(AdminServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_admin_config_server")
	msg.WithCalleeService("Admin")
	msg.WithCalleeMethod("DescribeConcurrencyRule")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &DescribeConcurrencyRuleRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *AdminClientProxyImpl) CheckWhitelist(ctx context.Context, req *CheckWhitelistReq, opts ...client.Option) (*CheckWhitelistRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_admin_config_server.Admin/CheckWhitelist")
	msg.WithCalleeServiceName(AdminServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_admin_config_server")
	msg.WithCalleeService("Admin")
	msg.WithCalleeMethod("CheckWhitelist")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &CheckWhitelistRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *AdminClientProxyImpl) GetMsgLogOverview(ctx context.Context, req *GetMsgLogOverviewReq, opts ...client.Option) (*GetMsgLogOverviewRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_admin_config_server.Admin/GetMsgLogOverview")
	msg.WithCalleeServiceName(AdminServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_admin_config_server")
	msg.WithCalleeService("Admin")
	msg.WithCalleeMethod("GetMsgLogOverview")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &GetMsgLogOverviewRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *AdminClientProxyImpl) GetMsgLogCountTrend(ctx context.Context, req *GetMsgLogCountTrendReq, opts ...client.Option) (*GetMsgLogCountTrendRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_admin_config_server.Admin/GetMsgLogCountTrend")
	msg.WithCalleeServiceName(AdminServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_admin_config_server")
	msg.WithCalleeService("Admin")
	msg.WithCalleeMethod("GetMsgLogCountTrend")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &GetMsgLogCountTrendRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *AdminClientProxyImpl) GetMsgLogUserCountTrend(ctx context.Context, req *GetMsgLogUserCountTrendReq, opts ...client.Option) (*GetMsgLogUserCountTrendRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_admin_config_server.Admin/GetMsgLogUserCountTrend")
	msg.WithCalleeServiceName(AdminServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_admin_config_server")
	msg.WithCalleeService("Admin")
	msg.WithCalleeMethod("GetMsgLogUserCountTrend")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &GetMsgLogUserCountTrendRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *AdminClientProxyImpl) GetMsgLogFeedbackCountTrend(ctx context.Context, req *GetMsgLogFeedbackCountTrendReq, opts ...client.Option) (*GetMsgLogFeedbackCountTrendRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_admin_config_server.Admin/GetMsgLogFeedbackCountTrend")
	msg.WithCalleeServiceName(AdminServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_admin_config_server")
	msg.WithCalleeService("Admin")
	msg.WithCalleeMethod("GetMsgLogFeedbackCountTrend")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &GetMsgLogFeedbackCountTrendRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *AdminClientProxyImpl) ExportMsgLogStatistical(ctx context.Context, req *ExportMsgLogStatisticalReq, opts ...client.Option) (*ExportMsgLogStatisticalRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_admin_config_server.Admin/ExportMsgLogStatistical")
	msg.WithCalleeServiceName(AdminServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_admin_config_server")
	msg.WithCalleeService("Admin")
	msg.WithCalleeMethod("ExportMsgLogStatistical")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &ExportMsgLogStatisticalRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *AdminClientProxyImpl) CreateApproval(ctx context.Context, req *CreateApprovalReq, opts ...client.Option) (*CreateApprovalRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_admin_config_server.Admin/CreateApproval")
	msg.WithCalleeServiceName(AdminServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_admin_config_server")
	msg.WithCalleeService("Admin")
	msg.WithCalleeMethod("CreateApproval")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &CreateApprovalRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *AdminClientProxyImpl) UpdateApprovalStatus(ctx context.Context, req *UpdateApprovalStatusReq, opts ...client.Option) (*UpdateApprovalStatusRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_admin_config_server.Admin/UpdateApprovalStatus")
	msg.WithCalleeServiceName(AdminServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_admin_config_server")
	msg.WithCalleeService("Admin")
	msg.WithCalleeMethod("UpdateApprovalStatus")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &UpdateApprovalStatusRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *AdminClientProxyImpl) GetLastApproval(ctx context.Context, req *GetLastApprovalReq, opts ...client.Option) (*GetLastApprovalRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_admin_config_server.Admin/GetLastApproval")
	msg.WithCalleeServiceName(AdminServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_admin_config_server")
	msg.WithCalleeService("Admin")
	msg.WithCalleeMethod("GetLastApproval")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &GetLastApprovalRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

// END ======================================= Client Service Definition ======================================= END
