// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.25.0
// 	protoc        v3.6.1
// source: plugin-config.proto

package plugin_config_server

import (
	reflect "reflect"
	sync "sync"

	KEP "git.woa.com/dialogue-platform/lke_proto/pb-protocol/KEP"
	proto "github.com/golang/protobuf/proto"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// This is a compile-time assertion that a sufficiently up-to-date version
// of the legacy proto package is being used.
const _ = proto.ProtoPackageIsVersion4

type TypeEnum int32

const (
	TypeEnum_STRING       TypeEnum = 0 // 默认值是string，如果不填就按string处理
	TypeEnum_INT          TypeEnum = 1
	TypeEnum_FLOAT        TypeEnum = 2
	TypeEnum_BOOL         TypeEnum = 3
	TypeEnum_OBJECT       TypeEnum = 4
	TypeEnum_ARRAY_STRING TypeEnum = 5
	TypeEnum_ARRAY_INT    TypeEnum = 6
	TypeEnum_ARRAY_FLOAT  TypeEnum = 7
	TypeEnum_ARRAY_BOOL   TypeEnum = 8
	TypeEnum_ARRAY_OBJECT TypeEnum = 9
	TypeEnum_NULL         TypeEnum = 99  // 空值
	TypeEnum_UNSPECIFIED  TypeEnum = 100 // 未指定类型，用于类型为OneOf和AnyOf的场景
)

// Enum value maps for TypeEnum.
var (
	TypeEnum_name = map[int32]string{
		0:   "STRING",
		1:   "INT",
		2:   "FLOAT",
		3:   "BOOL",
		4:   "OBJECT",
		5:   "ARRAY_STRING",
		6:   "ARRAY_INT",
		7:   "ARRAY_FLOAT",
		8:   "ARRAY_BOOL",
		9:   "ARRAY_OBJECT",
		99:  "NULL",
		100: "UNSPECIFIED",
	}
	TypeEnum_value = map[string]int32{
		"STRING":       0,
		"INT":          1,
		"FLOAT":        2,
		"BOOL":         3,
		"OBJECT":       4,
		"ARRAY_STRING": 5,
		"ARRAY_INT":    6,
		"ARRAY_FLOAT":  7,
		"ARRAY_BOOL":   8,
		"ARRAY_OBJECT": 9,
		"NULL":         99,
		"UNSPECIFIED":  100,
	}
)

func (x TypeEnum) Enum() *TypeEnum {
	p := new(TypeEnum)
	*p = x
	return p
}

func (x TypeEnum) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (TypeEnum) Descriptor() protoreflect.EnumDescriptor {
	return file_plugin_config_proto_enumTypes[0].Descriptor()
}

func (TypeEnum) Type() protoreflect.EnumType {
	return &file_plugin_config_proto_enumTypes[0]
}

func (x TypeEnum) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use TypeEnum.Descriptor instead.
func (TypeEnum) EnumDescriptor() ([]byte, []int) {
	return file_plugin_config_proto_rawDescGZIP(), []int{0}
}

// 插件类型
type PluginTypeEnum int32

const (
	PluginTypeEnum_CUSTOM      PluginTypeEnum = 0 // 自定义插件
	PluginTypeEnum_OFFICIAL    PluginTypeEnum = 1 // 官方插件
	PluginTypeEnum_THIRD_PARTY PluginTypeEnum = 2 // 第三方插件 目前用于第三方实现的mcp server
)

// Enum value maps for PluginTypeEnum.
var (
	PluginTypeEnum_name = map[int32]string{
		0: "CUSTOM",
		1: "OFFICIAL",
		2: "THIRD_PARTY",
	}
	PluginTypeEnum_value = map[string]int32{
		"CUSTOM":      0,
		"OFFICIAL":    1,
		"THIRD_PARTY": 2,
	}
)

func (x PluginTypeEnum) Enum() *PluginTypeEnum {
	p := new(PluginTypeEnum)
	*p = x
	return p
}

func (x PluginTypeEnum) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (PluginTypeEnum) Descriptor() protoreflect.EnumDescriptor {
	return file_plugin_config_proto_enumTypes[1].Descriptor()
}

func (PluginTypeEnum) Type() protoreflect.EnumType {
	return &file_plugin_config_proto_enumTypes[1]
}

func (x PluginTypeEnum) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use PluginTypeEnum.Descriptor instead.
func (PluginTypeEnum) EnumDescriptor() ([]byte, []int) {
	return file_plugin_config_proto_rawDescGZIP(), []int{1}
}

// 授权方式
type AuthTypeEnum int32

const (
	AuthTypeEnum_NONE    AuthTypeEnum = 0 // 无鉴权
	AuthTypeEnum_API_KEY AuthTypeEnum = 1 // api key鉴权
)

// Enum value maps for AuthTypeEnum.
var (
	AuthTypeEnum_name = map[int32]string{
		0: "NONE",
		1: "API_KEY",
	}
	AuthTypeEnum_value = map[string]int32{
		"NONE":    0,
		"API_KEY": 1,
	}
)

func (x AuthTypeEnum) Enum() *AuthTypeEnum {
	p := new(AuthTypeEnum)
	*p = x
	return p
}

func (x AuthTypeEnum) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (AuthTypeEnum) Descriptor() protoreflect.EnumDescriptor {
	return file_plugin_config_proto_enumTypes[2].Descriptor()
}

func (AuthTypeEnum) Type() protoreflect.EnumType {
	return &file_plugin_config_proto_enumTypes[2]
}

func (x AuthTypeEnum) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use AuthTypeEnum.Descriptor instead.
func (AuthTypeEnum) EnumDescriptor() ([]byte, []int) {
	return file_plugin_config_proto_rawDescGZIP(), []int{2}
}

// 计费类型
type FinanceTypeEnum int32

const (
	FinanceTypeEnum_FREE FinanceTypeEnum = 0 // 免费
	FinanceTypeEnum_PAID FinanceTypeEnum = 1 // 收费
)

// Enum value maps for FinanceTypeEnum.
var (
	FinanceTypeEnum_name = map[int32]string{
		0: "FREE",
		1: "PAID",
	}
	FinanceTypeEnum_value = map[string]int32{
		"FREE": 0,
		"PAID": 1,
	}
)

func (x FinanceTypeEnum) Enum() *FinanceTypeEnum {
	p := new(FinanceTypeEnum)
	*p = x
	return p
}

func (x FinanceTypeEnum) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (FinanceTypeEnum) Descriptor() protoreflect.EnumDescriptor {
	return file_plugin_config_proto_enumTypes[3].Descriptor()
}

func (FinanceTypeEnum) Type() protoreflect.EnumType {
	return &file_plugin_config_proto_enumTypes[3]
}

func (x FinanceTypeEnum) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use FinanceTypeEnum.Descriptor instead.
func (FinanceTypeEnum) EnumDescriptor() ([]byte, []int) {
	return file_plugin_config_proto_rawDescGZIP(), []int{3}
}

// 创建方式
type CreateTypeEnum int32

const (
	CreateTypeEnum_SERVICE CreateTypeEnum = 0 // 服务
	CreateTypeEnum_CODE    CreateTypeEnum = 1 // 代码
	CreateTypeEnum_MCP     CreateTypeEnum = 2 // MCP
)

// Enum value maps for CreateTypeEnum.
var (
	CreateTypeEnum_name = map[int32]string{
		0: "SERVICE",
		1: "CODE",
		2: "MCP",
	}
	CreateTypeEnum_value = map[string]int32{
		"SERVICE": 0,
		"CODE":    1,
		"MCP":     2,
	}
)

func (x CreateTypeEnum) Enum() *CreateTypeEnum {
	p := new(CreateTypeEnum)
	*p = x
	return p
}

func (x CreateTypeEnum) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (CreateTypeEnum) Descriptor() protoreflect.EnumDescriptor {
	return file_plugin_config_proto_enumTypes[4].Descriptor()
}

func (CreateTypeEnum) Type() protoreflect.EnumType {
	return &file_plugin_config_proto_enumTypes[4]
}

func (x CreateTypeEnum) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use CreateTypeEnum.Descriptor instead.
func (CreateTypeEnum) EnumDescriptor() ([]byte, []int) {
	return file_plugin_config_proto_rawDescGZIP(), []int{4}
}

type EnvType int32

const (
	EnvType_TEST EnvType = 0 // 测试环境
	EnvType_PROD EnvType = 1 // 正式环境
)

// Enum value maps for EnvType.
var (
	EnvType_name = map[int32]string{
		0: "TEST",
		1: "PROD",
	}
	EnvType_value = map[string]int32{
		"TEST": 0,
		"PROD": 1,
	}
)

func (x EnvType) Enum() *EnvType {
	p := new(EnvType)
	*p = x
	return p
}

func (x EnvType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (EnvType) Descriptor() protoreflect.EnumDescriptor {
	return file_plugin_config_proto_enumTypes[5].Descriptor()
}

func (EnvType) Type() protoreflect.EnumType {
	return &file_plugin_config_proto_enumTypes[5]
}

func (x EnvType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use EnvType.Descriptor instead.
func (EnvType) EnumDescriptor() ([]byte, []int) {
	return file_plugin_config_proto_rawDescGZIP(), []int{5}
}

type WhiteListTypeEnum int32

const (
	WhiteListTypeEnum_OPEN             WhiteListTypeEnum = 0 // 非白名单插件 全量开放
	WhiteListTypeEnum_IN_WHITELIST     WhiteListTypeEnum = 1 // 在白名单里
	WhiteListTypeEnum_NOT_IN_WHITELIST WhiteListTypeEnum = 2 // 不在白名单里,需要提交申请
)

// Enum value maps for WhiteListTypeEnum.
var (
	WhiteListTypeEnum_name = map[int32]string{
		0: "OPEN",
		1: "IN_WHITELIST",
		2: "NOT_IN_WHITELIST",
	}
	WhiteListTypeEnum_value = map[string]int32{
		"OPEN":             0,
		"IN_WHITELIST":     1,
		"NOT_IN_WHITELIST": 2,
	}
)

func (x WhiteListTypeEnum) Enum() *WhiteListTypeEnum {
	p := new(WhiteListTypeEnum)
	*p = x
	return p
}

func (x WhiteListTypeEnum) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (WhiteListTypeEnum) Descriptor() protoreflect.EnumDescriptor {
	return file_plugin_config_proto_enumTypes[6].Descriptor()
}

func (WhiteListTypeEnum) Type() protoreflect.EnumType {
	return &file_plugin_config_proto_enumTypes[6]
}

func (x WhiteListTypeEnum) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use WhiteListTypeEnum.Descriptor instead.
func (WhiteListTypeEnum) EnumDescriptor() ([]byte, []int) {
	return file_plugin_config_proto_rawDescGZIP(), []int{6}
}

type ListPluginsReq_QueryTypeEnum int32

const (
	ListPluginsReq_NAME ListPluginsReq_QueryTypeEnum = 0 // 插件名
	ListPluginsReq_ID   ListPluginsReq_QueryTypeEnum = 1 // 插件id
)

// Enum value maps for ListPluginsReq_QueryTypeEnum.
var (
	ListPluginsReq_QueryTypeEnum_name = map[int32]string{
		0: "NAME",
		1: "ID",
	}
	ListPluginsReq_QueryTypeEnum_value = map[string]int32{
		"NAME": 0,
		"ID":   1,
	}
)

func (x ListPluginsReq_QueryTypeEnum) Enum() *ListPluginsReq_QueryTypeEnum {
	p := new(ListPluginsReq_QueryTypeEnum)
	*p = x
	return p
}

func (x ListPluginsReq_QueryTypeEnum) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ListPluginsReq_QueryTypeEnum) Descriptor() protoreflect.EnumDescriptor {
	return file_plugin_config_proto_enumTypes[7].Descriptor()
}

func (ListPluginsReq_QueryTypeEnum) Type() protoreflect.EnumType {
	return &file_plugin_config_proto_enumTypes[7]
}

func (x ListPluginsReq_QueryTypeEnum) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ListPluginsReq_QueryTypeEnum.Descriptor instead.
func (ListPluginsReq_QueryTypeEnum) EnumDescriptor() ([]byte, []int) {
	return file_plugin_config_proto_rawDescGZIP(), []int{0, 0}
}

type ListPluginsReq_PluginTypeEnum int32

const (
	ListPluginsReq_ALL         ListPluginsReq_PluginTypeEnum = 0 // 所有插件
	ListPluginsReq_CUSTOM      ListPluginsReq_PluginTypeEnum = 1 // 自定义插件
	ListPluginsReq_OFFICIAL    ListPluginsReq_PluginTypeEnum = 2 // 官方插件
	ListPluginsReq_THIRD_PARTY ListPluginsReq_PluginTypeEnum = 3 // 第三方插件
)

// Enum value maps for ListPluginsReq_PluginTypeEnum.
var (
	ListPluginsReq_PluginTypeEnum_name = map[int32]string{
		0: "ALL",
		1: "CUSTOM",
		2: "OFFICIAL",
		3: "THIRD_PARTY",
	}
	ListPluginsReq_PluginTypeEnum_value = map[string]int32{
		"ALL":         0,
		"CUSTOM":      1,
		"OFFICIAL":    2,
		"THIRD_PARTY": 3,
	}
)

func (x ListPluginsReq_PluginTypeEnum) Enum() *ListPluginsReq_PluginTypeEnum {
	p := new(ListPluginsReq_PluginTypeEnum)
	*p = x
	return p
}

func (x ListPluginsReq_PluginTypeEnum) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ListPluginsReq_PluginTypeEnum) Descriptor() protoreflect.EnumDescriptor {
	return file_plugin_config_proto_enumTypes[8].Descriptor()
}

func (ListPluginsReq_PluginTypeEnum) Type() protoreflect.EnumType {
	return &file_plugin_config_proto_enumTypes[8]
}

func (x ListPluginsReq_PluginTypeEnum) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ListPluginsReq_PluginTypeEnum.Descriptor instead.
func (ListPluginsReq_PluginTypeEnum) EnumDescriptor() ([]byte, []int) {
	return file_plugin_config_proto_rawDescGZIP(), []int{0, 1}
}

type ListPluginsReq_ModuleEnum int32

const (
	ListPluginsReq_MODULE_ALL      ListPluginsReq_ModuleEnum = 0 // 所有模块
	ListPluginsReq_MODULE_AGENT    ListPluginsReq_ModuleEnum = 1 // agent模式模块
	ListPluginsReq_MODULE_WORKFLOW ListPluginsReq_ModuleEnum = 2 // 工作流模块
	ListPluginsReq_MODULE_STANDARD ListPluginsReq_ModuleEnum = 3 // 标准模式模块
)

// Enum value maps for ListPluginsReq_ModuleEnum.
var (
	ListPluginsReq_ModuleEnum_name = map[int32]string{
		0: "MODULE_ALL",
		1: "MODULE_AGENT",
		2: "MODULE_WORKFLOW",
		3: "MODULE_STANDARD",
	}
	ListPluginsReq_ModuleEnum_value = map[string]int32{
		"MODULE_ALL":      0,
		"MODULE_AGENT":    1,
		"MODULE_WORKFLOW": 2,
		"MODULE_STANDARD": 3,
	}
)

func (x ListPluginsReq_ModuleEnum) Enum() *ListPluginsReq_ModuleEnum {
	p := new(ListPluginsReq_ModuleEnum)
	*p = x
	return p
}

func (x ListPluginsReq_ModuleEnum) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ListPluginsReq_ModuleEnum) Descriptor() protoreflect.EnumDescriptor {
	return file_plugin_config_proto_enumTypes[9].Descriptor()
}

func (ListPluginsReq_ModuleEnum) Type() protoreflect.EnumType {
	return &file_plugin_config_proto_enumTypes[9]
}

func (x ListPluginsReq_ModuleEnum) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ListPluginsReq_ModuleEnum.Descriptor instead.
func (ListPluginsReq_ModuleEnum) EnumDescriptor() ([]byte, []int) {
	return file_plugin_config_proto_rawDescGZIP(), []int{0, 2}
}

// 密钥位置
type AuthInfo_KeyLocationTypeEnum int32

const (
	AuthInfo_HEADER AuthInfo_KeyLocationTypeEnum = 0 // 头鉴权
	AuthInfo_QUERY  AuthInfo_KeyLocationTypeEnum = 1 // 请求信息鉴权
)

// Enum value maps for AuthInfo_KeyLocationTypeEnum.
var (
	AuthInfo_KeyLocationTypeEnum_name = map[int32]string{
		0: "HEADER",
		1: "QUERY",
	}
	AuthInfo_KeyLocationTypeEnum_value = map[string]int32{
		"HEADER": 0,
		"QUERY":  1,
	}
)

func (x AuthInfo_KeyLocationTypeEnum) Enum() *AuthInfo_KeyLocationTypeEnum {
	p := new(AuthInfo_KeyLocationTypeEnum)
	*p = x
	return p
}

func (x AuthInfo_KeyLocationTypeEnum) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (AuthInfo_KeyLocationTypeEnum) Descriptor() protoreflect.EnumDescriptor {
	return file_plugin_config_proto_enumTypes[10].Descriptor()
}

func (AuthInfo_KeyLocationTypeEnum) Type() protoreflect.EnumType {
	return &file_plugin_config_proto_enumTypes[10]
}

func (x AuthInfo_KeyLocationTypeEnum) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use AuthInfo_KeyLocationTypeEnum.Descriptor instead.
func (AuthInfo_KeyLocationTypeEnum) EnumDescriptor() ([]byte, []int) {
	return file_plugin_config_proto_rawDescGZIP(), []int{33, 0}
}

type ToolRef_RefTypeEnum int32

const (
	ToolRef_AGENT    ToolRef_RefTypeEnum = 0 // agent引用
	ToolRef_WORKFLOW ToolRef_RefTypeEnum = 1 // 工作流引用
	ToolRef_STANDARD ToolRef_RefTypeEnum = 2 // 标准模式引用
)

// Enum value maps for ToolRef_RefTypeEnum.
var (
	ToolRef_RefTypeEnum_name = map[int32]string{
		0: "AGENT",
		1: "WORKFLOW",
		2: "STANDARD",
	}
	ToolRef_RefTypeEnum_value = map[string]int32{
		"AGENT":    0,
		"WORKFLOW": 1,
		"STANDARD": 2,
	}
)

func (x ToolRef_RefTypeEnum) Enum() *ToolRef_RefTypeEnum {
	p := new(ToolRef_RefTypeEnum)
	*p = x
	return p
}

func (x ToolRef_RefTypeEnum) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ToolRef_RefTypeEnum) Descriptor() protoreflect.EnumDescriptor {
	return file_plugin_config_proto_enumTypes[11].Descriptor()
}

func (ToolRef_RefTypeEnum) Type() protoreflect.EnumType {
	return &file_plugin_config_proto_enumTypes[11]
}

func (x ToolRef_RefTypeEnum) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ToolRef_RefTypeEnum.Descriptor instead.
func (ToolRef_RefTypeEnum) EnumDescriptor() ([]byte, []int) {
	return file_plugin_config_proto_rawDescGZIP(), []int{36, 0}
}

// 模块类型枚举
type CheckPermissionReq_ModuleEnum int32

const (
	CheckPermissionReq_MODULE_UNKNOWN  CheckPermissionReq_ModuleEnum = 0 // 未知模块
	CheckPermissionReq_MODULE_AGENT    CheckPermissionReq_ModuleEnum = 1 // agent模式模块
	CheckPermissionReq_MODULE_WORKFLOW CheckPermissionReq_ModuleEnum = 2 // 工作流模块
)

// Enum value maps for CheckPermissionReq_ModuleEnum.
var (
	CheckPermissionReq_ModuleEnum_name = map[int32]string{
		0: "MODULE_UNKNOWN",
		1: "MODULE_AGENT",
		2: "MODULE_WORKFLOW",
	}
	CheckPermissionReq_ModuleEnum_value = map[string]int32{
		"MODULE_UNKNOWN":  0,
		"MODULE_AGENT":    1,
		"MODULE_WORKFLOW": 2,
	}
)

func (x CheckPermissionReq_ModuleEnum) Enum() *CheckPermissionReq_ModuleEnum {
	p := new(CheckPermissionReq_ModuleEnum)
	*p = x
	return p
}

func (x CheckPermissionReq_ModuleEnum) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (CheckPermissionReq_ModuleEnum) Descriptor() protoreflect.EnumDescriptor {
	return file_plugin_config_proto_enumTypes[12].Descriptor()
}

func (CheckPermissionReq_ModuleEnum) Type() protoreflect.EnumType {
	return &file_plugin_config_proto_enumTypes[12]
}

func (x CheckPermissionReq_ModuleEnum) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use CheckPermissionReq_ModuleEnum.Descriptor instead.
func (CheckPermissionReq_ModuleEnum) EnumDescriptor() ([]byte, []int) {
	return file_plugin_config_proto_rawDescGZIP(), []int{67, 0}
}

// 权限状态枚举
type CheckPermissionRsp_PermissionStatus int32

const (
	CheckPermissionRsp_ALLOWED          CheckPermissionRsp_PermissionStatus = 0 // 有权限使用
	CheckPermissionRsp_NOT_IN_WHITELIST CheckPermissionRsp_PermissionStatus = 1 // 不在白名单中，不能使用
)

// Enum value maps for CheckPermissionRsp_PermissionStatus.
var (
	CheckPermissionRsp_PermissionStatus_name = map[int32]string{
		0: "ALLOWED",
		1: "NOT_IN_WHITELIST",
	}
	CheckPermissionRsp_PermissionStatus_value = map[string]int32{
		"ALLOWED":          0,
		"NOT_IN_WHITELIST": 1,
	}
)

func (x CheckPermissionRsp_PermissionStatus) Enum() *CheckPermissionRsp_PermissionStatus {
	p := new(CheckPermissionRsp_PermissionStatus)
	*p = x
	return p
}

func (x CheckPermissionRsp_PermissionStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (CheckPermissionRsp_PermissionStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_plugin_config_proto_enumTypes[13].Descriptor()
}

func (CheckPermissionRsp_PermissionStatus) Type() protoreflect.EnumType {
	return &file_plugin_config_proto_enumTypes[13]
}

func (x CheckPermissionRsp_PermissionStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use CheckPermissionRsp_PermissionStatus.Descriptor instead.
func (CheckPermissionRsp_PermissionStatus) EnumDescriptor() ([]byte, []int) {
	return file_plugin_config_proto_rawDescGZIP(), []int{68, 0}
}

type ListPluginsReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Query      string                        `protobuf:"bytes,1,opt,name=Query,proto3" json:"Query,omitempty"`                                                                             // 查询内容
	PageSize   int32                         `protobuf:"varint,2,opt,name=PageSize,proto3" json:"PageSize,omitempty"`                                                                      // 每页大小
	PageNumber int32                         `protobuf:"varint,3,opt,name=PageNumber,proto3" json:"PageNumber,omitempty"`                                                                  // 页码
	QueryType  ListPluginsReq_QueryTypeEnum  `protobuf:"varint,4,opt,name=QueryType,proto3,enum=trpc.KEP.plugin_config_server.ListPluginsReq_QueryTypeEnum" json:"QueryType,omitempty"`    // 查询类型，默认工具NAME查询
	PluginType ListPluginsReq_PluginTypeEnum `protobuf:"varint,5,opt,name=PluginType,proto3,enum=trpc.KEP.plugin_config_server.ListPluginsReq_PluginTypeEnum" json:"PluginType,omitempty"` // 插件类型
	PluginIds  []string                      `protobuf:"bytes,6,rep,name=PluginIds,proto3" json:"PluginIds,omitempty"`                                                                     // 插件id列表
	// 可见模块，插件中心调用可以不填或者填ModuleAll，agent模式中获取填ModuleAgent，工作流模式中获取填ModuleWorkflow，标准模式中获取填ModuleStandard
	Module ListPluginsReq_ModuleEnum `protobuf:"varint,7,opt,name=Module,proto3,enum=trpc.KEP.plugin_config_server.ListPluginsReq_ModuleEnum" json:"Module,omitempty"`
	// uin信息
	Uin         string           `protobuf:"bytes,8,opt,name=uin,proto3" json:"uin,omitempty"`
	CreateTypes []CreateTypeEnum `protobuf:"varint,9,rep,packed,name=CreateTypes,proto3,enum=trpc.KEP.plugin_config_server.CreateTypeEnum" json:"CreateTypes,omitempty"` // 插件创建类型，0-服务 1-代码 2-MCP 空表示不过滤
}

func (x *ListPluginsReq) Reset() {
	*x = ListPluginsReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_plugin_config_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListPluginsReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListPluginsReq) ProtoMessage() {}

func (x *ListPluginsReq) ProtoReflect() protoreflect.Message {
	mi := &file_plugin_config_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListPluginsReq.ProtoReflect.Descriptor instead.
func (*ListPluginsReq) Descriptor() ([]byte, []int) {
	return file_plugin_config_proto_rawDescGZIP(), []int{0}
}

func (x *ListPluginsReq) GetQuery() string {
	if x != nil {
		return x.Query
	}
	return ""
}

func (x *ListPluginsReq) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

func (x *ListPluginsReq) GetPageNumber() int32 {
	if x != nil {
		return x.PageNumber
	}
	return 0
}

func (x *ListPluginsReq) GetQueryType() ListPluginsReq_QueryTypeEnum {
	if x != nil {
		return x.QueryType
	}
	return ListPluginsReq_NAME
}

func (x *ListPluginsReq) GetPluginType() ListPluginsReq_PluginTypeEnum {
	if x != nil {
		return x.PluginType
	}
	return ListPluginsReq_ALL
}

func (x *ListPluginsReq) GetPluginIds() []string {
	if x != nil {
		return x.PluginIds
	}
	return nil
}

func (x *ListPluginsReq) GetModule() ListPluginsReq_ModuleEnum {
	if x != nil {
		return x.Module
	}
	return ListPluginsReq_MODULE_ALL
}

func (x *ListPluginsReq) GetUin() string {
	if x != nil {
		return x.Uin
	}
	return ""
}

func (x *ListPluginsReq) GetCreateTypes() []CreateTypeEnum {
	if x != nil {
		return x.CreateTypes
	}
	return nil
}

type ListPluginsRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Plugins    []*PluginInfo `protobuf:"bytes,1,rep,name=Plugins,proto3" json:"Plugins,omitempty"`
	Total      int32         `protobuf:"varint,2,opt,name=Total,proto3" json:"Total,omitempty"`
	PageNumber int32         `protobuf:"varint,3,opt,name=PageNumber,proto3" json:"PageNumber,omitempty"`
}

func (x *ListPluginsRsp) Reset() {
	*x = ListPluginsRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_plugin_config_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListPluginsRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListPluginsRsp) ProtoMessage() {}

func (x *ListPluginsRsp) ProtoReflect() protoreflect.Message {
	mi := &file_plugin_config_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListPluginsRsp.ProtoReflect.Descriptor instead.
func (*ListPluginsRsp) Descriptor() ([]byte, []int) {
	return file_plugin_config_proto_rawDescGZIP(), []int{1}
}

func (x *ListPluginsRsp) GetPlugins() []*PluginInfo {
	if x != nil {
		return x.Plugins
	}
	return nil
}

func (x *ListPluginsRsp) GetTotal() int32 {
	if x != nil {
		return x.Total
	}
	return 0
}

func (x *ListPluginsRsp) GetPageNumber() int32 {
	if x != nil {
		return x.PageNumber
	}
	return 0
}

type CreatePluginReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name       string         `protobuf:"bytes,1,opt,name=Name,proto3" json:"Name,omitempty"`                                                                // 插件名称
	Desc       string         `protobuf:"bytes,2,opt,name=Desc,proto3" json:"Desc,omitempty"`                                                                // 插件描述信息
	IconUrl    string         `protobuf:"bytes,3,opt,name=IconUrl,proto3" json:"IconUrl,omitempty"`                                                          // 插件图标url
	AuthType   AuthTypeEnum   `protobuf:"varint,4,opt,name=AuthType,proto3,enum=trpc.KEP.plugin_config_server.AuthTypeEnum" json:"AuthType,omitempty"`       // 授权方式，仅自定义插件返回
	AuthInfo   *AuthInfo      `protobuf:"bytes,5,opt,name=AuthInfo,proto3" json:"AuthInfo,omitempty"`                                                        // 授权信息，仅自定义插件返回
	OpenApi    string         `protobuf:"bytes,6,opt,name=OpenApi,proto3" json:"OpenApi,omitempty"`                                                          // OpenAPI描述，仅自定义插件返回
	PluginType PluginTypeEnum `protobuf:"varint,7,opt,name=PluginType,proto3,enum=trpc.KEP.plugin_config_server.PluginTypeEnum" json:"PluginType,omitempty"` // 插件类型
	Tools      []*ToolInfo    `protobuf:"bytes,8,rep,name=Tools,proto3" json:"Tools,omitempty"`                                                              // 工具信息
	CreateType CreateTypeEnum `protobuf:"varint,9,opt,name=CreateType,proto3,enum=trpc.KEP.plugin_config_server.CreateTypeEnum" json:"CreateType,omitempty"` // 创建方式
}

func (x *CreatePluginReq) Reset() {
	*x = CreatePluginReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_plugin_config_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreatePluginReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreatePluginReq) ProtoMessage() {}

func (x *CreatePluginReq) ProtoReflect() protoreflect.Message {
	mi := &file_plugin_config_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreatePluginReq.ProtoReflect.Descriptor instead.
func (*CreatePluginReq) Descriptor() ([]byte, []int) {
	return file_plugin_config_proto_rawDescGZIP(), []int{2}
}

func (x *CreatePluginReq) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *CreatePluginReq) GetDesc() string {
	if x != nil {
		return x.Desc
	}
	return ""
}

func (x *CreatePluginReq) GetIconUrl() string {
	if x != nil {
		return x.IconUrl
	}
	return ""
}

func (x *CreatePluginReq) GetAuthType() AuthTypeEnum {
	if x != nil {
		return x.AuthType
	}
	return AuthTypeEnum_NONE
}

func (x *CreatePluginReq) GetAuthInfo() *AuthInfo {
	if x != nil {
		return x.AuthInfo
	}
	return nil
}

func (x *CreatePluginReq) GetOpenApi() string {
	if x != nil {
		return x.OpenApi
	}
	return ""
}

func (x *CreatePluginReq) GetPluginType() PluginTypeEnum {
	if x != nil {
		return x.PluginType
	}
	return PluginTypeEnum_CUSTOM
}

func (x *CreatePluginReq) GetTools() []*ToolInfo {
	if x != nil {
		return x.Tools
	}
	return nil
}

func (x *CreatePluginReq) GetCreateType() CreateTypeEnum {
	if x != nil {
		return x.CreateType
	}
	return CreateTypeEnum_SERVICE
}

type CreatePluginRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PluginId string `protobuf:"bytes,1,opt,name=PluginId,proto3" json:"PluginId,omitempty"`
}

func (x *CreatePluginRsp) Reset() {
	*x = CreatePluginRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_plugin_config_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreatePluginRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreatePluginRsp) ProtoMessage() {}

func (x *CreatePluginRsp) ProtoReflect() protoreflect.Message {
	mi := &file_plugin_config_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreatePluginRsp.ProtoReflect.Descriptor instead.
func (*CreatePluginRsp) Descriptor() ([]byte, []int) {
	return file_plugin_config_proto_rawDescGZIP(), []int{3}
}

func (x *CreatePluginRsp) GetPluginId() string {
	if x != nil {
		return x.PluginId
	}
	return ""
}

type ModifyPluginReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PluginId      string         `protobuf:"bytes,1,opt,name=PluginId,proto3" json:"PluginId,omitempty"`                                                         // 插件id
	Name          string         `protobuf:"bytes,2,opt,name=Name,proto3" json:"Name,omitempty"`                                                                 // 插件名称
	PluginVersion int32          `protobuf:"varint,3,opt,name=PluginVersion,proto3" json:"PluginVersion,omitempty"`                                              // 插件版本，每次更新后会加1
	Desc          string         `protobuf:"bytes,4,opt,name=Desc,proto3" json:"Desc,omitempty"`                                                                 // 插件描述信息
	IconUrl       string         `protobuf:"bytes,5,opt,name=IconUrl,proto3" json:"IconUrl,omitempty"`                                                           // 插件图标url
	AuthType      AuthTypeEnum   `protobuf:"varint,6,opt,name=AuthType,proto3,enum=trpc.KEP.plugin_config_server.AuthTypeEnum" json:"AuthType,omitempty"`        // 授权方式，仅自定义插件返回
	AuthInfo      *AuthInfo      `protobuf:"bytes,7,opt,name=AuthInfo,proto3" json:"AuthInfo,omitempty"`                                                         // 授权信息，仅自定义插件返回
	OpenApi       string         `protobuf:"bytes,8,opt,name=OpenApi,proto3" json:"OpenApi,omitempty"`                                                           // OpenAPI描述，仅自定义插件返回
	PluginType    PluginTypeEnum `protobuf:"varint,9,opt,name=PluginType,proto3,enum=trpc.KEP.plugin_config_server.PluginTypeEnum" json:"PluginType,omitempty"`  // 插件类型
	Tools         []*ToolInfo    `protobuf:"bytes,10,rep,name=Tools,proto3" json:"Tools,omitempty"`                                                              // 工具信息
	CreateType    CreateTypeEnum `protobuf:"varint,11,opt,name=CreateType,proto3,enum=trpc.KEP.plugin_config_server.CreateTypeEnum" json:"CreateType,omitempty"` // 创建方式
}

func (x *ModifyPluginReq) Reset() {
	*x = ModifyPluginReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_plugin_config_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ModifyPluginReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ModifyPluginReq) ProtoMessage() {}

func (x *ModifyPluginReq) ProtoReflect() protoreflect.Message {
	mi := &file_plugin_config_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ModifyPluginReq.ProtoReflect.Descriptor instead.
func (*ModifyPluginReq) Descriptor() ([]byte, []int) {
	return file_plugin_config_proto_rawDescGZIP(), []int{4}
}

func (x *ModifyPluginReq) GetPluginId() string {
	if x != nil {
		return x.PluginId
	}
	return ""
}

func (x *ModifyPluginReq) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *ModifyPluginReq) GetPluginVersion() int32 {
	if x != nil {
		return x.PluginVersion
	}
	return 0
}

func (x *ModifyPluginReq) GetDesc() string {
	if x != nil {
		return x.Desc
	}
	return ""
}

func (x *ModifyPluginReq) GetIconUrl() string {
	if x != nil {
		return x.IconUrl
	}
	return ""
}

func (x *ModifyPluginReq) GetAuthType() AuthTypeEnum {
	if x != nil {
		return x.AuthType
	}
	return AuthTypeEnum_NONE
}

func (x *ModifyPluginReq) GetAuthInfo() *AuthInfo {
	if x != nil {
		return x.AuthInfo
	}
	return nil
}

func (x *ModifyPluginReq) GetOpenApi() string {
	if x != nil {
		return x.OpenApi
	}
	return ""
}

func (x *ModifyPluginReq) GetPluginType() PluginTypeEnum {
	if x != nil {
		return x.PluginType
	}
	return PluginTypeEnum_CUSTOM
}

func (x *ModifyPluginReq) GetTools() []*ToolInfo {
	if x != nil {
		return x.Tools
	}
	return nil
}

func (x *ModifyPluginReq) GetCreateType() CreateTypeEnum {
	if x != nil {
		return x.CreateType
	}
	return CreateTypeEnum_SERVICE
}

type ModifyPluginRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *ModifyPluginRsp) Reset() {
	*x = ModifyPluginRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_plugin_config_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ModifyPluginRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ModifyPluginRsp) ProtoMessage() {}

func (x *ModifyPluginRsp) ProtoReflect() protoreflect.Message {
	mi := &file_plugin_config_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ModifyPluginRsp.ProtoReflect.Descriptor instead.
func (*ModifyPluginRsp) Descriptor() ([]byte, []int) {
	return file_plugin_config_proto_rawDescGZIP(), []int{5}
}

// PluginHeader MCP插件header信息
type PluginHeader struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ParamName    string `protobuf:"bytes,1,opt,name=ParamName,proto3" json:"ParamName,omitempty"`        // 参数名称
	ParamValue   string `protobuf:"bytes,2,opt,name=ParamValue,proto3" json:"ParamValue,omitempty"`      // 参数值
	GlobalHidden bool   `protobuf:"varint,3,opt,name=GlobalHidden,proto3" json:"GlobalHidden,omitempty"` // header参数配置是否隐藏不可见，true-隐藏不可见，false-可见
	IsRequired   bool   `protobuf:"varint,4,opt,name=IsRequired,proto3" json:"IsRequired,omitempty"`     // header参数是否必填，true-必填，false-非必填
}

func (x *PluginHeader) Reset() {
	*x = PluginHeader{}
	if protoimpl.UnsafeEnabled {
		mi := &file_plugin_config_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PluginHeader) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PluginHeader) ProtoMessage() {}

func (x *PluginHeader) ProtoReflect() protoreflect.Message {
	mi := &file_plugin_config_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PluginHeader.ProtoReflect.Descriptor instead.
func (*PluginHeader) Descriptor() ([]byte, []int) {
	return file_plugin_config_proto_rawDescGZIP(), []int{6}
}

func (x *PluginHeader) GetParamName() string {
	if x != nil {
		return x.ParamName
	}
	return ""
}

func (x *PluginHeader) GetParamValue() string {
	if x != nil {
		return x.ParamValue
	}
	return ""
}

func (x *PluginHeader) GetGlobalHidden() bool {
	if x != nil {
		return x.GlobalHidden
	}
	return false
}

func (x *PluginHeader) GetIsRequired() bool {
	if x != nil {
		return x.IsRequired
	}
	return false
}

type CreateMCPPluginReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Plugins []*CreateMCPPluginReq_MCPPluginItem `protobuf:"bytes,1,rep,name=Plugins,proto3" json:"Plugins,omitempty"` // 插件信息
}

func (x *CreateMCPPluginReq) Reset() {
	*x = CreateMCPPluginReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_plugin_config_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateMCPPluginReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateMCPPluginReq) ProtoMessage() {}

func (x *CreateMCPPluginReq) ProtoReflect() protoreflect.Message {
	mi := &file_plugin_config_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateMCPPluginReq.ProtoReflect.Descriptor instead.
func (*CreateMCPPluginReq) Descriptor() ([]byte, []int) {
	return file_plugin_config_proto_rawDescGZIP(), []int{7}
}

func (x *CreateMCPPluginReq) GetPlugins() []*CreateMCPPluginReq_MCPPluginItem {
	if x != nil {
		return x.Plugins
	}
	return nil
}

type CreateMCPPluginRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PluginIds []string `protobuf:"bytes,1,rep,name=PluginIds,proto3" json:"PluginIds,omitempty"` // 创建成功返回对应的插件id
}

func (x *CreateMCPPluginRsp) Reset() {
	*x = CreateMCPPluginRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_plugin_config_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateMCPPluginRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateMCPPluginRsp) ProtoMessage() {}

func (x *CreateMCPPluginRsp) ProtoReflect() protoreflect.Message {
	mi := &file_plugin_config_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateMCPPluginRsp.ProtoReflect.Descriptor instead.
func (*CreateMCPPluginRsp) Descriptor() ([]byte, []int) {
	return file_plugin_config_proto_rawDescGZIP(), []int{8}
}

func (x *CreateMCPPluginRsp) GetPluginIds() []string {
	if x != nil {
		return x.PluginIds
	}
	return nil
}

type ModifyMCPPluginReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PluginId       string          `protobuf:"bytes,1,opt,name=PluginId,proto3" json:"PluginId,omitempty"`              // 插件id
	Name           string          `protobuf:"bytes,2,opt,name=Name,proto3" json:"Name,omitempty"`                      // 插件名称
	PluginVersion  int32           `protobuf:"varint,3,opt,name=PluginVersion,proto3" json:"PluginVersion,omitempty"`   // 插件版本，每次更新后会加1
	Desc           string          `protobuf:"bytes,4,opt,name=Desc,proto3" json:"Desc,omitempty"`                      // 插件描述信息
	IconUrl        string          `protobuf:"bytes,5,opt,name=IconUrl,proto3" json:"IconUrl,omitempty"`                // 插件图标url
	McpServerUrl   string          `protobuf:"bytes,6,opt,name=McpServerUrl,proto3" json:"McpServerUrl,omitempty"`      // MCP server地址
	Headers        []*PluginHeader `protobuf:"bytes,7,rep,name=Headers,proto3" json:"Headers,omitempty"`                // MCP server header信息
	Timeout        int32           `protobuf:"varint,8,opt,name=Timeout,proto3" json:"Timeout,omitempty"`               // 超时时间，单位秒
	SseReadTimeout int32           `protobuf:"varint,9,opt,name=SseReadTimeout,proto3" json:"SseReadTimeout,omitempty"` // sse服务超时时间，单位秒
}

func (x *ModifyMCPPluginReq) Reset() {
	*x = ModifyMCPPluginReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_plugin_config_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ModifyMCPPluginReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ModifyMCPPluginReq) ProtoMessage() {}

func (x *ModifyMCPPluginReq) ProtoReflect() protoreflect.Message {
	mi := &file_plugin_config_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ModifyMCPPluginReq.ProtoReflect.Descriptor instead.
func (*ModifyMCPPluginReq) Descriptor() ([]byte, []int) {
	return file_plugin_config_proto_rawDescGZIP(), []int{9}
}

func (x *ModifyMCPPluginReq) GetPluginId() string {
	if x != nil {
		return x.PluginId
	}
	return ""
}

func (x *ModifyMCPPluginReq) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *ModifyMCPPluginReq) GetPluginVersion() int32 {
	if x != nil {
		return x.PluginVersion
	}
	return 0
}

func (x *ModifyMCPPluginReq) GetDesc() string {
	if x != nil {
		return x.Desc
	}
	return ""
}

func (x *ModifyMCPPluginReq) GetIconUrl() string {
	if x != nil {
		return x.IconUrl
	}
	return ""
}

func (x *ModifyMCPPluginReq) GetMcpServerUrl() string {
	if x != nil {
		return x.McpServerUrl
	}
	return ""
}

func (x *ModifyMCPPluginReq) GetHeaders() []*PluginHeader {
	if x != nil {
		return x.Headers
	}
	return nil
}

func (x *ModifyMCPPluginReq) GetTimeout() int32 {
	if x != nil {
		return x.Timeout
	}
	return 0
}

func (x *ModifyMCPPluginReq) GetSseReadTimeout() int32 {
	if x != nil {
		return x.SseReadTimeout
	}
	return 0
}

type ModifyMCPPluginRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *ModifyMCPPluginRsp) Reset() {
	*x = ModifyMCPPluginRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_plugin_config_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ModifyMCPPluginRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ModifyMCPPluginRsp) ProtoMessage() {}

func (x *ModifyMCPPluginRsp) ProtoReflect() protoreflect.Message {
	mi := &file_plugin_config_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ModifyMCPPluginRsp.ProtoReflect.Descriptor instead.
func (*ModifyMCPPluginRsp) Descriptor() ([]byte, []int) {
	return file_plugin_config_proto_rawDescGZIP(), []int{10}
}

type CheckMCPServerReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PluginId string `protobuf:"bytes,1,opt,name=PluginId,proto3" json:"PluginId,omitempty"` // 插件id
}

func (x *CheckMCPServerReq) Reset() {
	*x = CheckMCPServerReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_plugin_config_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CheckMCPServerReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CheckMCPServerReq) ProtoMessage() {}

func (x *CheckMCPServerReq) ProtoReflect() protoreflect.Message {
	mi := &file_plugin_config_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CheckMCPServerReq.ProtoReflect.Descriptor instead.
func (*CheckMCPServerReq) Descriptor() ([]byte, []int) {
	return file_plugin_config_proto_rawDescGZIP(), []int{11}
}

func (x *CheckMCPServerReq) GetPluginId() string {
	if x != nil {
		return x.PluginId
	}
	return ""
}

type CheckMCPServerRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status int32 `protobuf:"varint,1,opt,name=Status,proto3" json:"Status,omitempty"` //插件状态 1-成功(可用)，2-不可用
}

func (x *CheckMCPServerRsp) Reset() {
	*x = CheckMCPServerRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_plugin_config_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CheckMCPServerRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CheckMCPServerRsp) ProtoMessage() {}

func (x *CheckMCPServerRsp) ProtoReflect() protoreflect.Message {
	mi := &file_plugin_config_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CheckMCPServerRsp.ProtoReflect.Descriptor instead.
func (*CheckMCPServerRsp) Descriptor() ([]byte, []int) {
	return file_plugin_config_proto_rawDescGZIP(), []int{12}
}

func (x *CheckMCPServerRsp) GetStatus() int32 {
	if x != nil {
		return x.Status
	}
	return 0
}

type CreateMCPPluginInnerReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name           string                                  `protobuf:"bytes,1,opt,name=Name,proto3" json:"Name,omitempty"`                                                                 // 插件名称
	Desc           string                                  `protobuf:"bytes,2,opt,name=Desc,proto3" json:"Desc,omitempty"`                                                                 // 插件描述信息
	IconUrl        string                                  `protobuf:"bytes,3,opt,name=IconUrl,proto3" json:"IconUrl,omitempty"`                                                           // 插件图标url
	Module         string                                  `protobuf:"bytes,4,opt,name=Module,proto3" json:"Module,omitempty"`                                                             //工具所属模块
	McpServerUrl   string                                  `protobuf:"bytes,5,opt,name=McpServerUrl,proto3" json:"McpServerUrl,omitempty"`                                                 // MCP server地址
	Headers        []*CreateMCPPluginInnerReq_PluginHeader `protobuf:"bytes,6,rep,name=Headers,proto3" json:"Headers,omitempty"`                                                           // MCP server header信息
	Timeout        int32                                   `protobuf:"varint,7,opt,name=Timeout,proto3" json:"Timeout,omitempty"`                                                          // 超时时间，单位秒
	SseReadTimeout int32                                   `protobuf:"varint,8,opt,name=SseReadTimeout,proto3" json:"SseReadTimeout,omitempty"`                                            // sse服务超时时间，单位秒
	RunCommand     string                                  `protobuf:"bytes,9,opt,name=RunCommand,proto3" json:"RunCommand,omitempty"`                                                     //启动命令
	RunArgs        string                                  `protobuf:"bytes,10,opt,name=RunArgs,proto3" json:"RunArgs,omitempty"`                                                          //启动参数
	RunEnv         string                                  `protobuf:"bytes,11,opt,name=RunEnv,proto3" json:"RunEnv,omitempty"`                                                            //环境变量
	Developer      string                                  `protobuf:"bytes,12,opt,name=Developer,proto3" json:"Developer,omitempty"`                                                      //开发者
	PluginType     PluginTypeEnum                          `protobuf:"varint,13,opt,name=PluginType,proto3,enum=trpc.KEP.plugin_config_server.PluginTypeEnum" json:"PluginType,omitempty"` // 插件类型
}

func (x *CreateMCPPluginInnerReq) Reset() {
	*x = CreateMCPPluginInnerReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_plugin_config_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateMCPPluginInnerReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateMCPPluginInnerReq) ProtoMessage() {}

func (x *CreateMCPPluginInnerReq) ProtoReflect() protoreflect.Message {
	mi := &file_plugin_config_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateMCPPluginInnerReq.ProtoReflect.Descriptor instead.
func (*CreateMCPPluginInnerReq) Descriptor() ([]byte, []int) {
	return file_plugin_config_proto_rawDescGZIP(), []int{13}
}

func (x *CreateMCPPluginInnerReq) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *CreateMCPPluginInnerReq) GetDesc() string {
	if x != nil {
		return x.Desc
	}
	return ""
}

func (x *CreateMCPPluginInnerReq) GetIconUrl() string {
	if x != nil {
		return x.IconUrl
	}
	return ""
}

func (x *CreateMCPPluginInnerReq) GetModule() string {
	if x != nil {
		return x.Module
	}
	return ""
}

func (x *CreateMCPPluginInnerReq) GetMcpServerUrl() string {
	if x != nil {
		return x.McpServerUrl
	}
	return ""
}

func (x *CreateMCPPluginInnerReq) GetHeaders() []*CreateMCPPluginInnerReq_PluginHeader {
	if x != nil {
		return x.Headers
	}
	return nil
}

func (x *CreateMCPPluginInnerReq) GetTimeout() int32 {
	if x != nil {
		return x.Timeout
	}
	return 0
}

func (x *CreateMCPPluginInnerReq) GetSseReadTimeout() int32 {
	if x != nil {
		return x.SseReadTimeout
	}
	return 0
}

func (x *CreateMCPPluginInnerReq) GetRunCommand() string {
	if x != nil {
		return x.RunCommand
	}
	return ""
}

func (x *CreateMCPPluginInnerReq) GetRunArgs() string {
	if x != nil {
		return x.RunArgs
	}
	return ""
}

func (x *CreateMCPPluginInnerReq) GetRunEnv() string {
	if x != nil {
		return x.RunEnv
	}
	return ""
}

func (x *CreateMCPPluginInnerReq) GetDeveloper() string {
	if x != nil {
		return x.Developer
	}
	return ""
}

func (x *CreateMCPPluginInnerReq) GetPluginType() PluginTypeEnum {
	if x != nil {
		return x.PluginType
	}
	return PluginTypeEnum_CUSTOM
}

type CreateMCPPluginInnerRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PluginId string `protobuf:"bytes,1,opt,name=PluginId,proto3" json:"PluginId,omitempty"`
}

func (x *CreateMCPPluginInnerRsp) Reset() {
	*x = CreateMCPPluginInnerRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_plugin_config_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateMCPPluginInnerRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateMCPPluginInnerRsp) ProtoMessage() {}

func (x *CreateMCPPluginInnerRsp) ProtoReflect() protoreflect.Message {
	mi := &file_plugin_config_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateMCPPluginInnerRsp.ProtoReflect.Descriptor instead.
func (*CreateMCPPluginInnerRsp) Descriptor() ([]byte, []int) {
	return file_plugin_config_proto_rawDescGZIP(), []int{14}
}

func (x *CreateMCPPluginInnerRsp) GetPluginId() string {
	if x != nil {
		return x.PluginId
	}
	return ""
}

type ModifyMCPPluginInnerReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PluginId       string                                  `protobuf:"bytes,1,opt,name=PluginId,proto3" json:"PluginId,omitempty"`                                                         // 插件id
	Name           string                                  `protobuf:"bytes,2,opt,name=Name,proto3" json:"Name,omitempty"`                                                                 // 插件名称
	PluginVersion  int32                                   `protobuf:"varint,3,opt,name=PluginVersion,proto3" json:"PluginVersion,omitempty"`                                              // 插件版本，每次更新后会加1
	Desc           string                                  `protobuf:"bytes,4,opt,name=Desc,proto3" json:"Desc,omitempty"`                                                                 // 插件描述信息
	IconUrl        string                                  `protobuf:"bytes,5,opt,name=IconUrl,proto3" json:"IconUrl,omitempty"`                                                           // 插件图标url
	Module         string                                  `protobuf:"bytes,6,opt,name=Module,proto3" json:"Module,omitempty"`                                                             //工具所属模块
	McpServerUrl   string                                  `protobuf:"bytes,7,opt,name=McpServerUrl,proto3" json:"McpServerUrl,omitempty"`                                                 // MCP server地址
	Headers        []*ModifyMCPPluginInnerReq_PluginHeader `protobuf:"bytes,8,rep,name=Headers,proto3" json:"Headers,omitempty"`                                                           // MCP server header信息
	Timeout        int32                                   `protobuf:"varint,9,opt,name=Timeout,proto3" json:"Timeout,omitempty"`                                                          // 超时时间，单位秒
	SseReadTimeout int32                                   `protobuf:"varint,10,opt,name=SseReadTimeout,proto3" json:"SseReadTimeout,omitempty"`                                           // sse服务超时时间，单位秒
	RunCommand     string                                  `protobuf:"bytes,11,opt,name=RunCommand,proto3" json:"RunCommand,omitempty"`                                                    //启动命令
	RunArgs        string                                  `protobuf:"bytes,12,opt,name=RunArgs,proto3" json:"RunArgs,omitempty"`                                                          //启动参数
	RunEnv         string                                  `protobuf:"bytes,13,opt,name=RunEnv,proto3" json:"RunEnv,omitempty"`                                                            //环境变量
	Developer      string                                  `protobuf:"bytes,14,opt,name=Developer,proto3" json:"Developer,omitempty"`                                                      //开发者
	PluginType     PluginTypeEnum                          `protobuf:"varint,15,opt,name=PluginType,proto3,enum=trpc.KEP.plugin_config_server.PluginTypeEnum" json:"PluginType,omitempty"` // 插件类型
}

func (x *ModifyMCPPluginInnerReq) Reset() {
	*x = ModifyMCPPluginInnerReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_plugin_config_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ModifyMCPPluginInnerReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ModifyMCPPluginInnerReq) ProtoMessage() {}

func (x *ModifyMCPPluginInnerReq) ProtoReflect() protoreflect.Message {
	mi := &file_plugin_config_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ModifyMCPPluginInnerReq.ProtoReflect.Descriptor instead.
func (*ModifyMCPPluginInnerReq) Descriptor() ([]byte, []int) {
	return file_plugin_config_proto_rawDescGZIP(), []int{15}
}

func (x *ModifyMCPPluginInnerReq) GetPluginId() string {
	if x != nil {
		return x.PluginId
	}
	return ""
}

func (x *ModifyMCPPluginInnerReq) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *ModifyMCPPluginInnerReq) GetPluginVersion() int32 {
	if x != nil {
		return x.PluginVersion
	}
	return 0
}

func (x *ModifyMCPPluginInnerReq) GetDesc() string {
	if x != nil {
		return x.Desc
	}
	return ""
}

func (x *ModifyMCPPluginInnerReq) GetIconUrl() string {
	if x != nil {
		return x.IconUrl
	}
	return ""
}

func (x *ModifyMCPPluginInnerReq) GetModule() string {
	if x != nil {
		return x.Module
	}
	return ""
}

func (x *ModifyMCPPluginInnerReq) GetMcpServerUrl() string {
	if x != nil {
		return x.McpServerUrl
	}
	return ""
}

func (x *ModifyMCPPluginInnerReq) GetHeaders() []*ModifyMCPPluginInnerReq_PluginHeader {
	if x != nil {
		return x.Headers
	}
	return nil
}

func (x *ModifyMCPPluginInnerReq) GetTimeout() int32 {
	if x != nil {
		return x.Timeout
	}
	return 0
}

func (x *ModifyMCPPluginInnerReq) GetSseReadTimeout() int32 {
	if x != nil {
		return x.SseReadTimeout
	}
	return 0
}

func (x *ModifyMCPPluginInnerReq) GetRunCommand() string {
	if x != nil {
		return x.RunCommand
	}
	return ""
}

func (x *ModifyMCPPluginInnerReq) GetRunArgs() string {
	if x != nil {
		return x.RunArgs
	}
	return ""
}

func (x *ModifyMCPPluginInnerReq) GetRunEnv() string {
	if x != nil {
		return x.RunEnv
	}
	return ""
}

func (x *ModifyMCPPluginInnerReq) GetDeveloper() string {
	if x != nil {
		return x.Developer
	}
	return ""
}

func (x *ModifyMCPPluginInnerReq) GetPluginType() PluginTypeEnum {
	if x != nil {
		return x.PluginType
	}
	return PluginTypeEnum_CUSTOM
}

type ModifyMCPPluginInnerRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *ModifyMCPPluginInnerRsp) Reset() {
	*x = ModifyMCPPluginInnerRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_plugin_config_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ModifyMCPPluginInnerRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ModifyMCPPluginInnerRsp) ProtoMessage() {}

func (x *ModifyMCPPluginInnerRsp) ProtoReflect() protoreflect.Message {
	mi := &file_plugin_config_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ModifyMCPPluginInnerRsp.ProtoReflect.Descriptor instead.
func (*ModifyMCPPluginInnerRsp) Descriptor() ([]byte, []int) {
	return file_plugin_config_proto_rawDescGZIP(), []int{16}
}

type DeletePluginReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PluginIds []string `protobuf:"bytes,1,rep,name=PluginIds,proto3" json:"PluginIds,omitempty"`
}

func (x *DeletePluginReq) Reset() {
	*x = DeletePluginReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_plugin_config_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeletePluginReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeletePluginReq) ProtoMessage() {}

func (x *DeletePluginReq) ProtoReflect() protoreflect.Message {
	mi := &file_plugin_config_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeletePluginReq.ProtoReflect.Descriptor instead.
func (*DeletePluginReq) Descriptor() ([]byte, []int) {
	return file_plugin_config_proto_rawDescGZIP(), []int{17}
}

func (x *DeletePluginReq) GetPluginIds() []string {
	if x != nil {
		return x.PluginIds
	}
	return nil
}

type DeletePluginRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *DeletePluginRsp) Reset() {
	*x = DeletePluginRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_plugin_config_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeletePluginRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeletePluginRsp) ProtoMessage() {}

func (x *DeletePluginRsp) ProtoReflect() protoreflect.Message {
	mi := &file_plugin_config_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeletePluginRsp.ProtoReflect.Descriptor instead.
func (*DeletePluginRsp) Descriptor() ([]byte, []int) {
	return file_plugin_config_proto_rawDescGZIP(), []int{18}
}

type DescribePluginReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PluginId string `protobuf:"bytes,1,opt,name=PluginId,proto3" json:"PluginId,omitempty"` // 插件id
}

func (x *DescribePluginReq) Reset() {
	*x = DescribePluginReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_plugin_config_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DescribePluginReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DescribePluginReq) ProtoMessage() {}

func (x *DescribePluginReq) ProtoReflect() protoreflect.Message {
	mi := &file_plugin_config_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DescribePluginReq.ProtoReflect.Descriptor instead.
func (*DescribePluginReq) Descriptor() ([]byte, []int) {
	return file_plugin_config_proto_rawDescGZIP(), []int{19}
}

func (x *DescribePluginReq) GetPluginId() string {
	if x != nil {
		return x.PluginId
	}
	return ""
}

type DescribePluginRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PluginId       string          `protobuf:"bytes,1,opt,name=PluginId,proto3" json:"PluginId,omitempty"`                                                            // 插件id
	Name           string          `protobuf:"bytes,2,opt,name=Name,proto3" json:"Name,omitempty"`                                                                    // 插件名称
	PluginVersion  int32           `protobuf:"varint,3,opt,name=PluginVersion,proto3" json:"PluginVersion,omitempty"`                                                 // 插件版本，每次更新后会加1
	Desc           string          `protobuf:"bytes,4,opt,name=Desc,proto3" json:"Desc,omitempty"`                                                                    // 插件描述信息
	IconUrl        string          `protobuf:"bytes,5,opt,name=IconUrl,proto3" json:"IconUrl,omitempty"`                                                              // 插件图标url
	AuthType       AuthTypeEnum    `protobuf:"varint,6,opt,name=AuthType,proto3,enum=trpc.KEP.plugin_config_server.AuthTypeEnum" json:"AuthType,omitempty"`           // 授权方式，仅自定义插件返回
	AuthInfo       *AuthInfo       `protobuf:"bytes,7,opt,name=AuthInfo,proto3" json:"AuthInfo,omitempty"`                                                            // 授权信息，仅自定义插件返回
	OpenApi        string          `protobuf:"bytes,8,opt,name=OpenApi,proto3" json:"OpenApi,omitempty"`                                                              // OpenAPI描述，仅自定义插件返回
	UserInfo       *UserInfo       `protobuf:"bytes,9,opt,name=UserInfo,proto3" json:"UserInfo,omitempty"`                                                            // 创建者信息
	PluginType     PluginTypeEnum  `protobuf:"varint,10,opt,name=PluginType,proto3,enum=trpc.KEP.plugin_config_server.PluginTypeEnum" json:"PluginType,omitempty"`    // 插件类型
	Tools          []*ToolInfo     `protobuf:"bytes,11,rep,name=Tools,proto3" json:"Tools,omitempty"`                                                                 // 工具信息
	CreateTime     string          `protobuf:"bytes,12,opt,name=CreateTime,proto3" json:"CreateTime,omitempty"`                                                       // 创建时间
	UpdateTime     string          `protobuf:"bytes,13,opt,name=UpdateTime,proto3" json:"UpdateTime,omitempty"`                                                       // 更新时间
	FinanceType    FinanceTypeEnum `protobuf:"varint,14,opt,name=FinanceType,proto3,enum=trpc.KEP.plugin_config_server.FinanceTypeEnum" json:"FinanceType,omitempty"` // 计费类型
	CreateType     CreateTypeEnum  `protobuf:"varint,15,opt,name=CreateType,proto3,enum=trpc.KEP.plugin_config_server.CreateTypeEnum" json:"CreateType,omitempty"`    // 创建方式
	Status         int32           `protobuf:"varint,16,opt,name=Status,proto3" json:"Status,omitempty"`                                                              //插件状态 1-成功(可用)，2-不可用
	McpServerUrl   string          `protobuf:"bytes,17,opt,name=McpServerUrl,proto3" json:"McpServerUrl,omitempty"`                                                   // MCP server地址
	Headers        []*PluginHeader `protobuf:"bytes,18,rep,name=Headers,proto3" json:"Headers,omitempty"`                                                             // mcp插件的header参数
	Timeout        int32           `protobuf:"varint,19,opt,name=Timeout,proto3" json:"Timeout,omitempty"`                                                            // 超时时间，单位秒
	SseReadTimeout int32           `protobuf:"varint,20,opt,name=SseReadTimeout,proto3" json:"SseReadTimeout,omitempty"`                                              // sse服务超时时间，单位秒
}

func (x *DescribePluginRsp) Reset() {
	*x = DescribePluginRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_plugin_config_proto_msgTypes[20]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DescribePluginRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DescribePluginRsp) ProtoMessage() {}

func (x *DescribePluginRsp) ProtoReflect() protoreflect.Message {
	mi := &file_plugin_config_proto_msgTypes[20]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DescribePluginRsp.ProtoReflect.Descriptor instead.
func (*DescribePluginRsp) Descriptor() ([]byte, []int) {
	return file_plugin_config_proto_rawDescGZIP(), []int{20}
}

func (x *DescribePluginRsp) GetPluginId() string {
	if x != nil {
		return x.PluginId
	}
	return ""
}

func (x *DescribePluginRsp) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *DescribePluginRsp) GetPluginVersion() int32 {
	if x != nil {
		return x.PluginVersion
	}
	return 0
}

func (x *DescribePluginRsp) GetDesc() string {
	if x != nil {
		return x.Desc
	}
	return ""
}

func (x *DescribePluginRsp) GetIconUrl() string {
	if x != nil {
		return x.IconUrl
	}
	return ""
}

func (x *DescribePluginRsp) GetAuthType() AuthTypeEnum {
	if x != nil {
		return x.AuthType
	}
	return AuthTypeEnum_NONE
}

func (x *DescribePluginRsp) GetAuthInfo() *AuthInfo {
	if x != nil {
		return x.AuthInfo
	}
	return nil
}

func (x *DescribePluginRsp) GetOpenApi() string {
	if x != nil {
		return x.OpenApi
	}
	return ""
}

func (x *DescribePluginRsp) GetUserInfo() *UserInfo {
	if x != nil {
		return x.UserInfo
	}
	return nil
}

func (x *DescribePluginRsp) GetPluginType() PluginTypeEnum {
	if x != nil {
		return x.PluginType
	}
	return PluginTypeEnum_CUSTOM
}

func (x *DescribePluginRsp) GetTools() []*ToolInfo {
	if x != nil {
		return x.Tools
	}
	return nil
}

func (x *DescribePluginRsp) GetCreateTime() string {
	if x != nil {
		return x.CreateTime
	}
	return ""
}

func (x *DescribePluginRsp) GetUpdateTime() string {
	if x != nil {
		return x.UpdateTime
	}
	return ""
}

func (x *DescribePluginRsp) GetFinanceType() FinanceTypeEnum {
	if x != nil {
		return x.FinanceType
	}
	return FinanceTypeEnum_FREE
}

func (x *DescribePluginRsp) GetCreateType() CreateTypeEnum {
	if x != nil {
		return x.CreateType
	}
	return CreateTypeEnum_SERVICE
}

func (x *DescribePluginRsp) GetStatus() int32 {
	if x != nil {
		return x.Status
	}
	return 0
}

func (x *DescribePluginRsp) GetMcpServerUrl() string {
	if x != nil {
		return x.McpServerUrl
	}
	return ""
}

func (x *DescribePluginRsp) GetHeaders() []*PluginHeader {
	if x != nil {
		return x.Headers
	}
	return nil
}

func (x *DescribePluginRsp) GetTimeout() int32 {
	if x != nil {
		return x.Timeout
	}
	return 0
}

func (x *DescribePluginRsp) GetSseReadTimeout() int32 {
	if x != nil {
		return x.SseReadTimeout
	}
	return 0
}

type ListToolsReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ToolIds []string `protobuf:"bytes,1,rep,name=ToolIds,proto3" json:"ToolIds,omitempty"` // 工具id
}

func (x *ListToolsReq) Reset() {
	*x = ListToolsReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_plugin_config_proto_msgTypes[21]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListToolsReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListToolsReq) ProtoMessage() {}

func (x *ListToolsReq) ProtoReflect() protoreflect.Message {
	mi := &file_plugin_config_proto_msgTypes[21]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListToolsReq.ProtoReflect.Descriptor instead.
func (*ListToolsReq) Descriptor() ([]byte, []int) {
	return file_plugin_config_proto_rawDescGZIP(), []int{21}
}

func (x *ListToolsReq) GetToolIds() []string {
	if x != nil {
		return x.ToolIds
	}
	return nil
}

type ListToolsRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Tools []*ToolInfo `protobuf:"bytes,1,rep,name=Tools,proto3" json:"Tools,omitempty"` // 工具信息
}

func (x *ListToolsRsp) Reset() {
	*x = ListToolsRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_plugin_config_proto_msgTypes[22]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListToolsRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListToolsRsp) ProtoMessage() {}

func (x *ListToolsRsp) ProtoReflect() protoreflect.Message {
	mi := &file_plugin_config_proto_msgTypes[22]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListToolsRsp.ProtoReflect.Descriptor instead.
func (*ListToolsRsp) Descriptor() ([]byte, []int) {
	return file_plugin_config_proto_rawDescGZIP(), []int{22}
}

func (x *ListToolsRsp) GetTools() []*ToolInfo {
	if x != nil {
		return x.Tools
	}
	return nil
}

type DescribeToolReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ToolId   string `protobuf:"bytes,1,opt,name=ToolId,proto3" json:"ToolId,omitempty"`     // 工具id
	PluginId string `protobuf:"bytes,2,opt,name=PluginId,proto3" json:"PluginId,omitempty"` // 插件id
}

func (x *DescribeToolReq) Reset() {
	*x = DescribeToolReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_plugin_config_proto_msgTypes[23]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DescribeToolReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DescribeToolReq) ProtoMessage() {}

func (x *DescribeToolReq) ProtoReflect() protoreflect.Message {
	mi := &file_plugin_config_proto_msgTypes[23]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DescribeToolReq.ProtoReflect.Descriptor instead.
func (*DescribeToolReq) Descriptor() ([]byte, []int) {
	return file_plugin_config_proto_rawDescGZIP(), []int{23}
}

func (x *DescribeToolReq) GetToolId() string {
	if x != nil {
		return x.ToolId
	}
	return ""
}

func (x *DescribeToolReq) GetPluginId() string {
	if x != nil {
		return x.PluginId
	}
	return ""
}

type DescribeToolRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ToolId        string           `protobuf:"bytes,1,opt,name=ToolId,proto3" json:"ToolId,omitempty"`                                                             // 工具id
	PluginId      string           `protobuf:"bytes,2,opt,name=PluginId,proto3" json:"PluginId,omitempty"`                                                         // 插件id
	Name          string           `protobuf:"bytes,3,opt,name=Name,proto3" json:"Name,omitempty"`                                                                 // 工具名称
	Desc          string           `protobuf:"bytes,4,opt,name=Desc,proto3" json:"Desc,omitempty"`                                                                 // 工具描述信息
	Url           string           `protobuf:"bytes,5,opt,name=Url,proto3" json:"Url,omitempty"`                                                                   // 请求的url
	Path          string           `protobuf:"bytes,6,opt,name=Path,proto3" json:"Path,omitempty"`                                                                 // 请求的path
	Method        string           `protobuf:"bytes,7,opt,name=Method,proto3" json:"Method,omitempty"`                                                             // 请求method
	Header        []*RequestParam  `protobuf:"bytes,8,rep,name=Header,proto3" json:"Header,omitempty"`                                                             // 头信息
	Query         []*RequestParam  `protobuf:"bytes,9,rep,name=Query,proto3" json:"Query,omitempty"`                                                               //  输入参数query
	Body          []*RequestParam  `protobuf:"bytes,10,rep,name=Body,proto3" json:"Body,omitempty"`                                                                //  输入参数body
	Outputs       []*ResponseParam `protobuf:"bytes,11,rep,name=Outputs,proto3" json:"Outputs,omitempty"`                                                          // 输出参数
	Inputs        []*RequestParam  `protobuf:"bytes,12,rep,name=Inputs,proto3" json:"Inputs,omitempty"`                                                            // 输入参数
	Example       *ToolExample     `protobuf:"bytes,13,opt,name=Example,proto3" json:"Example,omitempty"`                                                          // 示例
	AuthType      AuthTypeEnum     `protobuf:"varint,14,opt,name=AuthType,proto3,enum=trpc.KEP.plugin_config_server.AuthTypeEnum" json:"AuthType,omitempty"`       // 授权方式
	AuthInfo      *AuthInfo        `protobuf:"bytes,15,opt,name=AuthInfo,proto3" json:"AuthInfo,omitempty"`                                                        // 授权信息
	Code          string           `protobuf:"bytes,16,opt,name=Code,proto3" json:"Code,omitempty"`                                                                // 代码
	CreateType    CreateTypeEnum   `protobuf:"varint,17,opt,name=CreateType,proto3,enum=trpc.KEP.plugin_config_server.CreateTypeEnum" json:"CreateType,omitempty"` // 创建方式
	MCPServer     *MCPServerInfo   `protobuf:"bytes,18,opt,name=MCPServer,proto3" json:"MCPServer,omitempty"`                                                      // MCP插件的配置信息
	IsStreamReply bool             `protobuf:"varint,19,opt,name=IsStreamReply,proto3" json:"IsStreamReply,omitempty"`                                             // 是否是流式回复
}

func (x *DescribeToolRsp) Reset() {
	*x = DescribeToolRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_plugin_config_proto_msgTypes[24]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DescribeToolRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DescribeToolRsp) ProtoMessage() {}

func (x *DescribeToolRsp) ProtoReflect() protoreflect.Message {
	mi := &file_plugin_config_proto_msgTypes[24]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DescribeToolRsp.ProtoReflect.Descriptor instead.
func (*DescribeToolRsp) Descriptor() ([]byte, []int) {
	return file_plugin_config_proto_rawDescGZIP(), []int{24}
}

func (x *DescribeToolRsp) GetToolId() string {
	if x != nil {
		return x.ToolId
	}
	return ""
}

func (x *DescribeToolRsp) GetPluginId() string {
	if x != nil {
		return x.PluginId
	}
	return ""
}

func (x *DescribeToolRsp) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *DescribeToolRsp) GetDesc() string {
	if x != nil {
		return x.Desc
	}
	return ""
}

func (x *DescribeToolRsp) GetUrl() string {
	if x != nil {
		return x.Url
	}
	return ""
}

func (x *DescribeToolRsp) GetPath() string {
	if x != nil {
		return x.Path
	}
	return ""
}

func (x *DescribeToolRsp) GetMethod() string {
	if x != nil {
		return x.Method
	}
	return ""
}

func (x *DescribeToolRsp) GetHeader() []*RequestParam {
	if x != nil {
		return x.Header
	}
	return nil
}

func (x *DescribeToolRsp) GetQuery() []*RequestParam {
	if x != nil {
		return x.Query
	}
	return nil
}

func (x *DescribeToolRsp) GetBody() []*RequestParam {
	if x != nil {
		return x.Body
	}
	return nil
}

func (x *DescribeToolRsp) GetOutputs() []*ResponseParam {
	if x != nil {
		return x.Outputs
	}
	return nil
}

func (x *DescribeToolRsp) GetInputs() []*RequestParam {
	if x != nil {
		return x.Inputs
	}
	return nil
}

func (x *DescribeToolRsp) GetExample() *ToolExample {
	if x != nil {
		return x.Example
	}
	return nil
}

func (x *DescribeToolRsp) GetAuthType() AuthTypeEnum {
	if x != nil {
		return x.AuthType
	}
	return AuthTypeEnum_NONE
}

func (x *DescribeToolRsp) GetAuthInfo() *AuthInfo {
	if x != nil {
		return x.AuthInfo
	}
	return nil
}

func (x *DescribeToolRsp) GetCode() string {
	if x != nil {
		return x.Code
	}
	return ""
}

func (x *DescribeToolRsp) GetCreateType() CreateTypeEnum {
	if x != nil {
		return x.CreateType
	}
	return CreateTypeEnum_SERVICE
}

func (x *DescribeToolRsp) GetMCPServer() *MCPServerInfo {
	if x != nil {
		return x.MCPServer
	}
	return nil
}

func (x *DescribeToolRsp) GetIsStreamReply() bool {
	if x != nil {
		return x.IsStreamReply
	}
	return false
}

type ToolExample struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Request  string `protobuf:"bytes,1,opt,name=Request,proto3" json:"Request,omitempty"`   // 请求示例，json字符串
	Response string `protobuf:"bytes,2,opt,name=Response,proto3" json:"Response,omitempty"` // 回复示例，json字符串
}

func (x *ToolExample) Reset() {
	*x = ToolExample{}
	if protoimpl.UnsafeEnabled {
		mi := &file_plugin_config_proto_msgTypes[25]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ToolExample) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ToolExample) ProtoMessage() {}

func (x *ToolExample) ProtoReflect() protoreflect.Message {
	mi := &file_plugin_config_proto_msgTypes[25]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ToolExample.ProtoReflect.Descriptor instead.
func (*ToolExample) Descriptor() ([]byte, []int) {
	return file_plugin_config_proto_rawDescGZIP(), []int{25}
}

func (x *ToolExample) GetRequest() string {
	if x != nil {
		return x.Request
	}
	return ""
}

func (x *ToolExample) GetResponse() string {
	if x != nil {
		return x.Response
	}
	return ""
}

// 定义工具的请求信息
type RequestParam struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name         string          `protobuf:"bytes,1,opt,name=Name,proto3" json:"Name,omitempty"`                                              // 参数名称
	Desc         string          `protobuf:"bytes,2,opt,name=Desc,proto3" json:"Desc,omitempty"`                                              // 参数描述
	Type         TypeEnum        `protobuf:"varint,3,opt,name=Type,proto3,enum=trpc.KEP.plugin_config_server.TypeEnum" json:"Type,omitempty"` // 参数类型
	IsRequired   bool            `protobuf:"varint,4,opt,name=IsRequired,proto3" json:"IsRequired,omitempty"`                                 // 是否必选
	DefaultValue string          `protobuf:"bytes,5,opt,name=DefaultValue,proto3" json:"DefaultValue,omitempty"`                              // 默认值
	SubParams    []*RequestParam `protobuf:"bytes,6,rep,name=SubParams,proto3" json:"SubParams,omitempty"`                                    // 子参数,ParamType 是OBJECT 或 ARRAY<>类型有用
	GlobalHidden bool            `protobuf:"varint,7,opt,name=GlobalHidden,proto3" json:"GlobalHidden,omitempty"`                             //插件参数配置是否隐藏不可见，true-隐藏不可见，false-可见
	OneOf        []*RequestParam `protobuf:"bytes,8,rep,name=OneOf,proto3" json:"OneOf,omitempty"`                                            // OneOf类型的参数
	AnyOf        []*RequestParam `protobuf:"bytes,9,rep,name=AnyOf,proto3" json:"AnyOf,omitempty"`                                            // AnyOf类型的参数
}

func (x *RequestParam) Reset() {
	*x = RequestParam{}
	if protoimpl.UnsafeEnabled {
		mi := &file_plugin_config_proto_msgTypes[26]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RequestParam) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RequestParam) ProtoMessage() {}

func (x *RequestParam) ProtoReflect() protoreflect.Message {
	mi := &file_plugin_config_proto_msgTypes[26]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RequestParam.ProtoReflect.Descriptor instead.
func (*RequestParam) Descriptor() ([]byte, []int) {
	return file_plugin_config_proto_rawDescGZIP(), []int{26}
}

func (x *RequestParam) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *RequestParam) GetDesc() string {
	if x != nil {
		return x.Desc
	}
	return ""
}

func (x *RequestParam) GetType() TypeEnum {
	if x != nil {
		return x.Type
	}
	return TypeEnum_STRING
}

func (x *RequestParam) GetIsRequired() bool {
	if x != nil {
		return x.IsRequired
	}
	return false
}

func (x *RequestParam) GetDefaultValue() string {
	if x != nil {
		return x.DefaultValue
	}
	return ""
}

func (x *RequestParam) GetSubParams() []*RequestParam {
	if x != nil {
		return x.SubParams
	}
	return nil
}

func (x *RequestParam) GetGlobalHidden() bool {
	if x != nil {
		return x.GlobalHidden
	}
	return false
}

func (x *RequestParam) GetOneOf() []*RequestParam {
	if x != nil {
		return x.OneOf
	}
	return nil
}

func (x *RequestParam) GetAnyOf() []*RequestParam {
	if x != nil {
		return x.AnyOf
	}
	return nil
}

// 定义工具的回复信息
type ResponseParam struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name             string           `protobuf:"bytes,1,opt,name=Name,proto3" json:"Name,omitempty"`                                              // 参数名称
	Desc             string           `protobuf:"bytes,2,opt,name=Desc,proto3" json:"Desc,omitempty"`                                              // 变量描述
	Type             TypeEnum         `protobuf:"varint,3,opt,name=Type,proto3,enum=trpc.KEP.plugin_config_server.TypeEnum" json:"Type,omitempty"` // 参数类型
	SubParams        []*ResponseParam `protobuf:"bytes,4,rep,name=SubParams,proto3" json:"SubParams,omitempty"`                                    // 只对 OBJECT 或 ARRAY_OBJECT 类型有用
	IsIncrementReply bool             `protobuf:"varint,5,opt,name=IsIncrementReply,proto3" json:"IsIncrementReply,omitempty"`                     // 工具如果是流式回复，该字段是否是增量回复
}

func (x *ResponseParam) Reset() {
	*x = ResponseParam{}
	if protoimpl.UnsafeEnabled {
		mi := &file_plugin_config_proto_msgTypes[27]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ResponseParam) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ResponseParam) ProtoMessage() {}

func (x *ResponseParam) ProtoReflect() protoreflect.Message {
	mi := &file_plugin_config_proto_msgTypes[27]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ResponseParam.ProtoReflect.Descriptor instead.
func (*ResponseParam) Descriptor() ([]byte, []int) {
	return file_plugin_config_proto_rawDescGZIP(), []int{27}
}

func (x *ResponseParam) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *ResponseParam) GetDesc() string {
	if x != nil {
		return x.Desc
	}
	return ""
}

func (x *ResponseParam) GetType() TypeEnum {
	if x != nil {
		return x.Type
	}
	return TypeEnum_STRING
}

func (x *ResponseParam) GetSubParams() []*ResponseParam {
	if x != nil {
		return x.SubParams
	}
	return nil
}

func (x *ResponseParam) GetIsIncrementReply() bool {
	if x != nil {
		return x.IsIncrementReply
	}
	return false
}

type CheckToolReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Url           string           `protobuf:"bytes,1,opt,name=Url,proto3" json:"Url,omitempty"`                                                                   // 请求的url
	Path          string           `protobuf:"bytes,2,opt,name=Path,proto3" json:"Path,omitempty"`                                                                 // 请求的path
	Method        string           `protobuf:"bytes,3,opt,name=Method,proto3" json:"Method,omitempty"`                                                             // 请求method
	HeaderValue   string           `protobuf:"bytes,4,opt,name=HeaderValue,proto3" json:"HeaderValue,omitempty"`                                                   // 头信息
	QueryValue    string           `protobuf:"bytes,5,opt,name=QueryValue,proto3" json:"QueryValue,omitempty"`                                                     // 输入参数query
	BodyValue     string           `protobuf:"bytes,6,opt,name=BodyValue,proto3" json:"BodyValue,omitempty"`                                                       // 输入参数body
	Header        []*RequestParam  `protobuf:"bytes,7,rep,name=Header,proto3" json:"Header,omitempty"`                                                             // 头信息
	Query         []*RequestParam  `protobuf:"bytes,8,rep,name=Query,proto3" json:"Query,omitempty"`                                                               // 输入参数query
	Body          []*RequestParam  `protobuf:"bytes,9,rep,name=Body,proto3" json:"Body,omitempty"`                                                                 // 输入参数body
	Outputs       []*ResponseParam `protobuf:"bytes,10,rep,name=Outputs,proto3" json:"Outputs,omitempty"`                                                          // 输出参数
	AuthType      AuthTypeEnum     `protobuf:"varint,11,opt,name=AuthType,proto3,enum=trpc.KEP.plugin_config_server.AuthTypeEnum" json:"AuthType,omitempty"`       // 授权方式
	AuthInfo      *AuthInfo        `protobuf:"bytes,12,opt,name=AuthInfo,proto3" json:"AuthInfo,omitempty"`                                                        // 授权信息
	Code          string           `protobuf:"bytes,13,opt,name=Code,proto3" json:"Code,omitempty"`                                                                // 代码
	CreateType    CreateTypeEnum   `protobuf:"varint,14,opt,name=CreateType,proto3,enum=trpc.KEP.plugin_config_server.CreateTypeEnum" json:"CreateType,omitempty"` // 创建方式 0-服务 1-代码 2-MCP
	IsStreamReply bool             `protobuf:"varint,15,opt,name=IsStreamReply,proto3" json:"IsStreamReply,omitempty"`                                             // 是否是流式回复
}

func (x *CheckToolReq) Reset() {
	*x = CheckToolReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_plugin_config_proto_msgTypes[28]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CheckToolReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CheckToolReq) ProtoMessage() {}

func (x *CheckToolReq) ProtoReflect() protoreflect.Message {
	mi := &file_plugin_config_proto_msgTypes[28]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CheckToolReq.ProtoReflect.Descriptor instead.
func (*CheckToolReq) Descriptor() ([]byte, []int) {
	return file_plugin_config_proto_rawDescGZIP(), []int{28}
}

func (x *CheckToolReq) GetUrl() string {
	if x != nil {
		return x.Url
	}
	return ""
}

func (x *CheckToolReq) GetPath() string {
	if x != nil {
		return x.Path
	}
	return ""
}

func (x *CheckToolReq) GetMethod() string {
	if x != nil {
		return x.Method
	}
	return ""
}

func (x *CheckToolReq) GetHeaderValue() string {
	if x != nil {
		return x.HeaderValue
	}
	return ""
}

func (x *CheckToolReq) GetQueryValue() string {
	if x != nil {
		return x.QueryValue
	}
	return ""
}

func (x *CheckToolReq) GetBodyValue() string {
	if x != nil {
		return x.BodyValue
	}
	return ""
}

func (x *CheckToolReq) GetHeader() []*RequestParam {
	if x != nil {
		return x.Header
	}
	return nil
}

func (x *CheckToolReq) GetQuery() []*RequestParam {
	if x != nil {
		return x.Query
	}
	return nil
}

func (x *CheckToolReq) GetBody() []*RequestParam {
	if x != nil {
		return x.Body
	}
	return nil
}

func (x *CheckToolReq) GetOutputs() []*ResponseParam {
	if x != nil {
		return x.Outputs
	}
	return nil
}

func (x *CheckToolReq) GetAuthType() AuthTypeEnum {
	if x != nil {
		return x.AuthType
	}
	return AuthTypeEnum_NONE
}

func (x *CheckToolReq) GetAuthInfo() *AuthInfo {
	if x != nil {
		return x.AuthInfo
	}
	return nil
}

func (x *CheckToolReq) GetCode() string {
	if x != nil {
		return x.Code
	}
	return ""
}

func (x *CheckToolReq) GetCreateType() CreateTypeEnum {
	if x != nil {
		return x.CreateType
	}
	return CreateTypeEnum_SERVICE
}

func (x *CheckToolReq) GetIsStreamReply() bool {
	if x != nil {
		return x.IsStreamReply
	}
	return false
}

type CheckToolRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Result    string `protobuf:"bytes,1,opt,name=Result,proto3" json:"Result,omitempty"`       // 执行结果，解析后的json字符串
	RawResult string `protobuf:"bytes,2,opt,name=RawResult,proto3" json:"RawResult,omitempty"` // 执行结果，解析前的原始字符串
}

func (x *CheckToolRsp) Reset() {
	*x = CheckToolRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_plugin_config_proto_msgTypes[29]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CheckToolRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CheckToolRsp) ProtoMessage() {}

func (x *CheckToolRsp) ProtoReflect() protoreflect.Message {
	mi := &file_plugin_config_proto_msgTypes[29]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CheckToolRsp.ProtoReflect.Descriptor instead.
func (*CheckToolRsp) Descriptor() ([]byte, []int) {
	return file_plugin_config_proto_rawDescGZIP(), []int{29}
}

func (x *CheckToolRsp) GetResult() string {
	if x != nil {
		return x.Result
	}
	return ""
}

func (x *CheckToolRsp) GetRawResult() string {
	if x != nil {
		return x.RawResult
	}
	return ""
}

// 用户信息
type UserInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name      string `protobuf:"bytes,1,opt,name=Name,proto3" json:"Name,omitempty"`           // 名称
	AvatarUrl string `protobuf:"bytes,2,opt,name=AvatarUrl,proto3" json:"AvatarUrl,omitempty"` // 用户头像
}

func (x *UserInfo) Reset() {
	*x = UserInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_plugin_config_proto_msgTypes[30]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UserInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserInfo) ProtoMessage() {}

func (x *UserInfo) ProtoReflect() protoreflect.Message {
	mi := &file_plugin_config_proto_msgTypes[30]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserInfo.ProtoReflect.Descriptor instead.
func (*UserInfo) Descriptor() ([]byte, []int) {
	return file_plugin_config_proto_rawDescGZIP(), []int{30}
}

func (x *UserInfo) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *UserInfo) GetAvatarUrl() string {
	if x != nil {
		return x.AvatarUrl
	}
	return ""
}

type PluginInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PluginId      string            `protobuf:"bytes,1,opt,name=PluginId,proto3" json:"PluginId,omitempty"`                                                                  // 插件id
	Name          string            `protobuf:"bytes,2,opt,name=Name,proto3" json:"Name,omitempty"`                                                                          // 插件名称
	PluginVersion int32             `protobuf:"varint,3,opt,name=PluginVersion,proto3" json:"PluginVersion,omitempty"`                                                       // 插件版本，每次更新后会加1
	Desc          string            `protobuf:"bytes,4,opt,name=Desc,proto3" json:"Desc,omitempty"`                                                                          // 插件描述信息
	IconUrl       string            `protobuf:"bytes,5,opt,name=IconUrl,proto3" json:"IconUrl,omitempty"`                                                                    // 插件图标url
	AuthType      AuthTypeEnum      `protobuf:"varint,6,opt,name=AuthType,proto3,enum=trpc.KEP.plugin_config_server.AuthTypeEnum" json:"AuthType,omitempty"`                 // 授权方式，仅自定义插件返回
	AuthInfo      *AuthInfo         `protobuf:"bytes,7,opt,name=AuthInfo,proto3" json:"AuthInfo,omitempty"`                                                                  // 授权信息，仅自定义插件返回
	OpenApi       string            `protobuf:"bytes,8,opt,name=OpenApi,proto3" json:"OpenApi,omitempty"`                                                                    // OpenAPI描述，仅自定义插件返回
	UserInfo      *UserInfo         `protobuf:"bytes,9,opt,name=UserInfo,proto3" json:"UserInfo,omitempty"`                                                                  // 创建者信息
	PluginType    PluginTypeEnum    `protobuf:"varint,10,opt,name=PluginType,proto3,enum=trpc.KEP.plugin_config_server.PluginTypeEnum" json:"PluginType,omitempty"`          // 插件类型
	Tools         []*ToolInfo       `protobuf:"bytes,11,rep,name=Tools,proto3" json:"Tools,omitempty"`                                                                       // 工具信息
	CreateTime    string            `protobuf:"bytes,12,opt,name=CreateTime,proto3" json:"CreateTime,omitempty"`                                                             // 创建时间
	UpdateTime    string            `protobuf:"bytes,13,opt,name=UpdateTime,proto3" json:"UpdateTime,omitempty"`                                                             // 更新时间
	FinanceType   FinanceTypeEnum   `protobuf:"varint,14,opt,name=FinanceType,proto3,enum=trpc.KEP.plugin_config_server.FinanceTypeEnum" json:"FinanceType,omitempty"`       // 计费类型
	CreateType    CreateTypeEnum    `protobuf:"varint,15,opt,name=CreateType,proto3,enum=trpc.KEP.plugin_config_server.CreateTypeEnum" json:"CreateType,omitempty"`          // 创建方式
	Status        int32             `protobuf:"varint,16,opt,name=Status,proto3" json:"Status,omitempty"`                                                                    //插件状态 1-成功(可用)，2-不可用
	Headers       []*PluginHeader   `protobuf:"bytes,17,rep,name=Headers,proto3" json:"Headers,omitempty"`                                                                   // mcp插件的header参数
	WhiteListType WhiteListTypeEnum `protobuf:"varint,18,opt,name=WhiteListType,proto3,enum=trpc.KEP.plugin_config_server.WhiteListTypeEnum" json:"WhiteListType,omitempty"` // 白名单类型
}

func (x *PluginInfo) Reset() {
	*x = PluginInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_plugin_config_proto_msgTypes[31]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PluginInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PluginInfo) ProtoMessage() {}

func (x *PluginInfo) ProtoReflect() protoreflect.Message {
	mi := &file_plugin_config_proto_msgTypes[31]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PluginInfo.ProtoReflect.Descriptor instead.
func (*PluginInfo) Descriptor() ([]byte, []int) {
	return file_plugin_config_proto_rawDescGZIP(), []int{31}
}

func (x *PluginInfo) GetPluginId() string {
	if x != nil {
		return x.PluginId
	}
	return ""
}

func (x *PluginInfo) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *PluginInfo) GetPluginVersion() int32 {
	if x != nil {
		return x.PluginVersion
	}
	return 0
}

func (x *PluginInfo) GetDesc() string {
	if x != nil {
		return x.Desc
	}
	return ""
}

func (x *PluginInfo) GetIconUrl() string {
	if x != nil {
		return x.IconUrl
	}
	return ""
}

func (x *PluginInfo) GetAuthType() AuthTypeEnum {
	if x != nil {
		return x.AuthType
	}
	return AuthTypeEnum_NONE
}

func (x *PluginInfo) GetAuthInfo() *AuthInfo {
	if x != nil {
		return x.AuthInfo
	}
	return nil
}

func (x *PluginInfo) GetOpenApi() string {
	if x != nil {
		return x.OpenApi
	}
	return ""
}

func (x *PluginInfo) GetUserInfo() *UserInfo {
	if x != nil {
		return x.UserInfo
	}
	return nil
}

func (x *PluginInfo) GetPluginType() PluginTypeEnum {
	if x != nil {
		return x.PluginType
	}
	return PluginTypeEnum_CUSTOM
}

func (x *PluginInfo) GetTools() []*ToolInfo {
	if x != nil {
		return x.Tools
	}
	return nil
}

func (x *PluginInfo) GetCreateTime() string {
	if x != nil {
		return x.CreateTime
	}
	return ""
}

func (x *PluginInfo) GetUpdateTime() string {
	if x != nil {
		return x.UpdateTime
	}
	return ""
}

func (x *PluginInfo) GetFinanceType() FinanceTypeEnum {
	if x != nil {
		return x.FinanceType
	}
	return FinanceTypeEnum_FREE
}

func (x *PluginInfo) GetCreateType() CreateTypeEnum {
	if x != nil {
		return x.CreateType
	}
	return CreateTypeEnum_SERVICE
}

func (x *PluginInfo) GetStatus() int32 {
	if x != nil {
		return x.Status
	}
	return 0
}

func (x *PluginInfo) GetHeaders() []*PluginHeader {
	if x != nil {
		return x.Headers
	}
	return nil
}

func (x *PluginInfo) GetWhiteListType() WhiteListTypeEnum {
	if x != nil {
		return x.WhiteListType
	}
	return WhiteListTypeEnum_OPEN
}

type ToolInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ToolId        string           `protobuf:"bytes,1,opt,name=ToolId,proto3" json:"ToolId,omitempty"`                 // 工具id
	PluginId      string           `protobuf:"bytes,2,opt,name=PluginId,proto3" json:"PluginId,omitempty"`             // 插件id
	Name          string           `protobuf:"bytes,3,opt,name=Name,proto3" json:"Name,omitempty"`                     // 工具名称
	Desc          string           `protobuf:"bytes,4,opt,name=Desc,proto3" json:"Desc,omitempty"`                     // 工具描述信息
	Url           string           `protobuf:"bytes,5,opt,name=Url,proto3" json:"Url,omitempty"`                       // 请求的url
	Path          string           `protobuf:"bytes,6,opt,name=Path,proto3" json:"Path,omitempty"`                     // 请求的path
	Method        string           `protobuf:"bytes,7,opt,name=Method,proto3" json:"Method,omitempty"`                 // 请求method
	Header        []*RequestParam  `protobuf:"bytes,8,rep,name=Header,proto3" json:"Header,omitempty"`                 // 头信息
	Query         []*RequestParam  `protobuf:"bytes,9,rep,name=Query,proto3" json:"Query,omitempty"`                   //  输入参数query
	Body          []*RequestParam  `protobuf:"bytes,10,rep,name=Body,proto3" json:"Body,omitempty"`                    //  输入参数body
	Outputs       []*ResponseParam `protobuf:"bytes,11,rep,name=Outputs,proto3" json:"Outputs,omitempty"`              // 输出参数
	Inputs        []*RequestParam  `protobuf:"bytes,12,rep,name=Inputs,proto3" json:"Inputs,omitempty"`                // 输入参数
	Example       *ToolExample     `protobuf:"bytes,13,opt,name=Example,proto3" json:"Example,omitempty"`              // 示例
	Code          string           `protobuf:"bytes,14,opt,name=Code,proto3" json:"Code,omitempty"`                    // 代码
	IsStreamReply bool             `protobuf:"varint,15,opt,name=IsStreamReply,proto3" json:"IsStreamReply,omitempty"` // 是否是流式回复
}

func (x *ToolInfo) Reset() {
	*x = ToolInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_plugin_config_proto_msgTypes[32]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ToolInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ToolInfo) ProtoMessage() {}

func (x *ToolInfo) ProtoReflect() protoreflect.Message {
	mi := &file_plugin_config_proto_msgTypes[32]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ToolInfo.ProtoReflect.Descriptor instead.
func (*ToolInfo) Descriptor() ([]byte, []int) {
	return file_plugin_config_proto_rawDescGZIP(), []int{32}
}

func (x *ToolInfo) GetToolId() string {
	if x != nil {
		return x.ToolId
	}
	return ""
}

func (x *ToolInfo) GetPluginId() string {
	if x != nil {
		return x.PluginId
	}
	return ""
}

func (x *ToolInfo) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *ToolInfo) GetDesc() string {
	if x != nil {
		return x.Desc
	}
	return ""
}

func (x *ToolInfo) GetUrl() string {
	if x != nil {
		return x.Url
	}
	return ""
}

func (x *ToolInfo) GetPath() string {
	if x != nil {
		return x.Path
	}
	return ""
}

func (x *ToolInfo) GetMethod() string {
	if x != nil {
		return x.Method
	}
	return ""
}

func (x *ToolInfo) GetHeader() []*RequestParam {
	if x != nil {
		return x.Header
	}
	return nil
}

func (x *ToolInfo) GetQuery() []*RequestParam {
	if x != nil {
		return x.Query
	}
	return nil
}

func (x *ToolInfo) GetBody() []*RequestParam {
	if x != nil {
		return x.Body
	}
	return nil
}

func (x *ToolInfo) GetOutputs() []*ResponseParam {
	if x != nil {
		return x.Outputs
	}
	return nil
}

func (x *ToolInfo) GetInputs() []*RequestParam {
	if x != nil {
		return x.Inputs
	}
	return nil
}

func (x *ToolInfo) GetExample() *ToolExample {
	if x != nil {
		return x.Example
	}
	return nil
}

func (x *ToolInfo) GetCode() string {
	if x != nil {
		return x.Code
	}
	return ""
}

func (x *ToolInfo) GetIsStreamReply() bool {
	if x != nil {
		return x.IsStreamReply
	}
	return false
}

// 鉴权信息，后续可能要支持Oauth2.0
type AuthInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	KeyLocation   AuthInfo_KeyLocationTypeEnum `protobuf:"varint,1,opt,name=KeyLocation,proto3,enum=trpc.KEP.plugin_config_server.AuthInfo_KeyLocationTypeEnum" json:"KeyLocation,omitempty"` // 密钥位置 HEADER/QUERY
	KeyParamName  string                       `protobuf:"bytes,2,opt,name=KeyParamName,proto3" json:"KeyParamName,omitempty"`                                                                // 密钥参数名
	KeyParamValue string                       `protobuf:"bytes,3,opt,name=KeyParamValue,proto3" json:"KeyParamValue,omitempty"`                                                              // 密钥参数值
}

func (x *AuthInfo) Reset() {
	*x = AuthInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_plugin_config_proto_msgTypes[33]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AuthInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AuthInfo) ProtoMessage() {}

func (x *AuthInfo) ProtoReflect() protoreflect.Message {
	mi := &file_plugin_config_proto_msgTypes[33]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AuthInfo.ProtoReflect.Descriptor instead.
func (*AuthInfo) Descriptor() ([]byte, []int) {
	return file_plugin_config_proto_rawDescGZIP(), []int{33}
}

func (x *AuthInfo) GetKeyLocation() AuthInfo_KeyLocationTypeEnum {
	if x != nil {
		return x.KeyLocation
	}
	return AuthInfo_HEADER
}

func (x *AuthInfo) GetKeyParamName() string {
	if x != nil {
		return x.KeyParamName
	}
	return ""
}

func (x *AuthInfo) GetKeyParamValue() string {
	if x != nil {
		return x.KeyParamValue
	}
	return ""
}

type ListToolRefsReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ToolId   string `protobuf:"bytes,1,opt,name=ToolId,proto3" json:"ToolId,omitempty"`     // 工具id
	PluginId string `protobuf:"bytes,2,opt,name=PluginId,proto3" json:"PluginId,omitempty"` // 插件id
}

func (x *ListToolRefsReq) Reset() {
	*x = ListToolRefsReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_plugin_config_proto_msgTypes[34]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListToolRefsReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListToolRefsReq) ProtoMessage() {}

func (x *ListToolRefsReq) ProtoReflect() protoreflect.Message {
	mi := &file_plugin_config_proto_msgTypes[34]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListToolRefsReq.ProtoReflect.Descriptor instead.
func (*ListToolRefsReq) Descriptor() ([]byte, []int) {
	return file_plugin_config_proto_rawDescGZIP(), []int{34}
}

func (x *ListToolRefsReq) GetToolId() string {
	if x != nil {
		return x.ToolId
	}
	return ""
}

func (x *ListToolRefsReq) GetPluginId() string {
	if x != nil {
		return x.PluginId
	}
	return ""
}

type ListToolRefsRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ToolRefs []*ToolRef `protobuf:"bytes,1,rep,name=ToolRefs,proto3" json:"ToolRefs,omitempty"` // 工具引用信息
}

func (x *ListToolRefsRsp) Reset() {
	*x = ListToolRefsRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_plugin_config_proto_msgTypes[35]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListToolRefsRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListToolRefsRsp) ProtoMessage() {}

func (x *ListToolRefsRsp) ProtoReflect() protoreflect.Message {
	mi := &file_plugin_config_proto_msgTypes[35]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListToolRefsRsp.ProtoReflect.Descriptor instead.
func (*ListToolRefsRsp) Descriptor() ([]byte, []int) {
	return file_plugin_config_proto_rawDescGZIP(), []int{35}
}

func (x *ListToolRefsRsp) GetToolRefs() []*ToolRef {
	if x != nil {
		return x.ToolRefs
	}
	return nil
}

type ToolRef struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ToolId       string              `protobuf:"bytes,1,opt,name=ToolId,proto3" json:"ToolId,omitempty"`                                                           // 工具id
	PluginId     string              `protobuf:"bytes,2,opt,name=PluginId,proto3" json:"PluginId,omitempty"`                                                       // 插件id
	AppId        string              `protobuf:"bytes,3,opt,name=AppId,proto3" json:"AppId,omitempty"`                                                             // 应用id
	AppName      string              `protobuf:"bytes,4,opt,name=AppName,proto3" json:"AppName,omitempty"`                                                         // 应用名称
	WorkflowId   string              `protobuf:"bytes,5,opt,name=WorkflowId,proto3" json:"WorkflowId,omitempty"`                                                   // 工作流id
	WorkflowName string              `protobuf:"bytes,6,opt,name=WorkflowName,proto3" json:"WorkflowName,omitempty"`                                               // 工作流名称
	RefType      ToolRef_RefTypeEnum `protobuf:"varint,7,opt,name=RefType,proto3,enum=trpc.KEP.plugin_config_server.ToolRef_RefTypeEnum" json:"RefType,omitempty"` // 引用类型
}

func (x *ToolRef) Reset() {
	*x = ToolRef{}
	if protoimpl.UnsafeEnabled {
		mi := &file_plugin_config_proto_msgTypes[36]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ToolRef) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ToolRef) ProtoMessage() {}

func (x *ToolRef) ProtoReflect() protoreflect.Message {
	mi := &file_plugin_config_proto_msgTypes[36]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ToolRef.ProtoReflect.Descriptor instead.
func (*ToolRef) Descriptor() ([]byte, []int) {
	return file_plugin_config_proto_rawDescGZIP(), []int{36}
}

func (x *ToolRef) GetToolId() string {
	if x != nil {
		return x.ToolId
	}
	return ""
}

func (x *ToolRef) GetPluginId() string {
	if x != nil {
		return x.PluginId
	}
	return ""
}

func (x *ToolRef) GetAppId() string {
	if x != nil {
		return x.AppId
	}
	return ""
}

func (x *ToolRef) GetAppName() string {
	if x != nil {
		return x.AppName
	}
	return ""
}

func (x *ToolRef) GetWorkflowId() string {
	if x != nil {
		return x.WorkflowId
	}
	return ""
}

func (x *ToolRef) GetWorkflowName() string {
	if x != nil {
		return x.WorkflowName
	}
	return ""
}

func (x *ToolRef) GetRefType() ToolRef_RefTypeEnum {
	if x != nil {
		return x.RefType
	}
	return ToolRef_AGENT
}

type AddAppToolReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AppBizId string `protobuf:"bytes,1,opt,name=AppBizId,proto3" json:"AppBizId,omitempty"` // 应用ID
	PluginId string `protobuf:"bytes,2,opt,name=PluginId,proto3" json:"PluginId,omitempty"` // 插件ID
	ToolId   string `protobuf:"bytes,3,opt,name=ToolId,proto3" json:"ToolId,omitempty"`     // 工具ID
}

func (x *AddAppToolReq) Reset() {
	*x = AddAppToolReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_plugin_config_proto_msgTypes[37]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AddAppToolReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddAppToolReq) ProtoMessage() {}

func (x *AddAppToolReq) ProtoReflect() protoreflect.Message {
	mi := &file_plugin_config_proto_msgTypes[37]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddAppToolReq.ProtoReflect.Descriptor instead.
func (*AddAppToolReq) Descriptor() ([]byte, []int) {
	return file_plugin_config_proto_rawDescGZIP(), []int{37}
}

func (x *AddAppToolReq) GetAppBizId() string {
	if x != nil {
		return x.AppBizId
	}
	return ""
}

func (x *AddAppToolReq) GetPluginId() string {
	if x != nil {
		return x.PluginId
	}
	return ""
}

func (x *AddAppToolReq) GetToolId() string {
	if x != nil {
		return x.ToolId
	}
	return ""
}

type AddAppToolRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *AddAppToolRsp) Reset() {
	*x = AddAppToolRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_plugin_config_proto_msgTypes[38]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AddAppToolRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddAppToolRsp) ProtoMessage() {}

func (x *AddAppToolRsp) ProtoReflect() protoreflect.Message {
	mi := &file_plugin_config_proto_msgTypes[38]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddAppToolRsp.ProtoReflect.Descriptor instead.
func (*AddAppToolRsp) Descriptor() ([]byte, []int) {
	return file_plugin_config_proto_rawDescGZIP(), []int{38}
}

type DeleteAppToolReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AppBizId string   `protobuf:"bytes,1,opt,name=AppBizId,proto3" json:"AppBizId,omitempty"` // 应用ID
	ToolIds  []string `protobuf:"bytes,2,rep,name=ToolIds,proto3" json:"ToolIds,omitempty"`   // 工具ID
}

func (x *DeleteAppToolReq) Reset() {
	*x = DeleteAppToolReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_plugin_config_proto_msgTypes[39]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteAppToolReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteAppToolReq) ProtoMessage() {}

func (x *DeleteAppToolReq) ProtoReflect() protoreflect.Message {
	mi := &file_plugin_config_proto_msgTypes[39]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteAppToolReq.ProtoReflect.Descriptor instead.
func (*DeleteAppToolReq) Descriptor() ([]byte, []int) {
	return file_plugin_config_proto_rawDescGZIP(), []int{39}
}

func (x *DeleteAppToolReq) GetAppBizId() string {
	if x != nil {
		return x.AppBizId
	}
	return ""
}

func (x *DeleteAppToolReq) GetToolIds() []string {
	if x != nil {
		return x.ToolIds
	}
	return nil
}

type DeleteAppToolRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *DeleteAppToolRsp) Reset() {
	*x = DeleteAppToolRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_plugin_config_proto_msgTypes[40]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteAppToolRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteAppToolRsp) ProtoMessage() {}

func (x *DeleteAppToolRsp) ProtoReflect() protoreflect.Message {
	mi := &file_plugin_config_proto_msgTypes[40]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteAppToolRsp.ProtoReflect.Descriptor instead.
func (*DeleteAppToolRsp) Descriptor() ([]byte, []int) {
	return file_plugin_config_proto_rawDescGZIP(), []int{40}
}

// 应用工具的请求参数定义
type AppToolReqParam struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name         string             `protobuf:"bytes,1,opt,name=Name,proto3" json:"Name,omitempty"`                                              // 参数名称
	Desc         string             `protobuf:"bytes,2,opt,name=Desc,proto3" json:"Desc,omitempty"`                                              // 参数描述
	Type         TypeEnum           `protobuf:"varint,3,opt,name=Type,proto3,enum=trpc.KEP.plugin_config_server.TypeEnum" json:"Type,omitempty"` // 参数类型
	IsRequired   bool               `protobuf:"varint,4,opt,name=IsRequired,proto3" json:"IsRequired,omitempty"`                                 // 是否必选
	DefaultValue string             `protobuf:"bytes,5,opt,name=DefaultValue,proto3" json:"DefaultValue,omitempty"`                              // 默认值
	SubParams    []*AppToolReqParam `protobuf:"bytes,6,rep,name=SubParams,proto3" json:"SubParams,omitempty"`                                    // 子参数,ParamType 是OBJECT 或 ARRAY<>类型有用
	AgentHidden  bool               `protobuf:"varint,7,opt,name=AgentHidden,proto3" json:"AgentHidden,omitempty"`                               //agent模式下模型是否可见
}

func (x *AppToolReqParam) Reset() {
	*x = AppToolReqParam{}
	if protoimpl.UnsafeEnabled {
		mi := &file_plugin_config_proto_msgTypes[41]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AppToolReqParam) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AppToolReqParam) ProtoMessage() {}

func (x *AppToolReqParam) ProtoReflect() protoreflect.Message {
	mi := &file_plugin_config_proto_msgTypes[41]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AppToolReqParam.ProtoReflect.Descriptor instead.
func (*AppToolReqParam) Descriptor() ([]byte, []int) {
	return file_plugin_config_proto_rawDescGZIP(), []int{41}
}

func (x *AppToolReqParam) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *AppToolReqParam) GetDesc() string {
	if x != nil {
		return x.Desc
	}
	return ""
}

func (x *AppToolReqParam) GetType() TypeEnum {
	if x != nil {
		return x.Type
	}
	return TypeEnum_STRING
}

func (x *AppToolReqParam) GetIsRequired() bool {
	if x != nil {
		return x.IsRequired
	}
	return false
}

func (x *AppToolReqParam) GetDefaultValue() string {
	if x != nil {
		return x.DefaultValue
	}
	return ""
}

func (x *AppToolReqParam) GetSubParams() []*AppToolReqParam {
	if x != nil {
		return x.SubParams
	}
	return nil
}

func (x *AppToolReqParam) GetAgentHidden() bool {
	if x != nil {
		return x.AgentHidden
	}
	return false
}

// 应用工具的响应参数定义
type AppToolRspParam struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name        string             `protobuf:"bytes,1,opt,name=Name,proto3" json:"Name,omitempty"`                                              // 参数名称
	Desc        string             `protobuf:"bytes,2,opt,name=Desc,proto3" json:"Desc,omitempty"`                                              // 变量描述
	Type        TypeEnum           `protobuf:"varint,3,opt,name=Type,proto3,enum=trpc.KEP.plugin_config_server.TypeEnum" json:"Type,omitempty"` // 参数类型
	SubParams   []*AppToolRspParam `protobuf:"bytes,4,rep,name=SubParams,proto3" json:"SubParams,omitempty"`                                    // 只对 OBJECT 或 ARRAY_OBJECT 类型有用
	AgentHidden bool               `protobuf:"varint,5,opt,name=AgentHidden,proto3" json:"AgentHidden,omitempty"`                               //agent模式下模型是否可见
}

func (x *AppToolRspParam) Reset() {
	*x = AppToolRspParam{}
	if protoimpl.UnsafeEnabled {
		mi := &file_plugin_config_proto_msgTypes[42]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AppToolRspParam) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AppToolRspParam) ProtoMessage() {}

func (x *AppToolRspParam) ProtoReflect() protoreflect.Message {
	mi := &file_plugin_config_proto_msgTypes[42]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AppToolRspParam.ProtoReflect.Descriptor instead.
func (*AppToolRspParam) Descriptor() ([]byte, []int) {
	return file_plugin_config_proto_rawDescGZIP(), []int{42}
}

func (x *AppToolRspParam) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *AppToolRspParam) GetDesc() string {
	if x != nil {
		return x.Desc
	}
	return ""
}

func (x *AppToolRspParam) GetType() TypeEnum {
	if x != nil {
		return x.Type
	}
	return TypeEnum_STRING
}

func (x *AppToolRspParam) GetSubParams() []*AppToolRspParam {
	if x != nil {
		return x.SubParams
	}
	return nil
}

func (x *AppToolRspParam) GetAgentHidden() bool {
	if x != nil {
		return x.AgentHidden
	}
	return false
}

type SaveAppToolReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AppBizId string             `protobuf:"bytes,1,opt,name=AppBizId,proto3" json:"AppBizId,omitempty"` // 应用ID
	PluginId string             `protobuf:"bytes,2,opt,name=PluginId,proto3" json:"PluginId,omitempty"` // 插件ID
	ToolId   string             `protobuf:"bytes,3,opt,name=ToolId,proto3" json:"ToolId,omitempty"`     // 工具ID
	ToolDesc string             `protobuf:"bytes,4,opt,name=ToolDesc,proto3" json:"ToolDesc,omitempty"` //自定义插件描述
	Inputs   []*AppToolReqParam `protobuf:"bytes,5,rep,name=Inputs,proto3" json:"Inputs,omitempty"`     //输入参数自定义配置
	Outputs  []*AppToolRspParam `protobuf:"bytes,6,rep,name=Outputs,proto3" json:"Outputs,omitempty"`   //输出参数自定义配置
}

func (x *SaveAppToolReq) Reset() {
	*x = SaveAppToolReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_plugin_config_proto_msgTypes[43]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SaveAppToolReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SaveAppToolReq) ProtoMessage() {}

func (x *SaveAppToolReq) ProtoReflect() protoreflect.Message {
	mi := &file_plugin_config_proto_msgTypes[43]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SaveAppToolReq.ProtoReflect.Descriptor instead.
func (*SaveAppToolReq) Descriptor() ([]byte, []int) {
	return file_plugin_config_proto_rawDescGZIP(), []int{43}
}

func (x *SaveAppToolReq) GetAppBizId() string {
	if x != nil {
		return x.AppBizId
	}
	return ""
}

func (x *SaveAppToolReq) GetPluginId() string {
	if x != nil {
		return x.PluginId
	}
	return ""
}

func (x *SaveAppToolReq) GetToolId() string {
	if x != nil {
		return x.ToolId
	}
	return ""
}

func (x *SaveAppToolReq) GetToolDesc() string {
	if x != nil {
		return x.ToolDesc
	}
	return ""
}

func (x *SaveAppToolReq) GetInputs() []*AppToolReqParam {
	if x != nil {
		return x.Inputs
	}
	return nil
}

func (x *SaveAppToolReq) GetOutputs() []*AppToolRspParam {
	if x != nil {
		return x.Outputs
	}
	return nil
}

type SaveAppToolRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *SaveAppToolRsp) Reset() {
	*x = SaveAppToolRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_plugin_config_proto_msgTypes[44]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SaveAppToolRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SaveAppToolRsp) ProtoMessage() {}

func (x *SaveAppToolRsp) ProtoReflect() protoreflect.Message {
	mi := &file_plugin_config_proto_msgTypes[44]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SaveAppToolRsp.ProtoReflect.Descriptor instead.
func (*SaveAppToolRsp) Descriptor() ([]byte, []int) {
	return file_plugin_config_proto_rawDescGZIP(), []int{44}
}

type ListAppToolsReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AppBizId string `protobuf:"bytes,1,opt,name=AppBizId,proto3" json:"AppBizId,omitempty"` // 应用ID
}

func (x *ListAppToolsReq) Reset() {
	*x = ListAppToolsReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_plugin_config_proto_msgTypes[45]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListAppToolsReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListAppToolsReq) ProtoMessage() {}

func (x *ListAppToolsReq) ProtoReflect() protoreflect.Message {
	mi := &file_plugin_config_proto_msgTypes[45]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListAppToolsReq.ProtoReflect.Descriptor instead.
func (*ListAppToolsReq) Descriptor() ([]byte, []int) {
	return file_plugin_config_proto_rawDescGZIP(), []int{45}
}

func (x *ListAppToolsReq) GetAppBizId() string {
	if x != nil {
		return x.AppBizId
	}
	return ""
}

type ListAppToolsRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Tools []*AppToolItem `protobuf:"bytes,1,rep,name=Tools,proto3" json:"Tools,omitempty"` // 应用的工具信息
}

func (x *ListAppToolsRsp) Reset() {
	*x = ListAppToolsRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_plugin_config_proto_msgTypes[46]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListAppToolsRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListAppToolsRsp) ProtoMessage() {}

func (x *ListAppToolsRsp) ProtoReflect() protoreflect.Message {
	mi := &file_plugin_config_proto_msgTypes[46]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListAppToolsRsp.ProtoReflect.Descriptor instead.
func (*ListAppToolsRsp) Descriptor() ([]byte, []int) {
	return file_plugin_config_proto_rawDescGZIP(), []int{46}
}

func (x *ListAppToolsRsp) GetTools() []*AppToolItem {
	if x != nil {
		return x.Tools
	}
	return nil
}

// 应用的工具信息（前端展示用）
type AppToolItem struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PluginId           string             `protobuf:"bytes,1,opt,name=PluginId,proto3" json:"PluginId,omitempty"`                                                        // 插件ID
	PluginName         string             `protobuf:"bytes,2,opt,name=PluginName,proto3" json:"PluginName,omitempty"`                                                    // 插件名称
	IconUrl            string             `protobuf:"bytes,3,opt,name=IconUrl,proto3" json:"IconUrl,omitempty"`                                                          // 插件图标url
	ToolId             string             `protobuf:"bytes,4,opt,name=ToolId,proto3" json:"ToolId,omitempty"`                                                            // 工具id
	ToolName           string             `protobuf:"bytes,5,opt,name=ToolName,proto3" json:"ToolName,omitempty"`                                                        // 工具名称
	ToolDesc           string             `protobuf:"bytes,6,opt,name=ToolDesc,proto3" json:"ToolDesc,omitempty"`                                                        // 工具描述信息
	Inputs             []*AppToolReqParam `protobuf:"bytes,7,rep,name=Inputs,proto3" json:"Inputs,omitempty"`                                                            // 输入参数
	IsBindingKnowledge bool               `protobuf:"varint,8,opt,name=IsBindingKnowledge,proto3" json:"IsBindingKnowledge,omitempty"`                                   //该工具是否和知识库绑定
	CreateType         CreateTypeEnum     `protobuf:"varint,9,opt,name=CreateType,proto3,enum=trpc.KEP.plugin_config_server.CreateTypeEnum" json:"CreateType,omitempty"` // 创建方式 0-服务 1-代码 2-MCP
	Status             int32              `protobuf:"varint,10,opt,name=Status,proto3" json:"Status,omitempty"`                                                          //插件状态 1-成功(可用)，2-不可用
	Headers            []*AppPluginHeader `protobuf:"bytes,11,rep,name=Headers,proto3" json:"Headers,omitempty"`                                                         // 应用配置的插件header信息
}

func (x *AppToolItem) Reset() {
	*x = AppToolItem{}
	if protoimpl.UnsafeEnabled {
		mi := &file_plugin_config_proto_msgTypes[47]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AppToolItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AppToolItem) ProtoMessage() {}

func (x *AppToolItem) ProtoReflect() protoreflect.Message {
	mi := &file_plugin_config_proto_msgTypes[47]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AppToolItem.ProtoReflect.Descriptor instead.
func (*AppToolItem) Descriptor() ([]byte, []int) {
	return file_plugin_config_proto_rawDescGZIP(), []int{47}
}

func (x *AppToolItem) GetPluginId() string {
	if x != nil {
		return x.PluginId
	}
	return ""
}

func (x *AppToolItem) GetPluginName() string {
	if x != nil {
		return x.PluginName
	}
	return ""
}

func (x *AppToolItem) GetIconUrl() string {
	if x != nil {
		return x.IconUrl
	}
	return ""
}

func (x *AppToolItem) GetToolId() string {
	if x != nil {
		return x.ToolId
	}
	return ""
}

func (x *AppToolItem) GetToolName() string {
	if x != nil {
		return x.ToolName
	}
	return ""
}

func (x *AppToolItem) GetToolDesc() string {
	if x != nil {
		return x.ToolDesc
	}
	return ""
}

func (x *AppToolItem) GetInputs() []*AppToolReqParam {
	if x != nil {
		return x.Inputs
	}
	return nil
}

func (x *AppToolItem) GetIsBindingKnowledge() bool {
	if x != nil {
		return x.IsBindingKnowledge
	}
	return false
}

func (x *AppToolItem) GetCreateType() CreateTypeEnum {
	if x != nil {
		return x.CreateType
	}
	return CreateTypeEnum_SERVICE
}

func (x *AppToolItem) GetStatus() int32 {
	if x != nil {
		return x.Status
	}
	return 0
}

func (x *AppToolItem) GetHeaders() []*AppPluginHeader {
	if x != nil {
		return x.Headers
	}
	return nil
}

type DescribeAppToolReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AppBizId string `protobuf:"bytes,1,opt,name=AppBizId,proto3" json:"AppBizId,omitempty"` // 应用ID
	ToolId   string `protobuf:"bytes,2,opt,name=ToolId,proto3" json:"ToolId,omitempty"`     // 工具id
}

func (x *DescribeAppToolReq) Reset() {
	*x = DescribeAppToolReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_plugin_config_proto_msgTypes[48]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DescribeAppToolReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DescribeAppToolReq) ProtoMessage() {}

func (x *DescribeAppToolReq) ProtoReflect() protoreflect.Message {
	mi := &file_plugin_config_proto_msgTypes[48]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DescribeAppToolReq.ProtoReflect.Descriptor instead.
func (*DescribeAppToolReq) Descriptor() ([]byte, []int) {
	return file_plugin_config_proto_rawDescGZIP(), []int{48}
}

func (x *DescribeAppToolReq) GetAppBizId() string {
	if x != nil {
		return x.AppBizId
	}
	return ""
}

func (x *DescribeAppToolReq) GetToolId() string {
	if x != nil {
		return x.ToolId
	}
	return ""
}

type DescribeAppToolRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ToolId   string             `protobuf:"bytes,1,opt,name=ToolId,proto3" json:"ToolId,omitempty"`     // 工具id
	ToolName string             `protobuf:"bytes,2,opt,name=ToolName,proto3" json:"ToolName,omitempty"` // 工具名称
	ToolDesc string             `protobuf:"bytes,3,opt,name=ToolDesc,proto3" json:"ToolDesc,omitempty"` // 工具描述信息
	Inputs   []*AppToolReqParam `protobuf:"bytes,4,rep,name=Inputs,proto3" json:"Inputs,omitempty"`     // 输入参数
	Outputs  []*AppToolRspParam `protobuf:"bytes,5,rep,name=Outputs,proto3" json:"Outputs,omitempty"`   // 输出参数
}

func (x *DescribeAppToolRsp) Reset() {
	*x = DescribeAppToolRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_plugin_config_proto_msgTypes[49]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DescribeAppToolRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DescribeAppToolRsp) ProtoMessage() {}

func (x *DescribeAppToolRsp) ProtoReflect() protoreflect.Message {
	mi := &file_plugin_config_proto_msgTypes[49]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DescribeAppToolRsp.ProtoReflect.Descriptor instead.
func (*DescribeAppToolRsp) Descriptor() ([]byte, []int) {
	return file_plugin_config_proto_rawDescGZIP(), []int{49}
}

func (x *DescribeAppToolRsp) GetToolId() string {
	if x != nil {
		return x.ToolId
	}
	return ""
}

func (x *DescribeAppToolRsp) GetToolName() string {
	if x != nil {
		return x.ToolName
	}
	return ""
}

func (x *DescribeAppToolRsp) GetToolDesc() string {
	if x != nil {
		return x.ToolDesc
	}
	return ""
}

func (x *DescribeAppToolRsp) GetInputs() []*AppToolReqParam {
	if x != nil {
		return x.Inputs
	}
	return nil
}

func (x *DescribeAppToolRsp) GetOutputs() []*AppToolRspParam {
	if x != nil {
		return x.Outputs
	}
	return nil
}

type ListAppToolsInfoReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AppBizId string  `protobuf:"bytes,1,opt,name=AppBizId,proto3" json:"AppBizId,omitempty"`                                         // 应用ID
	EnvTag   EnvType `protobuf:"varint,2,opt,name=EnvTag,proto3,enum=trpc.KEP.plugin_config_server.EnvType" json:"EnvTag,omitempty"` // 环境标识
}

func (x *ListAppToolsInfoReq) Reset() {
	*x = ListAppToolsInfoReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_plugin_config_proto_msgTypes[50]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListAppToolsInfoReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListAppToolsInfoReq) ProtoMessage() {}

func (x *ListAppToolsInfoReq) ProtoReflect() protoreflect.Message {
	mi := &file_plugin_config_proto_msgTypes[50]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListAppToolsInfoReq.ProtoReflect.Descriptor instead.
func (*ListAppToolsInfoReq) Descriptor() ([]byte, []int) {
	return file_plugin_config_proto_rawDescGZIP(), []int{50}
}

func (x *ListAppToolsInfoReq) GetAppBizId() string {
	if x != nil {
		return x.AppBizId
	}
	return ""
}

func (x *ListAppToolsInfoReq) GetEnvTag() EnvType {
	if x != nil {
		return x.EnvTag
	}
	return EnvType_TEST
}

type ListAppToolsInfoRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Tools []*AppToolInfo `protobuf:"bytes,1,rep,name=Tools,proto3" json:"Tools,omitempty"` // 应用的工具信息
}

func (x *ListAppToolsInfoRsp) Reset() {
	*x = ListAppToolsInfoRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_plugin_config_proto_msgTypes[51]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListAppToolsInfoRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListAppToolsInfoRsp) ProtoMessage() {}

func (x *ListAppToolsInfoRsp) ProtoReflect() protoreflect.Message {
	mi := &file_plugin_config_proto_msgTypes[51]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListAppToolsInfoRsp.ProtoReflect.Descriptor instead.
func (*ListAppToolsInfoRsp) Descriptor() ([]byte, []int) {
	return file_plugin_config_proto_rawDescGZIP(), []int{51}
}

func (x *ListAppToolsInfoRsp) GetTools() []*AppToolInfo {
	if x != nil {
		return x.Tools
	}
	return nil
}

// 应用配置MCP插件header信息
type AppPluginHeader struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ParamName  string `protobuf:"bytes,1,opt,name=ParamName,proto3" json:"ParamName,omitempty"`   // 参数名称
	ParamValue string `protobuf:"bytes,2,opt,name=ParamValue,proto3" json:"ParamValue,omitempty"` // 参数值
}

func (x *AppPluginHeader) Reset() {
	*x = AppPluginHeader{}
	if protoimpl.UnsafeEnabled {
		mi := &file_plugin_config_proto_msgTypes[52]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AppPluginHeader) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AppPluginHeader) ProtoMessage() {}

func (x *AppPluginHeader) ProtoReflect() protoreflect.Message {
	mi := &file_plugin_config_proto_msgTypes[52]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AppPluginHeader.ProtoReflect.Descriptor instead.
func (*AppPluginHeader) Descriptor() ([]byte, []int) {
	return file_plugin_config_proto_rawDescGZIP(), []int{52}
}

func (x *AppPluginHeader) GetParamName() string {
	if x != nil {
		return x.ParamName
	}
	return ""
}

func (x *AppPluginHeader) GetParamValue() string {
	if x != nil {
		return x.ParamValue
	}
	return ""
}

type GetAppPluginRequiredHeaderReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AppBizId string `protobuf:"bytes,1,opt,name=AppBizId,proto3" json:"AppBizId,omitempty"` // 应用id
	PluginId string `protobuf:"bytes,2,opt,name=PluginId,proto3" json:"PluginId,omitempty"` // 插件id
}

func (x *GetAppPluginRequiredHeaderReq) Reset() {
	*x = GetAppPluginRequiredHeaderReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_plugin_config_proto_msgTypes[53]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetAppPluginRequiredHeaderReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAppPluginRequiredHeaderReq) ProtoMessage() {}

func (x *GetAppPluginRequiredHeaderReq) ProtoReflect() protoreflect.Message {
	mi := &file_plugin_config_proto_msgTypes[53]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAppPluginRequiredHeaderReq.ProtoReflect.Descriptor instead.
func (*GetAppPluginRequiredHeaderReq) Descriptor() ([]byte, []int) {
	return file_plugin_config_proto_rawDescGZIP(), []int{53}
}

func (x *GetAppPluginRequiredHeaderReq) GetAppBizId() string {
	if x != nil {
		return x.AppBizId
	}
	return ""
}

func (x *GetAppPluginRequiredHeaderReq) GetPluginId() string {
	if x != nil {
		return x.PluginId
	}
	return ""
}

type GetAppPluginRequiredHeaderRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Headers []*AppPluginHeader `protobuf:"bytes,1,rep,name=Headers,proto3" json:"Headers,omitempty"` // mcp插件必填的header参数，仅对内置官方插件且应用未配置过才返回
}

func (x *GetAppPluginRequiredHeaderRsp) Reset() {
	*x = GetAppPluginRequiredHeaderRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_plugin_config_proto_msgTypes[54]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetAppPluginRequiredHeaderRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAppPluginRequiredHeaderRsp) ProtoMessage() {}

func (x *GetAppPluginRequiredHeaderRsp) ProtoReflect() protoreflect.Message {
	mi := &file_plugin_config_proto_msgTypes[54]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAppPluginRequiredHeaderRsp.ProtoReflect.Descriptor instead.
func (*GetAppPluginRequiredHeaderRsp) Descriptor() ([]byte, []int) {
	return file_plugin_config_proto_rawDescGZIP(), []int{54}
}

func (x *GetAppPluginRequiredHeaderRsp) GetHeaders() []*AppPluginHeader {
	if x != nil {
		return x.Headers
	}
	return nil
}

type DescribeAppPluginReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AppBizId string `protobuf:"bytes,1,opt,name=AppBizId,proto3" json:"AppBizId,omitempty"` // 应用id
	PluginId string `protobuf:"bytes,2,opt,name=PluginId,proto3" json:"PluginId,omitempty"` // 插件id
}

func (x *DescribeAppPluginReq) Reset() {
	*x = DescribeAppPluginReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_plugin_config_proto_msgTypes[55]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DescribeAppPluginReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DescribeAppPluginReq) ProtoMessage() {}

func (x *DescribeAppPluginReq) ProtoReflect() protoreflect.Message {
	mi := &file_plugin_config_proto_msgTypes[55]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DescribeAppPluginReq.ProtoReflect.Descriptor instead.
func (*DescribeAppPluginReq) Descriptor() ([]byte, []int) {
	return file_plugin_config_proto_rawDescGZIP(), []int{55}
}

func (x *DescribeAppPluginReq) GetAppBizId() string {
	if x != nil {
		return x.AppBizId
	}
	return ""
}

func (x *DescribeAppPluginReq) GetPluginId() string {
	if x != nil {
		return x.PluginId
	}
	return ""
}

type DescribeAppPluginRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Headers []*AppPluginHeader `protobuf:"bytes,1,rep,name=Headers,proto3" json:"Headers,omitempty"` // 应用配置的插件header信息
}

func (x *DescribeAppPluginRsp) Reset() {
	*x = DescribeAppPluginRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_plugin_config_proto_msgTypes[56]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DescribeAppPluginRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DescribeAppPluginRsp) ProtoMessage() {}

func (x *DescribeAppPluginRsp) ProtoReflect() protoreflect.Message {
	mi := &file_plugin_config_proto_msgTypes[56]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DescribeAppPluginRsp.ProtoReflect.Descriptor instead.
func (*DescribeAppPluginRsp) Descriptor() ([]byte, []int) {
	return file_plugin_config_proto_rawDescGZIP(), []int{56}
}

func (x *DescribeAppPluginRsp) GetHeaders() []*AppPluginHeader {
	if x != nil {
		return x.Headers
	}
	return nil
}

type SaveAppPluginReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AppBizId string             `protobuf:"bytes,1,opt,name=AppBizId,proto3" json:"AppBizId,omitempty"` // 应用id
	PluginId string             `protobuf:"bytes,2,opt,name=PluginId,proto3" json:"PluginId,omitempty"` // 插件id
	ToolId   string             `protobuf:"bytes,3,opt,name=ToolId,proto3" json:"ToolId,omitempty"`     // 工具id 配置的是插件维度，这里把对应的工具id也带上来
	Headers  []*AppPluginHeader `protobuf:"bytes,4,rep,name=Headers,proto3" json:"Headers,omitempty"`   // 应用配置的插件header信息
}

func (x *SaveAppPluginReq) Reset() {
	*x = SaveAppPluginReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_plugin_config_proto_msgTypes[57]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SaveAppPluginReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SaveAppPluginReq) ProtoMessage() {}

func (x *SaveAppPluginReq) ProtoReflect() protoreflect.Message {
	mi := &file_plugin_config_proto_msgTypes[57]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SaveAppPluginReq.ProtoReflect.Descriptor instead.
func (*SaveAppPluginReq) Descriptor() ([]byte, []int) {
	return file_plugin_config_proto_rawDescGZIP(), []int{57}
}

func (x *SaveAppPluginReq) GetAppBizId() string {
	if x != nil {
		return x.AppBizId
	}
	return ""
}

func (x *SaveAppPluginReq) GetPluginId() string {
	if x != nil {
		return x.PluginId
	}
	return ""
}

func (x *SaveAppPluginReq) GetToolId() string {
	if x != nil {
		return x.ToolId
	}
	return ""
}

func (x *SaveAppPluginReq) GetHeaders() []*AppPluginHeader {
	if x != nil {
		return x.Headers
	}
	return nil
}

type SaveAppPluginRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *SaveAppPluginRsp) Reset() {
	*x = SaveAppPluginRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_plugin_config_proto_msgTypes[58]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SaveAppPluginRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SaveAppPluginRsp) ProtoMessage() {}

func (x *SaveAppPluginRsp) ProtoReflect() protoreflect.Message {
	mi := &file_plugin_config_proto_msgTypes[58]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SaveAppPluginRsp.ProtoReflect.Descriptor instead.
func (*SaveAppPluginRsp) Descriptor() ([]byte, []int) {
	return file_plugin_config_proto_rawDescGZIP(), []int{58}
}

// 应用的工具信息（后端调用）
type AppToolInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PluginId   string             `protobuf:"bytes,1,opt,name=PluginId,proto3" json:"PluginId,omitempty"`                                                         // 插件ID
	PluginName string             `protobuf:"bytes,2,opt,name=PluginName,proto3" json:"PluginName,omitempty"`                                                     // 插件名称
	IconUrl    string             `protobuf:"bytes,3,opt,name=IconUrl,proto3" json:"IconUrl,omitempty"`                                                           // 插件图标url
	PluginType PluginTypeEnum     `protobuf:"varint,4,opt,name=PluginType,proto3,enum=trpc.KEP.plugin_config_server.PluginTypeEnum" json:"PluginType,omitempty"`  // 插件类型
	ToolId     string             `protobuf:"bytes,5,opt,name=ToolId,proto3" json:"ToolId,omitempty"`                                                             // 工具id
	ToolName   string             `protobuf:"bytes,6,opt,name=ToolName,proto3" json:"ToolName,omitempty"`                                                         // 工具名称
	ToolDesc   string             `protobuf:"bytes,7,opt,name=ToolDesc,proto3" json:"ToolDesc,omitempty"`                                                         // 工具描述信息
	Inputs     []*AppToolReqParam `protobuf:"bytes,8,rep,name=Inputs,proto3" json:"Inputs,omitempty"`                                                             // 输入参数
	Outputs    []*AppToolRspParam `protobuf:"bytes,9,rep,name=Outputs,proto3" json:"Outputs,omitempty"`                                                           // 输出参数
	CreateType CreateTypeEnum     `protobuf:"varint,10,opt,name=CreateType,proto3,enum=trpc.KEP.plugin_config_server.CreateTypeEnum" json:"CreateType,omitempty"` // 创建方式 0-服务 1-代码 2-MCP
	MCPServer  *MCPServerInfo     `protobuf:"bytes,11,opt,name=MCPServer,proto3" json:"MCPServer,omitempty"`                                                      // MCP插件的配置信息
}

func (x *AppToolInfo) Reset() {
	*x = AppToolInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_plugin_config_proto_msgTypes[59]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AppToolInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AppToolInfo) ProtoMessage() {}

func (x *AppToolInfo) ProtoReflect() protoreflect.Message {
	mi := &file_plugin_config_proto_msgTypes[59]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AppToolInfo.ProtoReflect.Descriptor instead.
func (*AppToolInfo) Descriptor() ([]byte, []int) {
	return file_plugin_config_proto_rawDescGZIP(), []int{59}
}

func (x *AppToolInfo) GetPluginId() string {
	if x != nil {
		return x.PluginId
	}
	return ""
}

func (x *AppToolInfo) GetPluginName() string {
	if x != nil {
		return x.PluginName
	}
	return ""
}

func (x *AppToolInfo) GetIconUrl() string {
	if x != nil {
		return x.IconUrl
	}
	return ""
}

func (x *AppToolInfo) GetPluginType() PluginTypeEnum {
	if x != nil {
		return x.PluginType
	}
	return PluginTypeEnum_CUSTOM
}

func (x *AppToolInfo) GetToolId() string {
	if x != nil {
		return x.ToolId
	}
	return ""
}

func (x *AppToolInfo) GetToolName() string {
	if x != nil {
		return x.ToolName
	}
	return ""
}

func (x *AppToolInfo) GetToolDesc() string {
	if x != nil {
		return x.ToolDesc
	}
	return ""
}

func (x *AppToolInfo) GetInputs() []*AppToolReqParam {
	if x != nil {
		return x.Inputs
	}
	return nil
}

func (x *AppToolInfo) GetOutputs() []*AppToolRspParam {
	if x != nil {
		return x.Outputs
	}
	return nil
}

func (x *AppToolInfo) GetCreateType() CreateTypeEnum {
	if x != nil {
		return x.CreateType
	}
	return CreateTypeEnum_SERVICE
}

func (x *AppToolInfo) GetMCPServer() *MCPServerInfo {
	if x != nil {
		return x.MCPServer
	}
	return nil
}

// MCP插件的配置信息
type MCPServerInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	McpServerUrl   string            `protobuf:"bytes,1,opt,name=McpServerUrl,proto3" json:"McpServerUrl,omitempty"`                                                                               // mcp server地址
	Headers        map[string]string `protobuf:"bytes,2,rep,name=Headers,proto3" json:"Headers,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"` // mcp server header信息
	Timeout        int32             `protobuf:"varint,3,opt,name=Timeout,proto3" json:"Timeout,omitempty"`                                                                                        // 超时时间，单位秒
	SseReadTimeout int32             `protobuf:"varint,4,opt,name=SseReadTimeout,proto3" json:"SseReadTimeout,omitempty"`                                                                          // sse服务超时时间，单位秒
}

func (x *MCPServerInfo) Reset() {
	*x = MCPServerInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_plugin_config_proto_msgTypes[60]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MCPServerInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MCPServerInfo) ProtoMessage() {}

func (x *MCPServerInfo) ProtoReflect() protoreflect.Message {
	mi := &file_plugin_config_proto_msgTypes[60]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MCPServerInfo.ProtoReflect.Descriptor instead.
func (*MCPServerInfo) Descriptor() ([]byte, []int) {
	return file_plugin_config_proto_rawDescGZIP(), []int{60}
}

func (x *MCPServerInfo) GetMcpServerUrl() string {
	if x != nil {
		return x.McpServerUrl
	}
	return ""
}

func (x *MCPServerInfo) GetHeaders() map[string]string {
	if x != nil {
		return x.Headers
	}
	return nil
}

func (x *MCPServerInfo) GetTimeout() int32 {
	if x != nil {
		return x.Timeout
	}
	return 0
}

func (x *MCPServerInfo) GetSseReadTimeout() int32 {
	if x != nil {
		return x.SseReadTimeout
	}
	return 0
}

type AddKnowledgeQAToolReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AppBizId string `protobuf:"bytes,1,opt,name=AppBizId,proto3" json:"AppBizId,omitempty"` // 应用ID
}

func (x *AddKnowledgeQAToolReq) Reset() {
	*x = AddKnowledgeQAToolReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_plugin_config_proto_msgTypes[61]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AddKnowledgeQAToolReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddKnowledgeQAToolReq) ProtoMessage() {}

func (x *AddKnowledgeQAToolReq) ProtoReflect() protoreflect.Message {
	mi := &file_plugin_config_proto_msgTypes[61]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddKnowledgeQAToolReq.ProtoReflect.Descriptor instead.
func (*AddKnowledgeQAToolReq) Descriptor() ([]byte, []int) {
	return file_plugin_config_proto_rawDescGZIP(), []int{61}
}

func (x *AddKnowledgeQAToolReq) GetAppBizId() string {
	if x != nil {
		return x.AppBizId
	}
	return ""
}

type AddKnowledgeQAToolRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *AddKnowledgeQAToolRsp) Reset() {
	*x = AddKnowledgeQAToolRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_plugin_config_proto_msgTypes[62]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AddKnowledgeQAToolRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddKnowledgeQAToolRsp) ProtoMessage() {}

func (x *AddKnowledgeQAToolRsp) ProtoReflect() protoreflect.Message {
	mi := &file_plugin_config_proto_msgTypes[62]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddKnowledgeQAToolRsp.ProtoReflect.Descriptor instead.
func (*AddKnowledgeQAToolRsp) Descriptor() ([]byte, []int) {
	return file_plugin_config_proto_rawDescGZIP(), []int{62}
}

// 获取应用插件发布列表请求
type ListAppToolReleasePreviewReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	BotBizId      uint64   `protobuf:"varint,1,opt,name=BotBizId,proto3" json:"BotBizId,omitempty" valid:"required~请传入正确的应用ID"`                  // 机器人
	Query         string   `protobuf:"bytes,2,opt,name=Query,proto3" json:"Query,omitempty"`                         // 查询关键字, 用于模糊匹配标题
	StartTime     uint64   `protobuf:"varint,3,opt,name=StartTime,proto3" json:"StartTime,omitempty"`                // 任务更新时间起点, 时间单位 unix 秒
	EndTime       uint64   `protobuf:"varint,4,opt,name=EndTime,proto3" json:"EndTime,omitempty"`                    // 任务更新时间止点, 时间单位 unix 秒
	Actions       []uint32 `protobuf:"varint,5,rep,packed,name=Actions,proto3" json:"Actions,omitempty"`             // 状态, 状态值：1:新增, 2:修改, 3:删除, 4:发布
	PageNumber    uint32   `protobuf:"varint,6,opt,name=PageNumber,proto3" json:"PageNumber,omitempty"`              // 页码
	PageSize      uint32   `protobuf:"varint,7,opt,name=PageSize,proto3" json:"PageSize,omitempty" valid:"required,range(1|200)~每页数量在1到200之间"`                  // 每页数量
	ReleaseBizId  uint64   `protobuf:"varint,8,opt,name=ReleaseBizId,proto3" json:"ReleaseBizId,omitempty"`          // 发布任务 ID
	ReleaseStatus []uint32 `protobuf:"varint,9,rep,packed,name=ReleaseStatus,proto3" json:"ReleaseStatus,omitempty"` // 发布状态(2 待发布 3 发布中 4 已发布 5 发布失败)
}

func (x *ListAppToolReleasePreviewReq) Reset() {
	*x = ListAppToolReleasePreviewReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_plugin_config_proto_msgTypes[63]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListAppToolReleasePreviewReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListAppToolReleasePreviewReq) ProtoMessage() {}

func (x *ListAppToolReleasePreviewReq) ProtoReflect() protoreflect.Message {
	mi := &file_plugin_config_proto_msgTypes[63]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListAppToolReleasePreviewReq.ProtoReflect.Descriptor instead.
func (*ListAppToolReleasePreviewReq) Descriptor() ([]byte, []int) {
	return file_plugin_config_proto_rawDescGZIP(), []int{63}
}

func (x *ListAppToolReleasePreviewReq) GetBotBizId() uint64 {
	if x != nil {
		return x.BotBizId
	}
	return 0
}

func (x *ListAppToolReleasePreviewReq) GetQuery() string {
	if x != nil {
		return x.Query
	}
	return ""
}

func (x *ListAppToolReleasePreviewReq) GetStartTime() uint64 {
	if x != nil {
		return x.StartTime
	}
	return 0
}

func (x *ListAppToolReleasePreviewReq) GetEndTime() uint64 {
	if x != nil {
		return x.EndTime
	}
	return 0
}

func (x *ListAppToolReleasePreviewReq) GetActions() []uint32 {
	if x != nil {
		return x.Actions
	}
	return nil
}

func (x *ListAppToolReleasePreviewReq) GetPageNumber() uint32 {
	if x != nil {
		return x.PageNumber
	}
	return 0
}

func (x *ListAppToolReleasePreviewReq) GetPageSize() uint32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

func (x *ListAppToolReleasePreviewReq) GetReleaseBizId() uint64 {
	if x != nil {
		return x.ReleaseBizId
	}
	return 0
}

func (x *ListAppToolReleasePreviewReq) GetReleaseStatus() []uint32 {
	if x != nil {
		return x.ReleaseStatus
	}
	return nil
}

// 获取应用插件发布列表返回
type ListAppToolReleasePreviewRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Total uint32                                  `protobuf:"varint,1,opt,name=Total,proto3" json:"Total,omitempty"` // 总数
	List  []*ListAppToolReleasePreviewRsp_AppTool `protobuf:"bytes,2,rep,name=List,proto3" json:"List,omitempty"`    // 列表
}

func (x *ListAppToolReleasePreviewRsp) Reset() {
	*x = ListAppToolReleasePreviewRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_plugin_config_proto_msgTypes[64]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListAppToolReleasePreviewRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListAppToolReleasePreviewRsp) ProtoMessage() {}

func (x *ListAppToolReleasePreviewRsp) ProtoReflect() protoreflect.Message {
	mi := &file_plugin_config_proto_msgTypes[64]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListAppToolReleasePreviewRsp.ProtoReflect.Descriptor instead.
func (*ListAppToolReleasePreviewRsp) Descriptor() ([]byte, []int) {
	return file_plugin_config_proto_rawDescGZIP(), []int{64}
}

func (x *ListAppToolReleasePreviewRsp) GetTotal() uint32 {
	if x != nil {
		return x.Total
	}
	return 0
}

func (x *ListAppToolReleasePreviewRsp) GetList() []*ListAppToolReleasePreviewRsp_AppTool {
	if x != nil {
		return x.List
	}
	return nil
}

type SyncAppToolRedisReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AppBizIds []string `protobuf:"bytes,1,rep,name=AppBizIds,proto3" json:"AppBizIds,omitempty"`                                       // 应用ID
	EnvTag    EnvType  `protobuf:"varint,2,opt,name=EnvTag,proto3,enum=trpc.KEP.plugin_config_server.EnvType" json:"EnvTag,omitempty"` // 环境标识
}

func (x *SyncAppToolRedisReq) Reset() {
	*x = SyncAppToolRedisReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_plugin_config_proto_msgTypes[65]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SyncAppToolRedisReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SyncAppToolRedisReq) ProtoMessage() {}

func (x *SyncAppToolRedisReq) ProtoReflect() protoreflect.Message {
	mi := &file_plugin_config_proto_msgTypes[65]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SyncAppToolRedisReq.ProtoReflect.Descriptor instead.
func (*SyncAppToolRedisReq) Descriptor() ([]byte, []int) {
	return file_plugin_config_proto_rawDescGZIP(), []int{65}
}

func (x *SyncAppToolRedisReq) GetAppBizIds() []string {
	if x != nil {
		return x.AppBizIds
	}
	return nil
}

func (x *SyncAppToolRedisReq) GetEnvTag() EnvType {
	if x != nil {
		return x.EnvTag
	}
	return EnvType_TEST
}

type SyncAppToolRedisRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *SyncAppToolRedisRsp) Reset() {
	*x = SyncAppToolRedisRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_plugin_config_proto_msgTypes[66]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SyncAppToolRedisRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SyncAppToolRedisRsp) ProtoMessage() {}

func (x *SyncAppToolRedisRsp) ProtoReflect() protoreflect.Message {
	mi := &file_plugin_config_proto_msgTypes[66]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SyncAppToolRedisRsp.ProtoReflect.Descriptor instead.
func (*SyncAppToolRedisRsp) Descriptor() ([]byte, []int) {
	return file_plugin_config_proto_rawDescGZIP(), []int{66}
}

type CheckPermissionReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Uin    string                          `protobuf:"bytes,1,opt,name=Uin,proto3" json:"Uin,omitempty"`                                                                         // 用户uin
	Module CheckPermissionReq_ModuleEnum   `protobuf:"varint,2,opt,name=Module,proto3,enum=trpc.KEP.plugin_config_server.CheckPermissionReq_ModuleEnum" json:"Module,omitempty"` //业务模块 agent/workflow
	List   []*CheckPermissionReq_CheckItem `protobuf:"bytes,3,rep,name=List,proto3" json:"List,omitempty"`                                                                       // 校验的插件列表
}

func (x *CheckPermissionReq) Reset() {
	*x = CheckPermissionReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_plugin_config_proto_msgTypes[67]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CheckPermissionReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CheckPermissionReq) ProtoMessage() {}

func (x *CheckPermissionReq) ProtoReflect() protoreflect.Message {
	mi := &file_plugin_config_proto_msgTypes[67]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CheckPermissionReq.ProtoReflect.Descriptor instead.
func (*CheckPermissionReq) Descriptor() ([]byte, []int) {
	return file_plugin_config_proto_rawDescGZIP(), []int{67}
}

func (x *CheckPermissionReq) GetUin() string {
	if x != nil {
		return x.Uin
	}
	return ""
}

func (x *CheckPermissionReq) GetModule() CheckPermissionReq_ModuleEnum {
	if x != nil {
		return x.Module
	}
	return CheckPermissionReq_MODULE_UNKNOWN
}

func (x *CheckPermissionReq) GetList() []*CheckPermissionReq_CheckItem {
	if x != nil {
		return x.List
	}
	return nil
}

type CheckPermissionRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Results []*CheckPermissionRsp_Result `protobuf:"bytes,1,rep,name=Results,proto3" json:"Results,omitempty"` // 校验结果
}

func (x *CheckPermissionRsp) Reset() {
	*x = CheckPermissionRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_plugin_config_proto_msgTypes[68]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CheckPermissionRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CheckPermissionRsp) ProtoMessage() {}

func (x *CheckPermissionRsp) ProtoReflect() protoreflect.Message {
	mi := &file_plugin_config_proto_msgTypes[68]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CheckPermissionRsp.ProtoReflect.Descriptor instead.
func (*CheckPermissionRsp) Descriptor() ([]byte, []int) {
	return file_plugin_config_proto_rawDescGZIP(), []int{68}
}

func (x *CheckPermissionRsp) GetResults() []*CheckPermissionRsp_Result {
	if x != nil {
		return x.Results
	}
	return nil
}

type CreateMCPPluginReq_MCPPluginItem struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name           string          `protobuf:"bytes,1,opt,name=Name,proto3" json:"Name,omitempty"`                      // 插件名称
	Desc           string          `protobuf:"bytes,2,opt,name=Desc,proto3" json:"Desc,omitempty"`                      // 插件描述信息
	IconUrl        string          `protobuf:"bytes,3,opt,name=IconUrl,proto3" json:"IconUrl,omitempty"`                // 插件图标url
	McpServerUrl   string          `protobuf:"bytes,4,opt,name=McpServerUrl,proto3" json:"McpServerUrl,omitempty"`      // MCP server地址
	Headers        []*PluginHeader `protobuf:"bytes,5,rep,name=Headers,proto3" json:"Headers,omitempty"`                // MCP server header信息
	Timeout        int32           `protobuf:"varint,6,opt,name=Timeout,proto3" json:"Timeout,omitempty"`               // 超时时间，单位秒
	SseReadTimeout int32           `protobuf:"varint,7,opt,name=SseReadTimeout,proto3" json:"SseReadTimeout,omitempty"` // sse服务超时时间，单位秒
}

func (x *CreateMCPPluginReq_MCPPluginItem) Reset() {
	*x = CreateMCPPluginReq_MCPPluginItem{}
	if protoimpl.UnsafeEnabled {
		mi := &file_plugin_config_proto_msgTypes[69]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateMCPPluginReq_MCPPluginItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateMCPPluginReq_MCPPluginItem) ProtoMessage() {}

func (x *CreateMCPPluginReq_MCPPluginItem) ProtoReflect() protoreflect.Message {
	mi := &file_plugin_config_proto_msgTypes[69]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateMCPPluginReq_MCPPluginItem.ProtoReflect.Descriptor instead.
func (*CreateMCPPluginReq_MCPPluginItem) Descriptor() ([]byte, []int) {
	return file_plugin_config_proto_rawDescGZIP(), []int{7, 0}
}

func (x *CreateMCPPluginReq_MCPPluginItem) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *CreateMCPPluginReq_MCPPluginItem) GetDesc() string {
	if x != nil {
		return x.Desc
	}
	return ""
}

func (x *CreateMCPPluginReq_MCPPluginItem) GetIconUrl() string {
	if x != nil {
		return x.IconUrl
	}
	return ""
}

func (x *CreateMCPPluginReq_MCPPluginItem) GetMcpServerUrl() string {
	if x != nil {
		return x.McpServerUrl
	}
	return ""
}

func (x *CreateMCPPluginReq_MCPPluginItem) GetHeaders() []*PluginHeader {
	if x != nil {
		return x.Headers
	}
	return nil
}

func (x *CreateMCPPluginReq_MCPPluginItem) GetTimeout() int32 {
	if x != nil {
		return x.Timeout
	}
	return 0
}

func (x *CreateMCPPluginReq_MCPPluginItem) GetSseReadTimeout() int32 {
	if x != nil {
		return x.SseReadTimeout
	}
	return 0
}

type CreateMCPPluginInnerReq_PluginHeader struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ParamName  string `protobuf:"bytes,1,opt,name=ParamName,proto3" json:"ParamName,omitempty"`    // 参数名称
	ParamValue string `protobuf:"bytes,2,opt,name=ParamValue,proto3" json:"ParamValue,omitempty"`  // 参数值
	ShowType   int32  `protobuf:"varint,3,opt,name=ShowType,proto3" json:"ShowType,omitempty"`     // header参数展示类型，1-展示参数名称，2-不展示该参数
	IsRequired bool   `protobuf:"varint,4,opt,name=IsRequired,proto3" json:"IsRequired,omitempty"` // header参数是否必填，true-必填，false-非必填
}

func (x *CreateMCPPluginInnerReq_PluginHeader) Reset() {
	*x = CreateMCPPluginInnerReq_PluginHeader{}
	if protoimpl.UnsafeEnabled {
		mi := &file_plugin_config_proto_msgTypes[70]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateMCPPluginInnerReq_PluginHeader) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateMCPPluginInnerReq_PluginHeader) ProtoMessage() {}

func (x *CreateMCPPluginInnerReq_PluginHeader) ProtoReflect() protoreflect.Message {
	mi := &file_plugin_config_proto_msgTypes[70]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateMCPPluginInnerReq_PluginHeader.ProtoReflect.Descriptor instead.
func (*CreateMCPPluginInnerReq_PluginHeader) Descriptor() ([]byte, []int) {
	return file_plugin_config_proto_rawDescGZIP(), []int{13, 0}
}

func (x *CreateMCPPluginInnerReq_PluginHeader) GetParamName() string {
	if x != nil {
		return x.ParamName
	}
	return ""
}

func (x *CreateMCPPluginInnerReq_PluginHeader) GetParamValue() string {
	if x != nil {
		return x.ParamValue
	}
	return ""
}

func (x *CreateMCPPluginInnerReq_PluginHeader) GetShowType() int32 {
	if x != nil {
		return x.ShowType
	}
	return 0
}

func (x *CreateMCPPluginInnerReq_PluginHeader) GetIsRequired() bool {
	if x != nil {
		return x.IsRequired
	}
	return false
}

type ModifyMCPPluginInnerReq_PluginHeader struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ParamName  string `protobuf:"bytes,1,opt,name=ParamName,proto3" json:"ParamName,omitempty"`    // 参数名称
	ParamValue string `protobuf:"bytes,2,opt,name=ParamValue,proto3" json:"ParamValue,omitempty"`  // 参数值
	ShowType   int32  `protobuf:"varint,3,opt,name=ShowType,proto3" json:"ShowType,omitempty"`     // header参数展示类型，1-展示参数名称，2-不展示该参数
	IsRequired bool   `protobuf:"varint,4,opt,name=IsRequired,proto3" json:"IsRequired,omitempty"` // header参数是否必填，true-必填，false-非必填
}

func (x *ModifyMCPPluginInnerReq_PluginHeader) Reset() {
	*x = ModifyMCPPluginInnerReq_PluginHeader{}
	if protoimpl.UnsafeEnabled {
		mi := &file_plugin_config_proto_msgTypes[71]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ModifyMCPPluginInnerReq_PluginHeader) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ModifyMCPPluginInnerReq_PluginHeader) ProtoMessage() {}

func (x *ModifyMCPPluginInnerReq_PluginHeader) ProtoReflect() protoreflect.Message {
	mi := &file_plugin_config_proto_msgTypes[71]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ModifyMCPPluginInnerReq_PluginHeader.ProtoReflect.Descriptor instead.
func (*ModifyMCPPluginInnerReq_PluginHeader) Descriptor() ([]byte, []int) {
	return file_plugin_config_proto_rawDescGZIP(), []int{15, 0}
}

func (x *ModifyMCPPluginInnerReq_PluginHeader) GetParamName() string {
	if x != nil {
		return x.ParamName
	}
	return ""
}

func (x *ModifyMCPPluginInnerReq_PluginHeader) GetParamValue() string {
	if x != nil {
		return x.ParamValue
	}
	return ""
}

func (x *ModifyMCPPluginInnerReq_PluginHeader) GetShowType() int32 {
	if x != nil {
		return x.ShowType
	}
	return 0
}

func (x *ModifyMCPPluginInnerReq_PluginHeader) GetIsRequired() bool {
	if x != nil {
		return x.IsRequired
	}
	return false
}

// AppTool 发布列表详情
type ListAppToolReleasePreviewRsp_AppTool struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ToolId     string `protobuf:"bytes,1,opt,name=ToolId,proto3" json:"ToolId,omitempty"`          // 工具ID
	ToolName   string `protobuf:"bytes,2,opt,name=ToolName,proto3" json:"ToolName,omitempty"`      // 工具名称
	UpdateTime uint64 `protobuf:"varint,3,opt,name=UpdateTime,proto3" json:"UpdateTime,omitempty"` // 更新时间, unix 秒时间戳 (s)
	Action     uint32 `protobuf:"varint,4,opt,name=Action,proto3" json:"Action,omitempty"`         // 状态, 状态值：1:新增, 2:修改, 3:删除, 4:发布
	ActionDesc string `protobuf:"bytes,5,opt,name=ActionDesc,proto3" json:"ActionDesc,omitempty"`  // 状态, 状态值：1:新增, 2:修改, 3:删除, 4:发布
	Message    string `protobuf:"bytes,6,opt,name=Message,proto3" json:"Message,omitempty"`        // 发布消息
}

func (x *ListAppToolReleasePreviewRsp_AppTool) Reset() {
	*x = ListAppToolReleasePreviewRsp_AppTool{}
	if protoimpl.UnsafeEnabled {
		mi := &file_plugin_config_proto_msgTypes[73]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListAppToolReleasePreviewRsp_AppTool) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListAppToolReleasePreviewRsp_AppTool) ProtoMessage() {}

func (x *ListAppToolReleasePreviewRsp_AppTool) ProtoReflect() protoreflect.Message {
	mi := &file_plugin_config_proto_msgTypes[73]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListAppToolReleasePreviewRsp_AppTool.ProtoReflect.Descriptor instead.
func (*ListAppToolReleasePreviewRsp_AppTool) Descriptor() ([]byte, []int) {
	return file_plugin_config_proto_rawDescGZIP(), []int{64, 0}
}

func (x *ListAppToolReleasePreviewRsp_AppTool) GetToolId() string {
	if x != nil {
		return x.ToolId
	}
	return ""
}

func (x *ListAppToolReleasePreviewRsp_AppTool) GetToolName() string {
	if x != nil {
		return x.ToolName
	}
	return ""
}

func (x *ListAppToolReleasePreviewRsp_AppTool) GetUpdateTime() uint64 {
	if x != nil {
		return x.UpdateTime
	}
	return 0
}

func (x *ListAppToolReleasePreviewRsp_AppTool) GetAction() uint32 {
	if x != nil {
		return x.Action
	}
	return 0
}

func (x *ListAppToolReleasePreviewRsp_AppTool) GetActionDesc() string {
	if x != nil {
		return x.ActionDesc
	}
	return ""
}

func (x *ListAppToolReleasePreviewRsp_AppTool) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

type CheckPermissionReq_CheckItem struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PluginId string `protobuf:"bytes,1,opt,name=PluginId,proto3" json:"PluginId,omitempty"` // 插件ID
}

func (x *CheckPermissionReq_CheckItem) Reset() {
	*x = CheckPermissionReq_CheckItem{}
	if protoimpl.UnsafeEnabled {
		mi := &file_plugin_config_proto_msgTypes[74]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CheckPermissionReq_CheckItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CheckPermissionReq_CheckItem) ProtoMessage() {}

func (x *CheckPermissionReq_CheckItem) ProtoReflect() protoreflect.Message {
	mi := &file_plugin_config_proto_msgTypes[74]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CheckPermissionReq_CheckItem.ProtoReflect.Descriptor instead.
func (*CheckPermissionReq_CheckItem) Descriptor() ([]byte, []int) {
	return file_plugin_config_proto_rawDescGZIP(), []int{67, 0}
}

func (x *CheckPermissionReq_CheckItem) GetPluginId() string {
	if x != nil {
		return x.PluginId
	}
	return ""
}

// 校验权限结果
type CheckPermissionRsp_Result struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PluginId string                              `protobuf:"bytes,1,opt,name=PluginId,proto3" json:"PluginId,omitempty"`                                                                     // 插件ID
	Status   CheckPermissionRsp_PermissionStatus `protobuf:"varint,2,opt,name=Status,proto3,enum=trpc.KEP.plugin_config_server.CheckPermissionRsp_PermissionStatus" json:"Status,omitempty"` // 权限状态
}

func (x *CheckPermissionRsp_Result) Reset() {
	*x = CheckPermissionRsp_Result{}
	if protoimpl.UnsafeEnabled {
		mi := &file_plugin_config_proto_msgTypes[75]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CheckPermissionRsp_Result) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CheckPermissionRsp_Result) ProtoMessage() {}

func (x *CheckPermissionRsp_Result) ProtoReflect() protoreflect.Message {
	mi := &file_plugin_config_proto_msgTypes[75]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CheckPermissionRsp_Result.ProtoReflect.Descriptor instead.
func (*CheckPermissionRsp_Result) Descriptor() ([]byte, []int) {
	return file_plugin_config_proto_rawDescGZIP(), []int{68, 0}
}

func (x *CheckPermissionRsp_Result) GetPluginId() string {
	if x != nil {
		return x.PluginId
	}
	return ""
}

func (x *CheckPermissionRsp_Result) GetStatus() CheckPermissionRsp_PermissionStatus {
	if x != nil {
		return x.Status
	}
	return CheckPermissionRsp_ALLOWED
}

var File_plugin_config_proto protoreflect.FileDescriptor

var file_plugin_config_proto_rawDesc = []byte{
	0x0a, 0x13, 0x70, 0x6c, 0x75, 0x67, 0x69, 0x6e, 0x2d, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x1d, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e,
	0x70, 0x6c, 0x75, 0x67, 0x69, 0x6e, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x73, 0x65,
	0x72, 0x76, 0x65, 0x72, 0x1a, 0x0a, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x1a, 0x26, 0x62, 0x6f, 0x74, 0x2d, 0x74, 0x61, 0x73, 0x6b, 0x2d, 0x63, 0x6f, 0x6e, 0x66, 0x69,
	0x67, 0x2d, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2f, 0x64, 0x61, 0x74, 0x61, 0x2d, 0x73, 0x79,
	0x6e, 0x63, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xb1, 0x05, 0x0a, 0x0e, 0x4c, 0x69, 0x73,
	0x74, 0x50, 0x6c, 0x75, 0x67, 0x69, 0x6e, 0x73, 0x52, 0x65, 0x71, 0x12, 0x14, 0x0a, 0x05, 0x51,
	0x75, 0x65, 0x72, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x51, 0x75, 0x65, 0x72,
	0x79, 0x12, 0x1a, 0x0a, 0x08, 0x50, 0x61, 0x67, 0x65, 0x53, 0x69, 0x7a, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x08, 0x50, 0x61, 0x67, 0x65, 0x53, 0x69, 0x7a, 0x65, 0x12, 0x1e, 0x0a,
	0x0a, 0x50, 0x61, 0x67, 0x65, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x0a, 0x50, 0x61, 0x67, 0x65, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x12, 0x59, 0x0a,
	0x09, 0x51, 0x75, 0x65, 0x72, 0x79, 0x54, 0x79, 0x70, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e,
	0x32, 0x3b, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x70, 0x6c, 0x75, 0x67,
	0x69, 0x6e, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72,
	0x2e, 0x4c, 0x69, 0x73, 0x74, 0x50, 0x6c, 0x75, 0x67, 0x69, 0x6e, 0x73, 0x52, 0x65, 0x71, 0x2e,
	0x51, 0x75, 0x65, 0x72, 0x79, 0x54, 0x79, 0x70, 0x65, 0x45, 0x6e, 0x75, 0x6d, 0x52, 0x09, 0x51,
	0x75, 0x65, 0x72, 0x79, 0x54, 0x79, 0x70, 0x65, 0x12, 0x5c, 0x0a, 0x0a, 0x50, 0x6c, 0x75, 0x67,
	0x69, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x3c, 0x2e, 0x74,
	0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x70, 0x6c, 0x75, 0x67, 0x69, 0x6e, 0x5f, 0x63,
	0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x4c, 0x69, 0x73,
	0x74, 0x50, 0x6c, 0x75, 0x67, 0x69, 0x6e, 0x73, 0x52, 0x65, 0x71, 0x2e, 0x50, 0x6c, 0x75, 0x67,
	0x69, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x45, 0x6e, 0x75, 0x6d, 0x52, 0x0a, 0x50, 0x6c, 0x75, 0x67,
	0x69, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1c, 0x0a, 0x09, 0x50, 0x6c, 0x75, 0x67, 0x69, 0x6e,
	0x49, 0x64, 0x73, 0x18, 0x06, 0x20, 0x03, 0x28, 0x09, 0x52, 0x09, 0x50, 0x6c, 0x75, 0x67, 0x69,
	0x6e, 0x49, 0x64, 0x73, 0x12, 0x50, 0x0a, 0x06, 0x4d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x18, 0x07,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x38, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e,
	0x70, 0x6c, 0x75, 0x67, 0x69, 0x6e, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x73, 0x65,
	0x72, 0x76, 0x65, 0x72, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x50, 0x6c, 0x75, 0x67, 0x69, 0x6e, 0x73,
	0x52, 0x65, 0x71, 0x2e, 0x4d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x45, 0x6e, 0x75, 0x6d, 0x52, 0x06,
	0x4d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x75, 0x69, 0x6e, 0x18, 0x08, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x03, 0x75, 0x69, 0x6e, 0x12, 0x4f, 0x0a, 0x0b, 0x43, 0x72, 0x65, 0x61,
	0x74, 0x65, 0x54, 0x79, 0x70, 0x65, 0x73, 0x18, 0x09, 0x20, 0x03, 0x28, 0x0e, 0x32, 0x2d, 0x2e,
	0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x70, 0x6c, 0x75, 0x67, 0x69, 0x6e, 0x5f,
	0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x43, 0x72,
	0x65, 0x61, 0x74, 0x65, 0x54, 0x79, 0x70, 0x65, 0x45, 0x6e, 0x75, 0x6d, 0x52, 0x0b, 0x43, 0x72,
	0x65, 0x61, 0x74, 0x65, 0x54, 0x79, 0x70, 0x65, 0x73, 0x22, 0x21, 0x0a, 0x0d, 0x51, 0x75, 0x65,
	0x72, 0x79, 0x54, 0x79, 0x70, 0x65, 0x45, 0x6e, 0x75, 0x6d, 0x12, 0x08, 0x0a, 0x04, 0x4e, 0x41,
	0x4d, 0x45, 0x10, 0x00, 0x12, 0x06, 0x0a, 0x02, 0x49, 0x44, 0x10, 0x01, 0x22, 0x44, 0x0a, 0x0e,
	0x50, 0x6c, 0x75, 0x67, 0x69, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x45, 0x6e, 0x75, 0x6d, 0x12, 0x07,
	0x0a, 0x03, 0x41, 0x4c, 0x4c, 0x10, 0x00, 0x12, 0x0a, 0x0a, 0x06, 0x43, 0x55, 0x53, 0x54, 0x4f,
	0x4d, 0x10, 0x01, 0x12, 0x0c, 0x0a, 0x08, 0x4f, 0x46, 0x46, 0x49, 0x43, 0x49, 0x41, 0x4c, 0x10,
	0x02, 0x12, 0x0f, 0x0a, 0x0b, 0x54, 0x48, 0x49, 0x52, 0x44, 0x5f, 0x50, 0x41, 0x52, 0x54, 0x59,
	0x10, 0x03, 0x22, 0x58, 0x0a, 0x0a, 0x4d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x45, 0x6e, 0x75, 0x6d,
	0x12, 0x0e, 0x0a, 0x0a, 0x4d, 0x4f, 0x44, 0x55, 0x4c, 0x45, 0x5f, 0x41, 0x4c, 0x4c, 0x10, 0x00,
	0x12, 0x10, 0x0a, 0x0c, 0x4d, 0x4f, 0x44, 0x55, 0x4c, 0x45, 0x5f, 0x41, 0x47, 0x45, 0x4e, 0x54,
	0x10, 0x01, 0x12, 0x13, 0x0a, 0x0f, 0x4d, 0x4f, 0x44, 0x55, 0x4c, 0x45, 0x5f, 0x57, 0x4f, 0x52,
	0x4b, 0x46, 0x4c, 0x4f, 0x57, 0x10, 0x02, 0x12, 0x13, 0x0a, 0x0f, 0x4d, 0x4f, 0x44, 0x55, 0x4c,
	0x45, 0x5f, 0x53, 0x54, 0x41, 0x4e, 0x44, 0x41, 0x52, 0x44, 0x10, 0x03, 0x22, 0x8b, 0x01, 0x0a,
	0x0e, 0x4c, 0x69, 0x73, 0x74, 0x50, 0x6c, 0x75, 0x67, 0x69, 0x6e, 0x73, 0x52, 0x73, 0x70, 0x12,
	0x43, 0x0a, 0x07, 0x50, 0x6c, 0x75, 0x67, 0x69, 0x6e, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x29, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x70, 0x6c, 0x75, 0x67,
	0x69, 0x6e, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72,
	0x2e, 0x50, 0x6c, 0x75, 0x67, 0x69, 0x6e, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x07, 0x50, 0x6c, 0x75,
	0x67, 0x69, 0x6e, 0x73, 0x12, 0x14, 0x0a, 0x05, 0x54, 0x6f, 0x74, 0x61, 0x6c, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x05, 0x54, 0x6f, 0x74, 0x61, 0x6c, 0x12, 0x1e, 0x0a, 0x0a, 0x50, 0x61,
	0x67, 0x65, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0a,
	0x50, 0x61, 0x67, 0x65, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x22, 0xd8, 0x03, 0x0a, 0x0f, 0x43,
	0x72, 0x65, 0x61, 0x74, 0x65, 0x50, 0x6c, 0x75, 0x67, 0x69, 0x6e, 0x52, 0x65, 0x71, 0x12, 0x12,
	0x0a, 0x04, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x4e, 0x61,
	0x6d, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x44, 0x65, 0x73, 0x63, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x04, 0x44, 0x65, 0x73, 0x63, 0x12, 0x18, 0x0a, 0x07, 0x49, 0x63, 0x6f, 0x6e, 0x55, 0x72,
	0x6c, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x49, 0x63, 0x6f, 0x6e, 0x55, 0x72, 0x6c,
	0x12, 0x47, 0x0a, 0x08, 0x41, 0x75, 0x74, 0x68, 0x54, 0x79, 0x70, 0x65, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x2b, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x70, 0x6c,
	0x75, 0x67, 0x69, 0x6e, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x73, 0x65, 0x72, 0x76,
	0x65, 0x72, 0x2e, 0x41, 0x75, 0x74, 0x68, 0x54, 0x79, 0x70, 0x65, 0x45, 0x6e, 0x75, 0x6d, 0x52,
	0x08, 0x41, 0x75, 0x74, 0x68, 0x54, 0x79, 0x70, 0x65, 0x12, 0x43, 0x0a, 0x08, 0x41, 0x75, 0x74,
	0x68, 0x49, 0x6e, 0x66, 0x6f, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x27, 0x2e, 0x74, 0x72,
	0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x70, 0x6c, 0x75, 0x67, 0x69, 0x6e, 0x5f, 0x63, 0x6f,
	0x6e, 0x66, 0x69, 0x67, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x41, 0x75, 0x74, 0x68,
	0x49, 0x6e, 0x66, 0x6f, 0x52, 0x08, 0x41, 0x75, 0x74, 0x68, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x18,
	0x0a, 0x07, 0x4f, 0x70, 0x65, 0x6e, 0x41, 0x70, 0x69, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x07, 0x4f, 0x70, 0x65, 0x6e, 0x41, 0x70, 0x69, 0x12, 0x4d, 0x0a, 0x0a, 0x50, 0x6c, 0x75, 0x67,
	0x69, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x2d, 0x2e, 0x74,
	0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x70, 0x6c, 0x75, 0x67, 0x69, 0x6e, 0x5f, 0x63,
	0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x50, 0x6c, 0x75,
	0x67, 0x69, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x45, 0x6e, 0x75, 0x6d, 0x52, 0x0a, 0x50, 0x6c, 0x75,
	0x67, 0x69, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x12, 0x3d, 0x0a, 0x05, 0x54, 0x6f, 0x6f, 0x6c, 0x73,
	0x18, 0x08, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x27, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45,
	0x50, 0x2e, 0x70, 0x6c, 0x75, 0x67, 0x69, 0x6e, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f,
	0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x54, 0x6f, 0x6f, 0x6c, 0x49, 0x6e, 0x66, 0x6f, 0x52,
	0x05, 0x54, 0x6f, 0x6f, 0x6c, 0x73, 0x12, 0x4d, 0x0a, 0x0a, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65,
	0x54, 0x79, 0x70, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x2d, 0x2e, 0x74, 0x72, 0x70,
	0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x70, 0x6c, 0x75, 0x67, 0x69, 0x6e, 0x5f, 0x63, 0x6f, 0x6e,
	0x66, 0x69, 0x67, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74,
	0x65, 0x54, 0x79, 0x70, 0x65, 0x45, 0x6e, 0x75, 0x6d, 0x52, 0x0a, 0x43, 0x72, 0x65, 0x61, 0x74,
	0x65, 0x54, 0x79, 0x70, 0x65, 0x22, 0x2d, 0x0a, 0x0f, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x50,
	0x6c, 0x75, 0x67, 0x69, 0x6e, 0x52, 0x73, 0x70, 0x12, 0x1a, 0x0a, 0x08, 0x50, 0x6c, 0x75, 0x67,
	0x69, 0x6e, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x50, 0x6c, 0x75, 0x67,
	0x69, 0x6e, 0x49, 0x64, 0x22, 0x9a, 0x04, 0x0a, 0x0f, 0x4d, 0x6f, 0x64, 0x69, 0x66, 0x79, 0x50,
	0x6c, 0x75, 0x67, 0x69, 0x6e, 0x52, 0x65, 0x71, 0x12, 0x1a, 0x0a, 0x08, 0x50, 0x6c, 0x75, 0x67,
	0x69, 0x6e, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x50, 0x6c, 0x75, 0x67,
	0x69, 0x6e, 0x49, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x04, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x24, 0x0a, 0x0d, 0x50, 0x6c, 0x75, 0x67,
	0x69, 0x6e, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x0d, 0x50, 0x6c, 0x75, 0x67, 0x69, 0x6e, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x12,
	0x0a, 0x04, 0x44, 0x65, 0x73, 0x63, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x44, 0x65,
	0x73, 0x63, 0x12, 0x18, 0x0a, 0x07, 0x49, 0x63, 0x6f, 0x6e, 0x55, 0x72, 0x6c, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x07, 0x49, 0x63, 0x6f, 0x6e, 0x55, 0x72, 0x6c, 0x12, 0x47, 0x0a, 0x08,
	0x41, 0x75, 0x74, 0x68, 0x54, 0x79, 0x70, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x2b,
	0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x70, 0x6c, 0x75, 0x67, 0x69, 0x6e,
	0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x41,
	0x75, 0x74, 0x68, 0x54, 0x79, 0x70, 0x65, 0x45, 0x6e, 0x75, 0x6d, 0x52, 0x08, 0x41, 0x75, 0x74,
	0x68, 0x54, 0x79, 0x70, 0x65, 0x12, 0x43, 0x0a, 0x08, 0x41, 0x75, 0x74, 0x68, 0x49, 0x6e, 0x66,
	0x6f, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x27, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b,
	0x45, 0x50, 0x2e, 0x70, 0x6c, 0x75, 0x67, 0x69, 0x6e, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67,
	0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x41, 0x75, 0x74, 0x68, 0x49, 0x6e, 0x66, 0x6f,
	0x52, 0x08, 0x41, 0x75, 0x74, 0x68, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x18, 0x0a, 0x07, 0x4f, 0x70,
	0x65, 0x6e, 0x41, 0x70, 0x69, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x4f, 0x70, 0x65,
	0x6e, 0x41, 0x70, 0x69, 0x12, 0x4d, 0x0a, 0x0a, 0x50, 0x6c, 0x75, 0x67, 0x69, 0x6e, 0x54, 0x79,
	0x70, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x2d, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e,
	0x4b, 0x45, 0x50, 0x2e, 0x70, 0x6c, 0x75, 0x67, 0x69, 0x6e, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69,
	0x67, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x50, 0x6c, 0x75, 0x67, 0x69, 0x6e, 0x54,
	0x79, 0x70, 0x65, 0x45, 0x6e, 0x75, 0x6d, 0x52, 0x0a, 0x50, 0x6c, 0x75, 0x67, 0x69, 0x6e, 0x54,
	0x79, 0x70, 0x65, 0x12, 0x3d, 0x0a, 0x05, 0x54, 0x6f, 0x6f, 0x6c, 0x73, 0x18, 0x0a, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x27, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x70, 0x6c,
	0x75, 0x67, 0x69, 0x6e, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x73, 0x65, 0x72, 0x76,
	0x65, 0x72, 0x2e, 0x54, 0x6f, 0x6f, 0x6c, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x05, 0x54, 0x6f, 0x6f,
	0x6c, 0x73, 0x12, 0x4d, 0x0a, 0x0a, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x79, 0x70, 0x65,
	0x18, 0x0b, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x2d, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45,
	0x50, 0x2e, 0x70, 0x6c, 0x75, 0x67, 0x69, 0x6e, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f,
	0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x79, 0x70,
	0x65, 0x45, 0x6e, 0x75, 0x6d, 0x52, 0x0a, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x79, 0x70,
	0x65, 0x22, 0x11, 0x0a, 0x0f, 0x4d, 0x6f, 0x64, 0x69, 0x66, 0x79, 0x50, 0x6c, 0x75, 0x67, 0x69,
	0x6e, 0x52, 0x73, 0x70, 0x22, 0x90, 0x01, 0x0a, 0x0c, 0x50, 0x6c, 0x75, 0x67, 0x69, 0x6e, 0x48,
	0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x1c, 0x0a, 0x09, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x4e, 0x61,
	0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x4e,
	0x61, 0x6d, 0x65, 0x12, 0x1e, 0x0a, 0x0a, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x56, 0x61, 0x6c, 0x75,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x56, 0x61,
	0x6c, 0x75, 0x65, 0x12, 0x22, 0x0a, 0x0c, 0x47, 0x6c, 0x6f, 0x62, 0x61, 0x6c, 0x48, 0x69, 0x64,
	0x64, 0x65, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0c, 0x47, 0x6c, 0x6f, 0x62, 0x61,
	0x6c, 0x48, 0x69, 0x64, 0x64, 0x65, 0x6e, 0x12, 0x1e, 0x0a, 0x0a, 0x49, 0x73, 0x52, 0x65, 0x71,
	0x75, 0x69, 0x72, 0x65, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0a, 0x49, 0x73, 0x52,
	0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x22, 0xf0, 0x02, 0x0a, 0x12, 0x43, 0x72, 0x65, 0x61,
	0x74, 0x65, 0x4d, 0x43, 0x50, 0x50, 0x6c, 0x75, 0x67, 0x69, 0x6e, 0x52, 0x65, 0x71, 0x12, 0x59,
	0x0a, 0x07, 0x50, 0x6c, 0x75, 0x67, 0x69, 0x6e, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x3f, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x70, 0x6c, 0x75, 0x67, 0x69,
	0x6e, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e,
	0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x4d, 0x43, 0x50, 0x50, 0x6c, 0x75, 0x67, 0x69, 0x6e, 0x52,
	0x65, 0x71, 0x2e, 0x4d, 0x43, 0x50, 0x50, 0x6c, 0x75, 0x67, 0x69, 0x6e, 0x49, 0x74, 0x65, 0x6d,
	0x52, 0x07, 0x50, 0x6c, 0x75, 0x67, 0x69, 0x6e, 0x73, 0x1a, 0xfe, 0x01, 0x0a, 0x0d, 0x4d, 0x43,
	0x50, 0x50, 0x6c, 0x75, 0x67, 0x69, 0x6e, 0x49, 0x74, 0x65, 0x6d, 0x12, 0x12, 0x0a, 0x04, 0x4e,
	0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x4e, 0x61, 0x6d, 0x65, 0x12,
	0x12, 0x0a, 0x04, 0x44, 0x65, 0x73, 0x63, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x44,
	0x65, 0x73, 0x63, 0x12, 0x18, 0x0a, 0x07, 0x49, 0x63, 0x6f, 0x6e, 0x55, 0x72, 0x6c, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x49, 0x63, 0x6f, 0x6e, 0x55, 0x72, 0x6c, 0x12, 0x22, 0x0a,
	0x0c, 0x4d, 0x63, 0x70, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x55, 0x72, 0x6c, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0c, 0x4d, 0x63, 0x70, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x55, 0x72,
	0x6c, 0x12, 0x45, 0x0a, 0x07, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x73, 0x18, 0x05, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x2b, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x70, 0x6c,
	0x75, 0x67, 0x69, 0x6e, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x73, 0x65, 0x72, 0x76,
	0x65, 0x72, 0x2e, 0x50, 0x6c, 0x75, 0x67, 0x69, 0x6e, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52,
	0x07, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x73, 0x12, 0x18, 0x0a, 0x07, 0x54, 0x69, 0x6d, 0x65,
	0x6f, 0x75, 0x74, 0x18, 0x06, 0x20, 0x01, 0x28, 0x05, 0x52, 0x07, 0x54, 0x69, 0x6d, 0x65, 0x6f,
	0x75, 0x74, 0x12, 0x26, 0x0a, 0x0e, 0x53, 0x73, 0x65, 0x52, 0x65, 0x61, 0x64, 0x54, 0x69, 0x6d,
	0x65, 0x6f, 0x75, 0x74, 0x18, 0x07, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0e, 0x53, 0x73, 0x65, 0x52,
	0x65, 0x61, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x6f, 0x75, 0x74, 0x22, 0x32, 0x0a, 0x12, 0x43, 0x72,
	0x65, 0x61, 0x74, 0x65, 0x4d, 0x43, 0x50, 0x50, 0x6c, 0x75, 0x67, 0x69, 0x6e, 0x52, 0x73, 0x70,
	0x12, 0x1c, 0x0a, 0x09, 0x50, 0x6c, 0x75, 0x67, 0x69, 0x6e, 0x49, 0x64, 0x73, 0x18, 0x01, 0x20,
	0x03, 0x28, 0x09, 0x52, 0x09, 0x50, 0x6c, 0x75, 0x67, 0x69, 0x6e, 0x49, 0x64, 0x73, 0x22, 0xc5,
	0x02, 0x0a, 0x12, 0x4d, 0x6f, 0x64, 0x69, 0x66, 0x79, 0x4d, 0x43, 0x50, 0x50, 0x6c, 0x75, 0x67,
	0x69, 0x6e, 0x52, 0x65, 0x71, 0x12, 0x1a, 0x0a, 0x08, 0x50, 0x6c, 0x75, 0x67, 0x69, 0x6e, 0x49,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x50, 0x6c, 0x75, 0x67, 0x69, 0x6e, 0x49,
	0x64, 0x12, 0x12, 0x0a, 0x04, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x04, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x24, 0x0a, 0x0d, 0x50, 0x6c, 0x75, 0x67, 0x69, 0x6e, 0x56,
	0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0d, 0x50, 0x6c,
	0x75, 0x67, 0x69, 0x6e, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x12, 0x0a, 0x04, 0x44,
	0x65, 0x73, 0x63, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x44, 0x65, 0x73, 0x63, 0x12,
	0x18, 0x0a, 0x07, 0x49, 0x63, 0x6f, 0x6e, 0x55, 0x72, 0x6c, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x07, 0x49, 0x63, 0x6f, 0x6e, 0x55, 0x72, 0x6c, 0x12, 0x22, 0x0a, 0x0c, 0x4d, 0x63, 0x70,
	0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x55, 0x72, 0x6c, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0c, 0x4d, 0x63, 0x70, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x55, 0x72, 0x6c, 0x12, 0x45, 0x0a,
	0x07, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x73, 0x18, 0x07, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2b,
	0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x70, 0x6c, 0x75, 0x67, 0x69, 0x6e,
	0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x50,
	0x6c, 0x75, 0x67, 0x69, 0x6e, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x07, 0x48, 0x65, 0x61,
	0x64, 0x65, 0x72, 0x73, 0x12, 0x18, 0x0a, 0x07, 0x54, 0x69, 0x6d, 0x65, 0x6f, 0x75, 0x74, 0x18,
	0x08, 0x20, 0x01, 0x28, 0x05, 0x52, 0x07, 0x54, 0x69, 0x6d, 0x65, 0x6f, 0x75, 0x74, 0x12, 0x26,
	0x0a, 0x0e, 0x53, 0x73, 0x65, 0x52, 0x65, 0x61, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x6f, 0x75, 0x74,
	0x18, 0x09, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0e, 0x53, 0x73, 0x65, 0x52, 0x65, 0x61, 0x64, 0x54,
	0x69, 0x6d, 0x65, 0x6f, 0x75, 0x74, 0x22, 0x14, 0x0a, 0x12, 0x4d, 0x6f, 0x64, 0x69, 0x66, 0x79,
	0x4d, 0x43, 0x50, 0x50, 0x6c, 0x75, 0x67, 0x69, 0x6e, 0x52, 0x73, 0x70, 0x22, 0x2f, 0x0a, 0x11,
	0x43, 0x68, 0x65, 0x63, 0x6b, 0x4d, 0x43, 0x50, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x52, 0x65,
	0x71, 0x12, 0x1a, 0x0a, 0x08, 0x50, 0x6c, 0x75, 0x67, 0x69, 0x6e, 0x49, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x08, 0x50, 0x6c, 0x75, 0x67, 0x69, 0x6e, 0x49, 0x64, 0x22, 0x2b, 0x0a,
	0x11, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x4d, 0x43, 0x50, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x52,
	0x73, 0x70, 0x12, 0x16, 0x0a, 0x06, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x06, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x22, 0x82, 0x05, 0x0a, 0x17, 0x43,
	0x72, 0x65, 0x61, 0x74, 0x65, 0x4d, 0x43, 0x50, 0x50, 0x6c, 0x75, 0x67, 0x69, 0x6e, 0x49, 0x6e,
	0x6e, 0x65, 0x72, 0x52, 0x65, 0x71, 0x12, 0x12, 0x0a, 0x04, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x44, 0x65,
	0x73, 0x63, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x44, 0x65, 0x73, 0x63, 0x12, 0x18,
	0x0a, 0x07, 0x49, 0x63, 0x6f, 0x6e, 0x55, 0x72, 0x6c, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x07, 0x49, 0x63, 0x6f, 0x6e, 0x55, 0x72, 0x6c, 0x12, 0x16, 0x0a, 0x06, 0x4d, 0x6f, 0x64, 0x75,
	0x6c, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x4d, 0x6f, 0x64, 0x75, 0x6c, 0x65,
	0x12, 0x22, 0x0a, 0x0c, 0x4d, 0x63, 0x70, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x55, 0x72, 0x6c,
	0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x4d, 0x63, 0x70, 0x53, 0x65, 0x72, 0x76, 0x65,
	0x72, 0x55, 0x72, 0x6c, 0x12, 0x5d, 0x0a, 0x07, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x73, 0x18,
	0x06, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x43, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50,
	0x2e, 0x70, 0x6c, 0x75, 0x67, 0x69, 0x6e, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x73,
	0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x4d, 0x43, 0x50, 0x50,
	0x6c, 0x75, 0x67, 0x69, 0x6e, 0x49, 0x6e, 0x6e, 0x65, 0x72, 0x52, 0x65, 0x71, 0x2e, 0x50, 0x6c,
	0x75, 0x67, 0x69, 0x6e, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x07, 0x48, 0x65, 0x61, 0x64,
	0x65, 0x72, 0x73, 0x12, 0x18, 0x0a, 0x07, 0x54, 0x69, 0x6d, 0x65, 0x6f, 0x75, 0x74, 0x18, 0x07,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x07, 0x54, 0x69, 0x6d, 0x65, 0x6f, 0x75, 0x74, 0x12, 0x26, 0x0a,
	0x0e, 0x53, 0x73, 0x65, 0x52, 0x65, 0x61, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x6f, 0x75, 0x74, 0x18,
	0x08, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0e, 0x53, 0x73, 0x65, 0x52, 0x65, 0x61, 0x64, 0x54, 0x69,
	0x6d, 0x65, 0x6f, 0x75, 0x74, 0x12, 0x1e, 0x0a, 0x0a, 0x52, 0x75, 0x6e, 0x43, 0x6f, 0x6d, 0x6d,
	0x61, 0x6e, 0x64, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x52, 0x75, 0x6e, 0x43, 0x6f,
	0x6d, 0x6d, 0x61, 0x6e, 0x64, 0x12, 0x18, 0x0a, 0x07, 0x52, 0x75, 0x6e, 0x41, 0x72, 0x67, 0x73,
	0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x52, 0x75, 0x6e, 0x41, 0x72, 0x67, 0x73, 0x12,
	0x16, 0x0a, 0x06, 0x52, 0x75, 0x6e, 0x45, 0x6e, 0x76, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x06, 0x52, 0x75, 0x6e, 0x45, 0x6e, 0x76, 0x12, 0x1c, 0x0a, 0x09, 0x44, 0x65, 0x76, 0x65, 0x6c,
	0x6f, 0x70, 0x65, 0x72, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x44, 0x65, 0x76, 0x65,
	0x6c, 0x6f, 0x70, 0x65, 0x72, 0x12, 0x4d, 0x0a, 0x0a, 0x50, 0x6c, 0x75, 0x67, 0x69, 0x6e, 0x54,
	0x79, 0x70, 0x65, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x2d, 0x2e, 0x74, 0x72, 0x70, 0x63,
	0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x70, 0x6c, 0x75, 0x67, 0x69, 0x6e, 0x5f, 0x63, 0x6f, 0x6e, 0x66,
	0x69, 0x67, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x50, 0x6c, 0x75, 0x67, 0x69, 0x6e,
	0x54, 0x79, 0x70, 0x65, 0x45, 0x6e, 0x75, 0x6d, 0x52, 0x0a, 0x50, 0x6c, 0x75, 0x67, 0x69, 0x6e,
	0x54, 0x79, 0x70, 0x65, 0x1a, 0x88, 0x01, 0x0a, 0x0c, 0x50, 0x6c, 0x75, 0x67, 0x69, 0x6e, 0x48,
	0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x1c, 0x0a, 0x09, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x4e, 0x61,
	0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x4e,
	0x61, 0x6d, 0x65, 0x12, 0x1e, 0x0a, 0x0a, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x56, 0x61, 0x6c, 0x75,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x56, 0x61,
	0x6c, 0x75, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x53, 0x68, 0x6f, 0x77, 0x54, 0x79, 0x70, 0x65, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x53, 0x68, 0x6f, 0x77, 0x54, 0x79, 0x70, 0x65, 0x12,
	0x1e, 0x0a, 0x0a, 0x49, 0x73, 0x52, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x08, 0x52, 0x0a, 0x49, 0x73, 0x52, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x22,
	0x35, 0x0a, 0x17, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x4d, 0x43, 0x50, 0x50, 0x6c, 0x75, 0x67,
	0x69, 0x6e, 0x49, 0x6e, 0x6e, 0x65, 0x72, 0x52, 0x73, 0x70, 0x12, 0x1a, 0x0a, 0x08, 0x50, 0x6c,
	0x75, 0x67, 0x69, 0x6e, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x50, 0x6c,
	0x75, 0x67, 0x69, 0x6e, 0x49, 0x64, 0x22, 0xc4, 0x05, 0x0a, 0x17, 0x4d, 0x6f, 0x64, 0x69, 0x66,
	0x79, 0x4d, 0x43, 0x50, 0x50, 0x6c, 0x75, 0x67, 0x69, 0x6e, 0x49, 0x6e, 0x6e, 0x65, 0x72, 0x52,
	0x65, 0x71, 0x12, 0x1a, 0x0a, 0x08, 0x50, 0x6c, 0x75, 0x67, 0x69, 0x6e, 0x49, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x50, 0x6c, 0x75, 0x67, 0x69, 0x6e, 0x49, 0x64, 0x12, 0x12,
	0x0a, 0x04, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x4e, 0x61,
	0x6d, 0x65, 0x12, 0x24, 0x0a, 0x0d, 0x50, 0x6c, 0x75, 0x67, 0x69, 0x6e, 0x56, 0x65, 0x72, 0x73,
	0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0d, 0x50, 0x6c, 0x75, 0x67, 0x69,
	0x6e, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x12, 0x0a, 0x04, 0x44, 0x65, 0x73, 0x63,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x44, 0x65, 0x73, 0x63, 0x12, 0x18, 0x0a, 0x07,
	0x49, 0x63, 0x6f, 0x6e, 0x55, 0x72, 0x6c, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x49,
	0x63, 0x6f, 0x6e, 0x55, 0x72, 0x6c, 0x12, 0x16, 0x0a, 0x06, 0x4d, 0x6f, 0x64, 0x75, 0x6c, 0x65,
	0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x4d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x12, 0x22,
	0x0a, 0x0c, 0x4d, 0x63, 0x70, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x55, 0x72, 0x6c, 0x18, 0x07,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x4d, 0x63, 0x70, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x55,
	0x72, 0x6c, 0x12, 0x5d, 0x0a, 0x07, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x73, 0x18, 0x08, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x43, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x70,
	0x6c, 0x75, 0x67, 0x69, 0x6e, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x73, 0x65, 0x72,
	0x76, 0x65, 0x72, 0x2e, 0x4d, 0x6f, 0x64, 0x69, 0x66, 0x79, 0x4d, 0x43, 0x50, 0x50, 0x6c, 0x75,
	0x67, 0x69, 0x6e, 0x49, 0x6e, 0x6e, 0x65, 0x72, 0x52, 0x65, 0x71, 0x2e, 0x50, 0x6c, 0x75, 0x67,
	0x69, 0x6e, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x07, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72,
	0x73, 0x12, 0x18, 0x0a, 0x07, 0x54, 0x69, 0x6d, 0x65, 0x6f, 0x75, 0x74, 0x18, 0x09, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x07, 0x54, 0x69, 0x6d, 0x65, 0x6f, 0x75, 0x74, 0x12, 0x26, 0x0a, 0x0e, 0x53,
	0x73, 0x65, 0x52, 0x65, 0x61, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x6f, 0x75, 0x74, 0x18, 0x0a, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x0e, 0x53, 0x73, 0x65, 0x52, 0x65, 0x61, 0x64, 0x54, 0x69, 0x6d, 0x65,
	0x6f, 0x75, 0x74, 0x12, 0x1e, 0x0a, 0x0a, 0x52, 0x75, 0x6e, 0x43, 0x6f, 0x6d, 0x6d, 0x61, 0x6e,
	0x64, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x52, 0x75, 0x6e, 0x43, 0x6f, 0x6d, 0x6d,
	0x61, 0x6e, 0x64, 0x12, 0x18, 0x0a, 0x07, 0x52, 0x75, 0x6e, 0x41, 0x72, 0x67, 0x73, 0x18, 0x0c,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x52, 0x75, 0x6e, 0x41, 0x72, 0x67, 0x73, 0x12, 0x16, 0x0a,
	0x06, 0x52, 0x75, 0x6e, 0x45, 0x6e, 0x76, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x52,
	0x75, 0x6e, 0x45, 0x6e, 0x76, 0x12, 0x1c, 0x0a, 0x09, 0x44, 0x65, 0x76, 0x65, 0x6c, 0x6f, 0x70,
	0x65, 0x72, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x44, 0x65, 0x76, 0x65, 0x6c, 0x6f,
	0x70, 0x65, 0x72, 0x12, 0x4d, 0x0a, 0x0a, 0x50, 0x6c, 0x75, 0x67, 0x69, 0x6e, 0x54, 0x79, 0x70,
	0x65, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x2d, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b,
	0x45, 0x50, 0x2e, 0x70, 0x6c, 0x75, 0x67, 0x69, 0x6e, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67,
	0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x50, 0x6c, 0x75, 0x67, 0x69, 0x6e, 0x54, 0x79,
	0x70, 0x65, 0x45, 0x6e, 0x75, 0x6d, 0x52, 0x0a, 0x50, 0x6c, 0x75, 0x67, 0x69, 0x6e, 0x54, 0x79,
	0x70, 0x65, 0x1a, 0x88, 0x01, 0x0a, 0x0c, 0x50, 0x6c, 0x75, 0x67, 0x69, 0x6e, 0x48, 0x65, 0x61,
	0x64, 0x65, 0x72, 0x12, 0x1c, 0x0a, 0x09, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x4e, 0x61, 0x6d, 0x65,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x4e, 0x61, 0x6d,
	0x65, 0x12, 0x1e, 0x0a, 0x0a, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x56, 0x61, 0x6c, 0x75,
	0x65, 0x12, 0x1a, 0x0a, 0x08, 0x53, 0x68, 0x6f, 0x77, 0x54, 0x79, 0x70, 0x65, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x08, 0x53, 0x68, 0x6f, 0x77, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1e, 0x0a,
	0x0a, 0x49, 0x73, 0x52, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x08, 0x52, 0x0a, 0x49, 0x73, 0x52, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x22, 0x19, 0x0a,
	0x17, 0x4d, 0x6f, 0x64, 0x69, 0x66, 0x79, 0x4d, 0x43, 0x50, 0x50, 0x6c, 0x75, 0x67, 0x69, 0x6e,
	0x49, 0x6e, 0x6e, 0x65, 0x72, 0x52, 0x73, 0x70, 0x22, 0x2f, 0x0a, 0x0f, 0x44, 0x65, 0x6c, 0x65,
	0x74, 0x65, 0x50, 0x6c, 0x75, 0x67, 0x69, 0x6e, 0x52, 0x65, 0x71, 0x12, 0x1c, 0x0a, 0x09, 0x50,
	0x6c, 0x75, 0x67, 0x69, 0x6e, 0x49, 0x64, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x09, 0x52, 0x09,
	0x50, 0x6c, 0x75, 0x67, 0x69, 0x6e, 0x49, 0x64, 0x73, 0x22, 0x11, 0x0a, 0x0f, 0x44, 0x65, 0x6c,
	0x65, 0x74, 0x65, 0x50, 0x6c, 0x75, 0x67, 0x69, 0x6e, 0x52, 0x73, 0x70, 0x22, 0x2f, 0x0a, 0x11,
	0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x50, 0x6c, 0x75, 0x67, 0x69, 0x6e, 0x52, 0x65,
	0x71, 0x12, 0x1a, 0x0a, 0x08, 0x50, 0x6c, 0x75, 0x67, 0x69, 0x6e, 0x49, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x08, 0x50, 0x6c, 0x75, 0x67, 0x69, 0x6e, 0x49, 0x64, 0x22, 0xb8, 0x07,
	0x0a, 0x11, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x50, 0x6c, 0x75, 0x67, 0x69, 0x6e,
	0x52, 0x73, 0x70, 0x12, 0x1a, 0x0a, 0x08, 0x50, 0x6c, 0x75, 0x67, 0x69, 0x6e, 0x49, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x50, 0x6c, 0x75, 0x67, 0x69, 0x6e, 0x49, 0x64, 0x12,
	0x12, 0x0a, 0x04, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x4e,
	0x61, 0x6d, 0x65, 0x12, 0x24, 0x0a, 0x0d, 0x50, 0x6c, 0x75, 0x67, 0x69, 0x6e, 0x56, 0x65, 0x72,
	0x73, 0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0d, 0x50, 0x6c, 0x75, 0x67,
	0x69, 0x6e, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x12, 0x0a, 0x04, 0x44, 0x65, 0x73,
	0x63, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x44, 0x65, 0x73, 0x63, 0x12, 0x18, 0x0a,
	0x07, 0x49, 0x63, 0x6f, 0x6e, 0x55, 0x72, 0x6c, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07,
	0x49, 0x63, 0x6f, 0x6e, 0x55, 0x72, 0x6c, 0x12, 0x47, 0x0a, 0x08, 0x41, 0x75, 0x74, 0x68, 0x54,
	0x79, 0x70, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x2b, 0x2e, 0x74, 0x72, 0x70, 0x63,
	0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x70, 0x6c, 0x75, 0x67, 0x69, 0x6e, 0x5f, 0x63, 0x6f, 0x6e, 0x66,
	0x69, 0x67, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x41, 0x75, 0x74, 0x68, 0x54, 0x79,
	0x70, 0x65, 0x45, 0x6e, 0x75, 0x6d, 0x52, 0x08, 0x41, 0x75, 0x74, 0x68, 0x54, 0x79, 0x70, 0x65,
	0x12, 0x43, 0x0a, 0x08, 0x41, 0x75, 0x74, 0x68, 0x49, 0x6e, 0x66, 0x6f, 0x18, 0x07, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x27, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x70, 0x6c,
	0x75, 0x67, 0x69, 0x6e, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x73, 0x65, 0x72, 0x76,
	0x65, 0x72, 0x2e, 0x41, 0x75, 0x74, 0x68, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x08, 0x41, 0x75, 0x74,
	0x68, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x18, 0x0a, 0x07, 0x4f, 0x70, 0x65, 0x6e, 0x41, 0x70, 0x69,
	0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x4f, 0x70, 0x65, 0x6e, 0x41, 0x70, 0x69, 0x12,
	0x43, 0x0a, 0x08, 0x55, 0x73, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x18, 0x09, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x27, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x70, 0x6c, 0x75,
	0x67, 0x69, 0x6e, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65,
	0x72, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x08, 0x55, 0x73, 0x65, 0x72,
	0x49, 0x6e, 0x66, 0x6f, 0x12, 0x4d, 0x0a, 0x0a, 0x50, 0x6c, 0x75, 0x67, 0x69, 0x6e, 0x54, 0x79,
	0x70, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x2d, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e,
	0x4b, 0x45, 0x50, 0x2e, 0x70, 0x6c, 0x75, 0x67, 0x69, 0x6e, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69,
	0x67, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x50, 0x6c, 0x75, 0x67, 0x69, 0x6e, 0x54,
	0x79, 0x70, 0x65, 0x45, 0x6e, 0x75, 0x6d, 0x52, 0x0a, 0x50, 0x6c, 0x75, 0x67, 0x69, 0x6e, 0x54,
	0x79, 0x70, 0x65, 0x12, 0x3d, 0x0a, 0x05, 0x54, 0x6f, 0x6f, 0x6c, 0x73, 0x18, 0x0b, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x27, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x70, 0x6c,
	0x75, 0x67, 0x69, 0x6e, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x73, 0x65, 0x72, 0x76,
	0x65, 0x72, 0x2e, 0x54, 0x6f, 0x6f, 0x6c, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x05, 0x54, 0x6f, 0x6f,
	0x6c, 0x73, 0x12, 0x1e, 0x0a, 0x0a, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65,
	0x18, 0x0c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x69,
	0x6d, 0x65, 0x12, 0x1e, 0x0a, 0x0a, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65,
	0x18, 0x0d, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x54, 0x69,
	0x6d, 0x65, 0x12, 0x50, 0x0a, 0x0b, 0x46, 0x69, 0x6e, 0x61, 0x6e, 0x63, 0x65, 0x54, 0x79, 0x70,
	0x65, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x2e, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b,
	0x45, 0x50, 0x2e, 0x70, 0x6c, 0x75, 0x67, 0x69, 0x6e, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67,
	0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x46, 0x69, 0x6e, 0x61, 0x6e, 0x63, 0x65, 0x54,
	0x79, 0x70, 0x65, 0x45, 0x6e, 0x75, 0x6d, 0x52, 0x0b, 0x46, 0x69, 0x6e, 0x61, 0x6e, 0x63, 0x65,
	0x54, 0x79, 0x70, 0x65, 0x12, 0x4d, 0x0a, 0x0a, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x79,
	0x70, 0x65, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x2d, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e,
	0x4b, 0x45, 0x50, 0x2e, 0x70, 0x6c, 0x75, 0x67, 0x69, 0x6e, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69,
	0x67, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54,
	0x79, 0x70, 0x65, 0x45, 0x6e, 0x75, 0x6d, 0x52, 0x0a, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54,
	0x79, 0x70, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x10, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x06, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x22, 0x0a, 0x0c, 0x4d,
	0x63, 0x70, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x55, 0x72, 0x6c, 0x18, 0x11, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0c, 0x4d, 0x63, 0x70, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x55, 0x72, 0x6c, 0x12,
	0x45, 0x0a, 0x07, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x73, 0x18, 0x12, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x2b, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x70, 0x6c, 0x75, 0x67,
	0x69, 0x6e, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72,
	0x2e, 0x50, 0x6c, 0x75, 0x67, 0x69, 0x6e, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x07, 0x48,
	0x65, 0x61, 0x64, 0x65, 0x72, 0x73, 0x12, 0x18, 0x0a, 0x07, 0x54, 0x69, 0x6d, 0x65, 0x6f, 0x75,
	0x74, 0x18, 0x13, 0x20, 0x01, 0x28, 0x05, 0x52, 0x07, 0x54, 0x69, 0x6d, 0x65, 0x6f, 0x75, 0x74,
	0x12, 0x26, 0x0a, 0x0e, 0x53, 0x73, 0x65, 0x52, 0x65, 0x61, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x6f,
	0x75, 0x74, 0x18, 0x14, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0e, 0x53, 0x73, 0x65, 0x52, 0x65, 0x61,
	0x64, 0x54, 0x69, 0x6d, 0x65, 0x6f, 0x75, 0x74, 0x22, 0x28, 0x0a, 0x0c, 0x4c, 0x69, 0x73, 0x74,
	0x54, 0x6f, 0x6f, 0x6c, 0x73, 0x52, 0x65, 0x71, 0x12, 0x18, 0x0a, 0x07, 0x54, 0x6f, 0x6f, 0x6c,
	0x49, 0x64, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x09, 0x52, 0x07, 0x54, 0x6f, 0x6f, 0x6c, 0x49,
	0x64, 0x73, 0x22, 0x4d, 0x0a, 0x0c, 0x4c, 0x69, 0x73, 0x74, 0x54, 0x6f, 0x6f, 0x6c, 0x73, 0x52,
	0x73, 0x70, 0x12, 0x3d, 0x0a, 0x05, 0x54, 0x6f, 0x6f, 0x6c, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x27, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x70, 0x6c, 0x75,
	0x67, 0x69, 0x6e, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65,
	0x72, 0x2e, 0x54, 0x6f, 0x6f, 0x6c, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x05, 0x54, 0x6f, 0x6f, 0x6c,
	0x73, 0x22, 0x45, 0x0a, 0x0f, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x54, 0x6f, 0x6f,
	0x6c, 0x52, 0x65, 0x71, 0x12, 0x16, 0x0a, 0x06, 0x54, 0x6f, 0x6f, 0x6c, 0x49, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x54, 0x6f, 0x6f, 0x6c, 0x49, 0x64, 0x12, 0x1a, 0x0a, 0x08,
	0x50, 0x6c, 0x75, 0x67, 0x69, 0x6e, 0x49, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08,
	0x50, 0x6c, 0x75, 0x67, 0x69, 0x6e, 0x49, 0x64, 0x22, 0xaa, 0x07, 0x0a, 0x0f, 0x44, 0x65, 0x73,
	0x63, 0x72, 0x69, 0x62, 0x65, 0x54, 0x6f, 0x6f, 0x6c, 0x52, 0x73, 0x70, 0x12, 0x16, 0x0a, 0x06,
	0x54, 0x6f, 0x6f, 0x6c, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x54, 0x6f,
	0x6f, 0x6c, 0x49, 0x64, 0x12, 0x1a, 0x0a, 0x08, 0x50, 0x6c, 0x75, 0x67, 0x69, 0x6e, 0x49, 0x64,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x50, 0x6c, 0x75, 0x67, 0x69, 0x6e, 0x49, 0x64,
	0x12, 0x12, 0x0a, 0x04, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04,
	0x4e, 0x61, 0x6d, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x44, 0x65, 0x73, 0x63, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x04, 0x44, 0x65, 0x73, 0x63, 0x12, 0x10, 0x0a, 0x03, 0x55, 0x72, 0x6c, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x55, 0x72, 0x6c, 0x12, 0x12, 0x0a, 0x04, 0x50, 0x61,
	0x74, 0x68, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x50, 0x61, 0x74, 0x68, 0x12, 0x16,
	0x0a, 0x06, 0x4d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06,
	0x4d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x12, 0x43, 0x0a, 0x06, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72,
	0x18, 0x08, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2b, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45,
	0x50, 0x2e, 0x70, 0x6c, 0x75, 0x67, 0x69, 0x6e, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f,
	0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x50, 0x61,
	0x72, 0x61, 0x6d, 0x52, 0x06, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x41, 0x0a, 0x05, 0x51,
	0x75, 0x65, 0x72, 0x79, 0x18, 0x09, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2b, 0x2e, 0x74, 0x72, 0x70,
	0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x70, 0x6c, 0x75, 0x67, 0x69, 0x6e, 0x5f, 0x63, 0x6f, 0x6e,
	0x66, 0x69, 0x67, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x52, 0x05, 0x51, 0x75, 0x65, 0x72, 0x79, 0x12, 0x3f,
	0x0a, 0x04, 0x42, 0x6f, 0x64, 0x79, 0x18, 0x0a, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2b, 0x2e, 0x74,
	0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x70, 0x6c, 0x75, 0x67, 0x69, 0x6e, 0x5f, 0x63,
	0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x52, 0x04, 0x42, 0x6f, 0x64, 0x79, 0x12,
	0x46, 0x0a, 0x07, 0x4f, 0x75, 0x74, 0x70, 0x75, 0x74, 0x73, 0x18, 0x0b, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x2c, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x70, 0x6c, 0x75, 0x67,
	0x69, 0x6e, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72,
	0x2e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x52, 0x07,
	0x4f, 0x75, 0x74, 0x70, 0x75, 0x74, 0x73, 0x12, 0x43, 0x0a, 0x06, 0x49, 0x6e, 0x70, 0x75, 0x74,
	0x73, 0x18, 0x0c, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2b, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b,
	0x45, 0x50, 0x2e, 0x70, 0x6c, 0x75, 0x67, 0x69, 0x6e, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67,
	0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x50,
	0x61, 0x72, 0x61, 0x6d, 0x52, 0x06, 0x49, 0x6e, 0x70, 0x75, 0x74, 0x73, 0x12, 0x44, 0x0a, 0x07,
	0x45, 0x78, 0x61, 0x6d, 0x70, 0x6c, 0x65, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2a, 0x2e,
	0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x70, 0x6c, 0x75, 0x67, 0x69, 0x6e, 0x5f,
	0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x54, 0x6f,
	0x6f, 0x6c, 0x45, 0x78, 0x61, 0x6d, 0x70, 0x6c, 0x65, 0x52, 0x07, 0x45, 0x78, 0x61, 0x6d, 0x70,
	0x6c, 0x65, 0x12, 0x47, 0x0a, 0x08, 0x41, 0x75, 0x74, 0x68, 0x54, 0x79, 0x70, 0x65, 0x18, 0x0e,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x2b, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e,
	0x70, 0x6c, 0x75, 0x67, 0x69, 0x6e, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x73, 0x65,
	0x72, 0x76, 0x65, 0x72, 0x2e, 0x41, 0x75, 0x74, 0x68, 0x54, 0x79, 0x70, 0x65, 0x45, 0x6e, 0x75,
	0x6d, 0x52, 0x08, 0x41, 0x75, 0x74, 0x68, 0x54, 0x79, 0x70, 0x65, 0x12, 0x43, 0x0a, 0x08, 0x41,
	0x75, 0x74, 0x68, 0x49, 0x6e, 0x66, 0x6f, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x27, 0x2e,
	0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x70, 0x6c, 0x75, 0x67, 0x69, 0x6e, 0x5f,
	0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x41, 0x75,
	0x74, 0x68, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x08, 0x41, 0x75, 0x74, 0x68, 0x49, 0x6e, 0x66, 0x6f,
	0x12, 0x12, 0x0a, 0x04, 0x43, 0x6f, 0x64, 0x65, 0x18, 0x10, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04,
	0x43, 0x6f, 0x64, 0x65, 0x12, 0x4d, 0x0a, 0x0a, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x79,
	0x70, 0x65, 0x18, 0x11, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x2d, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e,
	0x4b, 0x45, 0x50, 0x2e, 0x70, 0x6c, 0x75, 0x67, 0x69, 0x6e, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69,
	0x67, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54,
	0x79, 0x70, 0x65, 0x45, 0x6e, 0x75, 0x6d, 0x52, 0x0a, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54,
	0x79, 0x70, 0x65, 0x12, 0x4a, 0x0a, 0x09, 0x4d, 0x43, 0x50, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72,
	0x18, 0x12, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2c, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45,
	0x50, 0x2e, 0x70, 0x6c, 0x75, 0x67, 0x69, 0x6e, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f,
	0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x4d, 0x43, 0x50, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72,
	0x49, 0x6e, 0x66, 0x6f, 0x52, 0x09, 0x4d, 0x43, 0x50, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x12,
	0x24, 0x0a, 0x0d, 0x49, 0x73, 0x53, 0x74, 0x72, 0x65, 0x61, 0x6d, 0x52, 0x65, 0x70, 0x6c, 0x79,
	0x18, 0x13, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0d, 0x49, 0x73, 0x53, 0x74, 0x72, 0x65, 0x61, 0x6d,
	0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x43, 0x0a, 0x0b, 0x54, 0x6f, 0x6f, 0x6c, 0x45, 0x78, 0x61,
	0x6d, 0x70, 0x6c, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1a,
	0x0a, 0x08, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x08, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0xac, 0x03, 0x0a, 0x0c, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x12, 0x12, 0x0a, 0x04, 0x4e,
	0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x4e, 0x61, 0x6d, 0x65, 0x12,
	0x12, 0x0a, 0x04, 0x44, 0x65, 0x73, 0x63, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x44,
	0x65, 0x73, 0x63, 0x12, 0x3b, 0x0a, 0x04, 0x54, 0x79, 0x70, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x27, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x70, 0x6c, 0x75,
	0x67, 0x69, 0x6e, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65,
	0x72, 0x2e, 0x54, 0x79, 0x70, 0x65, 0x45, 0x6e, 0x75, 0x6d, 0x52, 0x04, 0x54, 0x79, 0x70, 0x65,
	0x12, 0x1e, 0x0a, 0x0a, 0x49, 0x73, 0x52, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x08, 0x52, 0x0a, 0x49, 0x73, 0x52, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64,
	0x12, 0x22, 0x0a, 0x0c, 0x44, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x56, 0x61, 0x6c, 0x75, 0x65,
	0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x44, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x56,
	0x61, 0x6c, 0x75, 0x65, 0x12, 0x49, 0x0a, 0x09, 0x53, 0x75, 0x62, 0x50, 0x61, 0x72, 0x61, 0x6d,
	0x73, 0x18, 0x06, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2b, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b,
	0x45, 0x50, 0x2e, 0x70, 0x6c, 0x75, 0x67, 0x69, 0x6e, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67,
	0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x50,
	0x61, 0x72, 0x61, 0x6d, 0x52, 0x09, 0x53, 0x75, 0x62, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12,
	0x22, 0x0a, 0x0c, 0x47, 0x6c, 0x6f, 0x62, 0x61, 0x6c, 0x48, 0x69, 0x64, 0x64, 0x65, 0x6e, 0x18,
	0x07, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0c, 0x47, 0x6c, 0x6f, 0x62, 0x61, 0x6c, 0x48, 0x69, 0x64,
	0x64, 0x65, 0x6e, 0x12, 0x41, 0x0a, 0x05, 0x4f, 0x6e, 0x65, 0x4f, 0x66, 0x18, 0x08, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x2b, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x70, 0x6c,
	0x75, 0x67, 0x69, 0x6e, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x73, 0x65, 0x72, 0x76,
	0x65, 0x72, 0x2e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x52,
	0x05, 0x4f, 0x6e, 0x65, 0x4f, 0x66, 0x12, 0x41, 0x0a, 0x05, 0x41, 0x6e, 0x79, 0x4f, 0x66, 0x18,
	0x09, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2b, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50,
	0x2e, 0x70, 0x6c, 0x75, 0x67, 0x69, 0x6e, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x73,
	0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x50, 0x61, 0x72,
	0x61, 0x6d, 0x52, 0x05, 0x41, 0x6e, 0x79, 0x4f, 0x66, 0x22, 0xec, 0x01, 0x0a, 0x0d, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x12, 0x12, 0x0a, 0x04, 0x4e,
	0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x4e, 0x61, 0x6d, 0x65, 0x12,
	0x12, 0x0a, 0x04, 0x44, 0x65, 0x73, 0x63, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x44,
	0x65, 0x73, 0x63, 0x12, 0x3b, 0x0a, 0x04, 0x54, 0x79, 0x70, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x27, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x70, 0x6c, 0x75,
	0x67, 0x69, 0x6e, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65,
	0x72, 0x2e, 0x54, 0x79, 0x70, 0x65, 0x45, 0x6e, 0x75, 0x6d, 0x52, 0x04, 0x54, 0x79, 0x70, 0x65,
	0x12, 0x4a, 0x0a, 0x09, 0x53, 0x75, 0x62, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x18, 0x04, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x2c, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x70,
	0x6c, 0x75, 0x67, 0x69, 0x6e, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x73, 0x65, 0x72,
	0x76, 0x65, 0x72, 0x2e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x50, 0x61, 0x72, 0x61,
	0x6d, 0x52, 0x09, 0x53, 0x75, 0x62, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x2a, 0x0a, 0x10,
	0x49, 0x73, 0x49, 0x6e, 0x63, 0x72, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x70, 0x6c, 0x79,
	0x18, 0x05, 0x20, 0x01, 0x28, 0x08, 0x52, 0x10, 0x49, 0x73, 0x49, 0x6e, 0x63, 0x72, 0x65, 0x6d,
	0x65, 0x6e, 0x74, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0xd4, 0x05, 0x0a, 0x0c, 0x43, 0x68, 0x65,
	0x63, 0x6b, 0x54, 0x6f, 0x6f, 0x6c, 0x52, 0x65, 0x71, 0x12, 0x10, 0x0a, 0x03, 0x55, 0x72, 0x6c,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x55, 0x72, 0x6c, 0x12, 0x12, 0x0a, 0x04, 0x50,
	0x61, 0x74, 0x68, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x50, 0x61, 0x74, 0x68, 0x12,
	0x16, 0x0a, 0x06, 0x4d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x06, 0x4d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x12, 0x20, 0x0a, 0x0b, 0x48, 0x65, 0x61, 0x64, 0x65,
	0x72, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x48, 0x65,
	0x61, 0x64, 0x65, 0x72, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x12, 0x1e, 0x0a, 0x0a, 0x51, 0x75, 0x65,
	0x72, 0x79, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x51,
	0x75, 0x65, 0x72, 0x79, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x12, 0x1c, 0x0a, 0x09, 0x42, 0x6f, 0x64,
	0x79, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x42, 0x6f,
	0x64, 0x79, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x12, 0x43, 0x0a, 0x06, 0x48, 0x65, 0x61, 0x64, 0x65,
	0x72, 0x18, 0x07, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2b, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b,
	0x45, 0x50, 0x2e, 0x70, 0x6c, 0x75, 0x67, 0x69, 0x6e, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67,
	0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x50,
	0x61, 0x72, 0x61, 0x6d, 0x52, 0x06, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x41, 0x0a, 0x05,
	0x51, 0x75, 0x65, 0x72, 0x79, 0x18, 0x08, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2b, 0x2e, 0x74, 0x72,
	0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x70, 0x6c, 0x75, 0x67, 0x69, 0x6e, 0x5f, 0x63, 0x6f,
	0x6e, 0x66, 0x69, 0x67, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x52, 0x05, 0x51, 0x75, 0x65, 0x72, 0x79, 0x12,
	0x3f, 0x0a, 0x04, 0x42, 0x6f, 0x64, 0x79, 0x18, 0x09, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2b, 0x2e,
	0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x70, 0x6c, 0x75, 0x67, 0x69, 0x6e, 0x5f,
	0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x52, 0x04, 0x42, 0x6f, 0x64, 0x79,
	0x12, 0x46, 0x0a, 0x07, 0x4f, 0x75, 0x74, 0x70, 0x75, 0x74, 0x73, 0x18, 0x0a, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x2c, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x70, 0x6c, 0x75,
	0x67, 0x69, 0x6e, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65,
	0x72, 0x2e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x52,
	0x07, 0x4f, 0x75, 0x74, 0x70, 0x75, 0x74, 0x73, 0x12, 0x47, 0x0a, 0x08, 0x41, 0x75, 0x74, 0x68,
	0x54, 0x79, 0x70, 0x65, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x2b, 0x2e, 0x74, 0x72, 0x70,
	0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x70, 0x6c, 0x75, 0x67, 0x69, 0x6e, 0x5f, 0x63, 0x6f, 0x6e,
	0x66, 0x69, 0x67, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x41, 0x75, 0x74, 0x68, 0x54,
	0x79, 0x70, 0x65, 0x45, 0x6e, 0x75, 0x6d, 0x52, 0x08, 0x41, 0x75, 0x74, 0x68, 0x54, 0x79, 0x70,
	0x65, 0x12, 0x43, 0x0a, 0x08, 0x41, 0x75, 0x74, 0x68, 0x49, 0x6e, 0x66, 0x6f, 0x18, 0x0c, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x27, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x70,
	0x6c, 0x75, 0x67, 0x69, 0x6e, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x73, 0x65, 0x72,
	0x76, 0x65, 0x72, 0x2e, 0x41, 0x75, 0x74, 0x68, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x08, 0x41, 0x75,
	0x74, 0x68, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x12, 0x0a, 0x04, 0x43, 0x6f, 0x64, 0x65, 0x18, 0x0d,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x4d, 0x0a, 0x0a, 0x43, 0x72,
	0x65, 0x61, 0x74, 0x65, 0x54, 0x79, 0x70, 0x65, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x2d,
	0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x70, 0x6c, 0x75, 0x67, 0x69, 0x6e,
	0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x43,
	0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x79, 0x70, 0x65, 0x45, 0x6e, 0x75, 0x6d, 0x52, 0x0a, 0x43,
	0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x24, 0x0a, 0x0d, 0x49, 0x73, 0x53,
	0x74, 0x72, 0x65, 0x61, 0x6d, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x08,
	0x52, 0x0d, 0x49, 0x73, 0x53, 0x74, 0x72, 0x65, 0x61, 0x6d, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22,
	0x44, 0x0a, 0x0c, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x54, 0x6f, 0x6f, 0x6c, 0x52, 0x73, 0x70, 0x12,
	0x16, 0x0a, 0x06, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x06, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x1c, 0x0a, 0x09, 0x52, 0x61, 0x77, 0x52, 0x65,
	0x73, 0x75, 0x6c, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x52, 0x61, 0x77, 0x52,
	0x65, 0x73, 0x75, 0x6c, 0x74, 0x22, 0x3c, 0x0a, 0x08, 0x55, 0x73, 0x65, 0x72, 0x49, 0x6e, 0x66,
	0x6f, 0x12, 0x12, 0x0a, 0x04, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x04, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1c, 0x0a, 0x09, 0x41, 0x76, 0x61, 0x74, 0x61, 0x72, 0x55,
	0x72, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x41, 0x76, 0x61, 0x74, 0x61, 0x72,
	0x55, 0x72, 0x6c, 0x22, 0xa3, 0x07, 0x0a, 0x0a, 0x50, 0x6c, 0x75, 0x67, 0x69, 0x6e, 0x49, 0x6e,
	0x66, 0x6f, 0x12, 0x1a, 0x0a, 0x08, 0x50, 0x6c, 0x75, 0x67, 0x69, 0x6e, 0x49, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x50, 0x6c, 0x75, 0x67, 0x69, 0x6e, 0x49, 0x64, 0x12, 0x12,
	0x0a, 0x04, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x4e, 0x61,
	0x6d, 0x65, 0x12, 0x24, 0x0a, 0x0d, 0x50, 0x6c, 0x75, 0x67, 0x69, 0x6e, 0x56, 0x65, 0x72, 0x73,
	0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0d, 0x50, 0x6c, 0x75, 0x67, 0x69,
	0x6e, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x12, 0x0a, 0x04, 0x44, 0x65, 0x73, 0x63,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x44, 0x65, 0x73, 0x63, 0x12, 0x18, 0x0a, 0x07,
	0x49, 0x63, 0x6f, 0x6e, 0x55, 0x72, 0x6c, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x49,
	0x63, 0x6f, 0x6e, 0x55, 0x72, 0x6c, 0x12, 0x47, 0x0a, 0x08, 0x41, 0x75, 0x74, 0x68, 0x54, 0x79,
	0x70, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x2b, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e,
	0x4b, 0x45, 0x50, 0x2e, 0x70, 0x6c, 0x75, 0x67, 0x69, 0x6e, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69,
	0x67, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x41, 0x75, 0x74, 0x68, 0x54, 0x79, 0x70,
	0x65, 0x45, 0x6e, 0x75, 0x6d, 0x52, 0x08, 0x41, 0x75, 0x74, 0x68, 0x54, 0x79, 0x70, 0x65, 0x12,
	0x43, 0x0a, 0x08, 0x41, 0x75, 0x74, 0x68, 0x49, 0x6e, 0x66, 0x6f, 0x18, 0x07, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x27, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x70, 0x6c, 0x75,
	0x67, 0x69, 0x6e, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65,
	0x72, 0x2e, 0x41, 0x75, 0x74, 0x68, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x08, 0x41, 0x75, 0x74, 0x68,
	0x49, 0x6e, 0x66, 0x6f, 0x12, 0x18, 0x0a, 0x07, 0x4f, 0x70, 0x65, 0x6e, 0x41, 0x70, 0x69, 0x18,
	0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x4f, 0x70, 0x65, 0x6e, 0x41, 0x70, 0x69, 0x12, 0x43,
	0x0a, 0x08, 0x55, 0x73, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x27, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x70, 0x6c, 0x75, 0x67,
	0x69, 0x6e, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72,
	0x2e, 0x55, 0x73, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x08, 0x55, 0x73, 0x65, 0x72, 0x49,
	0x6e, 0x66, 0x6f, 0x12, 0x4d, 0x0a, 0x0a, 0x50, 0x6c, 0x75, 0x67, 0x69, 0x6e, 0x54, 0x79, 0x70,
	0x65, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x2d, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b,
	0x45, 0x50, 0x2e, 0x70, 0x6c, 0x75, 0x67, 0x69, 0x6e, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67,
	0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x50, 0x6c, 0x75, 0x67, 0x69, 0x6e, 0x54, 0x79,
	0x70, 0x65, 0x45, 0x6e, 0x75, 0x6d, 0x52, 0x0a, 0x50, 0x6c, 0x75, 0x67, 0x69, 0x6e, 0x54, 0x79,
	0x70, 0x65, 0x12, 0x3d, 0x0a, 0x05, 0x54, 0x6f, 0x6f, 0x6c, 0x73, 0x18, 0x0b, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x27, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x70, 0x6c, 0x75,
	0x67, 0x69, 0x6e, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65,
	0x72, 0x2e, 0x54, 0x6f, 0x6f, 0x6c, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x05, 0x54, 0x6f, 0x6f, 0x6c,
	0x73, 0x12, 0x1e, 0x0a, 0x0a, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x18,
	0x0c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d,
	0x65, 0x12, 0x1e, 0x0a, 0x0a, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x18,
	0x0d, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d,
	0x65, 0x12, 0x50, 0x0a, 0x0b, 0x46, 0x69, 0x6e, 0x61, 0x6e, 0x63, 0x65, 0x54, 0x79, 0x70, 0x65,
	0x18, 0x0e, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x2e, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45,
	0x50, 0x2e, 0x70, 0x6c, 0x75, 0x67, 0x69, 0x6e, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f,
	0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x46, 0x69, 0x6e, 0x61, 0x6e, 0x63, 0x65, 0x54, 0x79,
	0x70, 0x65, 0x45, 0x6e, 0x75, 0x6d, 0x52, 0x0b, 0x46, 0x69, 0x6e, 0x61, 0x6e, 0x63, 0x65, 0x54,
	0x79, 0x70, 0x65, 0x12, 0x4d, 0x0a, 0x0a, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x79, 0x70,
	0x65, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x2d, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b,
	0x45, 0x50, 0x2e, 0x70, 0x6c, 0x75, 0x67, 0x69, 0x6e, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67,
	0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x79,
	0x70, 0x65, 0x45, 0x6e, 0x75, 0x6d, 0x52, 0x0a, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x79,
	0x70, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x10, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x06, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x45, 0x0a, 0x07, 0x48, 0x65,
	0x61, 0x64, 0x65, 0x72, 0x73, 0x18, 0x11, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2b, 0x2e, 0x74, 0x72,
	0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x70, 0x6c, 0x75, 0x67, 0x69, 0x6e, 0x5f, 0x63, 0x6f,
	0x6e, 0x66, 0x69, 0x67, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x50, 0x6c, 0x75, 0x67,
	0x69, 0x6e, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x07, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72,
	0x73, 0x12, 0x56, 0x0a, 0x0d, 0x57, 0x68, 0x69, 0x74, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x54, 0x79,
	0x70, 0x65, 0x18, 0x12, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x30, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e,
	0x4b, 0x45, 0x50, 0x2e, 0x70, 0x6c, 0x75, 0x67, 0x69, 0x6e, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69,
	0x67, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x57, 0x68, 0x69, 0x74, 0x65, 0x4c, 0x69,
	0x73, 0x74, 0x54, 0x79, 0x70, 0x65, 0x45, 0x6e, 0x75, 0x6d, 0x52, 0x0d, 0x57, 0x68, 0x69, 0x74,
	0x65, 0x4c, 0x69, 0x73, 0x74, 0x54, 0x79, 0x70, 0x65, 0x22, 0xfa, 0x04, 0x0a, 0x08, 0x54, 0x6f,
	0x6f, 0x6c, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x16, 0x0a, 0x06, 0x54, 0x6f, 0x6f, 0x6c, 0x49, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x54, 0x6f, 0x6f, 0x6c, 0x49, 0x64, 0x12, 0x1a,
	0x0a, 0x08, 0x50, 0x6c, 0x75, 0x67, 0x69, 0x6e, 0x49, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x08, 0x50, 0x6c, 0x75, 0x67, 0x69, 0x6e, 0x49, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x4e, 0x61,
	0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x12,
	0x0a, 0x04, 0x44, 0x65, 0x73, 0x63, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x44, 0x65,
	0x73, 0x63, 0x12, 0x10, 0x0a, 0x03, 0x55, 0x72, 0x6c, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x03, 0x55, 0x72, 0x6c, 0x12, 0x12, 0x0a, 0x04, 0x50, 0x61, 0x74, 0x68, 0x18, 0x06, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x04, 0x50, 0x61, 0x74, 0x68, 0x12, 0x16, 0x0a, 0x06, 0x4d, 0x65, 0x74, 0x68,
	0x6f, 0x64, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x4d, 0x65, 0x74, 0x68, 0x6f, 0x64,
	0x12, 0x43, 0x0a, 0x06, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x08, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x2b, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x70, 0x6c, 0x75, 0x67,
	0x69, 0x6e, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72,
	0x2e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x52, 0x06, 0x48,
	0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x41, 0x0a, 0x05, 0x51, 0x75, 0x65, 0x72, 0x79, 0x18, 0x09,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x2b, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e,
	0x70, 0x6c, 0x75, 0x67, 0x69, 0x6e, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x73, 0x65,
	0x72, 0x76, 0x65, 0x72, 0x2e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x50, 0x61, 0x72, 0x61,
	0x6d, 0x52, 0x05, 0x51, 0x75, 0x65, 0x72, 0x79, 0x12, 0x3f, 0x0a, 0x04, 0x42, 0x6f, 0x64, 0x79,
	0x18, 0x0a, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2b, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45,
	0x50, 0x2e, 0x70, 0x6c, 0x75, 0x67, 0x69, 0x6e, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f,
	0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x50, 0x61,
	0x72, 0x61, 0x6d, 0x52, 0x04, 0x42, 0x6f, 0x64, 0x79, 0x12, 0x46, 0x0a, 0x07, 0x4f, 0x75, 0x74,
	0x70, 0x75, 0x74, 0x73, 0x18, 0x0b, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2c, 0x2e, 0x74, 0x72, 0x70,
	0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x70, 0x6c, 0x75, 0x67, 0x69, 0x6e, 0x5f, 0x63, 0x6f, 0x6e,
	0x66, 0x69, 0x67, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x52, 0x07, 0x4f, 0x75, 0x74, 0x70, 0x75, 0x74,
	0x73, 0x12, 0x43, 0x0a, 0x06, 0x49, 0x6e, 0x70, 0x75, 0x74, 0x73, 0x18, 0x0c, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x2b, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x70, 0x6c, 0x75,
	0x67, 0x69, 0x6e, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65,
	0x72, 0x2e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x52, 0x06,
	0x49, 0x6e, 0x70, 0x75, 0x74, 0x73, 0x12, 0x44, 0x0a, 0x07, 0x45, 0x78, 0x61, 0x6d, 0x70, 0x6c,
	0x65, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2a, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b,
	0x45, 0x50, 0x2e, 0x70, 0x6c, 0x75, 0x67, 0x69, 0x6e, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67,
	0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x54, 0x6f, 0x6f, 0x6c, 0x45, 0x78, 0x61, 0x6d,
	0x70, 0x6c, 0x65, 0x52, 0x07, 0x45, 0x78, 0x61, 0x6d, 0x70, 0x6c, 0x65, 0x12, 0x12, 0x0a, 0x04,
	0x43, 0x6f, 0x64, 0x65, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x43, 0x6f, 0x64, 0x65,
	0x12, 0x24, 0x0a, 0x0d, 0x49, 0x73, 0x53, 0x74, 0x72, 0x65, 0x61, 0x6d, 0x52, 0x65, 0x70, 0x6c,
	0x79, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0d, 0x49, 0x73, 0x53, 0x74, 0x72, 0x65, 0x61,
	0x6d, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0xe1, 0x01, 0x0a, 0x08, 0x41, 0x75, 0x74, 0x68, 0x49,
	0x6e, 0x66, 0x6f, 0x12, 0x5d, 0x0a, 0x0b, 0x4b, 0x65, 0x79, 0x4c, 0x6f, 0x63, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x3b, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e,
	0x4b, 0x45, 0x50, 0x2e, 0x70, 0x6c, 0x75, 0x67, 0x69, 0x6e, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69,
	0x67, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x41, 0x75, 0x74, 0x68, 0x49, 0x6e, 0x66,
	0x6f, 0x2e, 0x4b, 0x65, 0x79, 0x4c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70,
	0x65, 0x45, 0x6e, 0x75, 0x6d, 0x52, 0x0b, 0x4b, 0x65, 0x79, 0x4c, 0x6f, 0x63, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x12, 0x22, 0x0a, 0x0c, 0x4b, 0x65, 0x79, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x4e, 0x61,
	0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x4b, 0x65, 0x79, 0x50, 0x61, 0x72,
	0x61, 0x6d, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x24, 0x0a, 0x0d, 0x4b, 0x65, 0x79, 0x50, 0x61, 0x72,
	0x61, 0x6d, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x4b,
	0x65, 0x79, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x22, 0x2c, 0x0a, 0x13,
	0x4b, 0x65, 0x79, 0x4c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x45,
	0x6e, 0x75, 0x6d, 0x12, 0x0a, 0x0a, 0x06, 0x48, 0x45, 0x41, 0x44, 0x45, 0x52, 0x10, 0x00, 0x12,
	0x09, 0x0a, 0x05, 0x51, 0x55, 0x45, 0x52, 0x59, 0x10, 0x01, 0x22, 0x45, 0x0a, 0x0f, 0x4c, 0x69,
	0x73, 0x74, 0x54, 0x6f, 0x6f, 0x6c, 0x52, 0x65, 0x66, 0x73, 0x52, 0x65, 0x71, 0x12, 0x16, 0x0a,
	0x06, 0x54, 0x6f, 0x6f, 0x6c, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x54,
	0x6f, 0x6f, 0x6c, 0x49, 0x64, 0x12, 0x1a, 0x0a, 0x08, 0x50, 0x6c, 0x75, 0x67, 0x69, 0x6e, 0x49,
	0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x50, 0x6c, 0x75, 0x67, 0x69, 0x6e, 0x49,
	0x64, 0x22, 0x55, 0x0a, 0x0f, 0x4c, 0x69, 0x73, 0x74, 0x54, 0x6f, 0x6f, 0x6c, 0x52, 0x65, 0x66,
	0x73, 0x52, 0x73, 0x70, 0x12, 0x42, 0x0a, 0x08, 0x54, 0x6f, 0x6f, 0x6c, 0x52, 0x65, 0x66, 0x73,
	0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x26, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45,
	0x50, 0x2e, 0x70, 0x6c, 0x75, 0x67, 0x69, 0x6e, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f,
	0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x54, 0x6f, 0x6f, 0x6c, 0x52, 0x65, 0x66, 0x52, 0x08,
	0x54, 0x6f, 0x6f, 0x6c, 0x52, 0x65, 0x66, 0x73, 0x22, 0xb5, 0x02, 0x0a, 0x07, 0x54, 0x6f, 0x6f,
	0x6c, 0x52, 0x65, 0x66, 0x12, 0x16, 0x0a, 0x06, 0x54, 0x6f, 0x6f, 0x6c, 0x49, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x54, 0x6f, 0x6f, 0x6c, 0x49, 0x64, 0x12, 0x1a, 0x0a, 0x08,
	0x50, 0x6c, 0x75, 0x67, 0x69, 0x6e, 0x49, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08,
	0x50, 0x6c, 0x75, 0x67, 0x69, 0x6e, 0x49, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x41, 0x70, 0x70, 0x49,
	0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x41, 0x70, 0x70, 0x49, 0x64, 0x12, 0x18,
	0x0a, 0x07, 0x41, 0x70, 0x70, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x07, 0x41, 0x70, 0x70, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1e, 0x0a, 0x0a, 0x57, 0x6f, 0x72, 0x6b,
	0x66, 0x6c, 0x6f, 0x77, 0x49, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x57, 0x6f,
	0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x49, 0x64, 0x12, 0x22, 0x0a, 0x0c, 0x57, 0x6f, 0x72, 0x6b,
	0x66, 0x6c, 0x6f, 0x77, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c,
	0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x4c, 0x0a, 0x07,
	0x52, 0x65, 0x66, 0x54, 0x79, 0x70, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x32, 0x2e,
	0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x70, 0x6c, 0x75, 0x67, 0x69, 0x6e, 0x5f,
	0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x54, 0x6f,
	0x6f, 0x6c, 0x52, 0x65, 0x66, 0x2e, 0x52, 0x65, 0x66, 0x54, 0x79, 0x70, 0x65, 0x45, 0x6e, 0x75,
	0x6d, 0x52, 0x07, 0x52, 0x65, 0x66, 0x54, 0x79, 0x70, 0x65, 0x22, 0x34, 0x0a, 0x0b, 0x52, 0x65,
	0x66, 0x54, 0x79, 0x70, 0x65, 0x45, 0x6e, 0x75, 0x6d, 0x12, 0x09, 0x0a, 0x05, 0x41, 0x47, 0x45,
	0x4e, 0x54, 0x10, 0x00, 0x12, 0x0c, 0x0a, 0x08, 0x57, 0x4f, 0x52, 0x4b, 0x46, 0x4c, 0x4f, 0x57,
	0x10, 0x01, 0x12, 0x0c, 0x0a, 0x08, 0x53, 0x54, 0x41, 0x4e, 0x44, 0x41, 0x52, 0x44, 0x10, 0x02,
	0x22, 0x5f, 0x0a, 0x0d, 0x41, 0x64, 0x64, 0x41, 0x70, 0x70, 0x54, 0x6f, 0x6f, 0x6c, 0x52, 0x65,
	0x71, 0x12, 0x1a, 0x0a, 0x08, 0x41, 0x70, 0x70, 0x42, 0x69, 0x7a, 0x49, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x08, 0x41, 0x70, 0x70, 0x42, 0x69, 0x7a, 0x49, 0x64, 0x12, 0x1a, 0x0a,
	0x08, 0x50, 0x6c, 0x75, 0x67, 0x69, 0x6e, 0x49, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x08, 0x50, 0x6c, 0x75, 0x67, 0x69, 0x6e, 0x49, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x54, 0x6f, 0x6f,
	0x6c, 0x49, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x54, 0x6f, 0x6f, 0x6c, 0x49,
	0x64, 0x22, 0x0f, 0x0a, 0x0d, 0x41, 0x64, 0x64, 0x41, 0x70, 0x70, 0x54, 0x6f, 0x6f, 0x6c, 0x52,
	0x73, 0x70, 0x22, 0x48, 0x0a, 0x10, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x41, 0x70, 0x70, 0x54,
	0x6f, 0x6f, 0x6c, 0x52, 0x65, 0x71, 0x12, 0x1a, 0x0a, 0x08, 0x41, 0x70, 0x70, 0x42, 0x69, 0x7a,
	0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x41, 0x70, 0x70, 0x42, 0x69, 0x7a,
	0x49, 0x64, 0x12, 0x18, 0x0a, 0x07, 0x54, 0x6f, 0x6f, 0x6c, 0x49, 0x64, 0x73, 0x18, 0x02, 0x20,
	0x03, 0x28, 0x09, 0x52, 0x07, 0x54, 0x6f, 0x6f, 0x6c, 0x49, 0x64, 0x73, 0x22, 0x12, 0x0a, 0x10,
	0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x41, 0x70, 0x70, 0x54, 0x6f, 0x6f, 0x6c, 0x52, 0x73, 0x70,
	0x22, 0xaa, 0x02, 0x0a, 0x0f, 0x41, 0x70, 0x70, 0x54, 0x6f, 0x6f, 0x6c, 0x52, 0x65, 0x71, 0x50,
	0x61, 0x72, 0x61, 0x6d, 0x12, 0x12, 0x0a, 0x04, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x04, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x44, 0x65, 0x73, 0x63,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x44, 0x65, 0x73, 0x63, 0x12, 0x3b, 0x0a, 0x04,
	0x54, 0x79, 0x70, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x27, 0x2e, 0x74, 0x72, 0x70,
	0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x70, 0x6c, 0x75, 0x67, 0x69, 0x6e, 0x5f, 0x63, 0x6f, 0x6e,
	0x66, 0x69, 0x67, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x54, 0x79, 0x70, 0x65, 0x45,
	0x6e, 0x75, 0x6d, 0x52, 0x04, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1e, 0x0a, 0x0a, 0x49, 0x73, 0x52,
	0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0a, 0x49,
	0x73, 0x52, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x12, 0x22, 0x0a, 0x0c, 0x44, 0x65, 0x66,
	0x61, 0x75, 0x6c, 0x74, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0c, 0x44, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x12, 0x4c, 0x0a,
	0x09, 0x53, 0x75, 0x62, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x18, 0x06, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x2e, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x70, 0x6c, 0x75, 0x67,
	0x69, 0x6e, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72,
	0x2e, 0x41, 0x70, 0x70, 0x54, 0x6f, 0x6f, 0x6c, 0x52, 0x65, 0x71, 0x50, 0x61, 0x72, 0x61, 0x6d,
	0x52, 0x09, 0x53, 0x75, 0x62, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x20, 0x0a, 0x0b, 0x41,
	0x67, 0x65, 0x6e, 0x74, 0x48, 0x69, 0x64, 0x64, 0x65, 0x6e, 0x18, 0x07, 0x20, 0x01, 0x28, 0x08,
	0x52, 0x0b, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x48, 0x69, 0x64, 0x64, 0x65, 0x6e, 0x22, 0xe6, 0x01,
	0x0a, 0x0f, 0x41, 0x70, 0x70, 0x54, 0x6f, 0x6f, 0x6c, 0x52, 0x73, 0x70, 0x50, 0x61, 0x72, 0x61,
	0x6d, 0x12, 0x12, 0x0a, 0x04, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x04, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x44, 0x65, 0x73, 0x63, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x04, 0x44, 0x65, 0x73, 0x63, 0x12, 0x3b, 0x0a, 0x04, 0x54, 0x79, 0x70,
	0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x27, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b,
	0x45, 0x50, 0x2e, 0x70, 0x6c, 0x75, 0x67, 0x69, 0x6e, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67,
	0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x54, 0x79, 0x70, 0x65, 0x45, 0x6e, 0x75, 0x6d,
	0x52, 0x04, 0x54, 0x79, 0x70, 0x65, 0x12, 0x4c, 0x0a, 0x09, 0x53, 0x75, 0x62, 0x50, 0x61, 0x72,
	0x61, 0x6d, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2e, 0x2e, 0x74, 0x72, 0x70, 0x63,
	0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x70, 0x6c, 0x75, 0x67, 0x69, 0x6e, 0x5f, 0x63, 0x6f, 0x6e, 0x66,
	0x69, 0x67, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x41, 0x70, 0x70, 0x54, 0x6f, 0x6f,
	0x6c, 0x52, 0x73, 0x70, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x52, 0x09, 0x53, 0x75, 0x62, 0x50, 0x61,
	0x72, 0x61, 0x6d, 0x73, 0x12, 0x20, 0x0a, 0x0b, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x48, 0x69, 0x64,
	0x64, 0x65, 0x6e, 0x18, 0x05, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0b, 0x41, 0x67, 0x65, 0x6e, 0x74,
	0x48, 0x69, 0x64, 0x64, 0x65, 0x6e, 0x22, 0x8e, 0x02, 0x0a, 0x0e, 0x53, 0x61, 0x76, 0x65, 0x41,
	0x70, 0x70, 0x54, 0x6f, 0x6f, 0x6c, 0x52, 0x65, 0x71, 0x12, 0x1a, 0x0a, 0x08, 0x41, 0x70, 0x70,
	0x42, 0x69, 0x7a, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x41, 0x70, 0x70,
	0x42, 0x69, 0x7a, 0x49, 0x64, 0x12, 0x1a, 0x0a, 0x08, 0x50, 0x6c, 0x75, 0x67, 0x69, 0x6e, 0x49,
	0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x50, 0x6c, 0x75, 0x67, 0x69, 0x6e, 0x49,
	0x64, 0x12, 0x16, 0x0a, 0x06, 0x54, 0x6f, 0x6f, 0x6c, 0x49, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x06, 0x54, 0x6f, 0x6f, 0x6c, 0x49, 0x64, 0x12, 0x1a, 0x0a, 0x08, 0x54, 0x6f, 0x6f,
	0x6c, 0x44, 0x65, 0x73, 0x63, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x54, 0x6f, 0x6f,
	0x6c, 0x44, 0x65, 0x73, 0x63, 0x12, 0x46, 0x0a, 0x06, 0x49, 0x6e, 0x70, 0x75, 0x74, 0x73, 0x18,
	0x05, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2e, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50,
	0x2e, 0x70, 0x6c, 0x75, 0x67, 0x69, 0x6e, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x73,
	0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x41, 0x70, 0x70, 0x54, 0x6f, 0x6f, 0x6c, 0x52, 0x65, 0x71,
	0x50, 0x61, 0x72, 0x61, 0x6d, 0x52, 0x06, 0x49, 0x6e, 0x70, 0x75, 0x74, 0x73, 0x12, 0x48, 0x0a,
	0x07, 0x4f, 0x75, 0x74, 0x70, 0x75, 0x74, 0x73, 0x18, 0x06, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2e,
	0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x70, 0x6c, 0x75, 0x67, 0x69, 0x6e,
	0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x41,
	0x70, 0x70, 0x54, 0x6f, 0x6f, 0x6c, 0x52, 0x73, 0x70, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x52, 0x07,
	0x4f, 0x75, 0x74, 0x70, 0x75, 0x74, 0x73, 0x22, 0x10, 0x0a, 0x0e, 0x53, 0x61, 0x76, 0x65, 0x41,
	0x70, 0x70, 0x54, 0x6f, 0x6f, 0x6c, 0x52, 0x73, 0x70, 0x22, 0x2d, 0x0a, 0x0f, 0x4c, 0x69, 0x73,
	0x74, 0x41, 0x70, 0x70, 0x54, 0x6f, 0x6f, 0x6c, 0x73, 0x52, 0x65, 0x71, 0x12, 0x1a, 0x0a, 0x08,
	0x41, 0x70, 0x70, 0x42, 0x69, 0x7a, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08,
	0x41, 0x70, 0x70, 0x42, 0x69, 0x7a, 0x49, 0x64, 0x22, 0x53, 0x0a, 0x0f, 0x4c, 0x69, 0x73, 0x74,
	0x41, 0x70, 0x70, 0x54, 0x6f, 0x6f, 0x6c, 0x73, 0x52, 0x73, 0x70, 0x12, 0x40, 0x0a, 0x05, 0x54,
	0x6f, 0x6f, 0x6c, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2a, 0x2e, 0x74, 0x72, 0x70,
	0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x70, 0x6c, 0x75, 0x67, 0x69, 0x6e, 0x5f, 0x63, 0x6f, 0x6e,
	0x66, 0x69, 0x67, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x41, 0x70, 0x70, 0x54, 0x6f,
	0x6f, 0x6c, 0x49, 0x74, 0x65, 0x6d, 0x52, 0x05, 0x54, 0x6f, 0x6f, 0x6c, 0x73, 0x22, 0xdc, 0x03,
	0x0a, 0x0b, 0x41, 0x70, 0x70, 0x54, 0x6f, 0x6f, 0x6c, 0x49, 0x74, 0x65, 0x6d, 0x12, 0x1a, 0x0a,
	0x08, 0x50, 0x6c, 0x75, 0x67, 0x69, 0x6e, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x08, 0x50, 0x6c, 0x75, 0x67, 0x69, 0x6e, 0x49, 0x64, 0x12, 0x1e, 0x0a, 0x0a, 0x50, 0x6c, 0x75,
	0x67, 0x69, 0x6e, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x50,
	0x6c, 0x75, 0x67, 0x69, 0x6e, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x49, 0x63, 0x6f,
	0x6e, 0x55, 0x72, 0x6c, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x49, 0x63, 0x6f, 0x6e,
	0x55, 0x72, 0x6c, 0x12, 0x16, 0x0a, 0x06, 0x54, 0x6f, 0x6f, 0x6c, 0x49, 0x64, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x06, 0x54, 0x6f, 0x6f, 0x6c, 0x49, 0x64, 0x12, 0x1a, 0x0a, 0x08, 0x54,
	0x6f, 0x6f, 0x6c, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x54,
	0x6f, 0x6f, 0x6c, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x54, 0x6f, 0x6f, 0x6c, 0x44,
	0x65, 0x73, 0x63, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x54, 0x6f, 0x6f, 0x6c, 0x44,
	0x65, 0x73, 0x63, 0x12, 0x46, 0x0a, 0x06, 0x49, 0x6e, 0x70, 0x75, 0x74, 0x73, 0x18, 0x07, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x2e, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x70,
	0x6c, 0x75, 0x67, 0x69, 0x6e, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x73, 0x65, 0x72,
	0x76, 0x65, 0x72, 0x2e, 0x41, 0x70, 0x70, 0x54, 0x6f, 0x6f, 0x6c, 0x52, 0x65, 0x71, 0x50, 0x61,
	0x72, 0x61, 0x6d, 0x52, 0x06, 0x49, 0x6e, 0x70, 0x75, 0x74, 0x73, 0x12, 0x2e, 0x0a, 0x12, 0x49,
	0x73, 0x42, 0x69, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x4b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67,
	0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x08, 0x52, 0x12, 0x49, 0x73, 0x42, 0x69, 0x6e, 0x64, 0x69,
	0x6e, 0x67, 0x4b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x12, 0x4d, 0x0a, 0x0a, 0x43,
	0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x79, 0x70, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0e, 0x32,
	0x2d, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x70, 0x6c, 0x75, 0x67, 0x69,
	0x6e, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e,
	0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x79, 0x70, 0x65, 0x45, 0x6e, 0x75, 0x6d, 0x52, 0x0a,
	0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x53, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x53, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x12, 0x48, 0x0a, 0x07, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x73, 0x18, 0x0b, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x2e, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x70,
	0x6c, 0x75, 0x67, 0x69, 0x6e, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x73, 0x65, 0x72,
	0x76, 0x65, 0x72, 0x2e, 0x41, 0x70, 0x70, 0x50, 0x6c, 0x75, 0x67, 0x69, 0x6e, 0x48, 0x65, 0x61,
	0x64, 0x65, 0x72, 0x52, 0x07, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x73, 0x22, 0x48, 0x0a, 0x12,
	0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x41, 0x70, 0x70, 0x54, 0x6f, 0x6f, 0x6c, 0x52,
	0x65, 0x71, 0x12, 0x1a, 0x0a, 0x08, 0x41, 0x70, 0x70, 0x42, 0x69, 0x7a, 0x49, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x41, 0x70, 0x70, 0x42, 0x69, 0x7a, 0x49, 0x64, 0x12, 0x16,
	0x0a, 0x06, 0x54, 0x6f, 0x6f, 0x6c, 0x49, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06,
	0x54, 0x6f, 0x6f, 0x6c, 0x49, 0x64, 0x22, 0xf6, 0x01, 0x0a, 0x12, 0x44, 0x65, 0x73, 0x63, 0x72,
	0x69, 0x62, 0x65, 0x41, 0x70, 0x70, 0x54, 0x6f, 0x6f, 0x6c, 0x52, 0x73, 0x70, 0x12, 0x16, 0x0a,
	0x06, 0x54, 0x6f, 0x6f, 0x6c, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x54,
	0x6f, 0x6f, 0x6c, 0x49, 0x64, 0x12, 0x1a, 0x0a, 0x08, 0x54, 0x6f, 0x6f, 0x6c, 0x4e, 0x61, 0x6d,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x54, 0x6f, 0x6f, 0x6c, 0x4e, 0x61, 0x6d,
	0x65, 0x12, 0x1a, 0x0a, 0x08, 0x54, 0x6f, 0x6f, 0x6c, 0x44, 0x65, 0x73, 0x63, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x08, 0x54, 0x6f, 0x6f, 0x6c, 0x44, 0x65, 0x73, 0x63, 0x12, 0x46, 0x0a,
	0x06, 0x49, 0x6e, 0x70, 0x75, 0x74, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2e, 0x2e,
	0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x70, 0x6c, 0x75, 0x67, 0x69, 0x6e, 0x5f,
	0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x41, 0x70,
	0x70, 0x54, 0x6f, 0x6f, 0x6c, 0x52, 0x65, 0x71, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x52, 0x06, 0x49,
	0x6e, 0x70, 0x75, 0x74, 0x73, 0x12, 0x48, 0x0a, 0x07, 0x4f, 0x75, 0x74, 0x70, 0x75, 0x74, 0x73,
	0x18, 0x05, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2e, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45,
	0x50, 0x2e, 0x70, 0x6c, 0x75, 0x67, 0x69, 0x6e, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f,
	0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x41, 0x70, 0x70, 0x54, 0x6f, 0x6f, 0x6c, 0x52, 0x73,
	0x70, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x52, 0x07, 0x4f, 0x75, 0x74, 0x70, 0x75, 0x74, 0x73, 0x22,
	0x71, 0x0a, 0x13, 0x4c, 0x69, 0x73, 0x74, 0x41, 0x70, 0x70, 0x54, 0x6f, 0x6f, 0x6c, 0x73, 0x49,
	0x6e, 0x66, 0x6f, 0x52, 0x65, 0x71, 0x12, 0x1a, 0x0a, 0x08, 0x41, 0x70, 0x70, 0x42, 0x69, 0x7a,
	0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x41, 0x70, 0x70, 0x42, 0x69, 0x7a,
	0x49, 0x64, 0x12, 0x3e, 0x0a, 0x06, 0x45, 0x6e, 0x76, 0x54, 0x61, 0x67, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x26, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x70, 0x6c,
	0x75, 0x67, 0x69, 0x6e, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x73, 0x65, 0x72, 0x76,
	0x65, 0x72, 0x2e, 0x45, 0x6e, 0x76, 0x54, 0x79, 0x70, 0x65, 0x52, 0x06, 0x45, 0x6e, 0x76, 0x54,
	0x61, 0x67, 0x22, 0x57, 0x0a, 0x13, 0x4c, 0x69, 0x73, 0x74, 0x41, 0x70, 0x70, 0x54, 0x6f, 0x6f,
	0x6c, 0x73, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x73, 0x70, 0x12, 0x40, 0x0a, 0x05, 0x54, 0x6f, 0x6f,
	0x6c, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2a, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e,
	0x4b, 0x45, 0x50, 0x2e, 0x70, 0x6c, 0x75, 0x67, 0x69, 0x6e, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69,
	0x67, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x41, 0x70, 0x70, 0x54, 0x6f, 0x6f, 0x6c,
	0x49, 0x6e, 0x66, 0x6f, 0x52, 0x05, 0x54, 0x6f, 0x6f, 0x6c, 0x73, 0x22, 0x4f, 0x0a, 0x0f, 0x41,
	0x70, 0x70, 0x50, 0x6c, 0x75, 0x67, 0x69, 0x6e, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x1c,
	0x0a, 0x09, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x09, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1e, 0x0a, 0x0a,
	0x50, 0x61, 0x72, 0x61, 0x6d, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0a, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x22, 0x57, 0x0a, 0x1d,
	0x47, 0x65, 0x74, 0x41, 0x70, 0x70, 0x50, 0x6c, 0x75, 0x67, 0x69, 0x6e, 0x52, 0x65, 0x71, 0x75,
	0x69, 0x72, 0x65, 0x64, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x65, 0x71, 0x12, 0x1a, 0x0a,
	0x08, 0x41, 0x70, 0x70, 0x42, 0x69, 0x7a, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x08, 0x41, 0x70, 0x70, 0x42, 0x69, 0x7a, 0x49, 0x64, 0x12, 0x1a, 0x0a, 0x08, 0x50, 0x6c, 0x75,
	0x67, 0x69, 0x6e, 0x49, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x50, 0x6c, 0x75,
	0x67, 0x69, 0x6e, 0x49, 0x64, 0x22, 0x69, 0x0a, 0x1d, 0x47, 0x65, 0x74, 0x41, 0x70, 0x70, 0x50,
	0x6c, 0x75, 0x67, 0x69, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x48, 0x65, 0x61,
	0x64, 0x65, 0x72, 0x52, 0x73, 0x70, 0x12, 0x48, 0x0a, 0x07, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72,
	0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2e, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b,
	0x45, 0x50, 0x2e, 0x70, 0x6c, 0x75, 0x67, 0x69, 0x6e, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67,
	0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x41, 0x70, 0x70, 0x50, 0x6c, 0x75, 0x67, 0x69,
	0x6e, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x07, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x73,
	0x22, 0x4e, 0x0a, 0x14, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x41, 0x70, 0x70, 0x50,
	0x6c, 0x75, 0x67, 0x69, 0x6e, 0x52, 0x65, 0x71, 0x12, 0x1a, 0x0a, 0x08, 0x41, 0x70, 0x70, 0x42,
	0x69, 0x7a, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x41, 0x70, 0x70, 0x42,
	0x69, 0x7a, 0x49, 0x64, 0x12, 0x1a, 0x0a, 0x08, 0x50, 0x6c, 0x75, 0x67, 0x69, 0x6e, 0x49, 0x64,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x50, 0x6c, 0x75, 0x67, 0x69, 0x6e, 0x49, 0x64,
	0x22, 0x60, 0x0a, 0x14, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x41, 0x70, 0x70, 0x50,
	0x6c, 0x75, 0x67, 0x69, 0x6e, 0x52, 0x73, 0x70, 0x12, 0x48, 0x0a, 0x07, 0x48, 0x65, 0x61, 0x64,
	0x65, 0x72, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2e, 0x2e, 0x74, 0x72, 0x70, 0x63,
	0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x70, 0x6c, 0x75, 0x67, 0x69, 0x6e, 0x5f, 0x63, 0x6f, 0x6e, 0x66,
	0x69, 0x67, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x41, 0x70, 0x70, 0x50, 0x6c, 0x75,
	0x67, 0x69, 0x6e, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x07, 0x48, 0x65, 0x61, 0x64, 0x65,
	0x72, 0x73, 0x22, 0xac, 0x01, 0x0a, 0x10, 0x53, 0x61, 0x76, 0x65, 0x41, 0x70, 0x70, 0x50, 0x6c,
	0x75, 0x67, 0x69, 0x6e, 0x52, 0x65, 0x71, 0x12, 0x1a, 0x0a, 0x08, 0x41, 0x70, 0x70, 0x42, 0x69,
	0x7a, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x41, 0x70, 0x70, 0x42, 0x69,
	0x7a, 0x49, 0x64, 0x12, 0x1a, 0x0a, 0x08, 0x50, 0x6c, 0x75, 0x67, 0x69, 0x6e, 0x49, 0x64, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x50, 0x6c, 0x75, 0x67, 0x69, 0x6e, 0x49, 0x64, 0x12,
	0x16, 0x0a, 0x06, 0x54, 0x6f, 0x6f, 0x6c, 0x49, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x06, 0x54, 0x6f, 0x6f, 0x6c, 0x49, 0x64, 0x12, 0x48, 0x0a, 0x07, 0x48, 0x65, 0x61, 0x64, 0x65,
	0x72, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2e, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e,
	0x4b, 0x45, 0x50, 0x2e, 0x70, 0x6c, 0x75, 0x67, 0x69, 0x6e, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69,
	0x67, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x41, 0x70, 0x70, 0x50, 0x6c, 0x75, 0x67,
	0x69, 0x6e, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x07, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72,
	0x73, 0x22, 0x12, 0x0a, 0x10, 0x53, 0x61, 0x76, 0x65, 0x41, 0x70, 0x70, 0x50, 0x6c, 0x75, 0x67,
	0x69, 0x6e, 0x52, 0x73, 0x70, 0x22, 0xaf, 0x04, 0x0a, 0x0b, 0x41, 0x70, 0x70, 0x54, 0x6f, 0x6f,
	0x6c, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x1a, 0x0a, 0x08, 0x50, 0x6c, 0x75, 0x67, 0x69, 0x6e, 0x49,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x50, 0x6c, 0x75, 0x67, 0x69, 0x6e, 0x49,
	0x64, 0x12, 0x1e, 0x0a, 0x0a, 0x50, 0x6c, 0x75, 0x67, 0x69, 0x6e, 0x4e, 0x61, 0x6d, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x50, 0x6c, 0x75, 0x67, 0x69, 0x6e, 0x4e, 0x61, 0x6d,
	0x65, 0x12, 0x18, 0x0a, 0x07, 0x49, 0x63, 0x6f, 0x6e, 0x55, 0x72, 0x6c, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x07, 0x49, 0x63, 0x6f, 0x6e, 0x55, 0x72, 0x6c, 0x12, 0x4d, 0x0a, 0x0a, 0x50,
	0x6c, 0x75, 0x67, 0x69, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32,
	0x2d, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x70, 0x6c, 0x75, 0x67, 0x69,
	0x6e, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e,
	0x50, 0x6c, 0x75, 0x67, 0x69, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x45, 0x6e, 0x75, 0x6d, 0x52, 0x0a,
	0x50, 0x6c, 0x75, 0x67, 0x69, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x54, 0x6f,
	0x6f, 0x6c, 0x49, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x54, 0x6f, 0x6f, 0x6c,
	0x49, 0x64, 0x12, 0x1a, 0x0a, 0x08, 0x54, 0x6f, 0x6f, 0x6c, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x06,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x54, 0x6f, 0x6f, 0x6c, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1a,
	0x0a, 0x08, 0x54, 0x6f, 0x6f, 0x6c, 0x44, 0x65, 0x73, 0x63, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x08, 0x54, 0x6f, 0x6f, 0x6c, 0x44, 0x65, 0x73, 0x63, 0x12, 0x46, 0x0a, 0x06, 0x49, 0x6e,
	0x70, 0x75, 0x74, 0x73, 0x18, 0x08, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2e, 0x2e, 0x74, 0x72, 0x70,
	0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x70, 0x6c, 0x75, 0x67, 0x69, 0x6e, 0x5f, 0x63, 0x6f, 0x6e,
	0x66, 0x69, 0x67, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x41, 0x70, 0x70, 0x54, 0x6f,
	0x6f, 0x6c, 0x52, 0x65, 0x71, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x52, 0x06, 0x49, 0x6e, 0x70, 0x75,
	0x74, 0x73, 0x12, 0x48, 0x0a, 0x07, 0x4f, 0x75, 0x74, 0x70, 0x75, 0x74, 0x73, 0x18, 0x09, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x2e, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x70,
	0x6c, 0x75, 0x67, 0x69, 0x6e, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x73, 0x65, 0x72,
	0x76, 0x65, 0x72, 0x2e, 0x41, 0x70, 0x70, 0x54, 0x6f, 0x6f, 0x6c, 0x52, 0x73, 0x70, 0x50, 0x61,
	0x72, 0x61, 0x6d, 0x52, 0x07, 0x4f, 0x75, 0x74, 0x70, 0x75, 0x74, 0x73, 0x12, 0x4d, 0x0a, 0x0a,
	0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x79, 0x70, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0e,
	0x32, 0x2d, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x70, 0x6c, 0x75, 0x67,
	0x69, 0x6e, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72,
	0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x79, 0x70, 0x65, 0x45, 0x6e, 0x75, 0x6d, 0x52,
	0x0a, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x4a, 0x0a, 0x09, 0x4d,
	0x43, 0x50, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2c,
	0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x70, 0x6c, 0x75, 0x67, 0x69, 0x6e,
	0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x4d,
	0x43, 0x50, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x09, 0x4d, 0x43,
	0x50, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x22, 0x86, 0x02, 0x0a, 0x0d, 0x4d, 0x43, 0x50, 0x53,
	0x65, 0x72, 0x76, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x22, 0x0a, 0x0c, 0x4d, 0x63, 0x70,
	0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x55, 0x72, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0c, 0x4d, 0x63, 0x70, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x55, 0x72, 0x6c, 0x12, 0x53, 0x0a,
	0x07, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x39,
	0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x70, 0x6c, 0x75, 0x67, 0x69, 0x6e,
	0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x4d,
	0x43, 0x50, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x2e, 0x48, 0x65, 0x61,
	0x64, 0x65, 0x72, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x07, 0x48, 0x65, 0x61, 0x64, 0x65,
	0x72, 0x73, 0x12, 0x18, 0x0a, 0x07, 0x54, 0x69, 0x6d, 0x65, 0x6f, 0x75, 0x74, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x07, 0x54, 0x69, 0x6d, 0x65, 0x6f, 0x75, 0x74, 0x12, 0x26, 0x0a, 0x0e,
	0x53, 0x73, 0x65, 0x52, 0x65, 0x61, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x6f, 0x75, 0x74, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x0e, 0x53, 0x73, 0x65, 0x52, 0x65, 0x61, 0x64, 0x54, 0x69, 0x6d,
	0x65, 0x6f, 0x75, 0x74, 0x1a, 0x3a, 0x0a, 0x0c, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x73, 0x45,
	0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01,
	0x22, 0x33, 0x0a, 0x15, 0x41, 0x64, 0x64, 0x4b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65,
	0x51, 0x41, 0x54, 0x6f, 0x6f, 0x6c, 0x52, 0x65, 0x71, 0x12, 0x1a, 0x0a, 0x08, 0x41, 0x70, 0x70,
	0x42, 0x69, 0x7a, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x41, 0x70, 0x70,
	0x42, 0x69, 0x7a, 0x49, 0x64, 0x22, 0x17, 0x0a, 0x15, 0x41, 0x64, 0x64, 0x4b, 0x6e, 0x6f, 0x77,
	0x6c, 0x65, 0x64, 0x67, 0x65, 0x51, 0x41, 0x54, 0x6f, 0x6f, 0x6c, 0x52, 0x73, 0x70, 0x22, 0x99,
	0x03, 0x0a, 0x1c, 0x4c, 0x69, 0x73, 0x74, 0x41, 0x70, 0x70, 0x54, 0x6f, 0x6f, 0x6c, 0x52, 0x65,
	0x6c, 0x65, 0x61, 0x73, 0x65, 0x50, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x52, 0x65, 0x71, 0x12,
	0x4b, 0x0a, 0x08, 0x42, 0x6f, 0x74, 0x42, 0x69, 0x7a, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x04, 0x42, 0x2f, 0x92, 0xb8, 0x18, 0x2b, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x3a, 0x22, 0x72, 0x65,
	0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x7e, 0xe8, 0xaf, 0xb7, 0xe4, 0xbc, 0xa0, 0xe5, 0x85, 0xa5,
	0xe6, 0xad, 0xa3, 0xe7, 0xa1, 0xae, 0xe7, 0x9a, 0x84, 0xe5, 0xba, 0x94, 0xe7, 0x94, 0xa8, 0x49,
	0x44, 0x22, 0x52, 0x08, 0x42, 0x6f, 0x74, 0x42, 0x69, 0x7a, 0x49, 0x64, 0x12, 0x14, 0x0a, 0x05,
	0x51, 0x75, 0x65, 0x72, 0x79, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x51, 0x75, 0x65,
	0x72, 0x79, 0x12, 0x1c, 0x0a, 0x09, 0x53, 0x74, 0x61, 0x72, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x04, 0x52, 0x09, 0x53, 0x74, 0x61, 0x72, 0x74, 0x54, 0x69, 0x6d, 0x65,
	0x12, 0x18, 0x0a, 0x07, 0x45, 0x6e, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x04, 0x52, 0x07, 0x45, 0x6e, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x41, 0x63,
	0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x05, 0x20, 0x03, 0x28, 0x0d, 0x52, 0x07, 0x41, 0x63, 0x74,
	0x69, 0x6f, 0x6e, 0x73, 0x12, 0x1e, 0x0a, 0x0a, 0x50, 0x61, 0x67, 0x65, 0x4e, 0x75, 0x6d, 0x62,
	0x65, 0x72, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0a, 0x50, 0x61, 0x67, 0x65, 0x4e, 0x75,
	0x6d, 0x62, 0x65, 0x72, 0x12, 0x5a, 0x0a, 0x08, 0x50, 0x61, 0x67, 0x65, 0x53, 0x69, 0x7a, 0x65,
	0x18, 0x07, 0x20, 0x01, 0x28, 0x0d, 0x42, 0x3e, 0x92, 0xb8, 0x18, 0x3a, 0x76, 0x61, 0x6c, 0x69,
	0x64, 0x3a, 0x22, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x2c, 0x72, 0x61, 0x6e, 0x67,
	0x65, 0x28, 0x31, 0x7c, 0x32, 0x30, 0x30, 0x29, 0x7e, 0xe6, 0xaf, 0x8f, 0xe9, 0xa1, 0xb5, 0xe6,
	0x95, 0xb0, 0xe9, 0x87, 0x8f, 0xe5, 0x9c, 0xa8, 0x31, 0xe5, 0x88, 0xb0, 0x32, 0x30, 0x30, 0xe4,
	0xb9, 0x8b, 0xe9, 0x97, 0xb4, 0x22, 0x52, 0x08, 0x50, 0x61, 0x67, 0x65, 0x53, 0x69, 0x7a, 0x65,
	0x12, 0x22, 0x0a, 0x0c, 0x52, 0x65, 0x6c, 0x65, 0x61, 0x73, 0x65, 0x42, 0x69, 0x7a, 0x49, 0x64,
	0x18, 0x08, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0c, 0x52, 0x65, 0x6c, 0x65, 0x61, 0x73, 0x65, 0x42,
	0x69, 0x7a, 0x49, 0x64, 0x12, 0x24, 0x0a, 0x0d, 0x52, 0x65, 0x6c, 0x65, 0x61, 0x73, 0x65, 0x53,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x09, 0x20, 0x03, 0x28, 0x0d, 0x52, 0x0d, 0x52, 0x65, 0x6c,
	0x65, 0x61, 0x73, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x22, 0xbf, 0x02, 0x0a, 0x1c, 0x4c,
	0x69, 0x73, 0x74, 0x41, 0x70, 0x70, 0x54, 0x6f, 0x6f, 0x6c, 0x52, 0x65, 0x6c, 0x65, 0x61, 0x73,
	0x65, 0x50, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x52, 0x73, 0x70, 0x12, 0x14, 0x0a, 0x05, 0x54,
	0x6f, 0x74, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x05, 0x54, 0x6f, 0x74, 0x61,
	0x6c, 0x12, 0x57, 0x0a, 0x04, 0x4c, 0x69, 0x73, 0x74, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x43, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x70, 0x6c, 0x75, 0x67, 0x69,
	0x6e, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e,
	0x4c, 0x69, 0x73, 0x74, 0x41, 0x70, 0x70, 0x54, 0x6f, 0x6f, 0x6c, 0x52, 0x65, 0x6c, 0x65, 0x61,
	0x73, 0x65, 0x50, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x52, 0x73, 0x70, 0x2e, 0x41, 0x70, 0x70,
	0x54, 0x6f, 0x6f, 0x6c, 0x52, 0x04, 0x4c, 0x69, 0x73, 0x74, 0x1a, 0xaf, 0x01, 0x0a, 0x07, 0x41,
	0x70, 0x70, 0x54, 0x6f, 0x6f, 0x6c, 0x12, 0x16, 0x0a, 0x06, 0x54, 0x6f, 0x6f, 0x6c, 0x49, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x54, 0x6f, 0x6f, 0x6c, 0x49, 0x64, 0x12, 0x1a,
	0x0a, 0x08, 0x54, 0x6f, 0x6f, 0x6c, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x08, 0x54, 0x6f, 0x6f, 0x6c, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1e, 0x0a, 0x0a, 0x55, 0x70,
	0x64, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0a,
	0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x41, 0x63,
	0x74, 0x69, 0x6f, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x06, 0x41, 0x63, 0x74, 0x69,
	0x6f, 0x6e, 0x12, 0x1e, 0x0a, 0x0a, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x65, 0x73, 0x63,
	0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x65,
	0x73, 0x63, 0x12, 0x18, 0x0a, 0x07, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x06, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x07, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x22, 0x73, 0x0a, 0x13,
	0x53, 0x79, 0x6e, 0x63, 0x41, 0x70, 0x70, 0x54, 0x6f, 0x6f, 0x6c, 0x52, 0x65, 0x64, 0x69, 0x73,
	0x52, 0x65, 0x71, 0x12, 0x1c, 0x0a, 0x09, 0x41, 0x70, 0x70, 0x42, 0x69, 0x7a, 0x49, 0x64, 0x73,
	0x18, 0x01, 0x20, 0x03, 0x28, 0x09, 0x52, 0x09, 0x41, 0x70, 0x70, 0x42, 0x69, 0x7a, 0x49, 0x64,
	0x73, 0x12, 0x3e, 0x0a, 0x06, 0x45, 0x6e, 0x76, 0x54, 0x61, 0x67, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x26, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x70, 0x6c, 0x75,
	0x67, 0x69, 0x6e, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65,
	0x72, 0x2e, 0x45, 0x6e, 0x76, 0x54, 0x79, 0x70, 0x65, 0x52, 0x06, 0x45, 0x6e, 0x76, 0x54, 0x61,
	0x67, 0x22, 0x15, 0x0a, 0x13, 0x53, 0x79, 0x6e, 0x63, 0x41, 0x70, 0x70, 0x54, 0x6f, 0x6f, 0x6c,
	0x52, 0x65, 0x64, 0x69, 0x73, 0x52, 0x73, 0x70, 0x22, 0xbf, 0x02, 0x0a, 0x12, 0x43, 0x68, 0x65,
	0x63, 0x6b, 0x50, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x12,
	0x10, 0x0a, 0x03, 0x55, 0x69, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x55, 0x69,
	0x6e, 0x12, 0x54, 0x0a, 0x06, 0x4d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x3c, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x70, 0x6c, 0x75,
	0x67, 0x69, 0x6e, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65,
	0x72, 0x2e, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x50, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f,
	0x6e, 0x52, 0x65, 0x71, 0x2e, 0x4d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x45, 0x6e, 0x75, 0x6d, 0x52,
	0x06, 0x4d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x12, 0x4f, 0x0a, 0x04, 0x4c, 0x69, 0x73, 0x74, 0x18,
	0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x3b, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50,
	0x2e, 0x70, 0x6c, 0x75, 0x67, 0x69, 0x6e, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x73,
	0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x50, 0x65, 0x72, 0x6d, 0x69,
	0x73, 0x73, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x2e, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x49, 0x74,
	0x65, 0x6d, 0x52, 0x04, 0x4c, 0x69, 0x73, 0x74, 0x1a, 0x27, 0x0a, 0x09, 0x43, 0x68, 0x65, 0x63,
	0x6b, 0x49, 0x74, 0x65, 0x6d, 0x12, 0x1a, 0x0a, 0x08, 0x50, 0x6c, 0x75, 0x67, 0x69, 0x6e, 0x49,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x50, 0x6c, 0x75, 0x67, 0x69, 0x6e, 0x49,
	0x64, 0x22, 0x47, 0x0a, 0x0a, 0x4d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x45, 0x6e, 0x75, 0x6d, 0x12,
	0x12, 0x0a, 0x0e, 0x4d, 0x4f, 0x44, 0x55, 0x4c, 0x45, 0x5f, 0x55, 0x4e, 0x4b, 0x4e, 0x4f, 0x57,
	0x4e, 0x10, 0x00, 0x12, 0x10, 0x0a, 0x0c, 0x4d, 0x4f, 0x44, 0x55, 0x4c, 0x45, 0x5f, 0x41, 0x47,
	0x45, 0x4e, 0x54, 0x10, 0x01, 0x12, 0x13, 0x0a, 0x0f, 0x4d, 0x4f, 0x44, 0x55, 0x4c, 0x45, 0x5f,
	0x57, 0x4f, 0x52, 0x4b, 0x46, 0x4c, 0x4f, 0x57, 0x10, 0x02, 0x22, 0xa2, 0x02, 0x0a, 0x12, 0x43,
	0x68, 0x65, 0x63, 0x6b, 0x50, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x52, 0x73,
	0x70, 0x12, 0x52, 0x0a, 0x07, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x73, 0x18, 0x01, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x38, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x70, 0x6c,
	0x75, 0x67, 0x69, 0x6e, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x73, 0x65, 0x72, 0x76,
	0x65, 0x72, 0x2e, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x50, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69,
	0x6f, 0x6e, 0x52, 0x73, 0x70, 0x2e, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x52, 0x07, 0x52, 0x65,
	0x73, 0x75, 0x6c, 0x74, 0x73, 0x1a, 0x80, 0x01, 0x0a, 0x06, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74,
	0x12, 0x1a, 0x0a, 0x08, 0x50, 0x6c, 0x75, 0x67, 0x69, 0x6e, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x08, 0x50, 0x6c, 0x75, 0x67, 0x69, 0x6e, 0x49, 0x64, 0x12, 0x5a, 0x0a, 0x06,
	0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x42, 0x2e, 0x74,
	0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x70, 0x6c, 0x75, 0x67, 0x69, 0x6e, 0x5f, 0x63,
	0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x43, 0x68, 0x65,
	0x63, 0x6b, 0x50, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x52, 0x73, 0x70, 0x2e,
	0x50, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x52, 0x06, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x22, 0x35, 0x0a, 0x10, 0x50, 0x65, 0x72, 0x6d,
	0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x0b, 0x0a, 0x07,
	0x41, 0x4c, 0x4c, 0x4f, 0x57, 0x45, 0x44, 0x10, 0x00, 0x12, 0x14, 0x0a, 0x10, 0x4e, 0x4f, 0x54,
	0x5f, 0x49, 0x4e, 0x5f, 0x57, 0x48, 0x49, 0x54, 0x45, 0x4c, 0x49, 0x53, 0x54, 0x10, 0x01, 0x2a,
	0xaf, 0x01, 0x0a, 0x08, 0x54, 0x79, 0x70, 0x65, 0x45, 0x6e, 0x75, 0x6d, 0x12, 0x0a, 0x0a, 0x06,
	0x53, 0x54, 0x52, 0x49, 0x4e, 0x47, 0x10, 0x00, 0x12, 0x07, 0x0a, 0x03, 0x49, 0x4e, 0x54, 0x10,
	0x01, 0x12, 0x09, 0x0a, 0x05, 0x46, 0x4c, 0x4f, 0x41, 0x54, 0x10, 0x02, 0x12, 0x08, 0x0a, 0x04,
	0x42, 0x4f, 0x4f, 0x4c, 0x10, 0x03, 0x12, 0x0a, 0x0a, 0x06, 0x4f, 0x42, 0x4a, 0x45, 0x43, 0x54,
	0x10, 0x04, 0x12, 0x10, 0x0a, 0x0c, 0x41, 0x52, 0x52, 0x41, 0x59, 0x5f, 0x53, 0x54, 0x52, 0x49,
	0x4e, 0x47, 0x10, 0x05, 0x12, 0x0d, 0x0a, 0x09, 0x41, 0x52, 0x52, 0x41, 0x59, 0x5f, 0x49, 0x4e,
	0x54, 0x10, 0x06, 0x12, 0x0f, 0x0a, 0x0b, 0x41, 0x52, 0x52, 0x41, 0x59, 0x5f, 0x46, 0x4c, 0x4f,
	0x41, 0x54, 0x10, 0x07, 0x12, 0x0e, 0x0a, 0x0a, 0x41, 0x52, 0x52, 0x41, 0x59, 0x5f, 0x42, 0x4f,
	0x4f, 0x4c, 0x10, 0x08, 0x12, 0x10, 0x0a, 0x0c, 0x41, 0x52, 0x52, 0x41, 0x59, 0x5f, 0x4f, 0x42,
	0x4a, 0x45, 0x43, 0x54, 0x10, 0x09, 0x12, 0x08, 0x0a, 0x04, 0x4e, 0x55, 0x4c, 0x4c, 0x10, 0x63,
	0x12, 0x0f, 0x0a, 0x0b, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10,
	0x64, 0x2a, 0x3b, 0x0a, 0x0e, 0x50, 0x6c, 0x75, 0x67, 0x69, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x45,
	0x6e, 0x75, 0x6d, 0x12, 0x0a, 0x0a, 0x06, 0x43, 0x55, 0x53, 0x54, 0x4f, 0x4d, 0x10, 0x00, 0x12,
	0x0c, 0x0a, 0x08, 0x4f, 0x46, 0x46, 0x49, 0x43, 0x49, 0x41, 0x4c, 0x10, 0x01, 0x12, 0x0f, 0x0a,
	0x0b, 0x54, 0x48, 0x49, 0x52, 0x44, 0x5f, 0x50, 0x41, 0x52, 0x54, 0x59, 0x10, 0x02, 0x2a, 0x25,
	0x0a, 0x0c, 0x41, 0x75, 0x74, 0x68, 0x54, 0x79, 0x70, 0x65, 0x45, 0x6e, 0x75, 0x6d, 0x12, 0x08,
	0x0a, 0x04, 0x4e, 0x4f, 0x4e, 0x45, 0x10, 0x00, 0x12, 0x0b, 0x0a, 0x07, 0x41, 0x50, 0x49, 0x5f,
	0x4b, 0x45, 0x59, 0x10, 0x01, 0x2a, 0x25, 0x0a, 0x0f, 0x46, 0x69, 0x6e, 0x61, 0x6e, 0x63, 0x65,
	0x54, 0x79, 0x70, 0x65, 0x45, 0x6e, 0x75, 0x6d, 0x12, 0x08, 0x0a, 0x04, 0x46, 0x52, 0x45, 0x45,
	0x10, 0x00, 0x12, 0x08, 0x0a, 0x04, 0x50, 0x41, 0x49, 0x44, 0x10, 0x01, 0x2a, 0x30, 0x0a, 0x0e,
	0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x79, 0x70, 0x65, 0x45, 0x6e, 0x75, 0x6d, 0x12, 0x0b,
	0x0a, 0x07, 0x53, 0x45, 0x52, 0x56, 0x49, 0x43, 0x45, 0x10, 0x00, 0x12, 0x08, 0x0a, 0x04, 0x43,
	0x4f, 0x44, 0x45, 0x10, 0x01, 0x12, 0x07, 0x0a, 0x03, 0x4d, 0x43, 0x50, 0x10, 0x02, 0x2a, 0x1d,
	0x0a, 0x07, 0x45, 0x6e, 0x76, 0x54, 0x79, 0x70, 0x65, 0x12, 0x08, 0x0a, 0x04, 0x54, 0x45, 0x53,
	0x54, 0x10, 0x00, 0x12, 0x08, 0x0a, 0x04, 0x50, 0x52, 0x4f, 0x44, 0x10, 0x01, 0x2a, 0x45, 0x0a,
	0x11, 0x57, 0x68, 0x69, 0x74, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x54, 0x79, 0x70, 0x65, 0x45, 0x6e,
	0x75, 0x6d, 0x12, 0x08, 0x0a, 0x04, 0x4f, 0x50, 0x45, 0x4e, 0x10, 0x00, 0x12, 0x10, 0x0a, 0x0c,
	0x49, 0x4e, 0x5f, 0x57, 0x48, 0x49, 0x54, 0x45, 0x4c, 0x49, 0x53, 0x54, 0x10, 0x01, 0x12, 0x14,
	0x0a, 0x10, 0x4e, 0x4f, 0x54, 0x5f, 0x49, 0x4e, 0x5f, 0x57, 0x48, 0x49, 0x54, 0x45, 0x4c, 0x49,
	0x53, 0x54, 0x10, 0x02, 0x32, 0xe1, 0x12, 0x0a, 0x0c, 0x50, 0x6c, 0x75, 0x67, 0x69, 0x6e, 0x43,
	0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x6d, 0x0a, 0x0b, 0x4c, 0x69, 0x73, 0x74, 0x50, 0x6c, 0x75,
	0x67, 0x69, 0x6e, 0x73, 0x12, 0x2d, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e,
	0x70, 0x6c, 0x75, 0x67, 0x69, 0x6e, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x73, 0x65,
	0x72, 0x76, 0x65, 0x72, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x50, 0x6c, 0x75, 0x67, 0x69, 0x6e, 0x73,
	0x52, 0x65, 0x71, 0x1a, 0x2d, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x70,
	0x6c, 0x75, 0x67, 0x69, 0x6e, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x73, 0x65, 0x72,
	0x76, 0x65, 0x72, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x50, 0x6c, 0x75, 0x67, 0x69, 0x6e, 0x73, 0x52,
	0x73, 0x70, 0x22, 0x00, 0x12, 0x70, 0x0a, 0x0c, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x50, 0x6c,
	0x75, 0x67, 0x69, 0x6e, 0x12, 0x2e, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e,
	0x70, 0x6c, 0x75, 0x67, 0x69, 0x6e, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x73, 0x65,
	0x72, 0x76, 0x65, 0x72, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x50, 0x6c, 0x75, 0x67, 0x69,
	0x6e, 0x52, 0x65, 0x71, 0x1a, 0x2e, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e,
	0x70, 0x6c, 0x75, 0x67, 0x69, 0x6e, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x73, 0x65,
	0x72, 0x76, 0x65, 0x72, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x50, 0x6c, 0x75, 0x67, 0x69,
	0x6e, 0x52, 0x73, 0x70, 0x22, 0x00, 0x12, 0x70, 0x0a, 0x0c, 0x4d, 0x6f, 0x64, 0x69, 0x66, 0x79,
	0x50, 0x6c, 0x75, 0x67, 0x69, 0x6e, 0x12, 0x2e, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45,
	0x50, 0x2e, 0x70, 0x6c, 0x75, 0x67, 0x69, 0x6e, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f,
	0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x4d, 0x6f, 0x64, 0x69, 0x66, 0x79, 0x50, 0x6c, 0x75,
	0x67, 0x69, 0x6e, 0x52, 0x65, 0x71, 0x1a, 0x2e, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45,
	0x50, 0x2e, 0x70, 0x6c, 0x75, 0x67, 0x69, 0x6e, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f,
	0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x4d, 0x6f, 0x64, 0x69, 0x66, 0x79, 0x50, 0x6c, 0x75,
	0x67, 0x69, 0x6e, 0x52, 0x73, 0x70, 0x22, 0x00, 0x12, 0x79, 0x0a, 0x0f, 0x43, 0x72, 0x65, 0x61,
	0x74, 0x65, 0x4d, 0x43, 0x50, 0x50, 0x6c, 0x75, 0x67, 0x69, 0x6e, 0x12, 0x31, 0x2e, 0x74, 0x72,
	0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x70, 0x6c, 0x75, 0x67, 0x69, 0x6e, 0x5f, 0x63, 0x6f,
	0x6e, 0x66, 0x69, 0x67, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x43, 0x72, 0x65, 0x61,
	0x74, 0x65, 0x4d, 0x43, 0x50, 0x50, 0x6c, 0x75, 0x67, 0x69, 0x6e, 0x52, 0x65, 0x71, 0x1a, 0x31,
	0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x70, 0x6c, 0x75, 0x67, 0x69, 0x6e,
	0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x43,
	0x72, 0x65, 0x61, 0x74, 0x65, 0x4d, 0x43, 0x50, 0x50, 0x6c, 0x75, 0x67, 0x69, 0x6e, 0x52, 0x73,
	0x70, 0x22, 0x00, 0x12, 0x79, 0x0a, 0x0f, 0x4d, 0x6f, 0x64, 0x69, 0x66, 0x79, 0x4d, 0x43, 0x50,
	0x50, 0x6c, 0x75, 0x67, 0x69, 0x6e, 0x12, 0x31, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45,
	0x50, 0x2e, 0x70, 0x6c, 0x75, 0x67, 0x69, 0x6e, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f,
	0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x4d, 0x6f, 0x64, 0x69, 0x66, 0x79, 0x4d, 0x43, 0x50,
	0x50, 0x6c, 0x75, 0x67, 0x69, 0x6e, 0x52, 0x65, 0x71, 0x1a, 0x31, 0x2e, 0x74, 0x72, 0x70, 0x63,
	0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x70, 0x6c, 0x75, 0x67, 0x69, 0x6e, 0x5f, 0x63, 0x6f, 0x6e, 0x66,
	0x69, 0x67, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x4d, 0x6f, 0x64, 0x69, 0x66, 0x79,
	0x4d, 0x43, 0x50, 0x50, 0x6c, 0x75, 0x67, 0x69, 0x6e, 0x52, 0x73, 0x70, 0x22, 0x00, 0x12, 0x76,
	0x0a, 0x0e, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x4d, 0x43, 0x50, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72,
	0x12, 0x30, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x70, 0x6c, 0x75, 0x67,
	0x69, 0x6e, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72,
	0x2e, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x4d, 0x43, 0x50, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x52,
	0x65, 0x71, 0x1a, 0x30, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x70, 0x6c,
	0x75, 0x67, 0x69, 0x6e, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x73, 0x65, 0x72, 0x76,
	0x65, 0x72, 0x2e, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x4d, 0x43, 0x50, 0x53, 0x65, 0x72, 0x76, 0x65,
	0x72, 0x52, 0x73, 0x70, 0x22, 0x00, 0x12, 0x70, 0x0a, 0x0c, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65,
	0x50, 0x6c, 0x75, 0x67, 0x69, 0x6e, 0x12, 0x2e, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45,
	0x50, 0x2e, 0x70, 0x6c, 0x75, 0x67, 0x69, 0x6e, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f,
	0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x50, 0x6c, 0x75,
	0x67, 0x69, 0x6e, 0x52, 0x65, 0x71, 0x1a, 0x2e, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45,
	0x50, 0x2e, 0x70, 0x6c, 0x75, 0x67, 0x69, 0x6e, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f,
	0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x50, 0x6c, 0x75,
	0x67, 0x69, 0x6e, 0x52, 0x73, 0x70, 0x22, 0x00, 0x12, 0x76, 0x0a, 0x0e, 0x44, 0x65, 0x73, 0x63,
	0x72, 0x69, 0x62, 0x65, 0x50, 0x6c, 0x75, 0x67, 0x69, 0x6e, 0x12, 0x30, 0x2e, 0x74, 0x72, 0x70,
	0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x70, 0x6c, 0x75, 0x67, 0x69, 0x6e, 0x5f, 0x63, 0x6f, 0x6e,
	0x66, 0x69, 0x67, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x44, 0x65, 0x73, 0x63, 0x72,
	0x69, 0x62, 0x65, 0x50, 0x6c, 0x75, 0x67, 0x69, 0x6e, 0x52, 0x65, 0x71, 0x1a, 0x30, 0x2e, 0x74,
	0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x70, 0x6c, 0x75, 0x67, 0x69, 0x6e, 0x5f, 0x63,
	0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x44, 0x65, 0x73,
	0x63, 0x72, 0x69, 0x62, 0x65, 0x50, 0x6c, 0x75, 0x67, 0x69, 0x6e, 0x52, 0x73, 0x70, 0x22, 0x00,
	0x12, 0x67, 0x0a, 0x09, 0x4c, 0x69, 0x73, 0x74, 0x54, 0x6f, 0x6f, 0x6c, 0x73, 0x12, 0x2b, 0x2e,
	0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x70, 0x6c, 0x75, 0x67, 0x69, 0x6e, 0x5f,
	0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x4c, 0x69,
	0x73, 0x74, 0x54, 0x6f, 0x6f, 0x6c, 0x73, 0x52, 0x65, 0x71, 0x1a, 0x2b, 0x2e, 0x74, 0x72, 0x70,
	0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x70, 0x6c, 0x75, 0x67, 0x69, 0x6e, 0x5f, 0x63, 0x6f, 0x6e,
	0x66, 0x69, 0x67, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x54,
	0x6f, 0x6f, 0x6c, 0x73, 0x52, 0x73, 0x70, 0x22, 0x00, 0x12, 0x67, 0x0a, 0x09, 0x43, 0x68, 0x65,
	0x63, 0x6b, 0x54, 0x6f, 0x6f, 0x6c, 0x12, 0x2b, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45,
	0x50, 0x2e, 0x70, 0x6c, 0x75, 0x67, 0x69, 0x6e, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f,
	0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x54, 0x6f, 0x6f, 0x6c,
	0x52, 0x65, 0x71, 0x1a, 0x2b, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x70,
	0x6c, 0x75, 0x67, 0x69, 0x6e, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x73, 0x65, 0x72,
	0x76, 0x65, 0x72, 0x2e, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x54, 0x6f, 0x6f, 0x6c, 0x52, 0x73, 0x70,
	0x22, 0x00, 0x12, 0x70, 0x0a, 0x0c, 0x4c, 0x69, 0x73, 0x74, 0x54, 0x6f, 0x6f, 0x6c, 0x52, 0x65,
	0x66, 0x73, 0x12, 0x2e, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x70, 0x6c,
	0x75, 0x67, 0x69, 0x6e, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x73, 0x65, 0x72, 0x76,
	0x65, 0x72, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x54, 0x6f, 0x6f, 0x6c, 0x52, 0x65, 0x66, 0x73, 0x52,
	0x65, 0x71, 0x1a, 0x2e, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x70, 0x6c,
	0x75, 0x67, 0x69, 0x6e, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x73, 0x65, 0x72, 0x76,
	0x65, 0x72, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x54, 0x6f, 0x6f, 0x6c, 0x52, 0x65, 0x66, 0x73, 0x52,
	0x73, 0x70, 0x22, 0x00, 0x12, 0x6a, 0x0a, 0x0a, 0x41, 0x64, 0x64, 0x41, 0x70, 0x70, 0x54, 0x6f,
	0x6f, 0x6c, 0x12, 0x2c, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x70, 0x6c,
	0x75, 0x67, 0x69, 0x6e, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x73, 0x65, 0x72, 0x76,
	0x65, 0x72, 0x2e, 0x41, 0x64, 0x64, 0x41, 0x70, 0x70, 0x54, 0x6f, 0x6f, 0x6c, 0x52, 0x65, 0x71,
	0x1a, 0x2c, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x70, 0x6c, 0x75, 0x67,
	0x69, 0x6e, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72,
	0x2e, 0x41, 0x64, 0x64, 0x41, 0x70, 0x70, 0x54, 0x6f, 0x6f, 0x6c, 0x52, 0x73, 0x70, 0x22, 0x00,
	0x12, 0x73, 0x0a, 0x0d, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x41, 0x70, 0x70, 0x54, 0x6f, 0x6f,
	0x6c, 0x12, 0x2f, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x70, 0x6c, 0x75,
	0x67, 0x69, 0x6e, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65,
	0x72, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x41, 0x70, 0x70, 0x54, 0x6f, 0x6f, 0x6c, 0x52,
	0x65, 0x71, 0x1a, 0x2f, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x70, 0x6c,
	0x75, 0x67, 0x69, 0x6e, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x73, 0x65, 0x72, 0x76,
	0x65, 0x72, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x41, 0x70, 0x70, 0x54, 0x6f, 0x6f, 0x6c,
	0x52, 0x73, 0x70, 0x22, 0x00, 0x12, 0x6d, 0x0a, 0x0b, 0x53, 0x61, 0x76, 0x65, 0x41, 0x70, 0x70,
	0x54, 0x6f, 0x6f, 0x6c, 0x12, 0x2d, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e,
	0x70, 0x6c, 0x75, 0x67, 0x69, 0x6e, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x73, 0x65,
	0x72, 0x76, 0x65, 0x72, 0x2e, 0x53, 0x61, 0x76, 0x65, 0x41, 0x70, 0x70, 0x54, 0x6f, 0x6f, 0x6c,
	0x52, 0x65, 0x71, 0x1a, 0x2d, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x70,
	0x6c, 0x75, 0x67, 0x69, 0x6e, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x73, 0x65, 0x72,
	0x76, 0x65, 0x72, 0x2e, 0x53, 0x61, 0x76, 0x65, 0x41, 0x70, 0x70, 0x54, 0x6f, 0x6f, 0x6c, 0x52,
	0x73, 0x70, 0x22, 0x00, 0x12, 0x70, 0x0a, 0x0c, 0x4c, 0x69, 0x73, 0x74, 0x41, 0x70, 0x70, 0x54,
	0x6f, 0x6f, 0x6c, 0x73, 0x12, 0x2e, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e,
	0x70, 0x6c, 0x75, 0x67, 0x69, 0x6e, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x73, 0x65,
	0x72, 0x76, 0x65, 0x72, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x41, 0x70, 0x70, 0x54, 0x6f, 0x6f, 0x6c,
	0x73, 0x52, 0x65, 0x71, 0x1a, 0x2e, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e,
	0x70, 0x6c, 0x75, 0x67, 0x69, 0x6e, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x73, 0x65,
	0x72, 0x76, 0x65, 0x72, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x41, 0x70, 0x70, 0x54, 0x6f, 0x6f, 0x6c,
	0x73, 0x52, 0x73, 0x70, 0x22, 0x00, 0x12, 0x79, 0x0a, 0x0f, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69,
	0x62, 0x65, 0x41, 0x70, 0x70, 0x54, 0x6f, 0x6f, 0x6c, 0x12, 0x31, 0x2e, 0x74, 0x72, 0x70, 0x63,
	0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x70, 0x6c, 0x75, 0x67, 0x69, 0x6e, 0x5f, 0x63, 0x6f, 0x6e, 0x66,
	0x69, 0x67, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69,
	0x62, 0x65, 0x41, 0x70, 0x70, 0x54, 0x6f, 0x6f, 0x6c, 0x52, 0x65, 0x71, 0x1a, 0x31, 0x2e, 0x74,
	0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x70, 0x6c, 0x75, 0x67, 0x69, 0x6e, 0x5f, 0x63,
	0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x44, 0x65, 0x73,
	0x63, 0x72, 0x69, 0x62, 0x65, 0x41, 0x70, 0x70, 0x54, 0x6f, 0x6f, 0x6c, 0x52, 0x73, 0x70, 0x22,
	0x00, 0x12, 0x98, 0x01, 0x0a, 0x1a, 0x47, 0x65, 0x74, 0x41, 0x70, 0x70, 0x50, 0x6c, 0x75, 0x67,
	0x69, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72,
	0x12, 0x3c, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x70, 0x6c, 0x75, 0x67,
	0x69, 0x6e, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72,
	0x2e, 0x47, 0x65, 0x74, 0x41, 0x70, 0x70, 0x50, 0x6c, 0x75, 0x67, 0x69, 0x6e, 0x52, 0x65, 0x71,
	0x75, 0x69, 0x72, 0x65, 0x64, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x65, 0x71, 0x1a, 0x3c,
	0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x70, 0x6c, 0x75, 0x67, 0x69, 0x6e,
	0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x47,
	0x65, 0x74, 0x41, 0x70, 0x70, 0x50, 0x6c, 0x75, 0x67, 0x69, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x69,
	0x72, 0x65, 0x64, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x73, 0x70, 0x12, 0x7d, 0x0a, 0x11,
	0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x41, 0x70, 0x70, 0x50, 0x6c, 0x75, 0x67, 0x69,
	0x6e, 0x12, 0x33, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x70, 0x6c, 0x75,
	0x67, 0x69, 0x6e, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65,
	0x72, 0x2e, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x41, 0x70, 0x70, 0x50, 0x6c, 0x75,
	0x67, 0x69, 0x6e, 0x52, 0x65, 0x71, 0x1a, 0x33, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45,
	0x50, 0x2e, 0x70, 0x6c, 0x75, 0x67, 0x69, 0x6e, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f,
	0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x41,
	0x70, 0x70, 0x50, 0x6c, 0x75, 0x67, 0x69, 0x6e, 0x52, 0x73, 0x70, 0x12, 0x73, 0x0a, 0x0d, 0x53,
	0x61, 0x76, 0x65, 0x41, 0x70, 0x70, 0x50, 0x6c, 0x75, 0x67, 0x69, 0x6e, 0x12, 0x2f, 0x2e, 0x74,
	0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x70, 0x6c, 0x75, 0x67, 0x69, 0x6e, 0x5f, 0x63,
	0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x53, 0x61, 0x76,
	0x65, 0x41, 0x70, 0x70, 0x50, 0x6c, 0x75, 0x67, 0x69, 0x6e, 0x52, 0x65, 0x71, 0x1a, 0x2f, 0x2e,
	0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x70, 0x6c, 0x75, 0x67, 0x69, 0x6e, 0x5f,
	0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x53, 0x61,
	0x76, 0x65, 0x41, 0x70, 0x70, 0x50, 0x6c, 0x75, 0x67, 0x69, 0x6e, 0x52, 0x73, 0x70, 0x22, 0x00,
	0x12, 0x95, 0x01, 0x0a, 0x19, 0x4c, 0x69, 0x73, 0x74, 0x41, 0x70, 0x70, 0x54, 0x6f, 0x6f, 0x6c,
	0x52, 0x65, 0x6c, 0x65, 0x61, 0x73, 0x65, 0x50, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x12, 0x3b,
	0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x70, 0x6c, 0x75, 0x67, 0x69, 0x6e,
	0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x4c,
	0x69, 0x73, 0x74, 0x41, 0x70, 0x70, 0x54, 0x6f, 0x6f, 0x6c, 0x52, 0x65, 0x6c, 0x65, 0x61, 0x73,
	0x65, 0x50, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x52, 0x65, 0x71, 0x1a, 0x3b, 0x2e, 0x74, 0x72,
	0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x70, 0x6c, 0x75, 0x67, 0x69, 0x6e, 0x5f, 0x63, 0x6f,
	0x6e, 0x66, 0x69, 0x67, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x4c, 0x69, 0x73, 0x74,
	0x41, 0x70, 0x70, 0x54, 0x6f, 0x6f, 0x6c, 0x52, 0x65, 0x6c, 0x65, 0x61, 0x73, 0x65, 0x50, 0x72,
	0x65, 0x76, 0x69, 0x65, 0x77, 0x52, 0x73, 0x70, 0x32, 0xb8, 0x10, 0x0a, 0x0f, 0x50, 0x6c, 0x75,
	0x67, 0x69, 0x6e, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x41, 0x70, 0x69, 0x12, 0x6d, 0x0a, 0x0b,
	0x4c, 0x69, 0x73, 0x74, 0x50, 0x6c, 0x75, 0x67, 0x69, 0x6e, 0x73, 0x12, 0x2d, 0x2e, 0x74, 0x72,
	0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x70, 0x6c, 0x75, 0x67, 0x69, 0x6e, 0x5f, 0x63, 0x6f,
	0x6e, 0x66, 0x69, 0x67, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x4c, 0x69, 0x73, 0x74,
	0x50, 0x6c, 0x75, 0x67, 0x69, 0x6e, 0x73, 0x52, 0x65, 0x71, 0x1a, 0x2d, 0x2e, 0x74, 0x72, 0x70,
	0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x70, 0x6c, 0x75, 0x67, 0x69, 0x6e, 0x5f, 0x63, 0x6f, 0x6e,
	0x66, 0x69, 0x67, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x50,
	0x6c, 0x75, 0x67, 0x69, 0x6e, 0x73, 0x52, 0x73, 0x70, 0x22, 0x00, 0x12, 0x70, 0x0a, 0x0c, 0x43,
	0x72, 0x65, 0x61, 0x74, 0x65, 0x50, 0x6c, 0x75, 0x67, 0x69, 0x6e, 0x12, 0x2e, 0x2e, 0x74, 0x72,
	0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x70, 0x6c, 0x75, 0x67, 0x69, 0x6e, 0x5f, 0x63, 0x6f,
	0x6e, 0x66, 0x69, 0x67, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x43, 0x72, 0x65, 0x61,
	0x74, 0x65, 0x50, 0x6c, 0x75, 0x67, 0x69, 0x6e, 0x52, 0x65, 0x71, 0x1a, 0x2e, 0x2e, 0x74, 0x72,
	0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x70, 0x6c, 0x75, 0x67, 0x69, 0x6e, 0x5f, 0x63, 0x6f,
	0x6e, 0x66, 0x69, 0x67, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x43, 0x72, 0x65, 0x61,
	0x74, 0x65, 0x50, 0x6c, 0x75, 0x67, 0x69, 0x6e, 0x52, 0x73, 0x70, 0x22, 0x00, 0x12, 0x88, 0x01,
	0x0a, 0x14, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x4d, 0x43, 0x50, 0x50, 0x6c, 0x75, 0x67, 0x69,
	0x6e, 0x49, 0x6e, 0x6e, 0x65, 0x72, 0x12, 0x36, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45,
	0x50, 0x2e, 0x70, 0x6c, 0x75, 0x67, 0x69, 0x6e, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f,
	0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x4d, 0x43, 0x50,
	0x50, 0x6c, 0x75, 0x67, 0x69, 0x6e, 0x49, 0x6e, 0x6e, 0x65, 0x72, 0x52, 0x65, 0x71, 0x1a, 0x36,
	0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x70, 0x6c, 0x75, 0x67, 0x69, 0x6e,
	0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x43,
	0x72, 0x65, 0x61, 0x74, 0x65, 0x4d, 0x43, 0x50, 0x50, 0x6c, 0x75, 0x67, 0x69, 0x6e, 0x49, 0x6e,
	0x6e, 0x65, 0x72, 0x52, 0x73, 0x70, 0x22, 0x00, 0x12, 0x70, 0x0a, 0x0c, 0x4d, 0x6f, 0x64, 0x69,
	0x66, 0x79, 0x50, 0x6c, 0x75, 0x67, 0x69, 0x6e, 0x12, 0x2e, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e,
	0x4b, 0x45, 0x50, 0x2e, 0x70, 0x6c, 0x75, 0x67, 0x69, 0x6e, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69,
	0x67, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x4d, 0x6f, 0x64, 0x69, 0x66, 0x79, 0x50,
	0x6c, 0x75, 0x67, 0x69, 0x6e, 0x52, 0x65, 0x71, 0x1a, 0x2e, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e,
	0x4b, 0x45, 0x50, 0x2e, 0x70, 0x6c, 0x75, 0x67, 0x69, 0x6e, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69,
	0x67, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x4d, 0x6f, 0x64, 0x69, 0x66, 0x79, 0x50,
	0x6c, 0x75, 0x67, 0x69, 0x6e, 0x52, 0x73, 0x70, 0x22, 0x00, 0x12, 0x88, 0x01, 0x0a, 0x14, 0x4d,
	0x6f, 0x64, 0x69, 0x66, 0x79, 0x4d, 0x43, 0x50, 0x50, 0x6c, 0x75, 0x67, 0x69, 0x6e, 0x49, 0x6e,
	0x6e, 0x65, 0x72, 0x12, 0x36, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x70,
	0x6c, 0x75, 0x67, 0x69, 0x6e, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x73, 0x65, 0x72,
	0x76, 0x65, 0x72, 0x2e, 0x4d, 0x6f, 0x64, 0x69, 0x66, 0x79, 0x4d, 0x43, 0x50, 0x50, 0x6c, 0x75,
	0x67, 0x69, 0x6e, 0x49, 0x6e, 0x6e, 0x65, 0x72, 0x52, 0x65, 0x71, 0x1a, 0x36, 0x2e, 0x74, 0x72,
	0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x70, 0x6c, 0x75, 0x67, 0x69, 0x6e, 0x5f, 0x63, 0x6f,
	0x6e, 0x66, 0x69, 0x67, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x4d, 0x6f, 0x64, 0x69,
	0x66, 0x79, 0x4d, 0x43, 0x50, 0x50, 0x6c, 0x75, 0x67, 0x69, 0x6e, 0x49, 0x6e, 0x6e, 0x65, 0x72,
	0x52, 0x73, 0x70, 0x22, 0x00, 0x12, 0x70, 0x0a, 0x0c, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x50,
	0x6c, 0x75, 0x67, 0x69, 0x6e, 0x12, 0x2e, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50,
	0x2e, 0x70, 0x6c, 0x75, 0x67, 0x69, 0x6e, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x73,
	0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x50, 0x6c, 0x75, 0x67,
	0x69, 0x6e, 0x52, 0x65, 0x71, 0x1a, 0x2e, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50,
	0x2e, 0x70, 0x6c, 0x75, 0x67, 0x69, 0x6e, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x73,
	0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x50, 0x6c, 0x75, 0x67,
	0x69, 0x6e, 0x52, 0x73, 0x70, 0x22, 0x00, 0x12, 0x76, 0x0a, 0x0e, 0x44, 0x65, 0x73, 0x63, 0x72,
	0x69, 0x62, 0x65, 0x50, 0x6c, 0x75, 0x67, 0x69, 0x6e, 0x12, 0x30, 0x2e, 0x74, 0x72, 0x70, 0x63,
	0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x70, 0x6c, 0x75, 0x67, 0x69, 0x6e, 0x5f, 0x63, 0x6f, 0x6e, 0x66,
	0x69, 0x67, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69,
	0x62, 0x65, 0x50, 0x6c, 0x75, 0x67, 0x69, 0x6e, 0x52, 0x65, 0x71, 0x1a, 0x30, 0x2e, 0x74, 0x72,
	0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x70, 0x6c, 0x75, 0x67, 0x69, 0x6e, 0x5f, 0x63, 0x6f,
	0x6e, 0x66, 0x69, 0x67, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x44, 0x65, 0x73, 0x63,
	0x72, 0x69, 0x62, 0x65, 0x50, 0x6c, 0x75, 0x67, 0x69, 0x6e, 0x52, 0x73, 0x70, 0x22, 0x00, 0x12,
	0x67, 0x0a, 0x09, 0x4c, 0x69, 0x73, 0x74, 0x54, 0x6f, 0x6f, 0x6c, 0x73, 0x12, 0x2b, 0x2e, 0x74,
	0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x70, 0x6c, 0x75, 0x67, 0x69, 0x6e, 0x5f, 0x63,
	0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x4c, 0x69, 0x73,
	0x74, 0x54, 0x6f, 0x6f, 0x6c, 0x73, 0x52, 0x65, 0x71, 0x1a, 0x2b, 0x2e, 0x74, 0x72, 0x70, 0x63,
	0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x70, 0x6c, 0x75, 0x67, 0x69, 0x6e, 0x5f, 0x63, 0x6f, 0x6e, 0x66,
	0x69, 0x67, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x54, 0x6f,
	0x6f, 0x6c, 0x73, 0x52, 0x73, 0x70, 0x22, 0x00, 0x12, 0x70, 0x0a, 0x0c, 0x44, 0x65, 0x73, 0x63,
	0x72, 0x69, 0x62, 0x65, 0x54, 0x6f, 0x6f, 0x6c, 0x12, 0x2e, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e,
	0x4b, 0x45, 0x50, 0x2e, 0x70, 0x6c, 0x75, 0x67, 0x69, 0x6e, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69,
	0x67, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62,
	0x65, 0x54, 0x6f, 0x6f, 0x6c, 0x52, 0x65, 0x71, 0x1a, 0x2e, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e,
	0x4b, 0x45, 0x50, 0x2e, 0x70, 0x6c, 0x75, 0x67, 0x69, 0x6e, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69,
	0x67, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62,
	0x65, 0x54, 0x6f, 0x6f, 0x6c, 0x52, 0x73, 0x70, 0x22, 0x00, 0x12, 0x67, 0x0a, 0x09, 0x43, 0x68,
	0x65, 0x63, 0x6b, 0x54, 0x6f, 0x6f, 0x6c, 0x12, 0x2b, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b,
	0x45, 0x50, 0x2e, 0x70, 0x6c, 0x75, 0x67, 0x69, 0x6e, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67,
	0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x54, 0x6f, 0x6f,
	0x6c, 0x52, 0x65, 0x71, 0x1a, 0x2b, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e,
	0x70, 0x6c, 0x75, 0x67, 0x69, 0x6e, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x73, 0x65,
	0x72, 0x76, 0x65, 0x72, 0x2e, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x54, 0x6f, 0x6f, 0x6c, 0x52, 0x73,
	0x70, 0x22, 0x00, 0x12, 0x7c, 0x0a, 0x10, 0x4c, 0x69, 0x73, 0x74, 0x41, 0x70, 0x70, 0x54, 0x6f,
	0x6f, 0x6c, 0x73, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x32, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b,
	0x45, 0x50, 0x2e, 0x70, 0x6c, 0x75, 0x67, 0x69, 0x6e, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67,
	0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x41, 0x70, 0x70, 0x54,
	0x6f, 0x6f, 0x6c, 0x73, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x71, 0x1a, 0x32, 0x2e, 0x74, 0x72,
	0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x70, 0x6c, 0x75, 0x67, 0x69, 0x6e, 0x5f, 0x63, 0x6f,
	0x6e, 0x66, 0x69, 0x67, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x4c, 0x69, 0x73, 0x74,
	0x41, 0x70, 0x70, 0x54, 0x6f, 0x6f, 0x6c, 0x73, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x73, 0x70, 0x22,
	0x00, 0x12, 0x82, 0x01, 0x0a, 0x12, 0x41, 0x64, 0x64, 0x4b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64,
	0x67, 0x65, 0x51, 0x41, 0x54, 0x6f, 0x6f, 0x6c, 0x12, 0x34, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e,
	0x4b, 0x45, 0x50, 0x2e, 0x70, 0x6c, 0x75, 0x67, 0x69, 0x6e, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69,
	0x67, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x41, 0x64, 0x64, 0x4b, 0x6e, 0x6f, 0x77,
	0x6c, 0x65, 0x64, 0x67, 0x65, 0x51, 0x41, 0x54, 0x6f, 0x6f, 0x6c, 0x52, 0x65, 0x71, 0x1a, 0x34,
	0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x70, 0x6c, 0x75, 0x67, 0x69, 0x6e,
	0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x41,
	0x64, 0x64, 0x4b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x51, 0x41, 0x54, 0x6f, 0x6f,
	0x6c, 0x52, 0x73, 0x70, 0x22, 0x00, 0x12, 0x79, 0x0a, 0x0f, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x50,
	0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x31, 0x2e, 0x74, 0x72, 0x70, 0x63,
	0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x70, 0x6c, 0x75, 0x67, 0x69, 0x6e, 0x5f, 0x63, 0x6f, 0x6e, 0x66,
	0x69, 0x67, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x50,
	0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x1a, 0x31, 0x2e, 0x74,
	0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x70, 0x6c, 0x75, 0x67, 0x69, 0x6e, 0x5f, 0x63,
	0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x43, 0x68, 0x65,
	0x63, 0x6b, 0x50, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x52, 0x73, 0x70, 0x22,
	0x00, 0x12, 0x84, 0x01, 0x0a, 0x12, 0x47, 0x65, 0x74, 0x55, 0x6e, 0x72, 0x65, 0x6c, 0x65, 0x61,
	0x73, 0x65, 0x64, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x36, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e,
	0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x63, 0x6f, 0x6e,
	0x66, 0x69, 0x67, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x47, 0x65, 0x74, 0x55, 0x6e,
	0x72, 0x65, 0x6c, 0x65, 0x61, 0x73, 0x65, 0x64, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x52, 0x65, 0x71,
	0x1a, 0x36, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f,
	0x74, 0x61, 0x73, 0x6b, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x73, 0x65, 0x72, 0x76,
	0x65, 0x72, 0x2e, 0x47, 0x65, 0x74, 0x55, 0x6e, 0x72, 0x65, 0x6c, 0x65, 0x61, 0x73, 0x65, 0x64,
	0x43, 0x6f, 0x75, 0x6e, 0x74, 0x52, 0x73, 0x70, 0x12, 0x8d, 0x01, 0x0a, 0x15, 0x53, 0x65, 0x6e,
	0x64, 0x44, 0x61, 0x74, 0x61, 0x53, 0x79, 0x6e, 0x63, 0x54, 0x61, 0x73, 0x6b, 0x45, 0x76, 0x65,
	0x6e, 0x74, 0x12, 0x39, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f,
	0x74, 0x5f, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x73, 0x65,
	0x72, 0x76, 0x65, 0x72, 0x2e, 0x53, 0x65, 0x6e, 0x64, 0x44, 0x61, 0x74, 0x61, 0x53, 0x79, 0x6e,
	0x63, 0x54, 0x61, 0x73, 0x6b, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x1a, 0x39, 0x2e,
	0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x74, 0x61, 0x73,
	0x6b, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e,
	0x53, 0x65, 0x6e, 0x64, 0x44, 0x61, 0x74, 0x61, 0x53, 0x79, 0x6e, 0x63, 0x54, 0x61, 0x73, 0x6b,
	0x45, 0x76, 0x65, 0x6e, 0x74, 0x52, 0x73, 0x70, 0x12, 0x7b, 0x0a, 0x0f, 0x47, 0x65, 0x74, 0x44,
	0x61, 0x74, 0x61, 0x53, 0x79, 0x6e, 0x63, 0x54, 0x61, 0x73, 0x6b, 0x12, 0x33, 0x2e, 0x74, 0x72,
	0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x74, 0x61, 0x73, 0x6b, 0x5f,
	0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x47, 0x65,
	0x74, 0x44, 0x61, 0x74, 0x61, 0x53, 0x79, 0x6e, 0x63, 0x54, 0x61, 0x73, 0x6b, 0x52, 0x65, 0x71,
	0x1a, 0x33, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f,
	0x74, 0x61, 0x73, 0x6b, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x73, 0x65, 0x72, 0x76,
	0x65, 0x72, 0x2e, 0x47, 0x65, 0x74, 0x44, 0x61, 0x74, 0x61, 0x53, 0x79, 0x6e, 0x63, 0x54, 0x61,
	0x73, 0x6b, 0x52, 0x73, 0x70, 0x12, 0x7c, 0x0a, 0x10, 0x53, 0x79, 0x6e, 0x63, 0x41, 0x70, 0x70,
	0x54, 0x6f, 0x6f, 0x6c, 0x52, 0x65, 0x64, 0x69, 0x73, 0x12, 0x32, 0x2e, 0x74, 0x72, 0x70, 0x63,
	0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x70, 0x6c, 0x75, 0x67, 0x69, 0x6e, 0x5f, 0x63, 0x6f, 0x6e, 0x66,
	0x69, 0x67, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x53, 0x79, 0x6e, 0x63, 0x41, 0x70,
	0x70, 0x54, 0x6f, 0x6f, 0x6c, 0x52, 0x65, 0x64, 0x69, 0x73, 0x52, 0x65, 0x71, 0x1a, 0x32, 0x2e,
	0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x70, 0x6c, 0x75, 0x67, 0x69, 0x6e, 0x5f,
	0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x53, 0x79,
	0x6e, 0x63, 0x41, 0x70, 0x70, 0x54, 0x6f, 0x6f, 0x6c, 0x52, 0x65, 0x64, 0x69, 0x73, 0x52, 0x73,
	0x70, 0x22, 0x00, 0x42, 0x4a, 0x5a, 0x48, 0x67, 0x69, 0x74, 0x2e, 0x77, 0x6f, 0x61, 0x2e, 0x63,
	0x6f, 0x6d, 0x2f, 0x64, 0x69, 0x61, 0x6c, 0x6f, 0x67, 0x75, 0x65, 0x2d, 0x70, 0x6c, 0x61, 0x74,
	0x66, 0x6f, 0x72, 0x6d, 0x2f, 0x6c, 0x6b, 0x65, 0x5f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x70,
	0x62, 0x2d, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x6f, 0x6c, 0x2f, 0x70, 0x6c, 0x75, 0x67, 0x69,
	0x6e, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x62,
	0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_plugin_config_proto_rawDescOnce sync.Once
	file_plugin_config_proto_rawDescData = file_plugin_config_proto_rawDesc
)

func file_plugin_config_proto_rawDescGZIP() []byte {
	file_plugin_config_proto_rawDescOnce.Do(func() {
		file_plugin_config_proto_rawDescData = protoimpl.X.CompressGZIP(file_plugin_config_proto_rawDescData)
	})
	return file_plugin_config_proto_rawDescData
}

var file_plugin_config_proto_enumTypes = make([]protoimpl.EnumInfo, 14)
var file_plugin_config_proto_msgTypes = make([]protoimpl.MessageInfo, 76)
var file_plugin_config_proto_goTypes = []interface{}{
	(TypeEnum)(0),                                // 0: trpc.KEP.plugin_config_server.TypeEnum
	(PluginTypeEnum)(0),                          // 1: trpc.KEP.plugin_config_server.PluginTypeEnum
	(AuthTypeEnum)(0),                            // 2: trpc.KEP.plugin_config_server.AuthTypeEnum
	(FinanceTypeEnum)(0),                         // 3: trpc.KEP.plugin_config_server.FinanceTypeEnum
	(CreateTypeEnum)(0),                          // 4: trpc.KEP.plugin_config_server.CreateTypeEnum
	(EnvType)(0),                                 // 5: trpc.KEP.plugin_config_server.EnvType
	(WhiteListTypeEnum)(0),                       // 6: trpc.KEP.plugin_config_server.WhiteListTypeEnum
	(ListPluginsReq_QueryTypeEnum)(0),            // 7: trpc.KEP.plugin_config_server.ListPluginsReq.QueryTypeEnum
	(ListPluginsReq_PluginTypeEnum)(0),           // 8: trpc.KEP.plugin_config_server.ListPluginsReq.PluginTypeEnum
	(ListPluginsReq_ModuleEnum)(0),               // 9: trpc.KEP.plugin_config_server.ListPluginsReq.ModuleEnum
	(AuthInfo_KeyLocationTypeEnum)(0),            // 10: trpc.KEP.plugin_config_server.AuthInfo.KeyLocationTypeEnum
	(ToolRef_RefTypeEnum)(0),                     // 11: trpc.KEP.plugin_config_server.ToolRef.RefTypeEnum
	(CheckPermissionReq_ModuleEnum)(0),           // 12: trpc.KEP.plugin_config_server.CheckPermissionReq.ModuleEnum
	(CheckPermissionRsp_PermissionStatus)(0),     // 13: trpc.KEP.plugin_config_server.CheckPermissionRsp.PermissionStatus
	(*ListPluginsReq)(nil),                       // 14: trpc.KEP.plugin_config_server.ListPluginsReq
	(*ListPluginsRsp)(nil),                       // 15: trpc.KEP.plugin_config_server.ListPluginsRsp
	(*CreatePluginReq)(nil),                      // 16: trpc.KEP.plugin_config_server.CreatePluginReq
	(*CreatePluginRsp)(nil),                      // 17: trpc.KEP.plugin_config_server.CreatePluginRsp
	(*ModifyPluginReq)(nil),                      // 18: trpc.KEP.plugin_config_server.ModifyPluginReq
	(*ModifyPluginRsp)(nil),                      // 19: trpc.KEP.plugin_config_server.ModifyPluginRsp
	(*PluginHeader)(nil),                         // 20: trpc.KEP.plugin_config_server.PluginHeader
	(*CreateMCPPluginReq)(nil),                   // 21: trpc.KEP.plugin_config_server.CreateMCPPluginReq
	(*CreateMCPPluginRsp)(nil),                   // 22: trpc.KEP.plugin_config_server.CreateMCPPluginRsp
	(*ModifyMCPPluginReq)(nil),                   // 23: trpc.KEP.plugin_config_server.ModifyMCPPluginReq
	(*ModifyMCPPluginRsp)(nil),                   // 24: trpc.KEP.plugin_config_server.ModifyMCPPluginRsp
	(*CheckMCPServerReq)(nil),                    // 25: trpc.KEP.plugin_config_server.CheckMCPServerReq
	(*CheckMCPServerRsp)(nil),                    // 26: trpc.KEP.plugin_config_server.CheckMCPServerRsp
	(*CreateMCPPluginInnerReq)(nil),              // 27: trpc.KEP.plugin_config_server.CreateMCPPluginInnerReq
	(*CreateMCPPluginInnerRsp)(nil),              // 28: trpc.KEP.plugin_config_server.CreateMCPPluginInnerRsp
	(*ModifyMCPPluginInnerReq)(nil),              // 29: trpc.KEP.plugin_config_server.ModifyMCPPluginInnerReq
	(*ModifyMCPPluginInnerRsp)(nil),              // 30: trpc.KEP.plugin_config_server.ModifyMCPPluginInnerRsp
	(*DeletePluginReq)(nil),                      // 31: trpc.KEP.plugin_config_server.DeletePluginReq
	(*DeletePluginRsp)(nil),                      // 32: trpc.KEP.plugin_config_server.DeletePluginRsp
	(*DescribePluginReq)(nil),                    // 33: trpc.KEP.plugin_config_server.DescribePluginReq
	(*DescribePluginRsp)(nil),                    // 34: trpc.KEP.plugin_config_server.DescribePluginRsp
	(*ListToolsReq)(nil),                         // 35: trpc.KEP.plugin_config_server.ListToolsReq
	(*ListToolsRsp)(nil),                         // 36: trpc.KEP.plugin_config_server.ListToolsRsp
	(*DescribeToolReq)(nil),                      // 37: trpc.KEP.plugin_config_server.DescribeToolReq
	(*DescribeToolRsp)(nil),                      // 38: trpc.KEP.plugin_config_server.DescribeToolRsp
	(*ToolExample)(nil),                          // 39: trpc.KEP.plugin_config_server.ToolExample
	(*RequestParam)(nil),                         // 40: trpc.KEP.plugin_config_server.RequestParam
	(*ResponseParam)(nil),                        // 41: trpc.KEP.plugin_config_server.ResponseParam
	(*CheckToolReq)(nil),                         // 42: trpc.KEP.plugin_config_server.CheckToolReq
	(*CheckToolRsp)(nil),                         // 43: trpc.KEP.plugin_config_server.CheckToolRsp
	(*UserInfo)(nil),                             // 44: trpc.KEP.plugin_config_server.UserInfo
	(*PluginInfo)(nil),                           // 45: trpc.KEP.plugin_config_server.PluginInfo
	(*ToolInfo)(nil),                             // 46: trpc.KEP.plugin_config_server.ToolInfo
	(*AuthInfo)(nil),                             // 47: trpc.KEP.plugin_config_server.AuthInfo
	(*ListToolRefsReq)(nil),                      // 48: trpc.KEP.plugin_config_server.ListToolRefsReq
	(*ListToolRefsRsp)(nil),                      // 49: trpc.KEP.plugin_config_server.ListToolRefsRsp
	(*ToolRef)(nil),                              // 50: trpc.KEP.plugin_config_server.ToolRef
	(*AddAppToolReq)(nil),                        // 51: trpc.KEP.plugin_config_server.AddAppToolReq
	(*AddAppToolRsp)(nil),                        // 52: trpc.KEP.plugin_config_server.AddAppToolRsp
	(*DeleteAppToolReq)(nil),                     // 53: trpc.KEP.plugin_config_server.DeleteAppToolReq
	(*DeleteAppToolRsp)(nil),                     // 54: trpc.KEP.plugin_config_server.DeleteAppToolRsp
	(*AppToolReqParam)(nil),                      // 55: trpc.KEP.plugin_config_server.AppToolReqParam
	(*AppToolRspParam)(nil),                      // 56: trpc.KEP.plugin_config_server.AppToolRspParam
	(*SaveAppToolReq)(nil),                       // 57: trpc.KEP.plugin_config_server.SaveAppToolReq
	(*SaveAppToolRsp)(nil),                       // 58: trpc.KEP.plugin_config_server.SaveAppToolRsp
	(*ListAppToolsReq)(nil),                      // 59: trpc.KEP.plugin_config_server.ListAppToolsReq
	(*ListAppToolsRsp)(nil),                      // 60: trpc.KEP.plugin_config_server.ListAppToolsRsp
	(*AppToolItem)(nil),                          // 61: trpc.KEP.plugin_config_server.AppToolItem
	(*DescribeAppToolReq)(nil),                   // 62: trpc.KEP.plugin_config_server.DescribeAppToolReq
	(*DescribeAppToolRsp)(nil),                   // 63: trpc.KEP.plugin_config_server.DescribeAppToolRsp
	(*ListAppToolsInfoReq)(nil),                  // 64: trpc.KEP.plugin_config_server.ListAppToolsInfoReq
	(*ListAppToolsInfoRsp)(nil),                  // 65: trpc.KEP.plugin_config_server.ListAppToolsInfoRsp
	(*AppPluginHeader)(nil),                      // 66: trpc.KEP.plugin_config_server.AppPluginHeader
	(*GetAppPluginRequiredHeaderReq)(nil),        // 67: trpc.KEP.plugin_config_server.GetAppPluginRequiredHeaderReq
	(*GetAppPluginRequiredHeaderRsp)(nil),        // 68: trpc.KEP.plugin_config_server.GetAppPluginRequiredHeaderRsp
	(*DescribeAppPluginReq)(nil),                 // 69: trpc.KEP.plugin_config_server.DescribeAppPluginReq
	(*DescribeAppPluginRsp)(nil),                 // 70: trpc.KEP.plugin_config_server.DescribeAppPluginRsp
	(*SaveAppPluginReq)(nil),                     // 71: trpc.KEP.plugin_config_server.SaveAppPluginReq
	(*SaveAppPluginRsp)(nil),                     // 72: trpc.KEP.plugin_config_server.SaveAppPluginRsp
	(*AppToolInfo)(nil),                          // 73: trpc.KEP.plugin_config_server.AppToolInfo
	(*MCPServerInfo)(nil),                        // 74: trpc.KEP.plugin_config_server.MCPServerInfo
	(*AddKnowledgeQAToolReq)(nil),                // 75: trpc.KEP.plugin_config_server.AddKnowledgeQAToolReq
	(*AddKnowledgeQAToolRsp)(nil),                // 76: trpc.KEP.plugin_config_server.AddKnowledgeQAToolRsp
	(*ListAppToolReleasePreviewReq)(nil),         // 77: trpc.KEP.plugin_config_server.ListAppToolReleasePreviewReq
	(*ListAppToolReleasePreviewRsp)(nil),         // 78: trpc.KEP.plugin_config_server.ListAppToolReleasePreviewRsp
	(*SyncAppToolRedisReq)(nil),                  // 79: trpc.KEP.plugin_config_server.SyncAppToolRedisReq
	(*SyncAppToolRedisRsp)(nil),                  // 80: trpc.KEP.plugin_config_server.SyncAppToolRedisRsp
	(*CheckPermissionReq)(nil),                   // 81: trpc.KEP.plugin_config_server.CheckPermissionReq
	(*CheckPermissionRsp)(nil),                   // 82: trpc.KEP.plugin_config_server.CheckPermissionRsp
	(*CreateMCPPluginReq_MCPPluginItem)(nil),     // 83: trpc.KEP.plugin_config_server.CreateMCPPluginReq.MCPPluginItem
	(*CreateMCPPluginInnerReq_PluginHeader)(nil), // 84: trpc.KEP.plugin_config_server.CreateMCPPluginInnerReq.PluginHeader
	(*ModifyMCPPluginInnerReq_PluginHeader)(nil), // 85: trpc.KEP.plugin_config_server.ModifyMCPPluginInnerReq.PluginHeader
	nil, // 86: trpc.KEP.plugin_config_server.MCPServerInfo.HeadersEntry
	(*ListAppToolReleasePreviewRsp_AppTool)(nil), // 87: trpc.KEP.plugin_config_server.ListAppToolReleasePreviewRsp.AppTool
	(*CheckPermissionReq_CheckItem)(nil),         // 88: trpc.KEP.plugin_config_server.CheckPermissionReq.CheckItem
	(*CheckPermissionRsp_Result)(nil),            // 89: trpc.KEP.plugin_config_server.CheckPermissionRsp.Result
	(*KEP.GetUnreleasedCountReq)(nil),            // 90: trpc.KEP.bot_task_config_server.GetUnreleasedCountReq
	(*KEP.SendDataSyncTaskEventReq)(nil),         // 91: trpc.KEP.bot_task_config_server.SendDataSyncTaskEventReq
	(*KEP.GetDataSyncTaskReq)(nil),               // 92: trpc.KEP.bot_task_config_server.GetDataSyncTaskReq
	(*KEP.GetUnreleasedCountRsp)(nil),            // 93: trpc.KEP.bot_task_config_server.GetUnreleasedCountRsp
	(*KEP.SendDataSyncTaskEventRsp)(nil),         // 94: trpc.KEP.bot_task_config_server.SendDataSyncTaskEventRsp
	(*KEP.GetDataSyncTaskRsp)(nil),               // 95: trpc.KEP.bot_task_config_server.GetDataSyncTaskRsp
}
var file_plugin_config_proto_depIdxs = []int32{
	7,   // 0: trpc.KEP.plugin_config_server.ListPluginsReq.QueryType:type_name -> trpc.KEP.plugin_config_server.ListPluginsReq.QueryTypeEnum
	8,   // 1: trpc.KEP.plugin_config_server.ListPluginsReq.PluginType:type_name -> trpc.KEP.plugin_config_server.ListPluginsReq.PluginTypeEnum
	9,   // 2: trpc.KEP.plugin_config_server.ListPluginsReq.Module:type_name -> trpc.KEP.plugin_config_server.ListPluginsReq.ModuleEnum
	4,   // 3: trpc.KEP.plugin_config_server.ListPluginsReq.CreateTypes:type_name -> trpc.KEP.plugin_config_server.CreateTypeEnum
	45,  // 4: trpc.KEP.plugin_config_server.ListPluginsRsp.Plugins:type_name -> trpc.KEP.plugin_config_server.PluginInfo
	2,   // 5: trpc.KEP.plugin_config_server.CreatePluginReq.AuthType:type_name -> trpc.KEP.plugin_config_server.AuthTypeEnum
	47,  // 6: trpc.KEP.plugin_config_server.CreatePluginReq.AuthInfo:type_name -> trpc.KEP.plugin_config_server.AuthInfo
	1,   // 7: trpc.KEP.plugin_config_server.CreatePluginReq.PluginType:type_name -> trpc.KEP.plugin_config_server.PluginTypeEnum
	46,  // 8: trpc.KEP.plugin_config_server.CreatePluginReq.Tools:type_name -> trpc.KEP.plugin_config_server.ToolInfo
	4,   // 9: trpc.KEP.plugin_config_server.CreatePluginReq.CreateType:type_name -> trpc.KEP.plugin_config_server.CreateTypeEnum
	2,   // 10: trpc.KEP.plugin_config_server.ModifyPluginReq.AuthType:type_name -> trpc.KEP.plugin_config_server.AuthTypeEnum
	47,  // 11: trpc.KEP.plugin_config_server.ModifyPluginReq.AuthInfo:type_name -> trpc.KEP.plugin_config_server.AuthInfo
	1,   // 12: trpc.KEP.plugin_config_server.ModifyPluginReq.PluginType:type_name -> trpc.KEP.plugin_config_server.PluginTypeEnum
	46,  // 13: trpc.KEP.plugin_config_server.ModifyPluginReq.Tools:type_name -> trpc.KEP.plugin_config_server.ToolInfo
	4,   // 14: trpc.KEP.plugin_config_server.ModifyPluginReq.CreateType:type_name -> trpc.KEP.plugin_config_server.CreateTypeEnum
	83,  // 15: trpc.KEP.plugin_config_server.CreateMCPPluginReq.Plugins:type_name -> trpc.KEP.plugin_config_server.CreateMCPPluginReq.MCPPluginItem
	20,  // 16: trpc.KEP.plugin_config_server.ModifyMCPPluginReq.Headers:type_name -> trpc.KEP.plugin_config_server.PluginHeader
	84,  // 17: trpc.KEP.plugin_config_server.CreateMCPPluginInnerReq.Headers:type_name -> trpc.KEP.plugin_config_server.CreateMCPPluginInnerReq.PluginHeader
	1,   // 18: trpc.KEP.plugin_config_server.CreateMCPPluginInnerReq.PluginType:type_name -> trpc.KEP.plugin_config_server.PluginTypeEnum
	85,  // 19: trpc.KEP.plugin_config_server.ModifyMCPPluginInnerReq.Headers:type_name -> trpc.KEP.plugin_config_server.ModifyMCPPluginInnerReq.PluginHeader
	1,   // 20: trpc.KEP.plugin_config_server.ModifyMCPPluginInnerReq.PluginType:type_name -> trpc.KEP.plugin_config_server.PluginTypeEnum
	2,   // 21: trpc.KEP.plugin_config_server.DescribePluginRsp.AuthType:type_name -> trpc.KEP.plugin_config_server.AuthTypeEnum
	47,  // 22: trpc.KEP.plugin_config_server.DescribePluginRsp.AuthInfo:type_name -> trpc.KEP.plugin_config_server.AuthInfo
	44,  // 23: trpc.KEP.plugin_config_server.DescribePluginRsp.UserInfo:type_name -> trpc.KEP.plugin_config_server.UserInfo
	1,   // 24: trpc.KEP.plugin_config_server.DescribePluginRsp.PluginType:type_name -> trpc.KEP.plugin_config_server.PluginTypeEnum
	46,  // 25: trpc.KEP.plugin_config_server.DescribePluginRsp.Tools:type_name -> trpc.KEP.plugin_config_server.ToolInfo
	3,   // 26: trpc.KEP.plugin_config_server.DescribePluginRsp.FinanceType:type_name -> trpc.KEP.plugin_config_server.FinanceTypeEnum
	4,   // 27: trpc.KEP.plugin_config_server.DescribePluginRsp.CreateType:type_name -> trpc.KEP.plugin_config_server.CreateTypeEnum
	20,  // 28: trpc.KEP.plugin_config_server.DescribePluginRsp.Headers:type_name -> trpc.KEP.plugin_config_server.PluginHeader
	46,  // 29: trpc.KEP.plugin_config_server.ListToolsRsp.Tools:type_name -> trpc.KEP.plugin_config_server.ToolInfo
	40,  // 30: trpc.KEP.plugin_config_server.DescribeToolRsp.Header:type_name -> trpc.KEP.plugin_config_server.RequestParam
	40,  // 31: trpc.KEP.plugin_config_server.DescribeToolRsp.Query:type_name -> trpc.KEP.plugin_config_server.RequestParam
	40,  // 32: trpc.KEP.plugin_config_server.DescribeToolRsp.Body:type_name -> trpc.KEP.plugin_config_server.RequestParam
	41,  // 33: trpc.KEP.plugin_config_server.DescribeToolRsp.Outputs:type_name -> trpc.KEP.plugin_config_server.ResponseParam
	40,  // 34: trpc.KEP.plugin_config_server.DescribeToolRsp.Inputs:type_name -> trpc.KEP.plugin_config_server.RequestParam
	39,  // 35: trpc.KEP.plugin_config_server.DescribeToolRsp.Example:type_name -> trpc.KEP.plugin_config_server.ToolExample
	2,   // 36: trpc.KEP.plugin_config_server.DescribeToolRsp.AuthType:type_name -> trpc.KEP.plugin_config_server.AuthTypeEnum
	47,  // 37: trpc.KEP.plugin_config_server.DescribeToolRsp.AuthInfo:type_name -> trpc.KEP.plugin_config_server.AuthInfo
	4,   // 38: trpc.KEP.plugin_config_server.DescribeToolRsp.CreateType:type_name -> trpc.KEP.plugin_config_server.CreateTypeEnum
	74,  // 39: trpc.KEP.plugin_config_server.DescribeToolRsp.MCPServer:type_name -> trpc.KEP.plugin_config_server.MCPServerInfo
	0,   // 40: trpc.KEP.plugin_config_server.RequestParam.Type:type_name -> trpc.KEP.plugin_config_server.TypeEnum
	40,  // 41: trpc.KEP.plugin_config_server.RequestParam.SubParams:type_name -> trpc.KEP.plugin_config_server.RequestParam
	40,  // 42: trpc.KEP.plugin_config_server.RequestParam.OneOf:type_name -> trpc.KEP.plugin_config_server.RequestParam
	40,  // 43: trpc.KEP.plugin_config_server.RequestParam.AnyOf:type_name -> trpc.KEP.plugin_config_server.RequestParam
	0,   // 44: trpc.KEP.plugin_config_server.ResponseParam.Type:type_name -> trpc.KEP.plugin_config_server.TypeEnum
	41,  // 45: trpc.KEP.plugin_config_server.ResponseParam.SubParams:type_name -> trpc.KEP.plugin_config_server.ResponseParam
	40,  // 46: trpc.KEP.plugin_config_server.CheckToolReq.Header:type_name -> trpc.KEP.plugin_config_server.RequestParam
	40,  // 47: trpc.KEP.plugin_config_server.CheckToolReq.Query:type_name -> trpc.KEP.plugin_config_server.RequestParam
	40,  // 48: trpc.KEP.plugin_config_server.CheckToolReq.Body:type_name -> trpc.KEP.plugin_config_server.RequestParam
	41,  // 49: trpc.KEP.plugin_config_server.CheckToolReq.Outputs:type_name -> trpc.KEP.plugin_config_server.ResponseParam
	2,   // 50: trpc.KEP.plugin_config_server.CheckToolReq.AuthType:type_name -> trpc.KEP.plugin_config_server.AuthTypeEnum
	47,  // 51: trpc.KEP.plugin_config_server.CheckToolReq.AuthInfo:type_name -> trpc.KEP.plugin_config_server.AuthInfo
	4,   // 52: trpc.KEP.plugin_config_server.CheckToolReq.CreateType:type_name -> trpc.KEP.plugin_config_server.CreateTypeEnum
	2,   // 53: trpc.KEP.plugin_config_server.PluginInfo.AuthType:type_name -> trpc.KEP.plugin_config_server.AuthTypeEnum
	47,  // 54: trpc.KEP.plugin_config_server.PluginInfo.AuthInfo:type_name -> trpc.KEP.plugin_config_server.AuthInfo
	44,  // 55: trpc.KEP.plugin_config_server.PluginInfo.UserInfo:type_name -> trpc.KEP.plugin_config_server.UserInfo
	1,   // 56: trpc.KEP.plugin_config_server.PluginInfo.PluginType:type_name -> trpc.KEP.plugin_config_server.PluginTypeEnum
	46,  // 57: trpc.KEP.plugin_config_server.PluginInfo.Tools:type_name -> trpc.KEP.plugin_config_server.ToolInfo
	3,   // 58: trpc.KEP.plugin_config_server.PluginInfo.FinanceType:type_name -> trpc.KEP.plugin_config_server.FinanceTypeEnum
	4,   // 59: trpc.KEP.plugin_config_server.PluginInfo.CreateType:type_name -> trpc.KEP.plugin_config_server.CreateTypeEnum
	20,  // 60: trpc.KEP.plugin_config_server.PluginInfo.Headers:type_name -> trpc.KEP.plugin_config_server.PluginHeader
	6,   // 61: trpc.KEP.plugin_config_server.PluginInfo.WhiteListType:type_name -> trpc.KEP.plugin_config_server.WhiteListTypeEnum
	40,  // 62: trpc.KEP.plugin_config_server.ToolInfo.Header:type_name -> trpc.KEP.plugin_config_server.RequestParam
	40,  // 63: trpc.KEP.plugin_config_server.ToolInfo.Query:type_name -> trpc.KEP.plugin_config_server.RequestParam
	40,  // 64: trpc.KEP.plugin_config_server.ToolInfo.Body:type_name -> trpc.KEP.plugin_config_server.RequestParam
	41,  // 65: trpc.KEP.plugin_config_server.ToolInfo.Outputs:type_name -> trpc.KEP.plugin_config_server.ResponseParam
	40,  // 66: trpc.KEP.plugin_config_server.ToolInfo.Inputs:type_name -> trpc.KEP.plugin_config_server.RequestParam
	39,  // 67: trpc.KEP.plugin_config_server.ToolInfo.Example:type_name -> trpc.KEP.plugin_config_server.ToolExample
	10,  // 68: trpc.KEP.plugin_config_server.AuthInfo.KeyLocation:type_name -> trpc.KEP.plugin_config_server.AuthInfo.KeyLocationTypeEnum
	50,  // 69: trpc.KEP.plugin_config_server.ListToolRefsRsp.ToolRefs:type_name -> trpc.KEP.plugin_config_server.ToolRef
	11,  // 70: trpc.KEP.plugin_config_server.ToolRef.RefType:type_name -> trpc.KEP.plugin_config_server.ToolRef.RefTypeEnum
	0,   // 71: trpc.KEP.plugin_config_server.AppToolReqParam.Type:type_name -> trpc.KEP.plugin_config_server.TypeEnum
	55,  // 72: trpc.KEP.plugin_config_server.AppToolReqParam.SubParams:type_name -> trpc.KEP.plugin_config_server.AppToolReqParam
	0,   // 73: trpc.KEP.plugin_config_server.AppToolRspParam.Type:type_name -> trpc.KEP.plugin_config_server.TypeEnum
	56,  // 74: trpc.KEP.plugin_config_server.AppToolRspParam.SubParams:type_name -> trpc.KEP.plugin_config_server.AppToolRspParam
	55,  // 75: trpc.KEP.plugin_config_server.SaveAppToolReq.Inputs:type_name -> trpc.KEP.plugin_config_server.AppToolReqParam
	56,  // 76: trpc.KEP.plugin_config_server.SaveAppToolReq.Outputs:type_name -> trpc.KEP.plugin_config_server.AppToolRspParam
	61,  // 77: trpc.KEP.plugin_config_server.ListAppToolsRsp.Tools:type_name -> trpc.KEP.plugin_config_server.AppToolItem
	55,  // 78: trpc.KEP.plugin_config_server.AppToolItem.Inputs:type_name -> trpc.KEP.plugin_config_server.AppToolReqParam
	4,   // 79: trpc.KEP.plugin_config_server.AppToolItem.CreateType:type_name -> trpc.KEP.plugin_config_server.CreateTypeEnum
	66,  // 80: trpc.KEP.plugin_config_server.AppToolItem.Headers:type_name -> trpc.KEP.plugin_config_server.AppPluginHeader
	55,  // 81: trpc.KEP.plugin_config_server.DescribeAppToolRsp.Inputs:type_name -> trpc.KEP.plugin_config_server.AppToolReqParam
	56,  // 82: trpc.KEP.plugin_config_server.DescribeAppToolRsp.Outputs:type_name -> trpc.KEP.plugin_config_server.AppToolRspParam
	5,   // 83: trpc.KEP.plugin_config_server.ListAppToolsInfoReq.EnvTag:type_name -> trpc.KEP.plugin_config_server.EnvType
	73,  // 84: trpc.KEP.plugin_config_server.ListAppToolsInfoRsp.Tools:type_name -> trpc.KEP.plugin_config_server.AppToolInfo
	66,  // 85: trpc.KEP.plugin_config_server.GetAppPluginRequiredHeaderRsp.Headers:type_name -> trpc.KEP.plugin_config_server.AppPluginHeader
	66,  // 86: trpc.KEP.plugin_config_server.DescribeAppPluginRsp.Headers:type_name -> trpc.KEP.plugin_config_server.AppPluginHeader
	66,  // 87: trpc.KEP.plugin_config_server.SaveAppPluginReq.Headers:type_name -> trpc.KEP.plugin_config_server.AppPluginHeader
	1,   // 88: trpc.KEP.plugin_config_server.AppToolInfo.PluginType:type_name -> trpc.KEP.plugin_config_server.PluginTypeEnum
	55,  // 89: trpc.KEP.plugin_config_server.AppToolInfo.Inputs:type_name -> trpc.KEP.plugin_config_server.AppToolReqParam
	56,  // 90: trpc.KEP.plugin_config_server.AppToolInfo.Outputs:type_name -> trpc.KEP.plugin_config_server.AppToolRspParam
	4,   // 91: trpc.KEP.plugin_config_server.AppToolInfo.CreateType:type_name -> trpc.KEP.plugin_config_server.CreateTypeEnum
	74,  // 92: trpc.KEP.plugin_config_server.AppToolInfo.MCPServer:type_name -> trpc.KEP.plugin_config_server.MCPServerInfo
	86,  // 93: trpc.KEP.plugin_config_server.MCPServerInfo.Headers:type_name -> trpc.KEP.plugin_config_server.MCPServerInfo.HeadersEntry
	87,  // 94: trpc.KEP.plugin_config_server.ListAppToolReleasePreviewRsp.List:type_name -> trpc.KEP.plugin_config_server.ListAppToolReleasePreviewRsp.AppTool
	5,   // 95: trpc.KEP.plugin_config_server.SyncAppToolRedisReq.EnvTag:type_name -> trpc.KEP.plugin_config_server.EnvType
	12,  // 96: trpc.KEP.plugin_config_server.CheckPermissionReq.Module:type_name -> trpc.KEP.plugin_config_server.CheckPermissionReq.ModuleEnum
	88,  // 97: trpc.KEP.plugin_config_server.CheckPermissionReq.List:type_name -> trpc.KEP.plugin_config_server.CheckPermissionReq.CheckItem
	89,  // 98: trpc.KEP.plugin_config_server.CheckPermissionRsp.Results:type_name -> trpc.KEP.plugin_config_server.CheckPermissionRsp.Result
	20,  // 99: trpc.KEP.plugin_config_server.CreateMCPPluginReq.MCPPluginItem.Headers:type_name -> trpc.KEP.plugin_config_server.PluginHeader
	13,  // 100: trpc.KEP.plugin_config_server.CheckPermissionRsp.Result.Status:type_name -> trpc.KEP.plugin_config_server.CheckPermissionRsp.PermissionStatus
	14,  // 101: trpc.KEP.plugin_config_server.PluginConfig.ListPlugins:input_type -> trpc.KEP.plugin_config_server.ListPluginsReq
	16,  // 102: trpc.KEP.plugin_config_server.PluginConfig.CreatePlugin:input_type -> trpc.KEP.plugin_config_server.CreatePluginReq
	18,  // 103: trpc.KEP.plugin_config_server.PluginConfig.ModifyPlugin:input_type -> trpc.KEP.plugin_config_server.ModifyPluginReq
	21,  // 104: trpc.KEP.plugin_config_server.PluginConfig.CreateMCPPlugin:input_type -> trpc.KEP.plugin_config_server.CreateMCPPluginReq
	23,  // 105: trpc.KEP.plugin_config_server.PluginConfig.ModifyMCPPlugin:input_type -> trpc.KEP.plugin_config_server.ModifyMCPPluginReq
	25,  // 106: trpc.KEP.plugin_config_server.PluginConfig.CheckMCPServer:input_type -> trpc.KEP.plugin_config_server.CheckMCPServerReq
	31,  // 107: trpc.KEP.plugin_config_server.PluginConfig.DeletePlugin:input_type -> trpc.KEP.plugin_config_server.DeletePluginReq
	33,  // 108: trpc.KEP.plugin_config_server.PluginConfig.DescribePlugin:input_type -> trpc.KEP.plugin_config_server.DescribePluginReq
	35,  // 109: trpc.KEP.plugin_config_server.PluginConfig.ListTools:input_type -> trpc.KEP.plugin_config_server.ListToolsReq
	42,  // 110: trpc.KEP.plugin_config_server.PluginConfig.CheckTool:input_type -> trpc.KEP.plugin_config_server.CheckToolReq
	48,  // 111: trpc.KEP.plugin_config_server.PluginConfig.ListToolRefs:input_type -> trpc.KEP.plugin_config_server.ListToolRefsReq
	51,  // 112: trpc.KEP.plugin_config_server.PluginConfig.AddAppTool:input_type -> trpc.KEP.plugin_config_server.AddAppToolReq
	53,  // 113: trpc.KEP.plugin_config_server.PluginConfig.DeleteAppTool:input_type -> trpc.KEP.plugin_config_server.DeleteAppToolReq
	57,  // 114: trpc.KEP.plugin_config_server.PluginConfig.SaveAppTool:input_type -> trpc.KEP.plugin_config_server.SaveAppToolReq
	59,  // 115: trpc.KEP.plugin_config_server.PluginConfig.ListAppTools:input_type -> trpc.KEP.plugin_config_server.ListAppToolsReq
	62,  // 116: trpc.KEP.plugin_config_server.PluginConfig.DescribeAppTool:input_type -> trpc.KEP.plugin_config_server.DescribeAppToolReq
	67,  // 117: trpc.KEP.plugin_config_server.PluginConfig.GetAppPluginRequiredHeader:input_type -> trpc.KEP.plugin_config_server.GetAppPluginRequiredHeaderReq
	69,  // 118: trpc.KEP.plugin_config_server.PluginConfig.DescribeAppPlugin:input_type -> trpc.KEP.plugin_config_server.DescribeAppPluginReq
	71,  // 119: trpc.KEP.plugin_config_server.PluginConfig.SaveAppPlugin:input_type -> trpc.KEP.plugin_config_server.SaveAppPluginReq
	77,  // 120: trpc.KEP.plugin_config_server.PluginConfig.ListAppToolReleasePreview:input_type -> trpc.KEP.plugin_config_server.ListAppToolReleasePreviewReq
	14,  // 121: trpc.KEP.plugin_config_server.PluginConfigApi.ListPlugins:input_type -> trpc.KEP.plugin_config_server.ListPluginsReq
	16,  // 122: trpc.KEP.plugin_config_server.PluginConfigApi.CreatePlugin:input_type -> trpc.KEP.plugin_config_server.CreatePluginReq
	27,  // 123: trpc.KEP.plugin_config_server.PluginConfigApi.CreateMCPPluginInner:input_type -> trpc.KEP.plugin_config_server.CreateMCPPluginInnerReq
	18,  // 124: trpc.KEP.plugin_config_server.PluginConfigApi.ModifyPlugin:input_type -> trpc.KEP.plugin_config_server.ModifyPluginReq
	29,  // 125: trpc.KEP.plugin_config_server.PluginConfigApi.ModifyMCPPluginInner:input_type -> trpc.KEP.plugin_config_server.ModifyMCPPluginInnerReq
	31,  // 126: trpc.KEP.plugin_config_server.PluginConfigApi.DeletePlugin:input_type -> trpc.KEP.plugin_config_server.DeletePluginReq
	33,  // 127: trpc.KEP.plugin_config_server.PluginConfigApi.DescribePlugin:input_type -> trpc.KEP.plugin_config_server.DescribePluginReq
	35,  // 128: trpc.KEP.plugin_config_server.PluginConfigApi.ListTools:input_type -> trpc.KEP.plugin_config_server.ListToolsReq
	37,  // 129: trpc.KEP.plugin_config_server.PluginConfigApi.DescribeTool:input_type -> trpc.KEP.plugin_config_server.DescribeToolReq
	42,  // 130: trpc.KEP.plugin_config_server.PluginConfigApi.CheckTool:input_type -> trpc.KEP.plugin_config_server.CheckToolReq
	64,  // 131: trpc.KEP.plugin_config_server.PluginConfigApi.ListAppToolsInfo:input_type -> trpc.KEP.plugin_config_server.ListAppToolsInfoReq
	75,  // 132: trpc.KEP.plugin_config_server.PluginConfigApi.AddKnowledgeQATool:input_type -> trpc.KEP.plugin_config_server.AddKnowledgeQAToolReq
	81,  // 133: trpc.KEP.plugin_config_server.PluginConfigApi.CheckPermission:input_type -> trpc.KEP.plugin_config_server.CheckPermissionReq
	90,  // 134: trpc.KEP.plugin_config_server.PluginConfigApi.GetUnreleasedCount:input_type -> trpc.KEP.bot_task_config_server.GetUnreleasedCountReq
	91,  // 135: trpc.KEP.plugin_config_server.PluginConfigApi.SendDataSyncTaskEvent:input_type -> trpc.KEP.bot_task_config_server.SendDataSyncTaskEventReq
	92,  // 136: trpc.KEP.plugin_config_server.PluginConfigApi.GetDataSyncTask:input_type -> trpc.KEP.bot_task_config_server.GetDataSyncTaskReq
	79,  // 137: trpc.KEP.plugin_config_server.PluginConfigApi.SyncAppToolRedis:input_type -> trpc.KEP.plugin_config_server.SyncAppToolRedisReq
	15,  // 138: trpc.KEP.plugin_config_server.PluginConfig.ListPlugins:output_type -> trpc.KEP.plugin_config_server.ListPluginsRsp
	17,  // 139: trpc.KEP.plugin_config_server.PluginConfig.CreatePlugin:output_type -> trpc.KEP.plugin_config_server.CreatePluginRsp
	19,  // 140: trpc.KEP.plugin_config_server.PluginConfig.ModifyPlugin:output_type -> trpc.KEP.plugin_config_server.ModifyPluginRsp
	22,  // 141: trpc.KEP.plugin_config_server.PluginConfig.CreateMCPPlugin:output_type -> trpc.KEP.plugin_config_server.CreateMCPPluginRsp
	24,  // 142: trpc.KEP.plugin_config_server.PluginConfig.ModifyMCPPlugin:output_type -> trpc.KEP.plugin_config_server.ModifyMCPPluginRsp
	26,  // 143: trpc.KEP.plugin_config_server.PluginConfig.CheckMCPServer:output_type -> trpc.KEP.plugin_config_server.CheckMCPServerRsp
	32,  // 144: trpc.KEP.plugin_config_server.PluginConfig.DeletePlugin:output_type -> trpc.KEP.plugin_config_server.DeletePluginRsp
	34,  // 145: trpc.KEP.plugin_config_server.PluginConfig.DescribePlugin:output_type -> trpc.KEP.plugin_config_server.DescribePluginRsp
	36,  // 146: trpc.KEP.plugin_config_server.PluginConfig.ListTools:output_type -> trpc.KEP.plugin_config_server.ListToolsRsp
	43,  // 147: trpc.KEP.plugin_config_server.PluginConfig.CheckTool:output_type -> trpc.KEP.plugin_config_server.CheckToolRsp
	49,  // 148: trpc.KEP.plugin_config_server.PluginConfig.ListToolRefs:output_type -> trpc.KEP.plugin_config_server.ListToolRefsRsp
	52,  // 149: trpc.KEP.plugin_config_server.PluginConfig.AddAppTool:output_type -> trpc.KEP.plugin_config_server.AddAppToolRsp
	54,  // 150: trpc.KEP.plugin_config_server.PluginConfig.DeleteAppTool:output_type -> trpc.KEP.plugin_config_server.DeleteAppToolRsp
	58,  // 151: trpc.KEP.plugin_config_server.PluginConfig.SaveAppTool:output_type -> trpc.KEP.plugin_config_server.SaveAppToolRsp
	60,  // 152: trpc.KEP.plugin_config_server.PluginConfig.ListAppTools:output_type -> trpc.KEP.plugin_config_server.ListAppToolsRsp
	63,  // 153: trpc.KEP.plugin_config_server.PluginConfig.DescribeAppTool:output_type -> trpc.KEP.plugin_config_server.DescribeAppToolRsp
	68,  // 154: trpc.KEP.plugin_config_server.PluginConfig.GetAppPluginRequiredHeader:output_type -> trpc.KEP.plugin_config_server.GetAppPluginRequiredHeaderRsp
	70,  // 155: trpc.KEP.plugin_config_server.PluginConfig.DescribeAppPlugin:output_type -> trpc.KEP.plugin_config_server.DescribeAppPluginRsp
	72,  // 156: trpc.KEP.plugin_config_server.PluginConfig.SaveAppPlugin:output_type -> trpc.KEP.plugin_config_server.SaveAppPluginRsp
	78,  // 157: trpc.KEP.plugin_config_server.PluginConfig.ListAppToolReleasePreview:output_type -> trpc.KEP.plugin_config_server.ListAppToolReleasePreviewRsp
	15,  // 158: trpc.KEP.plugin_config_server.PluginConfigApi.ListPlugins:output_type -> trpc.KEP.plugin_config_server.ListPluginsRsp
	17,  // 159: trpc.KEP.plugin_config_server.PluginConfigApi.CreatePlugin:output_type -> trpc.KEP.plugin_config_server.CreatePluginRsp
	28,  // 160: trpc.KEP.plugin_config_server.PluginConfigApi.CreateMCPPluginInner:output_type -> trpc.KEP.plugin_config_server.CreateMCPPluginInnerRsp
	19,  // 161: trpc.KEP.plugin_config_server.PluginConfigApi.ModifyPlugin:output_type -> trpc.KEP.plugin_config_server.ModifyPluginRsp
	30,  // 162: trpc.KEP.plugin_config_server.PluginConfigApi.ModifyMCPPluginInner:output_type -> trpc.KEP.plugin_config_server.ModifyMCPPluginInnerRsp
	32,  // 163: trpc.KEP.plugin_config_server.PluginConfigApi.DeletePlugin:output_type -> trpc.KEP.plugin_config_server.DeletePluginRsp
	34,  // 164: trpc.KEP.plugin_config_server.PluginConfigApi.DescribePlugin:output_type -> trpc.KEP.plugin_config_server.DescribePluginRsp
	36,  // 165: trpc.KEP.plugin_config_server.PluginConfigApi.ListTools:output_type -> trpc.KEP.plugin_config_server.ListToolsRsp
	38,  // 166: trpc.KEP.plugin_config_server.PluginConfigApi.DescribeTool:output_type -> trpc.KEP.plugin_config_server.DescribeToolRsp
	43,  // 167: trpc.KEP.plugin_config_server.PluginConfigApi.CheckTool:output_type -> trpc.KEP.plugin_config_server.CheckToolRsp
	65,  // 168: trpc.KEP.plugin_config_server.PluginConfigApi.ListAppToolsInfo:output_type -> trpc.KEP.plugin_config_server.ListAppToolsInfoRsp
	76,  // 169: trpc.KEP.plugin_config_server.PluginConfigApi.AddKnowledgeQATool:output_type -> trpc.KEP.plugin_config_server.AddKnowledgeQAToolRsp
	82,  // 170: trpc.KEP.plugin_config_server.PluginConfigApi.CheckPermission:output_type -> trpc.KEP.plugin_config_server.CheckPermissionRsp
	93,  // 171: trpc.KEP.plugin_config_server.PluginConfigApi.GetUnreleasedCount:output_type -> trpc.KEP.bot_task_config_server.GetUnreleasedCountRsp
	94,  // 172: trpc.KEP.plugin_config_server.PluginConfigApi.SendDataSyncTaskEvent:output_type -> trpc.KEP.bot_task_config_server.SendDataSyncTaskEventRsp
	95,  // 173: trpc.KEP.plugin_config_server.PluginConfigApi.GetDataSyncTask:output_type -> trpc.KEP.bot_task_config_server.GetDataSyncTaskRsp
	80,  // 174: trpc.KEP.plugin_config_server.PluginConfigApi.SyncAppToolRedis:output_type -> trpc.KEP.plugin_config_server.SyncAppToolRedisRsp
	138, // [138:175] is the sub-list for method output_type
	101, // [101:138] is the sub-list for method input_type
	101, // [101:101] is the sub-list for extension type_name
	101, // [101:101] is the sub-list for extension extendee
	0,   // [0:101] is the sub-list for field type_name
}

func init() { file_plugin_config_proto_init() }
func file_plugin_config_proto_init() {
	if File_plugin_config_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_plugin_config_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListPluginsReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_plugin_config_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListPluginsRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_plugin_config_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreatePluginReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_plugin_config_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreatePluginRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_plugin_config_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ModifyPluginReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_plugin_config_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ModifyPluginRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_plugin_config_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PluginHeader); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_plugin_config_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateMCPPluginReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_plugin_config_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateMCPPluginRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_plugin_config_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ModifyMCPPluginReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_plugin_config_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ModifyMCPPluginRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_plugin_config_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CheckMCPServerReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_plugin_config_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CheckMCPServerRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_plugin_config_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateMCPPluginInnerReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_plugin_config_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateMCPPluginInnerRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_plugin_config_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ModifyMCPPluginInnerReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_plugin_config_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ModifyMCPPluginInnerRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_plugin_config_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeletePluginReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_plugin_config_proto_msgTypes[18].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeletePluginRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_plugin_config_proto_msgTypes[19].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DescribePluginReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_plugin_config_proto_msgTypes[20].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DescribePluginRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_plugin_config_proto_msgTypes[21].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListToolsReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_plugin_config_proto_msgTypes[22].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListToolsRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_plugin_config_proto_msgTypes[23].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DescribeToolReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_plugin_config_proto_msgTypes[24].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DescribeToolRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_plugin_config_proto_msgTypes[25].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ToolExample); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_plugin_config_proto_msgTypes[26].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RequestParam); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_plugin_config_proto_msgTypes[27].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ResponseParam); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_plugin_config_proto_msgTypes[28].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CheckToolReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_plugin_config_proto_msgTypes[29].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CheckToolRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_plugin_config_proto_msgTypes[30].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UserInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_plugin_config_proto_msgTypes[31].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PluginInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_plugin_config_proto_msgTypes[32].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ToolInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_plugin_config_proto_msgTypes[33].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AuthInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_plugin_config_proto_msgTypes[34].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListToolRefsReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_plugin_config_proto_msgTypes[35].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListToolRefsRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_plugin_config_proto_msgTypes[36].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ToolRef); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_plugin_config_proto_msgTypes[37].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AddAppToolReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_plugin_config_proto_msgTypes[38].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AddAppToolRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_plugin_config_proto_msgTypes[39].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeleteAppToolReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_plugin_config_proto_msgTypes[40].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeleteAppToolRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_plugin_config_proto_msgTypes[41].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AppToolReqParam); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_plugin_config_proto_msgTypes[42].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AppToolRspParam); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_plugin_config_proto_msgTypes[43].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SaveAppToolReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_plugin_config_proto_msgTypes[44].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SaveAppToolRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_plugin_config_proto_msgTypes[45].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListAppToolsReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_plugin_config_proto_msgTypes[46].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListAppToolsRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_plugin_config_proto_msgTypes[47].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AppToolItem); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_plugin_config_proto_msgTypes[48].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DescribeAppToolReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_plugin_config_proto_msgTypes[49].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DescribeAppToolRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_plugin_config_proto_msgTypes[50].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListAppToolsInfoReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_plugin_config_proto_msgTypes[51].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListAppToolsInfoRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_plugin_config_proto_msgTypes[52].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AppPluginHeader); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_plugin_config_proto_msgTypes[53].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetAppPluginRequiredHeaderReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_plugin_config_proto_msgTypes[54].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetAppPluginRequiredHeaderRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_plugin_config_proto_msgTypes[55].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DescribeAppPluginReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_plugin_config_proto_msgTypes[56].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DescribeAppPluginRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_plugin_config_proto_msgTypes[57].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SaveAppPluginReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_plugin_config_proto_msgTypes[58].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SaveAppPluginRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_plugin_config_proto_msgTypes[59].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AppToolInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_plugin_config_proto_msgTypes[60].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MCPServerInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_plugin_config_proto_msgTypes[61].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AddKnowledgeQAToolReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_plugin_config_proto_msgTypes[62].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AddKnowledgeQAToolRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_plugin_config_proto_msgTypes[63].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListAppToolReleasePreviewReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_plugin_config_proto_msgTypes[64].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListAppToolReleasePreviewRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_plugin_config_proto_msgTypes[65].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SyncAppToolRedisReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_plugin_config_proto_msgTypes[66].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SyncAppToolRedisRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_plugin_config_proto_msgTypes[67].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CheckPermissionReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_plugin_config_proto_msgTypes[68].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CheckPermissionRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_plugin_config_proto_msgTypes[69].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateMCPPluginReq_MCPPluginItem); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_plugin_config_proto_msgTypes[70].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateMCPPluginInnerReq_PluginHeader); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_plugin_config_proto_msgTypes[71].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ModifyMCPPluginInnerReq_PluginHeader); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_plugin_config_proto_msgTypes[73].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListAppToolReleasePreviewRsp_AppTool); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_plugin_config_proto_msgTypes[74].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CheckPermissionReq_CheckItem); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_plugin_config_proto_msgTypes[75].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CheckPermissionRsp_Result); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_plugin_config_proto_rawDesc,
			NumEnums:      14,
			NumMessages:   76,
			NumExtensions: 0,
			NumServices:   2,
		},
		GoTypes:           file_plugin_config_proto_goTypes,
		DependencyIndexes: file_plugin_config_proto_depIdxs,
		EnumInfos:         file_plugin_config_proto_enumTypes,
		MessageInfos:      file_plugin_config_proto_msgTypes,
	}.Build()
	File_plugin_config_proto = out.File
	file_plugin_config_proto_rawDesc = nil
	file_plugin_config_proto_goTypes = nil
	file_plugin_config_proto_depIdxs = nil
}
